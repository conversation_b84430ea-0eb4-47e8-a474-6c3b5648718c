@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);

  --radius-md: calc(var(--radius) - 2px);

  --radius-lg: var(--radius);

  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);

  --color-foreground: var(--foreground);

  --color-card: var(--card);

  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);

  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);

  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);

  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);

  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);

  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);

  --color-border: var(--border);

  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);

  --color-chart-2: var(--chart-2);

  --color-chart-3: var(--chart-3);

  --color-chart-4: var(--chart-4);

  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);

  --color-sidebar-foreground: var(--sidebar-foreground);

  --color-sidebar-primary: var(--sidebar-primary);

  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);

  --color-sidebar-accent: var(--sidebar-accent);

  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);

  --color-sidebar-border: var(--sidebar-border);

  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;

  --background: #f4f4f4;

  --background-admin: #f1ebff;

  --background-welon-trust: #fffaef;

  --foreground: #18141a;

  --eggplant: #7c71fa;

  --purple: #241f52;

  --lemon: #ecfe7b;

  --off-white: #f4f4f4;

  --off-black: #18141a;

  --berry: #a3cbfc;

  --tangerine: #f5ab66;

  --lime: #acfd99;

  --custom-gray-light: #9b9b9b;

  --custom-gray-medium: #7e7e7e;

  --custom-gray-dark: #606060;

  --custom-blue-light: oklch(0.96 0.013 238.75);

  --slate-medium: oklch(0.62 0.018 264.5);

  --slate-dark: oklch(0.28 0.032 264.5);

  --card: oklch(1 0 0);

  --card-foreground: oklch(0.13 0.028 261.692);

  --popover: oklch(1 0 0);

  --popover-foreground: oklch(0.13 0.028 261.692);

  --primary: oklch(0.21 0.034 264.665);

  --primary-foreground: oklch(0.985 0.002 247.839);

  --secondary: oklch(0.967 0.003 264.542);

  --secondary-foreground: oklch(0.21 0.034 264.665);

  --muted: oklch(0.967 0.003 264.542);

  --muted-foreground: oklch(0.551 0.027 264.364);

  --accent: oklch(0.967 0.003 264.542);

  --accent-foreground: oklch(0.21 0.034 264.665);

  --destructive: oklch(0.577 0.245 27.325);

  --border: oklch(0.928 0.006 264.531);

  --input: oklch(0.928 0.006 264.531);

  --ring: oklch(0.707 0.022 261.325);

  --chart-1: oklch(0.646 0.222 41.116);

  --chart-2: oklch(0.6 0.118 184.704);

  --chart-3: oklch(0.398 0.07 227.392);

  --chart-4: oklch(0.828 0.189 84.429);

  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0.002 247.839);

  --sidebar-foreground: oklch(0.13 0.028 261.692);

  --sidebar-primary: oklch(0.21 0.034 264.665);

  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);

  --sidebar-accent: oklch(0.967 0.003 264.542);

  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);

  --sidebar-border: oklch(0.928 0.006 264.531);

  --sidebar-ring: oklch(0.707 0.022 261.325);
}

.dark {
  --background: #18141a;

  --background-admin: #18141a;

  --background-welon-trust: #18141a;

  --foreground: #f4f4f4;

  --custom-gray-light: oklch(0.25 0.04 264.5);

  --custom-gray-medium: oklch(0.55 0.025 264.5);

  --custom-gray-dark: oklch(0.85 0.01 264.5);

  --custom-blue-light: oklch(0.25 0.02 238.75);

  --slate-medium: oklch(0.62 0.018 264.5);

  --slate-dark: oklch(0.88 0.008 264.5);

  --card: oklch(0.21 0.034 264.665);

  --card-foreground: oklch(0.985 0.002 247.839);

  --popover: oklch(0.21 0.034 264.665);

  --popover-foreground: oklch(0.985 0.002 247.839);

  --primary: oklch(0.928 0.006 264.531);

  --primary-foreground: oklch(0.21 0.034 264.665);

  --secondary: oklch(0.278 0.033 256.848);

  --secondary-foreground: oklch(0.985 0.002 247.839);

  --muted: oklch(0.278 0.033 256.848);

  --muted-foreground: oklch(0.707 0.022 261.325);

  --accent: oklch(0.278 0.033 256.848);

  --accent-foreground: oklch(0.985 0.002 247.839);

  --destructive: oklch(0.704 0.191 22.216);

  --border: oklch(1 0 0 / 10%);

  --input: oklch(1 0 0 / 15%);

  --ring: oklch(0.551 0.027 264.364);

  --chart-1: oklch(0.488 0.243 264.376);

  --chart-2: oklch(0.696 0.17 162.48);

  --chart-3: oklch(0.769 0.188 70.08);

  --chart-4: oklch(0.627 0.265 303.9);

  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.21 0.034 264.665);

  --sidebar-foreground: oklch(0.985 0.002 247.839);

  --sidebar-primary: oklch(0.488 0.243 264.376);

  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);

  --sidebar-accent: oklch(0.278 0.033 256.848);

  --sidebar-accent-foreground: oklch(0.985 0.002 247.839);

  --sidebar-border: oklch(1 0 0 / 10%);

  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-inter), sans-serif;
  }

  /* Set DM Sans for all headings by default */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-dm-sans), sans-serif;
  }

  /* Prevent horizontal overflow in prose content */
  .prose {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Ensure all elements respect container boundaries */
  .prose * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Handle wide tables in prose content */
  .prose table {
    width: 100%;
    table-layout: fixed;
    word-wrap: break-word;
  }

  /* Handle long URLs and text */
  .prose a,
  .prose code,
  .prose pre {
    word-break: break-all;
    overflow-wrap: break-word;
  }

  #acc-portal-\[readingGuide-container\] {
    position: fixed !important;
    bottom: 0 !important;
    right: 0 !important;
    width: auto !important;
    height: auto !important;
    z-index: 9999 !important;
    pointer-events: none !important;
  }

  #acc-portal-\[readingGuide-container\]:empty {
    display: none !important;
  }
}
