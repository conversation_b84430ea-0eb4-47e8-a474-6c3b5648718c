'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Variable } from './tiptap-editor';

interface ConditionalLogicModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (conditionalBlock: string) => void;
  variables: Variable[];
}

export function ConditionalLogicModal({
  isOpen,
  onClose,
  onInsert,
  variables,
}: ConditionalLogicModalProps) {
  const [selectedVariable, setSelectedVariable] = useState('');
  const [condition, setCondition] = useState('equals');
  const [expectedValue, setExpectedValue] = useState('');
  const [contentWhenTrue, setContentWhenTrue] = useState('');

  console.log('variables', variables);

  const handleInsert = () => {
    if (!selectedVariable || !expectedValue || !contentWhenTrue) {
      return;
    }

    // Extract variable name from the full variable value (e.g., "{{variable}}" -> "variable")
    const variableName = selectedVariable.replace(/[{}]/g, '');

    // Create the appropriate condition based on the selected condition type
    const conditionHelper = condition === 'equals' ? 'eq' : 'ne';

    const conditionalBlock = `{{#if (${conditionHelper} ${variableName} "${expectedValue}")}}

${contentWhenTrue}

{{/if}}`;

    onInsert(conditionalBlock);
    handleClose();
  };

  const handleClose = () => {
    setSelectedVariable('');
    setCondition('equals');
    setExpectedValue('');
    setContentWhenTrue('');
    onClose();
  };

  // Filter variables to show only single simple variables (not multiple variables or complex logic)
  const answerVariables = variables.filter(v => {
    // Exclude user/document properties
    if (v.value.includes('user.') || v.value.includes('document.')) {
      return false;
    }

    // Only allow variables that contain exactly one simple variable like {{variableName}}
    // This regex matches exactly one {{word}} pattern with optional spaces around it
    const singleVariablePattern = /^\s*\{\{[a-zA-Z_][a-zA-Z0-9_]*\}\}\s*$/;

    return singleVariablePattern.test(v.value);
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Add Conditional Logic</DialogTitle>
          <DialogDescription>
            Create content that only shows when a specific condition is met.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          <div>
            <Label htmlFor='variable'>Variable</Label>
            <Select
              value={selectedVariable}
              onValueChange={setSelectedVariable}
            >
              <SelectTrigger className='h-auto min-h-[40px]'>
                <SelectValue placeholder='Select a variable'>
                  {selectedVariable &&
                    (() => {
                      const selectedVar = answerVariables.find(
                        v => v.value === selectedVariable
                      );
                      return selectedVar ? (
                        <div className='flex flex-col gap-1 py-1 text-left'>
                          <div className='font-medium text-sm'>
                            {selectedVar.label}
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            {selectedVar.questionInfo?.dataType && (
                              <span>
                                Type: {selectedVar.questionInfo.dataType}
                              </span>
                            )}
                            {selectedVar.questionInfo?.example && (
                              <span className='ml-2'>
                                Example: "{selectedVar.questionInfo.example}"
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        selectedVariable
                      );
                    })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent className='max-w-[400px]'>
                {answerVariables.map(variable => (
                  <SelectItem key={variable.id} value={variable.value}>
                    <div className='flex flex-col gap-1 py-1'>
                      <div className='font-medium'>{variable.label}</div>
                      <div className='text-xs text-muted-foreground break-words'>
                        {variable.questionInfo?.dataType && (
                          <span className='font-medium'>
                            Type: {variable.questionInfo.dataType}
                          </span>
                        )}
                        {variable.questionInfo?.example && (
                          <span className='ml-2'>
                            Example: "{variable.questionInfo.example}"
                          </span>
                        )}
                        {variable.value && (
                          <span> Value: "{variable.value}"</span>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor='condition'>Condition</Label>
            <Select value={condition} onValueChange={setCondition}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='equals'>Equals</SelectItem>
                <SelectItem value='not_equals'>Not Equals</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor='expected-value'>Expected Value</Label>
            <Input
              id='expected-value'
              value={expectedValue}
              onChange={e => setExpectedValue(e.target.value)}
              placeholder='e.g., Yes, No, etc.'
            />
          </div>

          <div>
            <Label htmlFor='content'>
              Content to show when condition is true
            </Label>
            <Textarea
              id='content'
              value={contentWhenTrue}
              onChange={e => setContentWhenTrue(e.target.value)}
              placeholder='Enter the content that should appear when the condition is met...'
              rows={4}
            />
          </div>

          <div className='p-3 bg-blue-50 rounded-md text-sm'>
            <strong>Preview:</strong>
            <pre className='mt-2 text-xs bg-white p-2 rounded border'>
              {selectedVariable && expectedValue && contentWhenTrue
                ? `{{#if (${condition === 'equals' ? 'eq' : 'ne'} ${selectedVariable.replace(/[{}]/g, '')} "${expectedValue}")}}

${contentWhenTrue}

{{/if}}`
                : 'Fill in the fields above to see the preview'}
            </pre>
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleInsert}
            disabled={!selectedVariable || !expectedValue || !contentWhenTrue}
          >
            Insert Conditional Block
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
