'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Users,
  Heart,
  Building,
  User,
  Code,
  Settings,
} from 'lucide-react';
import {
  StepsData,
  getInterviewV2Structure,
  createVariablesFromInterviewV2,
} from '@/lib/utils/interviewV2Variables';

interface InterviewV2SummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  stepsData?: StepsData;
  title?: string;
}

const InterviewV2SummaryModal = ({
  isOpen,
  onClose,
  stepsData,
  title = 'Interview V2 Variables & Structure',
}: InterviewV2SummaryModalProps) => {
  const [activeTab, setActiveTab] = useState('variables');
  const structure = getInterviewV2Structure();
  const variables = createVariablesFromInterviewV2();

  const getSectionIcon = (sectionName: string) => {
    switch (sectionName.toLowerCase()) {
      case 'your profile':
        return <User className='h-4 w-4' />;
      case 'people library':
        return <Users className='h-4 w-4' />;
      case 'emergency key considerations':
        return <Heart className='h-4 w-4' />;
      case 'after you die':
        return <FileText className='h-4 w-4' />;
      case 'financial decisions':
        return <Building className='h-4 w-4' />;
      case 'medical decisions':
        return <Heart className='h-4 w-4' />;
      case 'additional considerations':
        return <FileText className='h-4 w-4' />;
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  const getSectionColor = (sectionName: string) => {
    switch (sectionName.toLowerCase()) {
      case 'your profile':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'people library':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'emergency key considerations':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'after you die':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      case 'financial decisions':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'medical decisions':
        return 'bg-pink-50 border-pink-200 text-pink-800';
      case 'additional considerations':
        return 'bg-gray-50 border-gray-200 text-gray-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-[95vw] !max-w-[95vw] !w-[55vw] max-h-[95vh] flex flex-col overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            {title}
          </DialogTitle>
          <DialogDescription>
            Complete reference for Interview V2 variables and question
            structure. Use this to understand what data is available for your
            templates and how it's collected from users.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue='variables' className='flex-1 flex flex-col'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='variables' className='flex items-center gap-2'>
              <Code className='h-4 w-4' />
              Template Variables
            </TabsTrigger>
            <TabsTrigger value='structure' className='flex items-center gap-2'>
              <FileText className='h-4 w-4' />
              Interview Questions
            </TabsTrigger>
            <TabsTrigger value='handlebars' className='flex items-center gap-2'>
              <Settings className='h-4 w-4' />
              Handlebars Commands
            </TabsTrigger>
          </TabsList>

          <TabsContent value='variables' className='flex-1 mt-4'>
            <ScrollArea className='h-[60vh]'>
              <div className='space-y-4 pr-4'>
                <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'>
                  <h4 className='font-semibold text-blue-900 mb-2'>
                    How to Use Variables
                  </h4>
                  <div className='text-sm text-blue-800 space-y-2'>
                    <p>
                      • Use Handlebars syntax:{' '}
                      <code className='bg-blue-100 px-1 py-0.5 rounded'>
                        {'{{variableName}}'}
                      </code>
                    </p>
                    <p>
                      • For arrays, use loops:{' '}
                      <code className='bg-blue-100 px-1 py-0.5 rounded'>
                        {'{{#each arrayName}}{{property}}{{/each}}'}
                      </code>
                    </p>
                    <p>
                      • For conditionals:{' '}
                      <code className='bg-blue-100 px-1 py-0.5 rounded'>
                        {'{{#if condition}}text{{/if}}'}
                      </code>
                    </p>
                    <p>
                      • If-Else conditionals:{' '}
                      <code className='bg-blue-100 px-1 py-0.5 rounded'>
                        {'{{#if condition}}true text{{else}}false text{{/if}}'}
                      </code>
                    </p>
                    <p>
                      • Variables marked as "conditional" or "loop" show
                      complete examples with logic
                    </p>
                  </div>
                </div>

                {/* Group variables by their prefixes */}
                {Object.entries(
                  variables.reduce(
                    (groups, variable) => {
                      const prefix = variable.id.split('_')[0];
                      const groupName =
                        prefix.charAt(0).toUpperCase() + prefix.slice(1);
                      if (!groups[groupName]) groups[groupName] = [];
                      groups[groupName].push(variable);
                      return groups;
                    },
                    {} as Record<string, typeof variables>
                  )
                ).map(([groupName, groupVariables]) => (
                  <div key={groupName} className='space-y-2'>
                    <h3 className='font-semibold text-gray-800 border-b pb-1'>
                      {groupName}
                    </h3>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                      {groupVariables.map(variable => (
                        <div
                          key={variable.id}
                          className='bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow'
                        >
                          <div className='space-y-2'>
                            <div className='flex items-start justify-between gap-2'>
                              <p className='text-sm font-medium text-gray-900 leading-tight'>
                                {variable.label}
                              </p>
                              {variable.questionInfo && (
                                <Badge
                                  variant='secondary'
                                  className='text-xs shrink-0'
                                >
                                  {variable.questionInfo.dataType}
                                </Badge>
                              )}
                            </div>
                            <code className='text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded font-mono block break-all'>
                              {variable.value}
                            </code>
                            {variable.questionInfo && (
                              <div className='space-y-1'>
                                <p className='text-xs text-gray-600 leading-tight'>
                                  <span className='font-medium'>Question:</span>{' '}
                                  {variable.questionInfo.question}
                                </p>
                                {variable.questionInfo.possibleValues && (
                                  <p className='text-xs text-gray-500'>
                                    <span className='font-medium'>Values:</span>{' '}
                                    {variable.questionInfo.possibleValues.join(
                                      ', '
                                    )}
                                  </p>
                                )}
                                {variable.questionInfo.example && (
                                  <p className='text-xs text-green-600 bg-green-50 px-2 py-1 rounded'>
                                    <span className='font-medium'>
                                      Example:
                                    </span>{' '}
                                    {variable.questionInfo.example}
                                  </p>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value='structure' className='flex-1 mt-4'>
            <ScrollArea className='h-[60vh]'>
              <div className='space-y-6 pr-4'>
                <div className='bg-green-50 border border-green-200 rounded-lg p-4 mb-6'>
                  <h4 className='font-semibold text-green-900 mb-2'>
                    Interview Flow Structure
                  </h4>
                  <div className='text-sm text-green-800 space-y-1'>
                    <p>
                      • This shows the exact questions users answer during the
                      interview
                    </p>
                    <p>
                      • Each section represents a step in the interview process
                    </p>
                    <p>• Questions are asked in the order shown here</p>
                    <p>
                      • Use this to understand what data will be available for
                      your templates
                    </p>
                  </div>
                </div>
                {structure.map((section, sectionIndex) => (
                  <div key={sectionIndex} className='space-y-4'>
                    <div className='flex items-center gap-3'>
                      <Badge
                        variant='outline'
                        className={`px-3 py-1 ${getSectionColor(section.section)}`}
                      >
                        <span className='flex items-center gap-2'>
                          {getSectionIcon(section.section)}
                          {section.section}
                        </span>
                      </Badge>
                      <div className='flex-1 h-px bg-gray-200' />
                      <span className='text-sm text-gray-500'>
                        {section.questions.length}{' '}
                        {section.questions.length === 1
                          ? 'question'
                          : 'questions'}
                      </span>
                    </div>

                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                      {section.questions.map((qa, qaIndex) => (
                        <div
                          key={qaIndex}
                          className='bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow'
                        >
                          <div className='space-y-2'>
                            <div className='flex items-start gap-2'>
                              <Badge
                                variant='outline'
                                className='text-xs shrink-0 mt-0.5'
                              >
                                Q{qaIndex + 1}
                              </Badge>
                              <p className='text-sm font-medium text-gray-900 leading-tight'>
                                {qa.question}
                              </p>
                            </div>
                            <p className='text-xs text-gray-600 bg-blue-50 rounded px-2 py-1.5 border border-blue-200 leading-relaxed'>
                              <span className='font-medium text-blue-800'>
                                Purpose:
                              </span>{' '}
                              {qa.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {sectionIndex < structure.length - 1 && (
                      <Separator className='my-6' />
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value='handlebars' className='flex-1 mt-4'>
            <ScrollArea className='h-[60vh]'>
              <div className='space-y-6 pr-4'>
                {/* Basic Syntax Section */}
                <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-blue-900 mb-3 flex items-center gap-2'>
                    <Settings className='h-4 w-4' />
                    Basic Handlebars Syntax
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-blue-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Variable Output
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2'>
                        {`{{variableName}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Outputs the value of a variable
                      </p>
                    </div>

                    <div className='bg-white border border-blue-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Comments
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2'>
                        {`{{!-- This is a comment --}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Comments are not rendered in output
                      </p>
                    </div>
                  </div>
                </div>

                {/* Conditional Logic Section */}
                <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-green-900 mb-3'>
                    Conditional Logic
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-green-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        If Statement
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#if condition}}
  Content shown when true
{{/if}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Shows content only if condition is truthy
                      </p>
                    </div>

                    <div className='bg-white border border-green-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        If-Else Statement
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#if condition}}
  Content when true
{{else}}
  Content when false
{{/if}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Shows different content based on condition
                      </p>
                    </div>

                    <div className='bg-white border border-green-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Unless Statement
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#unless condition}}
  Content shown when false
{{/unless}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Shows content only if condition is falsy
                      </p>
                    </div>
                  </div>
                </div>

                {/* Loops Section */}
                <div className='bg-purple-50 border border-purple-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-purple-900 mb-3'>
                    Loops and Arrays
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-purple-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Each Loop
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#each arrayName}}
  {{this}} or {{property}}
{{/each}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Iterates through array items. Use this for simple values
                        or property for objects
                      </p>
                    </div>

                    <div className='bg-white border border-purple-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Each with Index
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#each arrayName}}
  {{@index}}: {{this}}
{{/each}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Access the current index with @index (0-based)
                      </p>
                    </div>

                    <div className='bg-white border border-purple-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Each with Else
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#each arrayName}}
  {{this}}
{{else}}
  No items found
{{/each}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Shows fallback content when array is empty
                      </p>
                    </div>
                  </div>
                </div>

                {/* Built-in Helpers Section */}
                <div className='bg-orange-50 border border-orange-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-orange-900 mb-3'>
                    Built-in Helpers
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-orange-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Comparison Helpers
                      </div>
                      <div className='space-y-2'>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (eq value1 value2)}}Equal{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (ne value1 value2)}}Not equal{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (gt number1 number2)}}Greater than{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (lt number1 number2)}}Less than{{/if}}`}
                        </code>
                      </div>
                      <p className='text-xs text-gray-600 mt-2'>
                        Compare values: eq, ne, gt, lt, gte, lte
                      </p>
                    </div>

                    <div className='bg-white border border-orange-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Logical Helpers
                      </div>
                      <div className='space-y-2'>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (and condition1 condition2)}}Both true{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (or condition1 condition2)}}At least one true{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (not condition)}}Condition is false{{/if}}`}
                        </code>
                      </div>
                      <p className='text-xs text-gray-600 mt-2'>
                        Combine conditions: and, or, not
                      </p>
                    </div>

                    <div className='bg-white border border-orange-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Existence Helpers
                      </div>
                      <div className='space-y-2'>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (exists value)}}Value exists{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (have value)}}Value has content{{/if}}`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{#if (has value)}}Value has content{{/if}}`}
                        </code>
                      </div>
                      <p className='text-xs text-gray-600 mt-2'>
                        Check if values exist and are not empty
                      </p>
                    </div>
                  </div>
                </div>

                {/* String and Array Helpers Section */}
                <div className='bg-teal-50 border border-teal-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-teal-900 mb-3'>
                    String & Array Helpers
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-teal-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        String Formatting
                      </div>
                      <div className='space-y-2'>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{upper "hello world"}} → HELLO WORLD`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{lower "HELLO WORLD"}} → hello world`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{formatDate dateValue}} → January 15, 2024`}
                        </code>
                      </div>
                      <p className='text-xs text-gray-600 mt-2'>
                        Format strings and dates
                      </p>
                    </div>

                    <div className='bg-white border border-teal-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Array Helpers
                      </div>
                      <div className='space-y-2'>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{length arrayName}} → 5`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{join arrayName ", "}} → item1, item2, item3`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{first arrayName}} → first item`}
                        </code>
                        <code className='text-xs bg-gray-100 px-2 py-1 rounded block'>
                          {`{{last arrayName}} → last item`}
                        </code>
                      </div>
                      <p className='text-xs text-gray-600 mt-2'>
                        Work with arrays: get length, join items, get first/last
                      </p>
                    </div>

                    <div className='bg-white border border-teal-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Lookup Helper
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2'>
                        {`{{lookup object "property name with spaces"}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Access object properties with spaces in names
                      </p>
                    </div>
                  </div>
                </div>

                {/* Practical Examples Section */}
                <div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
                  <h4 className='font-semibold text-gray-900 mb-3'>
                    Practical Examples
                  </h4>
                  <div className='space-y-3'>
                    <div className='bg-white border border-gray-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Conditional Text with Fallback
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#if spouse_firstName}}
married to {{spouse_firstName}} {{spouse_lastName}}
{{else}}
not married
{{/if}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Show different text based on marital status
                      </p>
                    </div>

                    <div className='bg-white border border-gray-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        List with Count Check
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#if (gt (length emergency_dependents) 0)}}
I have {{length emergency_dependents}} dependent(s):
{{#each emergency_dependents}}
- {{name}} ({{relationship}})
{{/each}}
{{else}}
I have no dependents.
{{/if}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Show list only if items exist, with count
                      </p>
                    </div>

                    <div className='bg-white border border-gray-200 rounded p-3'>
                      <div className='font-medium text-sm text-gray-900 mb-1'>
                        Complex Conditional Logic
                      </div>
                      <code className='text-xs bg-gray-100 px-2 py-1 rounded block mb-2 whitespace-pre'>
                        {`{{#if (and (exists spouse_firstName) (eq maritalStatus "married"))}}
My spouse {{spouse_firstName}} {{spouse_lastName}}
{{#if spouse_dateOfBirth}}
was born on {{formatDate spouse_dateOfBirth}}
{{/if}}
{{/if}}`}
                      </code>
                      <p className='text-xs text-gray-600'>
                        Combine multiple conditions and nested logic
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <div className='flex justify-between items-center pt-4 border-t bg-gray-50 -mx-6 -mb-6 px-6 pb-6 rounded-b-lg'>
          <div className='flex gap-6 text-sm text-gray-600'>
            <div className='flex items-center gap-2'>
              <Badge variant='secondary' className='text-xs'>
                {structure.reduce(
                  (total, section) => total + section.questions.length,
                  0
                )}
              </Badge>
              <span>Total Questions</span>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='secondary' className='text-xs'>
                {variables.length}
              </Badge>
              <span>Available Variables</span>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='secondary' className='text-xs'>
                {structure.length}
              </Badge>
              <span>Interview Sections</span>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='secondary' className='text-xs'>
                25+
              </Badge>
              <span>Handlebars Commands</span>
            </div>
          </div>
          <Button onClick={onClose} variant='outline'>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InterviewV2SummaryModal;
