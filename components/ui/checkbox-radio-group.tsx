'use client';

import * as React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface CheckboxRadioGroupProps {
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
}

interface CheckboxRadioItemProps {
  value: string;
  id: string;
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}

const CheckboxRadioGroupContext = React.createContext<{
  value?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
}>({});

function CheckboxRadioGroup({
  className,
  value,
  onValueChange,
  children,
  disabled = false,
  ...props
}: CheckboxRadioGroupProps) {
  return (
    <CheckboxRadioGroupContext.Provider
      value={{ value, onValueChange, disabled }}
    >
      <div className={cn('grid gap-3', className)} {...props}>
        {children}
      </div>
    </CheckboxRadioGroupContext.Provider>
  );
}

function CheckboxRadioItem({
  className,
  value: itemValue,
  id,
  children,
  disabled: itemDisabled = false,
  ...props
}: CheckboxRadioItemProps) {
  const {
    value,
    onValueChange,
    disabled: groupDisabled,
  } = React.useContext(CheckboxRadioGroupContext);

  const isChecked = value === itemValue;
  const isDisabled = itemDisabled || groupDisabled;

  const handleCheckedChange = (checked: boolean) => {
    if (isDisabled) return;

    if (checked) {
      onValueChange?.(itemValue);
    } else {
      // Allow unchecking - set to empty string or undefined
      onValueChange?.('');
    }
  };

  return (
    <div className={cn('flex items-center space-x-2', className)} {...props}>
      <Checkbox
        id={id}
        checked={isChecked}
        onCheckedChange={handleCheckedChange}
        disabled={isDisabled}
      />
      {children}
    </div>
  );
}

export { CheckboxRadioGroup, CheckboxRadioItem };
