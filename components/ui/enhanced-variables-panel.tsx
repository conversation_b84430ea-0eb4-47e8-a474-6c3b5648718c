'use client';

import React, { useState, useMemo } from 'react';
import { Button } from './button';
import { Input } from './input';
import { Badge } from './badge';
import {
  Search,
  Copy,
  ChevronDown,
  ChevronRight,
  User,
  Heart,
  Crown,
  DollarSign,
  Stethoscope,
  Settings,
  FileText,
  Users,
  Home,
  Gift,
  Info,
  X,
} from 'lucide-react';
import { toast } from 'sonner';

export interface Variable {
  id: string;
  label: string;
  value: string;
  questionInfo?: {
    question: string;
    description: string;
    section: string;
    dataType: string;
    possibleValues?: string[];
    example?: string;
  };
}

interface VariableGroup {
  id: string;
  label: string;
  icon: React.ReactNode;
  variables: Variable[];
  subgroups?: VariableGroup[];
}

interface EnhancedVariablesPanelProps {
  variables: Variable[];
  onVariableInsert: (variable: Variable) => void;
  onConditionalLogicClick?: () => void;
}

export function EnhancedVariablesPanel({
  variables,
  onVariableInsert,
  onConditionalLogicClick,
}: EnhancedVariablesPanelProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(['profile'])
  );
  const [selectedVariable, setSelectedVariable] = useState<Variable | null>(
    null
  );
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);

  // Group variables by category with hierarchical structure
  const groupedVariables = useMemo(() => {
    const groups: VariableGroup[] = [
      {
        id: 'profile',
        label: 'Profile Information',
        icon: <User className='h-4 w-4' />,
        variables: [],
        subgroups: [
          {
            id: 'profile_basic',
            label: 'Basic Information',
            icon: <User className='h-3 w-3' />,
            variables: variables.filter(
              v =>
                v.id.startsWith('profile_') &&
                !v.id.includes('address') &&
                !v.id.includes('spouse') &&
                !v.id.includes('selected') &&
                !v.id.includes('soundMind')
            ),
          },
          {
            id: 'profile_address',
            label: 'Address',
            icon: <Home className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('address')),
          },
          {
            id: 'profile_spouse',
            label: 'Spouse Information',
            icon: <Heart className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('spouse')),
          },
          {
            id: 'profile_documents',
            label: 'Document Selection',
            icon: <FileText className='h-3 w-3' />,
            variables: variables.filter(
              v => v.id.includes('selected') || v.id.includes('soundMind')
            ),
          },
        ],
      },
      {
        id: 'emergency',
        label: 'Emergency Considerations',
        icon: <Heart className='h-4 w-4' />,
        variables: [],
        subgroups: [
          {
            id: 'emergency_counts',
            label: 'Counts & Summary',
            icon: <Users className='h-3 w-3' />,
            variables: variables.filter(
              v => v.id.startsWith('emergency_') && v.id.includes('count')
            ),
          },
          {
            id: 'emergency_dependents',
            label: 'Dependents',
            icon: <Users className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('dependent')),
          },
          {
            id: 'emergency_pets',
            label: 'Pets',
            icon: <Heart className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('pet')),
          },
        ],
      },
      {
        id: 'afterYouDie',
        label: 'After You Die',
        icon: <Crown className='h-4 w-4' />,
        variables: [],
        subgroups: [
          {
            id: 'afterYouDie_executor',
            label: 'Executor',
            icon: <Crown className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('executor')),
          },
          {
            id: 'afterYouDie_trustee',
            label: 'Successor Trustee',
            icon: <Crown className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('successorTrustee')),
          },
          {
            id: 'afterYouDie_properties',
            label: 'Properties',
            icon: <Home className='h-3 w-3' />,
            variables: variables.filter(
              v => v.id.includes('property') || v.id.includes('properties')
            ),
          },
          {
            id: 'afterYouDie_gifts',
            label: 'Specific Gifts',
            icon: <Gift className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('gift')),
          },
          {
            id: 'afterYouDie_beneficiaries',
            label: 'Main Beneficiaries',
            icon: <Users className='h-3 w-3' />,
            variables: variables.filter(v => v.id.includes('beneficiar')),
          },
        ],
      },
      {
        id: 'financial',
        label: 'Financial Decisions',
        icon: <DollarSign className='h-4 w-4' />,
        variables: variables.filter(v => v.id.startsWith('financial_')),
      },
      {
        id: 'medical',
        label: 'Medical Decisions',
        icon: <Stethoscope className='h-4 w-4' />,
        variables: variables.filter(v => v.id.startsWith('medical_')),
      },
      {
        id: 'additional',
        label: 'Additional Considerations',
        icon: <Settings className='h-4 w-4' />,
        variables: variables.filter(v => v.id.startsWith('additional_')),
      },
      {
        id: 'document',
        label: 'Document Information',
        icon: <FileText className='h-4 w-4' />,
        variables: variables.filter(
          v => v.id.startsWith('document_') || v.id.startsWith('execution_')
        ),
      },
    ];

    return groups.filter(
      group =>
        group.variables.length > 0 ||
        (group.subgroups && group.subgroups.some(sg => sg.variables.length > 0))
    );
  }, [variables]);

  // Filter variables based on search term
  const filteredGroups = useMemo(() => {
    if (!searchTerm) return groupedVariables;

    const filterGroup = (group: VariableGroup): VariableGroup | null => {
      const filteredVariables = group.variables.filter(
        v =>
          v.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          v.value.toLowerCase().includes(searchTerm.toLowerCase())
      );

      const filteredSubgroups =
        (group.subgroups
          ?.map(filterGroup)
          .filter(Boolean) as VariableGroup[]) || [];

      if (filteredVariables.length > 0 || filteredSubgroups.length > 0) {
        return {
          ...group,
          variables: filteredVariables,
          subgroups: filteredSubgroups,
        };
      }

      return null;
    };

    return groupedVariables.map(filterGroup).filter(Boolean) as VariableGroup[];
  }, [groupedVariables, searchTerm]);

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Variable copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy variable');
    }
  };

  const openInfoModal = (variable: Variable) => {
    setSelectedVariable(variable);
    setIsInfoModalOpen(true);
  };

  const renderVariable = (variable: Variable) => (
    <div
      key={variable.id}
      className='flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 group transition-colors duration-200'
    >
      <div className='flex-1 min-w-0'>
        <div className='font-medium text-xs text-gray-900 truncate'>
          {variable.label}
        </div>
        <div className='text-xs text-gray-500 font-mono truncate bg-gray-100 px-1 py-0.5 rounded mt-1'>
          {variable.value}
        </div>
      </div>
      <div className='flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity'>
        {variable.questionInfo && (
          <Button
            variant='ghost'
            size='sm'
            onClick={() => openInfoModal(variable)}
            className='h-6 w-6 p-0 hover:bg-blue-100'
            title='View question info'
          >
            <Info className='h-3 w-3' />
          </Button>
        )}
        <Button
          variant='ghost'
          size='sm'
          onClick={() => copyToClipboard(variable.value)}
          className='h-6 w-6 p-0 hover:bg-blue-100'
          title='Copy variable'
        >
          <Copy className='h-3 w-3' />
        </Button>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => onVariableInsert(variable)}
          className='h-6 w-6 p-0 hover:bg-green-100'
          title='Insert variable'
        >
          <FileText className='h-3 w-3' />
        </Button>
      </div>
    </div>
  );

  const renderGroup = (group: VariableGroup, level: number = 0) => {
    const isExpanded = expandedGroups.has(group.id);
    const hasContent =
      group.variables.length > 0 ||
      (group.subgroups && group.subgroups.length > 0);

    if (!hasContent) return null;

    return (
      <div key={group.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => toggleGroup(group.id)}
          className='w-full justify-start p-2 h-auto font-medium text-left hover:none max-w-fit'
        >
          <div className='flex items-center gap-2'>
            {isExpanded ? (
              <ChevronDown className='h-3 w-3' />
            ) : (
              <ChevronRight className='h-3 w-3' />
            )}
            {group.icon}
            <span className='text-sm'>{group.label}</span>
            <Badge variant='secondary' className='ml-auto text-xs'>
              {group.variables.length +
                (group.subgroups?.reduce(
                  (acc, sg) => acc + sg.variables.length,
                  0
                ) || 0)}
            </Badge>
          </div>
        </Button>

        {isExpanded && (
          <div className='ml-2 mt-1 space-y-1'>
            {group.variables.map(renderVariable)}
            {group.subgroups?.map(subgroup => renderGroup(subgroup, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className='w-80 sticky top-20 self-start'>
      <div className='border rounded-lg p-4 bg-white shadow-sm'>
        <h3 className='font-medium text-sm mb-3 text-gray-900'>
          Available Variables
        </h3>

        {/* Search Input */}
        <div className='mb-3 relative'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
          <Input
            type='text'
            placeholder='Search variables...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='pl-10 h-8 text-xs'
          />
        </div>

        {/* Add Conditional Logic Button */}
        {onConditionalLogicClick && (
          <div className='mb-3'>
            <Button
              variant='default'
              size='sm'
              onClick={onConditionalLogicClick}
              className='w-full text-xs'
            >
              Add Conditional Logic
            </Button>
          </div>
        )}

        <div className='space-y-1 max-h-[calc(100vh-280px)] overflow-y-auto'>
          {filteredGroups.length > 0 ? (
            filteredGroups.map(group => renderGroup(group))
          ) : (
            <div className='text-xs text-gray-500 text-center py-4'>
              {searchTerm
                ? 'No variables found matching your search.'
                : 'No variables available.'}
            </div>
          )}
        </div>
      </div>

      {/* Info Modal */}
      {isInfoModalOpen && selectedVariable && (
        <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto'>
            <div className='flex justify-between items-start mb-4'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Variable Information
              </h3>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setIsInfoModalOpen(false)}
                className='h-6 w-6 p-0'
              >
                <X className='h-4 w-4' />
              </Button>
            </div>

            <div className='space-y-4'>
              <div>
                <label className='text-sm font-medium text-gray-700'>
                  Variable Name:
                </label>
                <div className='text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded mt-1'>
                  {selectedVariable.label}
                </div>
              </div>

              <div>
                <label className='text-sm font-medium text-gray-700'>
                  Template Code:
                </label>
                <div className='text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded mt-1'>
                  {selectedVariable.value}
                </div>
              </div>

              {selectedVariable.questionInfo && (
                <>
                  <div>
                    <label className='text-sm font-medium text-gray-700'>
                      Interview Section:
                    </label>
                    <div className='text-sm text-gray-900 mt-1'>
                      {selectedVariable.questionInfo.section}
                    </div>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-gray-700'>
                      Question:
                    </label>
                    <div className='text-sm text-gray-900 mt-1'>
                      {selectedVariable.questionInfo.question}
                    </div>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-gray-700'>
                      Description:
                    </label>
                    <div className='text-sm text-gray-600 mt-1'>
                      {selectedVariable.questionInfo.description}
                    </div>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-gray-700'>
                      Data Type:
                    </label>
                    <div className='text-sm text-gray-900 mt-1'>
                      <Badge variant='secondary'>
                        {selectedVariable.questionInfo.dataType}
                      </Badge>
                    </div>
                  </div>

                  {selectedVariable.questionInfo.possibleValues && (
                    <div>
                      <label className='text-sm font-medium text-gray-700'>
                        Possible Values:
                      </label>
                      <div className='text-sm text-gray-900 mt-1'>
                        {selectedVariable.questionInfo.possibleValues.join(
                          ', '
                        )}
                      </div>
                    </div>
                  )}

                  {selectedVariable.questionInfo.example && (
                    <div>
                      <label className='text-sm font-medium text-gray-700'>
                        Example:
                      </label>
                      <div className='text-sm text-gray-600 font-mono bg-gray-100 p-2 rounded mt-1'>
                        {selectedVariable.questionInfo.example}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            <div className='flex justify-end gap-2 mt-6'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => copyToClipboard(selectedVariable.value)}
              >
                <Copy className='h-3 w-3 mr-1' />
                Copy
              </Button>
              <Button
                variant='default'
                size='sm'
                onClick={() => {
                  onVariableInsert(selectedVariable);
                  setIsInfoModalOpen(false);
                }}
              >
                <FileText className='h-3 w-3 mr-1' />
                Insert
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
