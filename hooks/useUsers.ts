'use client';

import * as Sentry from '@sentry/nextjs';
import { useState, useEffect, useCallback } from 'react';
import { User } from '@/types/account';
import { fetchUsers, updateUser, deleteUser } from '@/lib/data/users';

interface UseUsersState {
  users: User[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateUserStatus: (userId: string, status: User['status']) => Promise<void>;
  removeUser: (userId: string) => Promise<void>;
  addUser: (userData: Omit<User, 'id' | 'createdAt'>) => Promise<void>;
}

export function useUsers(): UseUsersState {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchUsers();
      Sentry.captureMessage('Error fetching users');

      setUsers(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');

      Sentry.captureException(err);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const updateUserStatus = useCallback(
    async (userId: string, status: User['status']) => {
      try {
        setError(null);
        const updatedUser = await updateUser(userId, { status });

        setUsers(prevUsers =>
          prevUsers.map(user =>
            user.id === userId ? { ...user, status } : user
          )
        );
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update user');
        throw err;
      }
    },
    []
  );

  const removeUser = useCallback(async (userId: string) => {
    try {
      setError(null);
      await deleteUser(userId);

      setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete user');
      throw err;
    }
  }, []);

  const addUser = useCallback(
    async (userData: Omit<User, 'id' | 'createdAt'>) => {
      try {
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to create user');
        throw err;
      }
    },
    []
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    users,
    loading,
    error,
    refetch,
    updateUserStatus,
    removeUser,
    addUser,
  };
}
