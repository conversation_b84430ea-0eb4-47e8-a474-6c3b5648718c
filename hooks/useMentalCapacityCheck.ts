'use client';

import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/app/utils/interviewV2Progress';

export interface MentalCapacityStatus {
  isOfSoundMind: boolean | null; // null means not answered yet
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook to check if the user has declared they are of sound mind
 * Returns null if the question hasn't been answered yet
 * Returns false if user declared "NOT of sound mind"
 * Returns true if user declared they are "of sound mind"
 */
export function useMentalCapacityCheck(): MentalCapacityStatus {
  const {
    data: progress,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['mentalCapacityCheck'],
    queryFn: loadInterviewProgressV2,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    retry: 2,
  });

  const soundMind = progress?.stepsData?.profile?.soundMind;

  return {
    isOfSoundMind: soundMind ?? null,
    isLoading,
    error: error as Error | null,
  };
}
