'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/app/utils/interviewV2Progress';
import { useAuth } from '@/context/AuthContext';
import { getAssignedToUserWelonTrustCognitoId } from '@/lib/data/users';

// Generate the client outside of hook
const client = generateClient<Schema>();

// Define types for the care document data
export type CareDocumentSectionType =
  | 'PersonalIdentification'
  | 'Employment'
  | 'Medical'
  | 'EmergencyContacts'
  | 'EmergencyIncidents'
  | 'Pets'
  | 'Financial'
  | 'Insurance'
  | 'Property'
  | 'AssetInformation'
  | 'ImportantToMe'
  | 'LongTermCare'
  | 'LastWishes'
  | 'Legacy'
  | 'Uploads';

export interface UploadedFile {
  fileKey: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  reference: string;
}

export interface CareDocumentSectionData {
  sectionType: CareDocumentSectionType;
  content: string; // JSON string for section data
  uploadedFiles: UploadedFile[];
}

export interface CareDocumentNew {
  id: string;
  userId: string;
  version: number;
  data: CareDocumentSectionData[] | any[];
  createdAt?: string | null;
  updatedAt?: string | null;
}

export function useCareDocumentsNew() {
  const [document, setDocument] = useState<CareDocumentNew | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

   const {  userPersonalInfo } = useAuth();

  // Fetch the care document
  const fetchDocument = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // const user = await getCurrentUser();

      if (!userPersonalInfo) {
        return;
      }
     

      // Check if the user already has a care document
      const { data, errors } = await client.models.CareDocumentNew.list({
        filter: {
          userId: { eq: userPersonalInfo.id },
        },
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data && data.length > 0) {
        // User already has a care document, use the most recent one
        const doc = data[0];
        setDocument({
          id: doc.id!,
          userId: doc.userId,
          version: doc.version || 1,
          data: doc.data || [],
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt,
        });
      } else {
        // User doesn't have a care document yet
        setDocument(null);
      }
    } catch (err) {
      console.error('Error fetching care document:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch document');
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new care document
  const createDocument = async () => {
    try {
      setLoading(true);
      setError(null);

      // const user = await getCurrentUser();

      if (!userPersonalInfo) {
        return;
      }

      const assignedWelonTrustId = await getAssignedToUserWelonTrustCognitoId(userPersonalInfo.id);

      const { data, errors } = await client.models.CareDocumentNew.create({
        userId: userPersonalInfo.id,
        assignedWelonTrustId,
        version: 1,
        data: [],
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        setDocument({
          id: data.id!,
          userId: data.userId,
          version: data.version || 1,
          data: data.data || [],
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        });

        // Log the creation
        await logDocumentUpdate(data.id!, 'Created', 'Document created', 0, 1);
      }

      return data;
    } catch (err) {
      console.error('Error creating care document:', err);
      setError(err instanceof Error ? err.message : 'Failed to create document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update a section in the care document
  const updateSection = async (sectionType: CareDocumentSectionType, content: any, uploadedFiles: UploadedFile[] = []) => {
    try {
      if (!document) {
        throw new Error('No document found to update');
      }

      setLoading(true);
      setError(null);

      // Convert content to JSON string
      const contentString = JSON.stringify(content);

      // Create a new section data object
      const sectionData: CareDocumentSectionData = {
        sectionType,
        content: contentString,
        uploadedFiles,
      };

      // Check if this section already exists in the document
      const existingDataIndex = document.data.findIndex(d => d.sectionType === sectionType);
      let newData: CareDocumentSectionData[];

      if (existingDataIndex >= 0) {
        // Update existing section
        newData = [...document.data];
        newData[existingDataIndex] = sectionData;
      } else {
        // Add new section
        newData = [...document.data, sectionData];
      }

      const newVersion = document.version + 1;

      // Update the document in the database
      const { data, errors } = await client.models.CareDocumentNew.update({
        id: document.id,
        version: newVersion,
        data: newData,
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        setDocument({
          id: data.id!,
          userId: data.userId,
          version: data.version || 1,
          data: data.data || [],
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        });

        // Log the update
        await logDocumentUpdate(
          document.id,
          'Updated',
          `Updated ${sectionType} section`,
          document.version,
          newVersion
        );
      }

      return data;
    } catch (err) {
      console.error(`Error updating ${sectionType} section:`, err);
      setError(err instanceof Error ? err.message : `Failed to update ${sectionType} section`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get a specific section from the care document
  const getSection = (sectionType: CareDocumentSectionType) => {
    if (!document) return null;

    const section = document.data.find(d => d.sectionType === sectionType);
    if (!section) return null;

    try {
      // Parse the content JSON string back to an object
      const content = JSON.parse(section.content);
      return {
        content,
        uploadedFiles: section.uploadedFiles || [],
      };
    } catch (err) {
      console.error(`Error parsing ${sectionType} section content:`, err);
      return null;
    }
  };

  // Delete a section from the care document
  const deleteSection = async (sectionType: CareDocumentSectionType) => {
    try {
      if (!document) {
        throw new Error('No document found to update');
      }

      setLoading(true);
      setError(null);

      // Filter out the section to delete
      const newData = document.data.filter(d => d.sectionType !== sectionType);
      const newVersion = document.version + 1;

      // Update the document in the database
      const { data, errors } = await client.models.CareDocumentNew.update({
        id: document.id,
        version: newVersion,
        data: newData,
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        setDocument({
          id: data.id!,
          userId: data.userId,
          version: data.version || 1,
          data: data.data || [],
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        });

        // Log the update
        await logDocumentUpdate(
          document.id,
          'Updated',
          `Deleted ${sectionType} section`,
          document.version,
          newVersion
        );
      }

      return data;
    } catch (err) {
      console.error(`Error deleting ${sectionType} section:`, err);
      setError(err instanceof Error ? err.message : `Failed to delete ${sectionType} section`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete the entire care document
  const deleteDocument = async () => {
    try {
      if (!document) {
        throw new Error('No document found to delete');
      }

      setLoading(true);
      setError(null);

      // Delete the document from the database
      const { data, errors } = await client.models.CareDocumentNew.delete({
        id: document.id,
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      setDocument(null);

      // Log the deletion
      await logDocumentUpdate(document.id, 'Archived', 'Document deleted');

      return data;
    } catch (err) {
      console.error('Error deleting care document:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Helper function to log document updates
  const logDocumentUpdate = async (
    documentId: string,
    changeType: 'Created' | 'Updated' | 'Reviewed' | 'Archived',
    description?: string,
    previousVersion?: number,
    newVersion?: number
  ) => {
    try {
      const user = await getCurrentUser();

      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType,
        changeDescription: description,
        previousVersion,
        newVersion,
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      console.error('Error logging document update:', err);
      // Don't throw here as this is just logging
    }
  };

  // Load the document on initial render
  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  // Update multiple sections in the care document at once
  const updateMultipleSections = async (sectionsData: { sectionType: CareDocumentSectionType, content: any, uploadedFiles?: UploadedFile[]}[], isAutoSave?: boolean) => {
    try {
      if (!document) {
        throw new Error('No document found to update');
      }

      if (sectionsData.length === 0) {
        return null; // Nothing to update
      }

      setLoading(true);
      setError(null);

      // Create a copy of the current document data
      let newData = [...document.data];
      
      // Update all sections in the local copy
      for (const { sectionType, content, uploadedFiles = [] } of sectionsData) {
        // Convert content to JSON string
        const contentString = JSON.stringify(content);

        // Create a new section data object
        const sectionData: CareDocumentSectionData = {
          sectionType,
          content: contentString,
          uploadedFiles,
        };

        // Check if this section already exists in the document
        const existingDataIndex = newData.findIndex(d => d.sectionType === sectionType);
        
        if (existingDataIndex >= 0) {
          // Update existing section
          newData[existingDataIndex] = sectionData;
        } else {
          // Add new section
          newData.push(sectionData);
        }
      }

      const newVersion = document.version + 1;

      // Update the document in the database with all changes at once
      const { data, errors } = await client.models.CareDocumentNew.update({
        id: document.id,
        version: newVersion,
        data: newData,
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        if (!isAutoSave) {
          setDocument({
            id: data.id!,
            userId: data.userId,
            version: data.version || 1,
            data: data.data || [],
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
          });
        }

        // Log the updates
        for (const { sectionType } of sectionsData) {
          await logDocumentUpdate(
            document.id,
            'Updated',
            `Updated ${sectionType} section`,
            document.version,
            newVersion
          );
        }
      }

      return data;
    } catch (err) {
      console.error('Error updating multiple sections:', err);
      setError(err instanceof Error ? err.message : 'Failed to update multiple sections');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    document,
    loading,
    error,
    fetchDocument,
    createDocument,
    updateSection,
    updateMultipleSections,
    getSection,
    deleteSection,
    deleteDocument,
  };
}