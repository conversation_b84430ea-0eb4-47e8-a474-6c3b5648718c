import { useEffect } from 'react';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';

export enum SectionEnum {
  // Care Documents
  CARE_DOCUMENTS_PETS = 'Pets',
  CARE_DOCUMENTS_PERSONAL_IDENTIFICATION = 'PersonalIdentification',
  CARE_DOCUMENTS_EMERGENCY_CONTACTS = 'EmergencyContacts',
  CARE_DOCUMENTS_MEDICAL = 'Medical',
  CARE_DOCUMENTS_LAST_WISHES = 'LastWishes',
  CARE_DOCUMENTS_PROPERTY = 'Property',
  CARE_DOCUMENTS_FINANCIAL = 'Financial',
  CARE_DOCUMENTS_EMPLOYMENT = 'Employment',
  CARE_DOCUMENTS_ASSET_INFORMATION = 'AssetInformation',
  CARE_DOCUMENTS_EMERGENCY_INCIDENTS = 'EmergencyIncidents',
  CARE_DOCUMENTS_IMPORTANT_TO_ME = 'ImportantToMe',
  CARE_DOCUMENTS_INSURANCE = 'Insurance',
  CARE_DOCUMENTS_LEGACY = 'Legacy',
  CARE_DOCUMENTS_LONG_TERM_CARE = 'LongTermCare',
  CARE_DOCUMENTS_UPLOADS = 'Uploads',

  // Interview
  INTERVIEW_PROFILE = 'profile',
  INTERVIEW_EMERGENCY = 'emergency',
  INTERVIEW_AFTER_YOU_DIE = 'afterYouDie',
  INTERVIEW_FINANCIAL = 'financial',
  INTERVIEW_MEDICAL = 'medical',
  INTERVIEW_ADDITIONAL = 'additional',
  INTERVIEW_PEOPLE = 'people',
  INTERVIEW_COMPLETED_SUBSECTIONS = 'completedSubsections',
  INTERVIEW_REVIEW = 'review',
}

// Generate the client
const client = generateClient<Schema>();

/**
 * Custom hook to pre-fill data from linked accounts' shared fields
 * @param sectionType The type of section being pre-filled ('interview' or care document section type)
 * @param sectionData The current section data from context
 * @param setData The state setter function for the section
 */
export function useLinkedAccountPrefill(
  sectionType: string,
  sectionData: any,
  setData: (data: any) => void
) {
  const { userExternalId } = useAuth();

  useEffect(() => {
    // Only run if we don't already have section data
    if (sectionData && Object.keys(sectionData).length > 0) {
      return;
    }

    const fetchLinkedAccountData = async () => {
      try {
        // Get current user
        const user = await getCurrentUser();
        if (!userExternalId) {
          console.error('Error fetching user data');
          return;
        }

        // Fetch user data including linked accounts
        const { data: userList, errors } = await client.models.User.list({
          filter: { cognitoId: { eq: userExternalId } },
          selectionSet: ['id', 'cognitoId'],
        });

        if (errors || !userList || userList.length === 0) {
          console.error('Error fetching user data:', errors);
          return;
        }

        const userData = userList[0];

        if (!userData.cognitoId) {
          console.error('No cognitoId found for user');
          return;
        }

        // Fetch linked accounts where isAccepted is true
        const { data: linkedAccounts, errors: linkedAccountsErrors } =
          await client.models.LinkedAccountWithSharedFields.list({
            filter: {
              linkedUserId: { eq: userData.cognitoId },
              isAccepted: { eq: true },
            },
          });

        if (
          linkedAccountsErrors ||
          !linkedAccounts ||
          linkedAccounts.length === 0
        ) {
          console.log('No accepted linked accounts found for prefilling');
          return;
        }

        // Process the first accepted linked account
        const linkedAccount = linkedAccounts[0];

        // Parse shared fields
        let sharedFields;
        try {
          sharedFields = JSON.parse(linkedAccount.sharedFields);
        } catch (e) {
          console.error('Error parsing shared fields:', e);
          return;
        }

        // Map shared fields to section data based on section type
        const prefilledData = mapSharedFieldsToSection(
          sectionType,
          sharedFields
        );

        if (prefilledData) {
          console.log(`Pre-filling ${sectionType} with linked account data`);
          setData(prefilledData);
        }
      } catch (error) {
        console.error(
          'Error pre-filling section data from linked account:',
          error
        );
      }
    };

    fetchLinkedAccountData();
  }, [sectionType, sectionData, setData]);
}

/**
 * Maps shared fields to the appropriate section data structure
 * @param sectionType The type of section being pre-filled
 * @param sharedFields The shared fields from linked account
 * @returns The pre-filled section data
 */
function mapSharedFieldsToSection(sectionType: string, sharedFields: any[]) {
  if (
    !sharedFields ||
    !Array.isArray(sharedFields) ||
    sharedFields.length === 0
  ) {
    return null;
  }

  // For interview data, we need to transform the shared fields into stepsData structure
  if (sectionType === 'interview') {
    return mapSharedFieldsToInterviewData(sharedFields);
  }

  // For care documents, map to the appropriate section
  switch (sectionType) {
    case 'Pets':
      return mapSharedFieldsToPetsData(sharedFields);
    case 'PersonalIdentification':
      return mapSharedFieldsToPersonalIdentificationData(sharedFields);
    case 'EmergencyContacts':
      return mapSharedFieldsToEmergencyContactsData(sharedFields);
    case 'Medical':
      return mapSharedFieldsToMedicalData(sharedFields);
    case 'LastWishes':
      return mapSharedFieldsToLastWishesData(sharedFields);
    case 'Property':
      return mapSharedFieldsToPropertyData(sharedFields);
    case 'Financial':
      return mapSharedFieldsToFinancialData(sharedFields);
    case 'Employment':
      return mapSharedFieldsToEmploymentData(sharedFields);
    case 'AssetInformation':
      return mapSharedFieldsToAssetInformationData(sharedFields);
    case 'EmergencyIncidents':
      return mapSharedFieldsToEmergencyIncidentsData(sharedFields);
    case 'ImportantToMe':
      return mapSharedFieldsToImportantToMeData(sharedFields);
    case 'Insurance':
      return mapSharedFieldsToInsuranceData(sharedFields);
    case 'Legacy':
      return mapSharedFieldsToLegacyData(sharedFields);
    case 'LongTermCare':
      return mapSharedFieldsToLongTermCareData(sharedFields);
    case 'Uploads':
      return mapSharedFieldsToUploadsData(sharedFields);
    // Add more section mappings as needed
    default:
      return null;
  }
}

/**
 * Maps shared fields to interview data structure
 */
function mapSharedFieldsToInterviewData(sharedFields: any[]) {
  // Initialize stepsData structure
  const stepsData: any = {
    profile: {},
    emergency: {},
    afterYouDie: {},
    financial: {},
    medical: {},
    additional: {},
    people: [],
    completedSubsections: {},
  };

  // Process each shared field and map to the appropriate section in stepsData
  sharedFields.forEach(field => {
    // Only process interview data
    if (field.documentType !== 'Interview') return;

    const { id, answer } = field;

    // Skip fields without id or answer
    if (!id || answer === undefined) return;

    // Parse the path from the id (e.g., "profile.firstName" -> ["profile", "firstName"])
    const path = id.split('.');

    // Handle array paths like "people[0].firstName"
    if (path[0] === 'people' && path[1]?.includes('[')) {
      const indexMatch = path[1].match(/\[(\d+)\]/);
      if (indexMatch) {
        const index = parseInt(indexMatch[1]);
        const property = path[1].split('[')[0];

        // Ensure the array exists
        if (!stepsData.people) stepsData.people = [];

        // Ensure the object at the index exists
        if (!stepsData.people[index]) stepsData.people[index] = {};

        // Set the property
        if (path.length === 3) {
          stepsData.people[index][path[2]] = parseFieldValue(answer);
        } else {
          stepsData.people[index][property] = parseFieldValue(answer);
        }
      }
      return;
    }

    // Handle nested paths
    let current = stepsData;
    for (let i = 0; i < path.length - 1; i++) {
      const segment = path[i];

      // Handle array notation like "afterYouDie.specificGifts[0]"
      if (segment.includes('[')) {
        const [arrayName, indexStr] = segment.split('[');
        const index = parseInt(indexStr.replace(']', ''));

        if (!current[arrayName]) current[arrayName] = [];
        if (!current[arrayName][index]) current[arrayName][index] = {};

        current = current[arrayName][index];
      } else {
        if (!current[segment]) current[segment] = {};
        current = current[segment];
      }
    }

    // Set the final property
    const lastSegment = path[path.length - 1];
    current[lastSegment] = parseFieldValue(answer);
  });

  return stepsData;
}

/**
 * Parse field value to the appropriate type
 */
function parseFieldValue(value: string) {
  if (value === 'true') return true;
  if (value === 'false') return false;
  if (value === '') return '';
  if (value === 'null') return null;

  // Try to parse as number
  const num = Number(value);
  if (!isNaN(num) && value.trim() !== '') return num;

  // Try to parse as JSON
  try {
    return JSON.parse(value);
  } catch {
    // Return as string if all else fails
    return value;
  }
}

/**
 * Maps shared fields to Pets section data
 */
function mapSharedFieldsToPetsData(sharedFields: any[]) {
  // Filter fields that are for the Pets section
  // Either by id starting with 'Pets.' or by documentTitle being 'Pets'
  const petsFields = sharedFields.filter(
    field =>
      field.id.startsWith('Pets.') ||
      (field.documentType === 'Care Document' && field.documentTitle === 'Pets')
  );

  if (petsFields.length === 0) return null;

  // Extract pet data
  const petsMap = new Map();

  petsFields.forEach(field => {
    const match = field.id.match(/Pets\.pets\[(\d+)\]\.(.+)/);
    if (match) {
      const [, indexStr, property] = match;
      const index = parseInt(indexStr);

      if (!petsMap.has(index)) petsMap.set(index, {});
      petsMap.get(index)[property] = field.answer;
    } else if (field.id === 'Pets.hasPets') {
      // Handle the hasPets field separately
      petsMap.set('hasPets', field.answer === 'true');
    } else if (field.id === 'Pets.additionalInstructions') {
      // Handle additional instructions
      petsMap.set('additionalInstructions', field.answer);
    }
  });

  // Transform to pets array
  const pets = [];
  for (const [key, value] of petsMap.entries()) {
    if (typeof key === 'number') {
      pets.push({
        name: value.name || '',
        type: value.type || '',
        breed: value.breed || '',
        dob: value.dob || '', // Changed from age to date of birth
        veterinarian: value.veterinarian || '',
        vetPhone: value.vetPhone || '',
        // New fields from mockup
        isMicrochipped:
          value.isMicrochipped === 'true'
            ? true
            : value.isMicrochipped === 'false'
              ? false
              : null,
        microchipNumber: value.microchipNumber || '',
        hasPreferredBoarding:
          value.hasPreferredBoarding === 'true'
            ? true
            : value.hasPreferredBoarding === 'false'
              ? false
              : null,
        boardingName: value.boardingName || '',
        boardingPhone: value.boardingPhone || '',
        boardingAddress: value.boardingAddress || '',
        hasPreferredGuardian:
          value.hasPreferredGuardian === 'true'
            ? true
            : value.hasPreferredGuardian === 'false'
              ? false
              : null,
        guardianName: value.guardianName || '',
        guardianPhone: value.guardianPhone || '',
        guardianEmail: value.guardianEmail || '',
        hasPreferredSitter:
          value.hasPreferredSitter === 'true'
            ? true
            : value.hasPreferredSitter === 'false'
              ? false
              : null,
        sitterName: value.sitterName || '',
        sitterPhone: value.sitterPhone || '',
        sitterEmail: value.sitterEmail || '',
        specificInfo: value.specificInfo || '',
        shortTermEmergencyInfo: value.shortTermEmergencyInfo || '',
      });
    }
  }

  return {
    hasPets: petsMap.get('hasPets') || pets.length > 0,
    pets,
    additionalInstructions: petsMap.get('additionalInstructions') || '',
  };
}

/**
 * Maps shared fields to Personal Identification section data
 */
function mapSharedFieldsToPersonalIdentificationData(sharedFields: any[]) {
  // Filter fields that are for the Personal Identification section
  const personalIdFields = sharedFields.filter(
    field =>
      field.id.startsWith('PersonalIdentification.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'PersonalIdentification')
  );

  // Also include profile fields from interview data as fallback
  const profileFields = sharedFields.filter(
    field =>
      field.documentType === 'Interview' && field.id.startsWith('profile.')
  );

  if (personalIdFields.length === 0 && profileFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    identifyingDocuments: [],
    hasPasswordManager: null,
    passwordManagerName: '',
    passwordManagerUsername: '',
    passwordManagerPassword: '',
    noPasswordManagerInfo: '',
    socialMedia: [],
    socialAccounts: [],
    cloudStorage: [],
    cloudAccounts: [],
    deviceWishes: '',
    fullName: '',
    email: '',
    phone: '',
    address: '',
    dateOfBirth: '',
    additionalNotes: '',
  };

  // First process direct PersonalIdentification fields
  personalIdFields.forEach(field => {
    const property = field.id.replace('PersonalIdentification.', '');

    if (property === 'identifyingDocuments') {
      // Handle identifyingDocuments as a string array
      try {
        const docs = parseFieldValue(field.answer);
        if (Array.isArray(docs)) {
          result.identifyingDocuments = docs;
        } else if (typeof docs === 'string') {
          result.identifyingDocuments.push(docs);
        }
      } catch (e) {
        console.error('Error parsing identifyingDocuments:', e);
      }
    } else if (property === 'socialMedia') {
      // Handle socialMedia as a string array
      try {
        const social = parseFieldValue(field.answer);
        if (Array.isArray(social)) {
          result.socialMedia = social;
        } else if (typeof social === 'string') {
          result.socialMedia.push(social);
        }
      } catch (e) {
        console.error('Error parsing socialMedia:', e);
      }
    } else if (property === 'cloudStorage') {
      // Handle cloudStorage as a string array
      try {
        const cloud = parseFieldValue(field.answer);
        if (Array.isArray(cloud)) {
          result.cloudStorage = cloud;
        } else if (typeof cloud === 'string') {
          result.cloudStorage.push(cloud);
        }
      } catch (e) {
        console.error('Error parsing cloudStorage:', e);
      }
    } else if (property.startsWith('socialAccounts[')) {
      // Handle socialAccounts as array of objects
      const match = property.match(/socialAccounts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, accountProperty] = match;
        const index = parseInt(indexStr);

        if (!result.socialAccounts[index]) {
          result.socialAccounts[index] = {
            platform: '',
            username: '',
            action: '',
            notes: '',
          };
        }

        result.socialAccounts[index][accountProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('cloudAccounts[')) {
      // Handle cloudAccounts as array of objects
      const match = property.match(/cloudAccounts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, accountProperty] = match;
        const index = parseInt(indexStr);

        if (!result.cloudAccounts[index]) {
          result.cloudAccounts[index] = {
            platform: '',
            username: '',
            password: '',
            action: '',
            notes: '',
          };
        }

        result.cloudAccounts[index][accountProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('identifyingDocuments[')) {
      const match = property.match(/identifyingDocuments\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, docProperty] = match;
        const index = parseInt(indexStr);

        if (!result.identifyingDocuments[index]) {
          result.identifyingDocuments[index] = {};
        }

        result.identifyingDocuments[index][docProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('socialMedia[')) {
      const match = property.match(/socialMedia\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, smProperty] = match;
        const index = parseInt(indexStr);

        if (!result.socialMedia[index]) {
          result.socialMedia[index] = {};
        }

        result.socialMedia[index][smProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('cloudStorage[')) {
      const match = property.match(/cloudStorage\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, csProperty] = match;
        const index = parseInt(indexStr);

        if (!result.cloudStorage[index]) {
          result.cloudStorage[index] = {};
        }

        result.cloudStorage[index][csProperty] = parseFieldValue(field.answer);
      }
    } else {
      // Direct properties
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Clean up arrays to ensure no empty slots
  if (result.socialAccounts.length > 0) {
    result.socialAccounts = result.socialAccounts.filter(Boolean);
  }

  if (result.cloudAccounts.length > 0) {
    result.cloudAccounts = result.cloudAccounts.filter(Boolean);
  }

  // If we don't have direct personal identification data, use profile data from interview
  if (personalIdFields.length === 0 && profileFields.length > 0) {
    const profile: any = {};

    profileFields.forEach(field => {
      const property = field.id.replace('profile.', '');
      const segments = property.split('.');

      if (segments.length === 1) {
        profile[segments[0]] = field.answer;
      } else if (segments.length === 2) {
        if (!profile[segments[0]]) profile[segments[0]] = {};
        profile[segments[0]][segments[1]] = field.answer;
      }
    });

    // Map profile data to personal identification fields
    result.fullName =
      `${profile.firstName || ''} ${profile.lastName || ''}`.trim();
    result.email = profile.email || '';
    result.phone = profile.phone || '';
    result.address = profile.address
      ? `${profile.address.street || ''}, ${profile.address.city || ''}, ${profile.address.state || ''} ${profile.address.zip || ''}`.trim()
      : '';
    result.dateOfBirth = profile.dateOfBirth || '';
  }

  return result;
}

/**
 * Maps shared fields to Emergency Contacts section data
 */
function mapSharedFieldsToEmergencyContactsData(sharedFields: any[]) {
  // Filter fields that are for the Emergency Contacts section
  const emergencyContactsFields = sharedFields.filter(
    field =>
      field.id.startsWith('EmergencyContacts.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'EmergencyContacts')
  );

  // Also include people fields from interview data as fallback
  const peopleFields = sharedFields.filter(
    field =>
      field.documentType === 'Interview' && field.id.startsWith('people[')
  );

  if (emergencyContactsFields.length === 0 && peopleFields.length === 0)
    return null;

  // Initialize the result object with the updated structure
  const result: any = {
    importantEmergency: '',
    contacts: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        isPrimary: null,
        isAuthorized: null,
        contactTiming: '',
        notes: '',
      },
    ],
    hasContacts: null,
    hasNonContacts: null,
    nonContacts: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        contactWhen: null,
        contactWhenDetails: '',
        allowPresence: null,
        allowPresenceWhen: '',
        notes: '',
      },
    ],
    hasCareFor: null,
    careForPersons: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        careType: '',
        fillIn: '',
        contactTiming: '',
        notes: '',
      },
    ],
    additionalInstructions: '',
  };

  // First process direct EmergencyContacts fields
  const contactsMap = new Map();
  const nonContactsMap = new Map();
  const careForMap = new Map();

  emergencyContactsFields.forEach(field => {
    if (field.id === 'EmergencyContacts.additionalInstructions') {
      result.additionalInstructions = field.answer;
      return;
    }

    if (field.id === 'EmergencyContacts.importantEmergency') {
      result.importantEmergency = field.answer;
      return;
    }

    if (field.id === 'EmergencyContacts.hasContacts') {
      result.hasContacts = parseFieldValue(field.answer);
      return;
    }

    if (field.id === 'EmergencyContacts.hasNonContacts') {
      result.hasNonContacts = parseFieldValue(field.answer);
      return;
    }

    if (field.id === 'EmergencyContacts.hasCareFor') {
      result.hasCareFor = parseFieldValue(field.answer);
      return;
    }

    // Process contacts array
    const contactMatch = field.id.match(
      /EmergencyContacts\.contacts\[(\d+)\]\.(.+)/
    );
    if (contactMatch) {
      const [, indexStr, property] = contactMatch;
      const index = parseInt(indexStr);

      if (!contactsMap.has(index)) contactsMap.set(index, {});
      contactsMap.get(index)[property] = parseFieldValue(field.answer);
      return;
    }

    // Process nonContacts array
    const nonContactMatch = field.id.match(
      /EmergencyContacts\.nonContacts\[(\d+)\]\.(.+)/
    );
    if (nonContactMatch) {
      const [, indexStr, property] = nonContactMatch;
      const index = parseInt(indexStr);

      if (!nonContactsMap.has(index)) nonContactsMap.set(index, {});
      nonContactsMap.get(index)[property] = parseFieldValue(field.answer);
      return;
    }

    // Process careForPersons array
    const careForMatch = field.id.match(
      /EmergencyContacts\.careForPersons\[(\d+)\]\.(.+)/
    );
    if (careForMatch) {
      const [, indexStr, property] = careForMatch;
      const index = parseInt(indexStr);

      if (!careForMap.has(index)) careForMap.set(index, {});
      careForMap.get(index)[property] = parseFieldValue(field.answer);
      return;
    }
  });

  // If we have direct emergency contacts data, use it
  if (contactsMap.size > 0) {
    result.contacts = Array.from(contactsMap.values());
  }

  // If we have non-contacts data, use it
  if (nonContactsMap.size > 0) {
    result.nonContacts = Array.from(nonContactsMap.values());
  }

  // If we have care-for persons data, use it
  if (careForMap.size > 0) {
    result.careForPersons = Array.from(careForMap.values());
  }

  // If we don't have direct emergency contacts data, use people data from interview as fallback
  else if (peopleFields.length > 0 && contactsMap.size === 0) {
    const peopleMap = new Map();

    peopleFields.forEach(field => {
      const match = field.id.match(/people\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, property] = match;
        const index = parseInt(indexStr);

        if (!peopleMap.has(index)) peopleMap.set(index, {});
        peopleMap.get(index)[property] = field.answer;
      }
    });

    // Transform to contacts array with updated structure
    result.contacts = Array.from(peopleMap.values()).map(person => ({
      name: `${person.firstName || ''} ${person.lastName || ''}`.trim(),
      relationship: person.relationship || '',
      phone: person.phoneNumber || '',
      email: person.email || '',
      address1: person.address || '',
      address2: '',
      city: '',
      state: '',
      zip: '',
      country: '',
      isPrimary: null,
      isAuthorized: null,
      contactTiming: '',
      notes: '',
    }));

    // If we have contacts from interview data, set hasContacts to true
    if (result.contacts.length > 0) {
      result.hasContacts = true;
    }
  }

  return result;
}

/**
 * Maps shared fields to Medical section data
 */
function mapSharedFieldsToMedicalData(sharedFields: any[]) {
  // Filter fields that are for the Medical section
  const medicalFields = sharedFields.filter(
    field =>
      field.id.startsWith('Medical.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Medical')
  );

  // Also include medical fields from interview data as fallback
  const interviewMedicalFields = sharedFields.filter(
    field =>
      field.documentType === 'Interview' && field.id.startsWith('medical.')
  );

  if (medicalFields.length === 0 && interviewMedicalFields.length === 0)
    return null;

  // Initialize the result object
  const result: any = {
    professionals: [],
    medications: [],
    conditions: [],
    surgeries: [],
    allergies: [],
    pharmacy: {
      name: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      zip: '',
      website: '',
      phone: '',
    },
    bloodType: '',
    organDonation: false,
    organDonationDetails: '',
    medicalDirective: '',
    coreWishes: '',
    dignityMeaning: '',
    noMedical: '',
    foodPreferences: '',
    additionalMedical: '',
  };

  // First process direct Medical fields
  const professionalsMap = new Map();
  const medicationsMap = new Map();
  const conditionsMap = new Map();
  const surgeriesMap = new Map();
  const allergiesMap = new Map();

  medicalFields.forEach(field => {
    const property = field.id.replace('Medical.', '');

    if (property.startsWith('professionals[')) {
      const match = property.match(/professionals\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, profProperty] = match;
        const index = parseInt(indexStr);

        if (!professionalsMap.has(index))
          professionalsMap.set(index, { providers: [] });
        professionalsMap.get(index)[profProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('medications[')) {
      const match = property.match(/medications\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, medProperty] = match;
        const index = parseInt(indexStr);

        if (!medicationsMap.has(index))
          medicationsMap.set(index, { providers: [] });
        medicationsMap.get(index)[medProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('conditions[')) {
      const match = property.match(/conditions\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, condProperty] = match;
        const index = parseInt(indexStr);

        if (!conditionsMap.has(index))
          conditionsMap.set(index, { providers: [] });
        conditionsMap.get(index)[condProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('surgeries[')) {
      const match = property.match(/surgeries\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, surgProperty] = match;
        const index = parseInt(indexStr);

        if (!surgeriesMap.has(index))
          surgeriesMap.set(index, { providers: [] });
        surgeriesMap.get(index)[surgProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('allergies[')) {
      const match = property.match(/allergies\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, allergyProperty] = match;
        const index = parseInt(indexStr);

        if (!allergiesMap.has(index))
          allergiesMap.set(index, { providers: [] });
        allergiesMap.get(index)[allergyProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('pharmacy.')) {
      const pharmacyProperty = property.replace('pharmacy.', '');
      result.pharmacy[pharmacyProperty] = parseFieldValue(field.answer);
    } else {
      // Direct properties
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Add collections to result
  if (professionalsMap.size > 0) {
    result.professionals = Array.from(professionalsMap.values());
  }

  if (medicationsMap.size > 0) {
    result.medications = Array.from(medicationsMap.values());
  }

  if (conditionsMap.size > 0) {
    result.conditions = Array.from(conditionsMap.values());
  }

  if (surgeriesMap.size > 0) {
    result.surgeries = Array.from(surgeriesMap.values());
  }

  if (allergiesMap.size > 0) {
    result.allergies = Array.from(allergiesMap.values());
  }

  // If we don't have direct medical data, use medical data from interview
  if (medicalFields.length === 0 && interviewMedicalFields.length > 0) {
    const medical: any = {};

    interviewMedicalFields.forEach(field => {
      const property = field.id.replace('medical.', '');
      const segments = property.split('.');

      if (segments.length === 1) {
        medical[segments[0]] = field.answer;
      } else if (segments.length === 2) {
        if (!medical[segments[0]]) medical[segments[0]] = {};
        medical[segments[0]][segments[1]] =
          field.answer === 'true'
            ? true
            : field.answer === 'false'
              ? false
              : field.answer;
      }
    });

    // Map medical data from interview
    result.organDonation = medical.organDonation?.enabled === 'true' || false;
    if (medical.organDonation?.details) {
      result.organDonationDetails = medical.organDonation.details;
    }

    if (medical.directives) {
      result.medicalDirective = medical.directives;
    }

    if (medical.coreWishes) {
      result.coreWishes = medical.coreWishes;
    }

    if (medical.dignityMeaning) {
      result.dignityMeaning = medical.dignityMeaning;
    }
  }

  return result;
}

/**
 * Maps shared fields to Last Wishes section data
 */
function mapSharedFieldsToLastWishesData(sharedFields: any[]) {
  // Filter fields that are for the Last Wishes section
  const lastWishesFields = sharedFields.filter(
    field =>
      field.id.startsWith('LastWishes.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'LastWishes')
  );

  // Also include additional fields from interview data as fallback
  const additionalFields = sharedFields.filter(
    field =>
      field.documentType === 'Interview' && field.id.startsWith('additional.')
  );

  if (lastWishesFields.length === 0 && additionalFields.length === 0)
    return null;

  // Initialize the result object with all fields from the component
  const result: any = {
    // Existing fields
    funeralPreferences: '',
    memorialPreferences: '',
    burialPreferences: '',
    cremationPreferences: '',
    obituaryPreferences: '',
    memorialDonations: '',
    digitalLegacy: '',
    additionalInstructions: '',

    // New fields from component
    hasServicePlan: null,
    funeralHomes: [],
    hasPreferredFuneral: null,
    preferredFuneralHomes: [],
    wantsService: null,
    servicePlaces: [],
    wantsVisitation: null,
    visitationPlaces: [],
    officiants: [],
    hasPallbearers: null,
    pallbearers: [],
    wantsFlowers: null,
    flowerPreferences: '',
    readingsSongs: '',
    burialInfo: '',
    wantsObituary: null,
    obituaryInfo: '',
    publications: [],
    obituaryNotes: '',
    affiliations: [],
    military: [],
    firstResponders: [],
    wantsHonors: null,
    wantsPresence: null,
    presenceDetails: '',
    flagRecipients: [],
  };

  // Maps to collect array data
  const funeralHomesMap = new Map();
  const preferredFuneralHomesMap = new Map();
  const servicePlacesMap = new Map();
  const visitationPlacesMap = new Map();
  const officiantsMap = new Map();
  const pallbearersMap = new Map();
  const publicationsMap = new Map();
  const militaryMap = new Map();
  const firstRespondersMap = new Map();
  const flagRecipientsMap = new Map();

  // First process direct LastWishes fields
  lastWishesFields.forEach(field => {
    const property = field.id.replace('LastWishes.', '');

    // Handle array fields with pattern like funeralHomes.0.organization
    const funeralHomeMatch = property.match(/^funeralHomes\.(\d+)\.(\w+)$/);
    if (funeralHomeMatch) {
      const [, index, fieldName] = funeralHomeMatch;
      if (!funeralHomesMap.has(Number(index))) {
        funeralHomesMap.set(Number(index), {});
      }
      funeralHomesMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const preferredFuneralHomeMatch = property.match(
      /^preferredFuneralHomes\.(\d+)\.(\w+)$/
    );
    if (preferredFuneralHomeMatch) {
      const [, index, fieldName] = preferredFuneralHomeMatch;
      if (!preferredFuneralHomesMap.has(Number(index))) {
        preferredFuneralHomesMap.set(Number(index), {});
      }
      preferredFuneralHomesMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const servicePlaceMatch = property.match(/^servicePlaces\.(\d+)\.(\w+)$/);
    if (servicePlaceMatch) {
      const [, index, fieldName] = servicePlaceMatch;
      if (!servicePlacesMap.has(Number(index))) {
        servicePlacesMap.set(Number(index), {});
      }
      servicePlacesMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const visitationPlaceMatch = property.match(
      /^visitationPlaces\.(\d+)\.(\w+)$/
    );
    if (visitationPlaceMatch) {
      const [, index, fieldName] = visitationPlaceMatch;
      if (!visitationPlacesMap.has(Number(index))) {
        visitationPlacesMap.set(Number(index), {});
      }
      visitationPlacesMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const officiantMatch = property.match(/^officiants\.(\d+)\.(\w+)$/);
    if (officiantMatch) {
      const [, index, fieldName] = officiantMatch;
      if (!officiantsMap.has(Number(index))) {
        officiantsMap.set(Number(index), {});
      }
      officiantsMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const pallbearerMatch = property.match(/^pallbearers\.(\d+)\.(\w+)$/);
    if (pallbearerMatch) {
      const [, index, fieldName] = pallbearerMatch;
      if (!pallbearersMap.has(Number(index))) {
        pallbearersMap.set(Number(index), {});
      }
      pallbearersMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const publicationMatch = property.match(/^publications\.(\d+)\.(\w+)$/);
    if (publicationMatch) {
      const [, index, fieldName] = publicationMatch;
      if (!publicationsMap.has(Number(index))) {
        publicationsMap.set(Number(index), {});
      }
      publicationsMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const militaryMatch = property.match(/^military\.(\d+)\.(\w+)$/);
    if (militaryMatch) {
      const [, index, fieldName] = militaryMatch;
      if (!militaryMap.has(Number(index))) {
        militaryMap.set(Number(index), {});
      }
      militaryMap.get(Number(index))[fieldName] = parseFieldValue(field.answer);
      return;
    }

    const firstResponderMatch = property.match(
      /^firstResponders\.(\d+)\.(\w+)$/
    );
    if (firstResponderMatch) {
      const [, index, fieldName] = firstResponderMatch;
      if (!firstRespondersMap.has(Number(index))) {
        firstRespondersMap.set(Number(index), {});
      }
      firstRespondersMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    const flagRecipientMatch = property.match(/^flagRecipients\.(\d+)\.(\w+)$/);
    if (flagRecipientMatch) {
      const [, index, fieldName] = flagRecipientMatch;
      if (!flagRecipientsMap.has(Number(index))) {
        flagRecipientsMap.set(Number(index), {});
      }
      flagRecipientsMap.get(Number(index))[fieldName] = parseFieldValue(
        field.answer
      );
      return;
    }

    // Handle affiliations array
    if (property === 'affiliations') {
      result.affiliations = parseFieldValue(field.answer) || [];
      return;
    }

    // Handle direct properties
    if (property in result) {
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Convert Maps to arrays
  result.funeralHomes = Array.from(funeralHomesMap.values());
  result.preferredFuneralHomes = Array.from(preferredFuneralHomesMap.values());
  result.servicePlaces = Array.from(servicePlacesMap.values());
  result.visitationPlaces = Array.from(visitationPlacesMap.values());
  result.officiants = Array.from(officiantsMap.values());
  result.pallbearers = Array.from(pallbearersMap.values());
  result.publications = Array.from(publicationsMap.values());
  result.military = Array.from(militaryMap.values());
  result.firstResponders = Array.from(firstRespondersMap.values());
  result.flagRecipients = Array.from(flagRecipientsMap.values());

  // If we don't have direct last wishes data, use additional data from interview
  if (lastWishesFields.length === 0 && additionalFields.length > 0) {
    const additional: any = {};

    additionalFields.forEach(field => {
      const property = field.id.replace('additional.', '');
      const segments = property.split('.');

      if (segments.length === 1) {
        additional[segments[0]] = field.answer;
      } else if (segments.length === 2) {
        if (!additional[segments[0]]) additional[segments[0]] = {};
        additional[segments[0]][segments[1]] = field.answer;
      }
    });

    // Map additional data to last wishes fields
    result.burialPreferences = additional.burial || '';
    result.burialInfo = additional.burialInfo || '';
  }

  return result;
}

/**
 * Maps shared fields to Property section data
 */
function mapSharedFieldsToPropertyData(sharedFields: any[]) {
  // Filter fields that are for the Property section
  const propertyFields = sharedFields.filter(
    field =>
      field.id.startsWith('Property.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Property')
  );

  // Also include property fields from interview data as fallback
  const interviewPropertyFields = sharedFields.filter(
    field =>
      field.documentType === 'Interview' &&
      field.id.startsWith('afterYouDie.properties[')
  );

  if (propertyFields.length === 0 && interviewPropertyFields.length === 0)
    return null;

  // Initialize the result object
  const result: any = {
    properties: [],
    additionalInstructions: '',
  };

  // First process direct Property fields
  const propertiesMap = new Map();

  propertyFields.forEach(field => {
    if (field.id === 'Property.additionalInstructions') {
      result.additionalInstructions = field.answer;
      return;
    }

    const match = field.id.match(/Property\.properties\[(\d+)\]\.(.+)/);
    if (match) {
      const [, indexStr, property] = match;
      const index = parseInt(indexStr);

      if (!propertiesMap.has(index)) propertiesMap.set(index, {});
      propertiesMap.get(index)[property] = parseFieldValue(field.answer);
    }
  });

  // If we have direct property data, use it
  if (propertiesMap.size > 0) {
    result.properties = Array.from(propertiesMap.values());
  }
  // If we don't have direct property data, use property data from interview
  else if (interviewPropertyFields.length > 0) {
    const interviewPropertiesMap = new Map();

    interviewPropertyFields.forEach(field => {
      const match = field.id.match(/afterYouDie\.properties\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, property] = match;
        const index = parseInt(indexStr);

        if (!interviewPropertiesMap.has(index))
          interviewPropertiesMap.set(index, {});
        interviewPropertiesMap.get(index)[property] = field.answer;
      }
    });

    // Transform to properties array
    result.properties = Array.from(interviewPropertiesMap.values()).map(
      property => ({
        type: property.type || '',
        address: property.address || '',
        ownership: property.ownership ? `${property.ownership}%` : '',
        mortgage: '',
        lender: '',
        insuranceInfo: '',
        notes: '',
      })
    );
  }

  return result;
}

/**
 * Maps shared fields to Financial section data
 */
function mapSharedFieldsToFinancialData(sharedFields: any[]) {
  // Filter fields that are for the Financial section
  const financialFields = sharedFields.filter(
    field =>
      field.id.startsWith('Financial.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Financial')
  );

  // Also include financial fields from interview data as fallback
  const interviewFinancialFields = sharedFields.filter(
    field =>
      (field.documentType === 'Interview' &&
        field.id.startsWith('financial.')) ||
      (field.documentType === 'Interview' &&
        field.id.startsWith('afterYouDie.specificGifts['))
  );

  if (financialFields.length === 0 && interviewFinancialFields.length === 0)
    return null;

  // Initialize the result object with new fields from the FinancialSection component
  const result: any = {
    accounts: [],
    hasProfessionals: null,
    professionals: [],
    hasAdvisor: null,
    financialAdvisors: [],
    hasTaxProfessional: null,
    taxProfessionals: [],
    otherFinancial: [],
    assetAccounts: [],
    liabilityAccounts: [],
    taxState: '',
    taxFilingStatus: '',
    additionalInstructions: '',
  };

  // Maps to collect array data
  const accountsMap = new Map();
  const professionalsMap = new Map();
  const financialAdvisorsMap = new Map();
  const taxProfessionalsMap = new Map();
  const otherFinancialMap = new Map();
  const assetAccountsMap = new Map();
  const liabilityAccountsMap = new Map();

  financialFields.forEach(field => {
    const property = field.id.replace('Financial.', '');

    // Handle array fields with regex pattern matching
    if (property.startsWith('accounts[')) {
      const match = property.match(/accounts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, accProperty] = match;
        const index = parseInt(indexStr);

        if (!accountsMap.has(index)) accountsMap.set(index, {});
        accountsMap.get(index)[accProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('professionals[')) {
      const match = property.match(/professionals\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, profProperty] = match;
        const index = parseInt(indexStr);

        if (!professionalsMap.has(index)) professionalsMap.set(index, {});
        professionalsMap.get(index)[profProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('financialAdvisors[')) {
      const match = property.match(/financialAdvisors\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, advProperty] = match;
        const index = parseInt(indexStr);

        if (!financialAdvisorsMap.has(index))
          financialAdvisorsMap.set(index, {});
        financialAdvisorsMap.get(index)[advProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('taxProfessionals[')) {
      const match = property.match(/taxProfessionals\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, taxProperty] = match;
        const index = parseInt(indexStr);

        if (!taxProfessionalsMap.has(index)) taxProfessionalsMap.set(index, {});
        taxProfessionalsMap.get(index)[taxProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('otherFinancial[')) {
      const match = property.match(/otherFinancial\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, otherProperty] = match;
        const index = parseInt(indexStr);

        if (!otherFinancialMap.has(index)) otherFinancialMap.set(index, {});
        otherFinancialMap.get(index)[otherProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('assetAccounts[')) {
      const match = property.match(/assetAccounts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, assetProperty] = match;
        const index = parseInt(indexStr);

        if (!assetAccountsMap.has(index)) assetAccountsMap.set(index, {});
        assetAccountsMap.get(index)[assetProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property.startsWith('liabilityAccounts[')) {
      const match = property.match(/liabilityAccounts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, liabilityProperty] = match;
        const index = parseInt(indexStr);

        if (!liabilityAccountsMap.has(index))
          liabilityAccountsMap.set(index, {});
        liabilityAccountsMap.get(index)[liabilityProperty] = parseFieldValue(
          field.answer
        );
      }
    } else {
      // Direct properties
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Convert maps to arrays and add to result
  if (accountsMap.size > 0) {
    result.accounts = Array.from(accountsMap.values());
  }
  if (professionalsMap.size > 0) {
    result.professionals = Array.from(professionalsMap.values());
    result.hasProfessionals = true;
  }
  if (financialAdvisorsMap.size > 0) {
    result.financialAdvisors = Array.from(financialAdvisorsMap.values());
    result.hasAdvisor = true;
  }
  if (taxProfessionalsMap.size > 0) {
    result.taxProfessionals = Array.from(taxProfessionalsMap.values());
    result.hasTaxProfessional = true;
  }
  if (otherFinancialMap.size > 0) {
    result.otherFinancial = Array.from(otherFinancialMap.values());
  }
  if (assetAccountsMap.size > 0) {
    result.assetAccounts = Array.from(assetAccountsMap.values());
  }
  if (liabilityAccountsMap.size > 0) {
    result.liabilityAccounts = Array.from(liabilityAccountsMap.values());
  }

  // If we don't have direct financial data, use financial data from interview
  if (financialFields.length === 0 && interviewFinancialFields.length > 0) {
    // Extract financial gifts data
    const giftsMap = new Map();

    interviewFinancialFields.forEach(field => {
      if (field.id.startsWith('afterYouDie.specificGifts[')) {
        const match = field.id.match(
          /afterYouDie\.specificGifts\[(\d+)\]\.(.+)/
        );
        if (match) {
          const [, indexStr, property] = match;
          const index = parseInt(indexStr);

          if (!giftsMap.has(index)) giftsMap.set(index, {});
          giftsMap.get(index)[property] = field.answer;
        }
      }
    });

    // Transform to accounts array
    const accounts = Array.from(giftsMap.values())
      .filter(gift => gift.type === 'financial')
      .map(gift => ({
        type: 'Bank Account',
        institution: gift.bankName || '',
        accountNumber: gift.accountNumber || '',
        balance: gift.amount ? `${gift.amount}` : '',
        notes: '',
      }));

    if (accounts.length > 0) {
      result.accounts = accounts;
    }

    // Map interview financial data to asset accounts if available
    if (accounts.length > 0 && result.assetAccounts.length === 0) {
      result.assetAccounts = accounts.map(account => ({
        name: account.type,
        owner: '',
        institution: account.institution,
        number: account.accountNumber,
        type: 'Bank - Other',
      }));
    }
  }

  return result;
}

/**
 * Maps shared fields to Employment section data
 */
function mapSharedFieldsToEmploymentData(sharedFields: any[]) {
  // Filter fields that are for the Employment section
  const employmentFields = sharedFields.filter(
    field =>
      field.id.startsWith('Employment.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Employment')
  );

  if (employmentFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    workingStatus: [], // Changed from empty string to empty array
    employers: [],
    businesses: [], // Removed hasBusinesses property
    hasLicenses: null, // Changed from false to null to match component
    licenses: [],
  };

  // Process Employment fields
  const employersMap = new Map();
  const businessesMap = new Map();
  const licensesMap = new Map();

  employmentFields.forEach(field => {
    const property = field.id.replace('Employment.', '');

    if (property.startsWith('employers[')) {
      const match = property.match(/employers\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, empProperty] = match;
        const index = parseInt(indexStr);

        if (!employersMap.has(index)) employersMap.set(index, {});
        employersMap.get(index)[empProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('businesses[')) {
      const match = property.match(/businesses\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, busProperty] = match;
        const index = parseInt(indexStr);

        if (!businessesMap.has(index)) businessesMap.set(index, {});
        businessesMap.get(index)[busProperty] = parseFieldValue(field.answer);
      }
    } else if (property.startsWith('licenses[')) {
      const match = property.match(/licenses\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, licProperty] = match;
        const index = parseInt(indexStr);

        if (!licensesMap.has(index)) licensesMap.set(index, {});
        licensesMap.get(index)[licProperty] = parseFieldValue(field.answer);
      }
    } else if (property === 'hasLicenses') {
      result.hasLicenses = parseFieldValue(field.answer);
    } else if (property === 'workingStatus') {
      // Handle workingStatus as an array
      const value = parseFieldValue(field.answer);
      result.workingStatus = Array.isArray(value)
        ? value
        : [value].filter(Boolean);
    } else {
      // Direct properties
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Add employers, businesses, and licenses to result
  if (employersMap.size > 0) {
    result.employers = Array.from(employersMap.values());
  }
  if (businessesMap.size > 0) {
    result.businesses = Array.from(businessesMap.values());
  }
  if (licensesMap.size > 0) {
    result.licenses = Array.from(licensesMap.values());
  }

  return result;
}

/**
 * Maps shared fields to Asset Information section data
 */
function mapSharedFieldsToAssetInformationData(sharedFields: any[]) {
  // Filter fields that are for the Asset Information section
  const assetInfoFields = sharedFields.filter(
    field =>
      field.id.startsWith('AssetInformation.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'AssetInformation')
  );

  if (assetInfoFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    properties: [],
    utilities: [],
    hoa: {
      name: '',
      contact: '',
      website: '',
      fees: '',
      notes: '',
    },
    otherProperties: [],
    safeDeposit: {
      institution: '',
      location: '',
      boxNumber: '',
      keyLocation: '',
      authorizedPersons: '',
      contents: '',
    },
    additionalInstructions: '',
  };

  // Process properties
  const propertiesMap = new Map();
  const utilitiesMap = new Map();
  const otherPropertiesMap = new Map();

  assetInfoFields.forEach(field => {
    const property = field.id.replace('AssetInformation.', '');

    // Handle properties array
    if (property.startsWith('properties[')) {
      const match = property.match(/properties\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, propProperty] = match;
        const index = parseInt(indexStr);

        if (!propertiesMap.has(index)) propertiesMap.set(index, {});
        propertiesMap.get(index)[propProperty] = parseFieldValue(field.answer);
      }
    }
    // Handle utilities array
    else if (property.startsWith('utilities[')) {
      const match = property.match(/utilities\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, utilProperty] = match;
        const index = parseInt(indexStr);

        if (!utilitiesMap.has(index)) utilitiesMap.set(index, {});
        utilitiesMap.get(index)[utilProperty] = parseFieldValue(field.answer);
      }
    }
    // Handle HOA object
    else if (property.startsWith('hoa.')) {
      const hoaProperty = property.replace('hoa.', '');
      result.hoa[hoaProperty] = parseFieldValue(field.answer);
    }
    // Handle otherProperties array
    else if (property.startsWith('otherProperties[')) {
      const match = property.match(/otherProperties\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, otherPropProperty] = match;
        const index = parseInt(indexStr);

        if (!otherPropertiesMap.has(index)) otherPropertiesMap.set(index, {});
        otherPropertiesMap.get(index)[otherPropProperty] = parseFieldValue(
          field.answer
        );
      }
    }
    // Handle safeDeposit object
    else if (property.startsWith('safeDeposit.')) {
      const safeDepositProperty = property.replace('safeDeposit.', '');
      result.safeDeposit[safeDepositProperty] = parseFieldValue(field.answer);
    }
    // Handle direct properties
    else if (property === 'additionalInstructions') {
      result.additionalInstructions = parseFieldValue(field.answer);
    }
  });

  // Convert maps to arrays
  if (propertiesMap.size > 0) {
    result.properties = Array.from(propertiesMap.values()).map(prop => ({
      address1: prop.address1 || '',
      address2: prop.address2 || '',
      city: prop.city || '',
      state: prop.state || '',
      zip: prop.zip || '',
      type: prop.type || '',
      ownership: prop.ownership || '',
      notes: prop.notes || '',
    }));
  }

  if (utilitiesMap.size > 0) {
    result.utilities = Array.from(utilitiesMap.values()).map(util => ({
      name: util.name || '',
      type: util.type || '',
      account: util.account || '',
      contact: util.contact || '',
      notes: util.notes || '',
    }));
  }

  if (otherPropertiesMap.size > 0) {
    result.otherProperties = Array.from(otherPropertiesMap.values()).map(
      prop => ({
        type: prop.type || '',
        description: prop.description || '',
        location: prop.location || '',
        value: prop.value || '',
        notes: prop.notes || '',
      })
    );
  }

  return result;
}

/**
 * Maps shared fields to Emergency Incidents section data
 */
function mapSharedFieldsToEmergencyIncidentsData(sharedFields: any[]) {
  // Filter fields that are for the Emergency Incidents section
  const emergencyIncidentsFields = sharedFields.filter(
    field =>
      field.id.startsWith('EmergencyIncidents.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'EmergencyIncidents')
  );

  if (emergencyIncidentsFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    logs: [],
    additionalInstructions: '',
  };

  // Process logs array and additional instructions
  emergencyIncidentsFields.forEach(field => {
    if (field.id === 'EmergencyIncidents.additionalInstructions') {
      result.additionalInstructions = parseFieldValue(field.answer);
      return;
    }

    // Handle array notation if present
    const arrayMatch = field.id.match(/EmergencyIncidents\.logs\[(\d+)\]/);
    if (arrayMatch) {
      const [, indexStr] = arrayMatch;
      const index = parseInt(indexStr);

      // Ensure the array has enough elements
      while (result.logs.length <= index) {
        result.logs.push('');
      }

      result.logs[index] = parseFieldValue(field.answer);
      return;
    }

    // Handle single field with comma-separated values
    if (field.id === 'EmergencyIncidents.logs') {
      const logsValue = parseFieldValue(field.answer);

      // If the value is already an array, use it directly
      if (Array.isArray(logsValue)) {
        result.logs = logsValue;
      } else if (typeof logsValue === 'string') {
        // Split by comma and trim each entry
        result.logs = logsValue
          .split(',')
          .map(log => log.trim())
          .filter(log => log.length > 0);
      }
    }
  });

  return result;
}

/**
 * Maps shared fields to Important To Me section data
 */
function mapSharedFieldsToImportantToMeData(sharedFields: any[]) {
  // Filter fields that are for the Important To Me section
  const importantToMeFields = sharedFields.filter(
    field =>
      field.id.startsWith('ImportantToMe.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'ImportantToMe')
  );

  if (importantToMeFields.length === 0) return null;

  // Initialize the result object with the updated structure
  const result: any = {
    importantValues: '',
    importantPeople: '',
    importantPlaces: '',
    importantThings: '',
    importantExperiences: '',
    additionalInstructions: '',
    importantContacts: [],
    organizations: [],
  };

  // Create maps for array data
  const contactsMap = new Map();
  const organizationsMap = new Map();

  // Process each field
  importantToMeFields.forEach(field => {
    // Handle direct properties
    if (
      field.id === 'ImportantToMe.importantValues' ||
      field.id === 'ImportantToMe.importantPeople' ||
      field.id === 'ImportantToMe.importantPlaces' ||
      field.id === 'ImportantToMe.importantThings' ||
      field.id === 'ImportantToMe.importantExperiences' ||
      field.id === 'ImportantToMe.additionalInstructions'
    ) {
      const property = field.id.replace('ImportantToMe.', '');
      result[property] = parseFieldValue(field.answer);
      return;
    }

    // Process importantContacts array
    const contactMatch = field.id.match(
      /ImportantToMe\.importantContacts\[(\d+)\]\.(.+)/
    );
    if (contactMatch) {
      const [, indexStr, property] = contactMatch;
      const index = parseInt(indexStr);

      if (!contactsMap.has(index)) contactsMap.set(index, {});
      contactsMap.get(index)[property] = parseFieldValue(field.answer);
      return;
    }

    // Process organizations array
    const orgMatch = field.id.match(
      /ImportantToMe\.organizations\[(\d+)\]\.(.+)/
    );
    if (orgMatch) {
      const [, indexStr, property] = orgMatch;
      const index = parseInt(indexStr);

      if (!organizationsMap.has(index)) organizationsMap.set(index, {});
      organizationsMap.get(index)[property] = parseFieldValue(field.answer);
      return;
    }
  });

  // Convert maps to arrays
  if (contactsMap.size > 0) {
    result.importantContacts = Array.from(contactsMap.values());
  }

  if (organizationsMap.size > 0) {
    result.organizations = Array.from(organizationsMap.values());
  }

  return result;
}

/**
 * Maps shared fields to Insurance section data
 */
function mapSharedFieldsToInsuranceData(sharedFields: any[]) {
  // Filter fields that are for the Insurance section
  const insuranceFields = sharedFields.filter(
    field =>
      field.id.startsWith('Insurance.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Insurance')
  );

  if (insuranceFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    policies: [],
    additionalInstructions: '',
  };

  // Process policies array
  const policiesMap = new Map();

  insuranceFields.forEach(field => {
    if (field.id === 'Insurance.additionalInstructions') {
      result.additionalInstructions = parseFieldValue(field.answer);
      return;
    }

    const match = field.id.match(/Insurance\.policies\[(\d+)\]\.(.+)/);
    if (match) {
      const [, indexStr, policyProperty] = match;
      const index = parseInt(indexStr);

      if (!policiesMap.has(index)) policiesMap.set(index, {});
      policiesMap.get(index)[policyProperty] = parseFieldValue(field.answer);
    }
  });

  // Convert map to array
  if (policiesMap.size > 0) {
    result.policies = Array.from(policiesMap.values()).map(policy => ({
      type: policy.type || '',
      company: policy.company || '',
      policyNumber: policy.policyNumber || '',
      contactInfo: policy.contactInfo || '',
      coverage: policy.coverage || '',
      beneficiaries: policy.beneficiaries || '',
      notes: policy.notes || '',
      // Health insurance specific fields
      subscriber: policy.subscriber || '',
      groupNumber: policy.groupNumber || '',
      memberNumber: policy.memberNumber || '',
      isEmployer:
        policy.isEmployer === true
          ? true
          : policy.isEmployer === false
            ? false
            : undefined,
      employerType: policy.employerType || '',
      employerName: policy.employerName || '',
      // Agent-based insurance fields
      agentName: policy.agentName || '',
      agentPhone: policy.agentPhone || '',
      agentEmail: policy.agentEmail || '',
      // Other insurance specific field
      purpose: policy.purpose || '',
    }));
  }

  return result;
}

/**
 * Maps shared fields to Legacy section data
 */
function mapSharedFieldsToLegacyData(sharedFields: any[]) {
  // Filter fields that are for the Legacy section
  const legacyFields = sharedFields.filter(
    field =>
      field.id.startsWith('Legacy.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Legacy')
  );

  if (legacyFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    isLegacyImportant: null,
    howLegacy: '',
    rememberedHow: '',
    additionalLegacy: '',
    personalMessages: '',
    ethicalWill: '',
    lifeStory: '',
    familyHistory: '',
    legacyProjects: '',
    additionalInstructions: '',
  };

  // Process each field
  legacyFields.forEach(field => {
    const property = field.id.replace('Legacy.', '');
    if (Object.keys(result).includes(property)) {
      result[property] = parseFieldValue(field.answer);
    }
  });

  return result;
}

/**
 * Maps shared fields to Long Term Care section data
 */
function mapSharedFieldsToLongTermCareData(sharedFields: any[]) {
  // Filter fields that are for the Long Term Care section
  const longTermCareFields = sharedFields.filter(
    field =>
      field.id.startsWith('LongTermCare.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'LongTermCare')
  );

  if (longTermCareFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    preferredLivingArrangement: '',
    carePreferences: [],
    facilityPreferences: '',
    homeCarePreferences: '',
    financialConsiderations: '',
    additionalInstructions: '',
    hasWorkedWithLTC: null,
    ltcContacts: [],
    funding: [],
    fundingExplanation: '',
    careLocation: '',
    facilityImportance: '',
    carePhilosophy: '',
    careImportant: '',
    otherLTC: '',
  };

  // Maps for handling array fields
  const ltcContactsMap = new Map();

  // Process each field
  longTermCareFields.forEach(field => {
    const property = field.id.replace('LongTermCare.', '');

    // Handle ltcContacts array
    if (property.startsWith('ltcContacts[')) {
      const match = property.match(/ltcContacts\[(\d+)\]\.(.+)/);
      if (match) {
        const [, indexStr, contactProperty] = match;
        const index = parseInt(indexStr);

        if (!ltcContactsMap.has(index)) ltcContactsMap.set(index, {});
        ltcContactsMap.get(index)[contactProperty] = parseFieldValue(
          field.answer
        );
      }
    } else if (property === 'carePreferences' || property === 'funding') {
      // Handle array fields
      try {
        const parsedValue = parseFieldValue(field.answer);
        result[property] = Array.isArray(parsedValue)
          ? parsedValue
          : [parsedValue];
      } catch {
        result[property] = [];
      }
    } else if (Object.keys(result).includes(property)) {
      // Handle direct properties
      result[property] = parseFieldValue(field.answer);
    }
  });

  // Add ltcContacts to result
  if (ltcContactsMap.size > 0) {
    result.ltcContacts = Array.from(ltcContactsMap.values());
  }

  // Ensure carePreferences and funding are always arrays
  if (!Array.isArray(result.carePreferences)) {
    result.carePreferences = result.carePreferences
      ? [result.carePreferences]
      : [];
  }

  if (!Array.isArray(result.funding)) {
    result.funding = result.funding ? [result.funding] : [];
  }

  return result;
}

/**
 * Maps shared fields to Uploads section data
 */
function mapSharedFieldsToUploadsData(sharedFields: any[]) {
  // Filter fields that are for the Uploads section
  const uploadsFields = sharedFields.filter(
    field =>
      field.id.startsWith('Uploads.') ||
      (field.documentType === 'Care Document' &&
        field.documentTitle === 'Uploads')
  );

  if (uploadsFields.length === 0) return null;

  // Initialize the result object
  const result: any = {
    documentCategories: [],
    additionalNotes: '',
    uploadedFiles: [],
  };

  // Process document categories
  const categoriesField = uploadsFields.find(
    field => field.id === 'Uploads.documentCategories'
  );
  if (categoriesField) {
    try {
      // Try parsing as JSON first
      try {
        const categories = JSON.parse(categoriesField.answer);
        if (Array.isArray(categories)) {
          result.documentCategories = categories;
        }
      } catch {
        // If not valid JSON, split by comma and trim
        result.documentCategories = categoriesField.answer
          .split(',')
          .map((category: string) => category.trim())
          .filter((category: string) => category.length > 0);
      }
    } catch (e) {
      console.error('Error processing document categories:', e);
    }
  }

  // Process additional notes
  const notesField = uploadsFields.find(
    field => field.id === 'Uploads.additionalNotes'
  );
  if (notesField) {
    result.additionalNotes = parseFieldValue(notesField.answer);
  }

  // Process uploaded files
  // First check if there's a single field with all files
  const filesField = uploadsFields.find(
    field => field.id === 'Uploads.uploadedFiles'
  );
  if (filesField) {
    try {
      const files = JSON.parse(filesField.answer);
      if (Array.isArray(files)) {
        result.uploadedFiles = files;
      }
    } catch (e) {
      console.error('Error parsing uploaded files:', e);
    }
  } else {
    // Process individual file entries
    const fileMap = new Map();

    // Find all fields related to uploaded files
    const fileFields = uploadsFields.filter(field =>
      field.id.match(/^Uploads\.uploadedFiles\[\d+\]\.\w+$/)
    );

    // Group fields by file index
    fileFields.forEach(field => {
      const match = field.id.match(/^Uploads\.uploadedFiles\[(\d+)\]\.(\w+)$/);
      if (match) {
        const [, indexStr, property] = match;
        const index = parseInt(indexStr, 10);

        if (!fileMap.has(index)) {
          fileMap.set(index, {});
        }

        const fileObj = fileMap.get(index);
        fileObj[property] = parseFieldValue(field.answer);
      }
    });

    // Convert map to array
    result.uploadedFiles = Array.from(fileMap.values());
  }

  return result;
}
