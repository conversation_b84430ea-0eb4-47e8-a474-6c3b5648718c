'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState, useCallback } from 'react';
import {
  attorneyFormSchema,
  type AttorneyFormData,
  type AttorneyFormProps,
  type AttorneySubmissionData,
  getDefaultAttorneyFormValues,
  legalSpecialties,
} from '@/lib/validations/attorney';
import { validateAddress } from '@/app/utils/addressValidation';

interface UseAttorneyFormOptions {
  attorney?: AttorneyFormProps['attorney'];
  mode: AttorneyFormProps['mode'];
  onSave?: AttorneyFormProps['onSave'];
  isLoading?: boolean;
}

export function useAttorneyForm({
  attorney,
  mode,
  onSave,
  isLoading = false,
}: UseAttorneyFormOptions) {
  const router = useRouter();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm<AttorneyFormData>({
    resolver: zodResolver(attorneyFormSchema) as any,
    defaultValues: getDefaultAttorneyFormValues(attorney, mode),
    mode: 'onChange',
  });

  // Watch form values for dynamic behavior
  const watchedSpecialties = form.watch('specialties');

  // Check if form has unsaved changes
  const hasUnsavedChanges = form.formState.isDirty;

  // Handle form submission
  const onSubmit = useCallback(
    async (data: AttorneyFormData) => {
      try {
        setSubmitError(null);

        // Validate address if provided
        if (data.address && data.city && data.state && data.zipCode) {
          console.log('🔍 Validating attorney address...');
          setIsValidatingAddress(true);

          try {
            const addressValidationResult = await validateAddress({
              addressLine1: data.address,
              city: data.city,
              state: data.state,
              postalCode: data.zipCode,
            });

            if (!addressValidationResult.isValid) {
              setSubmitError(
                `Address validation failed: ${addressValidationResult.issues.join(', ')}`
              );
              setIsValidatingAddress(false);
              return;
            }

            console.log('✅ Attorney address validation passed');
          } catch (error) {
            console.error('❌ Attorney address validation error:', error);
            setSubmitError(
              'Address validation failed. Please check the address and try again.'
            );
            setIsValidatingAddress(false);
            return;
          }
          setIsValidatingAddress(false);
        }

        // Prepare submission data
        const submissionData: AttorneySubmissionData = {
          ...data,
          ...(mode === 'edit' && attorney?.id && { id: attorney.id }),
        };

        // Call the onSave callback if provided
        if (onSave) {
          await onSave(submissionData);
        }
      } catch (error) {
        console.error('Form submission error:', error);
        setSubmitError(
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred. Please try again.'
        );
      }
    },
    [onSave, mode, attorney?.id]
  );

  // Handle cancel action
  const handleCancel = useCallback(() => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave?'
      );
      if (!confirmLeave) return;
    }
    router.push('/admin/attorneys');
  }, [hasUnsavedChanges, router]);

  // Handle specialty changes
  const handleSpecialtyChange = useCallback(
    (specialty: string, checked: boolean) => {
      const currentSpecialties = form.getValues('specialties');

      if (checked) {
        // Add specialty if not already present
        if (!currentSpecialties.includes(specialty)) {
          form.setValue('specialties', [...currentSpecialties, specialty], {
            shouldDirty: true,
            shouldValidate: true,
          });
        }
      } else {
        // Remove specialty
        form.setValue(
          'specialties',
          currentSpecialties.filter(s => s !== specialty),
          {
            shouldDirty: true,
            shouldValidate: true,
          }
        );
      }
    },
    [form]
  );

  // Handle adding custom specialty
  const handleAddCustomSpecialty = useCallback(
    (customSpecialty: string) => {
      if (!customSpecialty.trim()) return;

      const currentSpecialties = form.getValues('specialties');

      // Check if specialty already exists (case-insensitive)
      const exists = currentSpecialties.some(
        s => s.toLowerCase() === customSpecialty.toLowerCase()
      );

      if (!exists) {
        form.setValue(
          'specialties',
          [...currentSpecialties, customSpecialty.trim()],
          {
            shouldDirty: true,
            shouldValidate: true,
          }
        );
      }
    },
    [form]
  );

  // Handle removing specialty
  const handleRemoveSpecialty = useCallback(
    (specialtyToRemove: string) => {
      const currentSpecialties = form.getValues('specialties');
      form.setValue(
        'specialties',
        currentSpecialties.filter(s => s !== specialtyToRemove),
        {
          shouldDirty: true,
          shouldValidate: true,
        }
      );
    },
    [form]
  );

  // Available specialties (predefined ones)
  const availableSpecialties = Array.from(legalSpecialties);

  return {
    // Form instance
    form,

    // Form state
    isSubmitting:
      form.formState.isSubmitting || isLoading || isValidatingAddress,
    submitError,
    hasUnsavedChanges,
    isValid: form.formState.isValid,
    isValidatingAddress,

    // Watched values
    watchedSpecialties,

    // Available options
    availableSpecialties,

    // Form actions
    onSubmit: form.handleSubmit(onSubmit),
    handleCancel,
    handleSpecialtyChange,
    handleAddCustomSpecialty,
    handleRemoveSpecialty,

    // Form utilities
    clearError: () => setSubmitError(null),
    resetForm: () => {
      const defaultValues = getDefaultAttorneyFormValues(attorney, mode);
      form.reset(defaultValues);
      setSubmitError(null);
    },
  };
}
