'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchAuthSession } from 'aws-amplify/auth';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { jwtDecode } from 'jwt-decode';
import {
  loadInterviewProgressV2,
  saveInterviewProgressV2,
} from '@/app/utils/interviewV2Progress';
import { v4 as uuidv4 } from "uuid";

const client = generateClient<Schema>();

export interface Person {
  id: string;
  type: 'individual' | 'charity' | 'business';
  firstName?: string;
  middleName?: string;
  lastName?: string;
  entityName?: string;
  dateOfBirth?: string;
  ein?: string;
  phoneNumber?: string;
  address?: string;
  relationshipType?: string; // e.g., 'Spouse', 'Friend', 'Family', 'Business Partner', etc.
}

async function getCurrentUserRecord() {
  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  if (!token) {
    throw new Error('No access token found');
  }

  const decodedToken = jwtDecode<{ externalId?: string }>(token);

  if (!decodedToken || !decodedToken.externalId) {
    throw new Error('Invalid token or missing externalId');
  }

  const { data: userData } = await client.models.User.list({
    filter: { cognitoId: { eq: decodedToken.externalId } },
  });

  if (!userData || userData.length === 0) {
    throw new Error('User not found');
  }

  return userData[0];
}

export function usePeopleLibrary() {
  const queryClient = useQueryClient();

  const {
    data: people = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['peopleLibrary'],
    queryFn: async (): Promise<Person[]> => {
      try {
        const user = await getCurrentUserRecord();
        const peopleLibrary = user.peopleLibrary;

        if (!peopleLibrary) return [];

        let parsed = peopleLibrary;
        if (typeof peopleLibrary === 'string') {
          parsed = JSON.parse(peopleLibrary);
        }

        return Array.isArray(parsed) ? parsed : [];
      } catch (error) {
        console.error('Error fetching people library:', error);
        return [];
      }
    },
  });

  const updatePeopleLibraryMutation = useMutation({
    mutationFn: async (newPeople: Person[]) => {
      const user = await getCurrentUserRecord();

      const { data, errors } = await client.models.User.update({
        id: user.id,
        peopleLibrary: JSON.stringify(newPeople),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.setQueryData(['peopleLibrary'], variables);
    },
  });

  const addPerson = useCallback(
    async (person: Omit<Person, 'id'>) => {
      const newPerson: Person = {
        ...person,
        id: Date.now().toString(),
      };

      let updatedPeople = [...people, newPerson];

      // If adding someone as 'Spouse', clear any existing spouse
      if (newPerson.relationshipType === 'Spouse') {
        updatedPeople = updatedPeople.map(p =>
          p.id !== newPerson.id && p.relationshipType === 'Spouse'
            ? { ...p, relationshipType: 'Friend' } // Change previous spouse to friend
            : p
        );
      }

      await updatePeopleLibraryMutation.mutateAsync(updatedPeople);

      // If the new person is a spouse, sync to set marital status
      if (newPerson.relationshipType === 'Spouse') {
        await syncPersonDataInSteps(newPerson);
      }

      return newPerson;
    },
    [people, updatePeopleLibraryMutation]
  );

  const addPersons = useCallback(
    async (persons: Omit<Person, 'id'>[]) => {
      const newPersons: Person[] = persons.filter(
        person => !checkPersonExists(person)
      ).map((person,index) => ({
        ...person,
        // Generate unique ID for each person
        id: uuidv4(),
      }));

      const updatedPeople = [...people, ...newPersons];
      await updatePeopleLibraryMutation.mutateAsync(updatedPeople);

      return newPersons;
    },
    [people, updatePeopleLibraryMutation]
  );

  const checkPersonExists = useCallback(
    (person: Omit<Person, 'id'>) => {
      return people.some(
        p =>
          p.type === person.type &&
          p.firstName === person.firstName &&
          p.lastName === person.lastName &&
          p.dateOfBirth === person.dateOfBirth && 
          p.phoneNumber === person.phoneNumber
      );
    },
    [people]
  );

  const updatePerson = useCallback(
    async (updatedPerson: Person) => {
      // If setting someone as 'Spouse', clear any existing spouse
      let updatedPeople = people.map(p =>
        p.id === updatedPerson.id ? updatedPerson : p
      );

      if (updatedPerson.relationshipType === 'Spouse') {
        // Clear relationshipType from any other person who was previously 'Spouse'
        updatedPeople = updatedPeople.map(p =>
          p.id !== updatedPerson.id && p.relationshipType === 'Spouse'
            ? { ...p, relationshipType: 'Friend' } // Change previous spouse to friend
            : p
        );
      }

      await updatePeopleLibraryMutation.mutateAsync(updatedPeople);

      // Sync the updated person data across all interview steps
      await syncPersonDataInSteps(updatedPerson);

      // Force page reload after successful update to ensure all components reflect changes
      setTimeout(() => {
        window.location.reload();
      }, 500);

      return updatedPerson;
    },
    [people, updatePeopleLibraryMutation]
  );

  const deletePerson = useCallback(
    async (personId: string) => {
      const updatedPeople = people.filter(p => p.id !== personId);
      await updatePeopleLibraryMutation.mutateAsync(updatedPeople);

      // Remove all references to this person from interview data
      await removePersonFromSteps(personId);

      // Force page reload after successful deletion to ensure all components reflect changes
      setTimeout(() => {
        window.location.reload();
      }, 500);

      return personId;
    },
    [people, updatePeopleLibraryMutation]
  );

  // Function to sync updated person data across all interview steps
  const syncPersonDataInSteps = useCallback(async (updatedPerson: Person) => {
    try {
      const currentProgress = await loadInterviewProgressV2();
      if (!currentProgress?.stepsData) return;

      const stepsData = currentProgress.stepsData;
      let hasChanges = false;

      // Helper function to update person object if it matches the updated person
      const updatePersonIfMatch = (
        person: Person | undefined
      ): Person | undefined => {
        if (person && person.id === updatedPerson.id) {
          hasChanges = true;
          return { ...updatedPerson };
        }
        return person;
      };

      // Helper function to update array of persons
      const updatePersonsArray = (
        persons: Person[] | undefined
      ): Person[] | undefined => {
        if (!persons) return persons;
        const updated = persons.map(
          person => updatePersonIfMatch(person) || person
        );
        return updated;
      };

      // Update emergency section
      if (stepsData.emergency) {
        // Update dependents
        if (stepsData.emergency.dependents) {
          stepsData.emergency.dependents = stepsData.emergency.dependents.map(
            (dependent: any) => ({
              ...dependent,
              person: updatePersonIfMatch(dependent.person) || dependent.person,
            })
          );
        }

        // Update pets
        if (stepsData.emergency.pets) {
          stepsData.emergency.pets = stepsData.emergency.pets.map(
            (pet: any) => ({
              ...pet,
              primaryGuardian: updatePersonIfMatch(pet.primaryGuardian),
              secondaryGuardian: updatePersonIfMatch(pet.secondaryGuardian),
              takerOfLastResort: updatePersonIfMatch(pet.takerOfLastResort),
              // Keep backward compatibility
              guardian: updatePersonIfMatch(pet.primaryGuardian),
            })
          );
        }
      }

      // Update afterYouDie section
      if (stepsData.afterYouDie) {
        // Update properties
        if (stepsData.afterYouDie.properties) {
          stepsData.afterYouDie.properties =
            stepsData.afterYouDie.properties.map((property: any) => ({
              ...property,
              recipient: updatePersonIfMatch(property.recipient),
              coOwners: property.coOwners?.map((coOwner: any) => ({
                ...coOwner,
                person: updatePersonIfMatch(coOwner.person) || coOwner.person,
              })),
            }));
        }

        // Update specific gifts
        if (stepsData.afterYouDie.specificGifts) {
          stepsData.afterYouDie.specificGifts =
            stepsData.afterYouDie.specificGifts.map((gift: any) => ({
              ...gift,
              recipient: updatePersonIfMatch(gift.recipient) || gift.recipient,
            }));
        }

        // Update main beneficiaries
        if (stepsData.afterYouDie.mainBeneficiaries) {
          stepsData.afterYouDie.mainBeneficiaries =
            stepsData.afterYouDie.mainBeneficiaries.map((beneficiary: any) => ({
              ...beneficiary,
              primaryBeneficiary:
                updatePersonIfMatch(beneficiary.primaryBeneficiary) ||
                beneficiary.primaryBeneficiary,
              successors: beneficiary.successors.map((successor: any) => ({
                ...successor,
                person:
                  updatePersonIfMatch(successor.person) || successor.person,
              })),
            }));
        }

        // Update last resort beneficiary
        stepsData.afterYouDie.lastResortBeneficiary = updatePersonIfMatch(
          stepsData.afterYouDie.lastResortBeneficiary
        );
      }

      // Update financial section
      if (stepsData.financial?.agent) {
        stepsData.financial.agent.primary = updatePersonIfMatch(
          stepsData.financial.agent.primary
        );
        stepsData.financial.agent.successors = updatePersonsArray(
          stepsData.financial.agent.successors
        );
      }

      // Update medical section
      if (stepsData.medical) {
        if (stepsData.medical.proxy) {
          stepsData.medical.proxy.primary = updatePersonIfMatch(
            stepsData.medical.proxy.primary
          );
          stepsData.medical.proxy.successors = updatePersonsArray(
            stepsData.medical.proxy.successors
          );
        }
        stepsData.medical.consultationPeople = updatePersonsArray(
          stepsData.medical.consultationPeople
        );
        stepsData.medical.medicalInfoAccessPeople = updatePersonsArray(
          stepsData.medical.medicalInfoAccessPeople
        );
      }

      // Update additional section
      if (stepsData.additional?.guardian) {
        stepsData.additional.guardian.primary = updatePersonIfMatch(
          stepsData.additional.guardian.primary
        );
        stepsData.additional.guardian.successors = updatePersonsArray(
          stepsData.additional.guardian.successors
        );
      }

      // Update profile section - handle spouse synchronization
      if (stepsData.profile?.spouse) {
        // Check if the updated person matches the current spouse
        if (stepsData.profile.spouse.id === updatedPerson.id) {
          // If relationshipType is no longer 'Spouse', clear marital status
          if (updatedPerson.relationshipType !== 'Spouse') {
            stepsData.profile.maritalStatus = undefined;
            stepsData.profile.spouse = undefined;
            hasChanges = true;
          } else {
            // Update spouse with latest data from People Library
            stepsData.profile.spouse = { ...updatedPerson };
            hasChanges = true;
          }
        }
      }

      // If someone's relationshipType is set to 'Spouse', automatically set marital status
      if (
        updatedPerson.relationshipType === 'Spouse' &&
        updatedPerson.type === 'individual' &&
        (!stepsData.profile?.spouse ||
          stepsData.profile.spouse.id !== updatedPerson.id)
      ) {
        // Set marital status to married and set spouse reference
        if (!stepsData.profile) stepsData.profile = {};
        stepsData.profile.maritalStatus = 'married';
        stepsData.profile.spouse = { ...updatedPerson };
        hasChanges = true;

        console.log(
          `Automatically set marital status to married for spouse: ${updatedPerson.firstName} ${updatedPerson.lastName}`
        );
      }

      // Save changes if any were made
      if (hasChanges) {
        await saveInterviewProgressV2({ stepsData });
      }
    } catch (error) {
      console.error('Error syncing person data in steps:', error);
    }
  }, []);

  // Function to remove all references to a deleted person from interview steps
  const removePersonFromSteps = useCallback(async (deletedPersonId: string) => {
    try {
      const currentProgress = await loadInterviewProgressV2();
      if (!currentProgress?.stepsData) return;

      const stepsData = currentProgress.stepsData;
      let hasChanges = false;

      // Helper function to remove person if it matches the deleted person
      const removePersonIfMatch = (
        person: Person | undefined
      ): Person | undefined => {
        if (person && person.id === deletedPersonId) {
          hasChanges = true;
          return undefined;
        }
        return person;
      };

      // Helper function to filter array of persons
      const filterPersonsArray = (
        persons: Person[] | undefined
      ): Person[] | undefined => {
        if (!persons) return persons;
        const filtered = persons.filter(
          person => person.id !== deletedPersonId
        );
        if (filtered.length !== persons.length) {
          hasChanges = true;
        }
        return filtered;
      };

      // Update emergency section
      if (stepsData.emergency) {
        // Remove dependents that reference the deleted person
        if (stepsData.emergency.dependents) {
          const filteredDependents = stepsData.emergency.dependents.filter(
            (dependent: any) => dependent.person.id !== deletedPersonId
          );
          if (
            filteredDependents.length !== stepsData.emergency.dependents.length
          ) {
            hasChanges = true;
            stepsData.emergency.dependents = filteredDependents;
          }
        }

        // Remove guardian references from pets
        if (stepsData.emergency.pets) {
          stepsData.emergency.pets = stepsData.emergency.pets.map(
            (pet: any) => ({
              ...pet,
              primaryGuardian: removePersonIfMatch(pet.primaryGuardian),
              secondaryGuardian: removePersonIfMatch(pet.secondaryGuardian),
              takerOfLastResort: removePersonIfMatch(pet.takerOfLastResort),
              // Keep backward compatibility
              guardian: removePersonIfMatch(pet.primaryGuardian),
            })
          );
        }
      }

      // Update afterYouDie section
      if (stepsData.afterYouDie) {
        // Update executor
        if (stepsData.afterYouDie.executor) {
          stepsData.afterYouDie.executor.primary = removePersonIfMatch(
            stepsData.afterYouDie.executor.primary
          );
          stepsData.afterYouDie.executor.successors = filterPersonsArray(
            stepsData.afterYouDie.executor.successors
          );
        }

        // Update successor trustee
        if (stepsData.afterYouDie.successorTrustee) {
          stepsData.afterYouDie.successorTrustee.primary = removePersonIfMatch(
            stepsData.afterYouDie.successorTrustee.primary
          );
          stepsData.afterYouDie.successorTrustee.successors =
            filterPersonsArray(
              stepsData.afterYouDie.successorTrustee.successors
            );
        }

        // Update properties
        if (stepsData.afterYouDie.properties) {
          stepsData.afterYouDie.properties =
            stepsData.afterYouDie.properties.map((property: any) => ({
              ...property,
              recipient: removePersonIfMatch(property.recipient),
              coOwners: property.coOwners?.filter(
                (coOwner: any) => coOwner.person.id !== deletedPersonId
              ),
            }));
        }

        // Remove specific gifts to the deleted person
        if (stepsData.afterYouDie.specificGifts) {
          const filteredGifts = stepsData.afterYouDie.specificGifts.filter(
            (gift: any) => gift.recipient.id !== deletedPersonId
          );
          if (
            filteredGifts.length !== stepsData.afterYouDie.specificGifts.length
          ) {
            hasChanges = true;
            stepsData.afterYouDie.specificGifts = filteredGifts;
          }
        }

        // Update main beneficiaries
        if (stepsData.afterYouDie.mainBeneficiaries) {
          // Remove beneficiaries where primary beneficiary is the deleted person
          const filteredBeneficiaries =
            stepsData.afterYouDie.mainBeneficiaries.filter(
              (beneficiary: any) =>
                beneficiary.primaryBeneficiary.id !== deletedPersonId
            );

          // Also remove successors that reference the deleted person
          const updatedBeneficiaries = filteredBeneficiaries.map(
            (beneficiary: any) => ({
              ...beneficiary,
              successors: beneficiary.successors.filter(
                (successor: any) => successor.person.id !== deletedPersonId
              ),
            })
          );

          if (
            updatedBeneficiaries.length !==
              stepsData.afterYouDie.mainBeneficiaries.length ||
            updatedBeneficiaries.some(
              (beneficiary: any, index: number) =>
                beneficiary.successors.length !==
                stepsData.afterYouDie.mainBeneficiaries[index]?.successors
                  ?.length
            )
          ) {
            hasChanges = true;
            stepsData.afterYouDie.mainBeneficiaries = updatedBeneficiaries;
          }
        }

        // Update last resort beneficiary
        stepsData.afterYouDie.lastResortBeneficiary = removePersonIfMatch(
          stepsData.afterYouDie.lastResortBeneficiary
        );
      }

      // Update financial section
      if (stepsData.financial?.agent) {
        stepsData.financial.agent.primary = removePersonIfMatch(
          stepsData.financial.agent.primary
        );
        stepsData.financial.agent.successors = filterPersonsArray(
          stepsData.financial.agent.successors
        );
      }

      // Update medical section
      if (stepsData.medical) {
        if (stepsData.medical.proxy) {
          stepsData.medical.proxy.primary = removePersonIfMatch(
            stepsData.medical.proxy.primary
          );
          stepsData.medical.proxy.successors = filterPersonsArray(
            stepsData.medical.proxy.successors
          );
        }
        stepsData.medical.consultationPeople = filterPersonsArray(
          stepsData.medical.consultationPeople
        );
        stepsData.medical.medicalInfoAccessPeople = filterPersonsArray(
          stepsData.medical.medicalInfoAccessPeople
        );
      }

      // Update additional section
      if (stepsData.additional?.guardian) {
        stepsData.additional.guardian.primary = removePersonIfMatch(
          stepsData.additional.guardian.primary
        );
        stepsData.additional.guardian.successors = filterPersonsArray(
          stepsData.additional.guardian.successors
        );
      }

      // Update profile section - remove spouse if deleted
      if (
        stepsData.profile?.spouse &&
        stepsData.profile.spouse.id === deletedPersonId
      ) {
        stepsData.profile.spouse = undefined;
        stepsData.profile.maritalStatus = undefined;
        hasChanges = true;
      }

      // Save changes if any were made
      if (hasChanges) {
        await saveInterviewProgressV2({ stepsData });
      }
    } catch (error) {
      console.error('Error removing person from steps:', error);
    }
  }, []);

  return {
    people,
    isLoading,
    error,
    addPerson,
    addPersons,
    updatePerson,
    deletePerson,
    refetch,
    isUpdating: updatePeopleLibraryMutation.isPending,
  };
}
