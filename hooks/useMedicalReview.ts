'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';
import * as Sentry from '@sentry/nextjs';

export type MedicalReport = Schema['MedicalReport']['type'];

// Generate the client
const client = generateClient<Schema>();

export function useMedicalReview() {
  const [medicalData, setMedicalData] = useState<{
    careDocuments: string;
    interviewData: string;
  }>({
    careDocuments: 'No medical data available.',
    interviewData: 'No medical data available.',
  });
  const [medicalReports, setMedicalReports] = useState<MedicalReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [medicalDataLoading, setMedicalDataLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, userExternalId } = useAuth();

  const formatToHtmlParagraphs = (text: string): string =>
    text
      .split('\n')
      .map(line => `<p>${line}</p>`)
      .join('');

  const fetchMedicalData = useCallback(
    async (userId: string) => {
      try {
        setLoading(true);
        setMedicalDataLoading(true);
        setError(null);

        const { data, errors } = await client.queries.getMedicalDataForReview({
          userId: userId,
        });

        if (errors) {
          throw new Error(errors[0].message);
        }

        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

        if (parsedData?.data?.medicalData) {
          const { careDocuments, interviewData } = parsedData.data.medicalData;

          setMedicalData({
            careDocuments: formatToHtmlParagraphs(careDocuments),
            interviewData: formatToHtmlParagraphs(interviewData),
          });
        }
      } catch (err) {
        console.error('Error fetching medical data:', err);
        Sentry.captureException(err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
        setMedicalDataLoading(false);
      }
    },
    [userExternalId]
  );

  const resetMedicalData = useCallback(() => {
    setMedicalData({
      careDocuments: 'No medical data available.',
      interviewData: 'No medical data available.',
    });
  }, []);

  const createMedicalReport = useCallback(
    async (
      medicalReport: Omit<
        MedicalReport,
        'id' | 'userId' | 'createdAt' | 'updatedAt' | 'user'
      >
    ) => {
      try {
        setLoading(true);
        setError(null);

        if (!user) {
          setError('Failed to save medical report');
          setLoading(false);

          return;
        }

        const { data, errors } = await client.models.MedicalReport.create({
          ...medicalReport,
          userId: user.userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });

        if (errors) {
          throw new Error(errors[0].message);
        }

        if (!data) {
          throw new Error('Failed to create medical report');
        }

        setMedicalReports(prev => [...prev, data]);

        return data;
      } catch (err) {
        console.error('Error saving medical report:', err);
        Sentry.captureException(err);
        setError(err instanceof Error ? err.message : 'Failed to save report');
      } finally {
        setLoading(false);
      }
    },
    [userExternalId]
  );

  const updateMedicalReport = useCallback(
    async (medicalReport: Omit<MedicalReport, 'user'>) => {
      try {
        setLoading(true);
        setError(null);

        if (!medicalReport.id) {
          setError('Failed to update medical report');
          setLoading(false);

          return;
        }

        const { data, errors } =
          await client.models.MedicalReport.update(medicalReport);

        if (errors) {
          throw new Error(errors[0].message);
        }

        return data;
      } catch (err) {
        console.error('Error updating medical report:', err);
        Sentry.captureException(err);
        setError(
          err instanceof Error ? err.message : 'Failed to update report'
        );
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const fetchMedicalReport = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        setError(null);

        if (!userExternalId) {
          setError('Failed to fetch medical report');
          setLoading(false);

          return;
        }

        const { data, errors } = await client.models.MedicalReport.get({
          id,
        });

        if (errors) {
          throw new Error(errors[0].message);
        }

        if (!data) {
          return null;
        }

        return data;
      } catch (err) {
        console.error('Error fetching medical report:', err);
        Sentry.captureException(err);
        setError(err instanceof Error ? err.message : 'Failed to fetch report');
      } finally {
        setLoading(false);
      }
    },
    [userExternalId]
  );

  const fetchMedicalReports = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to fetch medical reports');
        setLoading(false);

        return;
      }

      const { data, errors } = await client.models.MedicalReport.list({
        filter: {
          userId: { eq: user.userId },
        },
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      setMedicalReports(data || []);
    } catch (err) {
      console.error('Error fetching medical reports:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to fetch reports');
    } finally {
      setLoading(false);
    }
  }, [userExternalId]);

  useEffect(() => {
    if (user) {
      fetchMedicalReports();
    }
  }, [user, fetchMedicalReports]);

  return {
    medicalData,
    medicalReports,
    loading,
    medicalDataLoading,
    error,
    fetchMedicalData,
    createMedicalReport,
    updateMedicalReport,
    fetchMedicalReport,
    resetMedicalData,
  };
}
