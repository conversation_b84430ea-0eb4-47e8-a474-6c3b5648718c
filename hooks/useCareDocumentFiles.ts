// import { useState } from 'react';
import { getCurrentUser } from 'aws-amplify/auth';
import { uploadData, getUrl, remove } from 'aws-amplify/storage';
import { toast } from 'sonner';

export interface FileInfo {
  key: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
}

export function useCareDocumentFiles() {
  //   const [isUploading, setIsUploading] = useState(false);

  // Upload file to S3
  const uploadFile = async (
    file: File,
    documentId: string,
    questionId: string
  ): Promise<FileInfo> => {
    try {
      //   setIsUploading(true);
      let user;
      try {
        // Check if user is authenticated
        user = await getCurrentUser();
      } catch (authError) {
        console.error('User not authenticated:', authError);
        throw new Error('You must be logged in to upload files');
      }

      console.log('Uploading file:', file.name);

      // Create a unique file key using the care-documents path with owner-based access
      // The {identity} will be automatically replaced with the user's ID by Amplify
      const fileKey = `care-documents/{identity}/${documentId}/${questionId}/${file.name}`;

      // Upload the file to S3 using Amplify Storage
      const result = await uploadData({
        key: fileKey,
        data: file,
        options: {
          contentType: file.type,
          metadata: {
            documentId,
            questionId,
            fileName: file.name,
            uploadedBy: user.userId,
          },
        },
      });

      console.log('File uploaded successfully:', result);

      return {
        key: fileKey,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        uploadedBy: user.userId,
      };
    } catch (err) {
      console.error('Error uploading file:', err);
      throw err;
    } finally {
      //   setIsUploading(false);
    }
  };

  // Get a signed URL for a file
  const getFileUrl = async (fileKey: string): Promise<string> => {
    try {
      if (!fileKey) {
        throw new Error('File key is empty or undefined');
      }

      console.log('Getting URL for file key:', fileKey);

      const result = await getUrl({
        key: fileKey,
        options: {
          validateObjectExistence: true,
        },
      });

      console.log('Successfully retrieved URL:', result.url.toString());
      return result.url.toString();
    } catch (err) {
      console.error('Error getting file URL:', err, 'for key:', fileKey);
      // toast.error(
      //   `Failed to load file: ${err instanceof Error ? err.message : 'Unknown error'}`
      // );
      throw err;
    }
  };

  // Delete file from storage
  const deleteFile = async (fileKey: string): Promise<void> => {
    try {
      console.log('Deleting file:', fileKey);

      await remove({
        key: fileKey,
      });

      console.log('File deleted successfully');
    } catch (err: any) {
      console.error('Error deleting file:', err);
      throw new Error(
        `Failed to delete file: ${err.message || 'Unknown error'}`
      );
    }
  };

  return {
    uploadFile,
    getFileUrl,
    deleteFile,
    // isUploading,
  };
}
