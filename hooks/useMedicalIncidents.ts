'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';
import * as Sentry from '@sentry/nextjs';

export type MedicalIncident = Schema['MedicalIncident']['type'];

// Generate the client
const client = generateClient<Schema>();

export function useMedicalIncidents() {
  const [incidents, setIncidents] = useState<MedicalIncident[]>([]);
  const [incident, setIncident] = useState<MedicalIncident | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchIncidents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to fetch medical incidents');
        setLoading(false);

        return;
      }

      const { data, errors } = await client.models.MedicalIncident.list({
        filter: { userId: { eq: user.userId } },
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      setIncidents(data || []);
    } catch (err) {
      console.error('Error fetching medical incidents:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to fetch incidents');
    } finally {
      setLoading(false);
    }
  }, [user]);

  const fetchIncident = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, errors } = await client.models.MedicalIncident.get({
        id,
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      setIncident(data);

      return data;
    } catch (err) {
      console.error('Error fetching medical incident:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to fetch incident');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchIncidents();
  }, [fetchIncidents]);

  const createIncident = useCallback(async (incidentData: Omit<MedicalIncident, 'id' | 'userId' | 'createdAt' | 'updatedAt' | 'user'>
  ) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to save medical incident');
        setLoading(false);

        return;
      }

      const { data, errors } = await client.models.MedicalIncident.create({
        ...incidentData,
        userId: user?.userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      if (!data) {
        throw new Error('Failed to create medical incident');
      }
      
      setIncidents(prev => [data, ...prev]);

      return data;
    } catch (err) {
      console.error('Error saving medical incident:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to save incident');
    } finally {
      setLoading(false);
    }
  }, [user]);

  const updateIncident = useCallback(async (incidentData: Omit<MedicalIncident, 'createdAt' | 'user'>) => {
    try {
      setLoading(true);
      setError(null);
      
      if(!incidentData.id) {
        setError('Failed to update medical incident');
        setLoading(false);

        return;
      }

      const { data, errors } = await client.models.MedicalIncident.update(incidentData);

      if (errors) {
        throw new Error(errors[0].message);
      }

      setIncidents(prev => prev.map(incident => incident.id === data?.id ? data : incident));
    } catch (err) {
      console.error('Error updating medical incident:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to update incident');
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteIncident = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const { errors } = await client.models.MedicalIncident.delete({
        id,
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      setIncidents(prev => prev.filter(incident => incident.id !== id));
    } catch (err) {
      console.error('Error deleting medical incident:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to delete incident');
    } finally {
      setLoading(false);
    }
  }, []);

  const sendEmails = useCallback(async (id: string, emailType: 'fyi' | 'hospital' | 'support', clientData?: { firstName?: string | null; lastName?: string | null, birthdate?: string | null }) => {
    try {
      setLoading(true);
      setError(null);

      const { data, errors } = await client.mutations.sendMedicalIncident({
        incidentId: id,
        emailType,
        clientData,
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      return data;
    } catch (err) {
      console.error('Error sending medical incident emails:', err);
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'Failed to send emails');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    incidents,
    loading,
    error,
    fetchIncidents,
    createIncident,
    updateIncident,
    deleteIncident,
    fetchIncident,
    incident,
    sendEmails
  };
}
