import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchUserByCognitoId } from '@/lib/data/users';
import { useAuth } from '@/context/AuthContext';
import { Person, usePeopleLibrary } from './usePeopleLibrary';
import { useQueryClient } from '@tanstack/react-query';

const client = generateClient<Schema>();

export interface LinkedAccount {
  id: string;
  userId: string; // The ID of the user who owns this link
  userEmail: string;
  linkedUserId: string; // The ID of the user who is linked
  linkedUserEmail: string;
  sharedFields: string;
  isAccepted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserDetails {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

export function useLinkedAccounts() {
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [linkedByAccounts, setLinkedByAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user, userExternalId, userEmail } = useAuth();
  const { addPersons } = usePeopleLibrary();
  const queryClient = useQueryClient();

  const fetchUserDetails = async (
    userId: string
  ): Promise<UserDetails | null> => {
    try {
      const { data, errors } = await client.models.User.get({ id: userId });

      if (errors) {
        console.error('Error fetching user details:', errors);
        return null;
      }

      if (!data) return null;

      return {
        id: data.id,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
      };
    } catch (err) {
      console.error('Error fetching user details:', err);
      return null;
    }
  };

  const fetchLinkedAccounts = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to fetch linked accounts');
        setLoading(false);

        return;
      }

      if (!userExternalId) {
        setError('Failed to fetch linked accounts');
        setLoading(false);

        return;
      }

      const userData = await fetchUserByCognitoId(userExternalId);

      if (!userData) {
        setError('Failed to fetch linked accounts');
        setLoading(false);

        return;
      }

      // Fetch accounts that the current user has linked
      const { data: ownedLinks, errors: ownedErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            userId: { eq: userData.cognitoId },
          },
        });

      if (ownedErrors) {
        throw new Error(ownedErrors[0].message);
      }

      // Fetch accounts where the current user is linked by others
      const { data: linkedByOthers, errors: linkedByErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            linkedUserId: { eq: userData.cognitoId },
          },
        });

      if (linkedByErrors) {
        throw new Error(linkedByErrors[0].message);
      }

      setLinkedAccounts(ownedLinks || []);
      setLinkedByAccounts(linkedByOthers || []);
    } catch (err) {
      console.error('Error fetching linked accounts:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch linked accounts'
      );
    } finally {
      setLoading(false);
    }
  };

  // New function to check if a user is already linked by email
  const checkExistingLinkedAccountByEmail = async (
    email: string
  ): Promise<boolean> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      if (!userExternalId) {
        throw new Error('Failed to fetch linked accounts');
      }

      const userData = await fetchUserByCognitoId(userExternalId);

      if (!userData) {
        throw new Error('Failed to fetch user data');
      }

      // First, find the user with the given email
      const { data: users, errors: userErrors } = await client.models.User.list(
        {
          filter: { email: { eq: email } },
        }
      );

      if (userErrors) {
        throw new Error(userErrors[0].message);
      }

      if (!users || users.length === 0) {
        // No user found with this email, so no linked account exists
        return false;
      }

      const targetUser = users[0];

      if (!targetUser.cognitoId) {
        throw new Error('Failed to fetch target user data');
      }

      // Check if there's already a link between the current user and the target user
      const { data: existingLinks, errors: linkErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            and: [
              { userId: { eq: userData.cognitoId } },
              { linkedUserId: { eq: targetUser.cognitoId } },
            ],
          },
        });

      if (linkErrors) {
        throw new Error(linkErrors[0].message);
      }

      // Return true if there's at least one existing link
      return existingLinks && existingLinks.length > 0;
    } catch (err) {
      console.error('Error checking existing linked account:', err);
      throw err;
    }
  };

  const removeLinkedAccount = async (linkId: string) => {
    try {
      const { errors } =
        await client.models.LinkedAccountWithSharedFields.delete({
          id: linkId,
        });

      if (errors) {
        throw new Error(errors[0].message);
      }

      // Refresh the list
      await fetchLinkedAccounts();
      return true;
    } catch (err) {
      console.error('Error removing linked account:', err);
      throw err;
    }
  };

  const acceptLinkedAccount = async (linkId: string, sharedFields: string) => {
    try {
      const { errors } =
        await client.models.LinkedAccountWithSharedFields.update({
          id: linkId,
          sharedFields: sharedFields,
          isAccepted: true,
        });

      if (!userEmail) {
        throw new Error('Failed to fetch user email');
      }

      const { data: invites, errors: inviteErrors } =
        await client.models.UserInvite.list({
          filter: { email: { eq: userEmail } },
        });

      if (inviteErrors) {
        throw new Error(inviteErrors[0].message);
      }

      if (invites && invites.length > 0) {
        const notAcceptedInvites = invites.filter(
          invite =>
            invite.status !== 'accepted' && invite.status !== 'cancelled'
        );
        if (notAcceptedInvites.length > 0) {
          await Promise.all(
            notAcceptedInvites.map(async invite => {
              await client.models.UserInvite.update({
                id: invite.id,
                status: 'accepted',
                acceptedAt: new Date().toISOString(),
              });
            })
          );
        }
      }

      if (errors) {
        throw new Error(errors[0].message);
      }

      const parsedSharedFields = JSON.parse(sharedFields);
      let persons: Person[] = [];

      // Process each shared field and add people to people library
      parsedSharedFields.forEach((field: any) => {
        if (field.documentType === 'People Library') {
          const person = JSON.parse(field.answer);
          //delete id from person
          delete person.id;
          persons.push(person);
        }
      });

      await addPersons(persons);

      //invalidate query interviewV2Progress

      queryClient.invalidateQueries({ queryKey: ['interviewV2Progress'] });

      // Refresh the list
      await fetchLinkedAccounts();
      return true;
    } catch (err) {
      console.error('Error accepting linked account:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchLinkedAccounts();
  }, []);

  return {
    linkedAccounts,
    linkedByAccounts,
    loading,
    error,
    fetchLinkedAccounts,
    removeLinkedAccount,
    acceptLinkedAccount,
    checkExistingLinkedAccountByEmail,
  };
}
