import { useEffect } from 'react';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';

// Generate the client
const client = generateClient<Schema>();

/**
 * Custom hook to pre-fill care document sections with data from interviewProgressV2
 * @param sectionType The type of section being pre-filled
 * @param sectionData The current section data from context
 * @param setData The state setter function for the section
 */
export function useInterviewPrefill(
  sectionType: string,
  sectionData: any,
  setData: (data: any) => void
) {
  const { userExternalId } = useAuth();

  useEffect(() => {
    // Only run if we don't already have section data
    if (sectionData && Object.keys(sectionData).length > 0) {
      return;
    }

    const fetchUserInterviewData = async () => {
      try {

        if (!userExternalId) {
          console.error('Error fetching user data');
          return;
        }

        // Fetch user data including interviewProgressV2
        const { data: userList, errors } = await client.models.User.list({
          filter: { cognitoId: { eq: userExternalId } },
          selectionSet: ['id', 'cognitoId', 'interviewProgressV2.*'],
        });

        if (errors || !userList || userList.length === 0) {
          console.error('Error fetching user data:', errors);
          return;
        }

        const userData = userList[0];
        const interviewData = userData.interviewProgressV2;

        if (!interviewData) {
          console.log('No interview data available for pre-filling');
          return;
        }

        const interviewDataWithParsedStepsData = { ...interviewData, stepsData: JSON.parse(interviewData.stepsData as string) }

        // Map interview data to section data based on section type
        const prefilledData = mapInterviewDataToSection(sectionType, interviewDataWithParsedStepsData);

        if (prefilledData) {
          console.log(`Pre-filling ${sectionType} with interview data`);
          setData(prefilledData);
        }
      } catch (error) {
        console.error('Error pre-filling section data:', error);
      }
    };

    fetchUserInterviewData();
  }, [sectionType, sectionData, setData]);
}

/**
 * Maps interview data to the appropriate section data structure
 * @param sectionType The type of section being pre-filled
 * @param interviewData The interview data from interviewProgressV2
 * @returns The pre-filled section data
 */
function mapInterviewDataToSection(sectionType: string, interviewData: any) {
  // Extract stepsData which contains the structured interview data
  const { stepsData } = interviewData;
  if (!stepsData) return null;

  switch (sectionType) {
    case 'Pets':
      return mapPetsData(stepsData);
    case 'PersonalIdentification':
      return mapPersonalIdentificationData(stepsData);
    case 'EmergencyContacts':
      return mapEmergencyContactsData(stepsData);
    case 'Medical':
      return mapMedicalData(stepsData);
    case 'LastWishes':
      return mapLastWishesData(stepsData);
    case 'Property':
      return mapPropertyData(stepsData);
    case 'Financial':
      return mapFinancialData(stepsData);
    case 'Employment':
      return mapEmploymentData(stepsData);
    // Add more section mappings as needed
    default:
      return null;
  }
}

/**
 * Maps interview data to Pets section data
 */
function mapPetsData(stepsData: any) {
  // Check if we have pets data in the interview
  if (!stepsData.emergency?.pets?.length) {
    return null;
  }

  // Transform the pets data to match the section structure
  const pets = stepsData.emergency.pets.map((pet: any) => ({
    name: pet.name || '',
    type: '',  // Not available in interview data
    breed: '',  // Not available in interview data
    age: '',  // Not available in interview data
    veterinarian: '',  // Not available in interview data
    vetPhone: '',  // Not available in interview data
    careInstructions: '',  // Not available in interview data
    medications: '',  // Not available in interview data
    caregiverName: '',  // Not available in interview data
    caregiverPhone: '',  // Not available in interview data
    caregiverEmail: ''  // Not available in interview data
  }));

  return {
    hasPets: pets.length > 0,
    pets,
    additionalInstructions: ''
  };
}

/**
 * Maps interview data to Personal Identification section data
 */
function mapPersonalIdentificationData(stepsData: any) {
  if (!stepsData.profile) {
    return null;
  }

  return {
    identifyingDocuments: [],
    hasPasswordManager: null,
    passwordManagerName: '',
    passwordManagerUsername: '',
    passwordManagerPassword: '',
    noPasswordManagerInfo: '',
    socialMedia: [],
    socialAccounts: [],
    cloudStorage: [],
    cloudAccounts: [],
    deviceWishes: '',
    // Pre-fill with profile data if available
    fullName: `${stepsData.profile.firstName || ''} ${stepsData.profile.lastName || ''}`.trim(),
    email: stepsData.profile.email || '',
    phone: stepsData.profile.phone || '',
    address: stepsData.profile.address ? 
      `${stepsData.profile.address.street || ''}, ${stepsData.profile.address.city || ''}, ${stepsData.profile.address.state || ''} ${stepsData.profile.address.zip || ''}`.trim() : '',
    dateOfBirth: stepsData.profile.dateOfBirth || ''
  };
}

/**
 * Maps interview data to Emergency Contacts section data
 */
function mapEmergencyContactsData(stepsData: any) {
  if (!stepsData.people || !stepsData.people.length) {
    return null;
  }

  // Transform people data to emergency contacts
  const contacts = stepsData.people.map((person: any) => ({
    name: `${person.firstName || ''} ${person.lastName || ''}`.trim(),
    relationship: '',  // Not available in interview data
    phone: person.phoneNumber || '',
    email: '',  // Not available in interview data
    address: person.address || '',
    isPrimary: false,  // Default value
    isAuthorized: false,  // Default value
    notes: ''
  }));

  return {
    contacts,
    additionalInstructions: ''
  };
}

/**
 * Maps interview data to Medical section data
 */
function mapMedicalData(stepsData: any) {
  if (!stepsData.medical) {
    return null;
  }

  const medical = stepsData.medical;
  
  return {
    professionals: [{
      name: "",
      isPrimary: null as boolean | null,
      isSpecialist: null as boolean | null,
      specialty: "",
      hospital: "",
      phone: "",
      notes: ""
    }],  // Not enough data to pre-fill
    bloodType: '',  // Not available in interview data
    organDonation: medical.organDonation?.enabled || false,
    medicalDirective: medical.directives ? true : false,
    coreWishes: '',  // Not available in interview data
    dignityMeaning: '',  // Not available in interview data
    noMedical: '',  // Not available in interview data
    foodPreferences: '',  // Not available in interview data
    additionalMedical: ''  // Not available in interview data
  };
}

/**
 * Maps interview data to Last Wishes section data
 */
function mapLastWishesData(stepsData: any) {
  if (!stepsData.additional) {
    return null;
  }

  return {
    funeralPreferences: '',  // Not available in interview data
    memorialPreferences: '',  // Not available in interview data
    burialPreferences: stepsData.additional.burial || '',
    cremationPreferences: '',  // Not available in interview data
    obituaryPreferences: '',  // Not available in interview data
    memorialDonations: '',  // Not available in interview data
    digitalLegacy: '',  // Not available in interview data
    additionalInstructions: ''  // Not available in interview data
  };
}

/**
 * Maps interview data to Property section data
 */
function mapPropertyData(stepsData: any) {
  if (!stepsData.afterYouDie?.properties || !stepsData.afterYouDie.properties.length) {
    return null;
  }

  // Transform properties data
  const properties = stepsData.afterYouDie.properties.map((property: any) => ({
    type: property.type || '',
    address: property.address || '',
    ownership: property.ownership ? `${property.ownership}%` : '',
    mortgage: '',  // Not available in interview data
    lender: '',  // Not available in interview data
    insuranceInfo: '',  // Not available in interview data
    notes: ''  // Not available in interview data
  }));

  return {
    properties,
    additionalInstructions: ''
  };
}

/**
 * Maps interview data to Financial section data
 */
function mapFinancialData(stepsData: any) {
  if (!stepsData.financial || !stepsData.afterYouDie?.specificGifts) {
    return null;
  }

  // Transform financial gifts data
  const accounts = stepsData.afterYouDie.specificGifts
    .filter((gift: any) => gift.type === 'financial')
    .map((gift: any) => ({
      type: 'Bank Account',  // Default value
      institution: gift.bankName || '',
      accountNumber: gift.accountNumber || '',
      balance: gift.amount ? `${gift.amount}` : '',
      notes: ''
    }));

  return {
    accounts,
    hasTrusts: false,  // Default value
    trusts: [],
    hasAdvisor: false,  // Default value
    hasProfessionals: false,
    professionals: [],
    advisorName: '',
    advisorPhone: '',
    advisorEmail: '',
    advisorNotes: '',
    additionalInstructions: ''
  };
}

/**
 * Maps interview data to Employment section data
 */
function mapEmploymentData(stepsData: any) {
  // No direct employment data in the interview, but we can create a skeleton
  return {
    workingStatus: '',  // Not available in interview data
    employers: [],
    hasBusinesses: false,
    businesses: [],
    hasLicenses: false,
    licenses: []
  };
}