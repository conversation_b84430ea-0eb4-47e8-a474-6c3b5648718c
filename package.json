{"name": "multi-repo-example", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "format:staged": "lint-staged", "prepare": "husky", "test:commit": "node scripts/test-commit-message.js", "install-chrome": "npx puppeteer browsers install chrome", "postinstall": "npx puppeteer browsers install chrome"}, "dependencies": {"@aws-amplify/adapter-nextjs": "^1.6.2", "@aws-amplify/ui-react": "^6.11.1", "@aws-sdk/client-cognito-identity-provider": "^3.828.0", "@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/client-secrets-manager": "^3.848.0", "@aws-sdk/client-ses": "^3.840.0", "@aws-sdk/client-sns": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^10.8.0", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-query": "^5.79.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-blockquote": "^2.12.0", "@tiptap/extension-code": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-horizontal-rule": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/js-yaml": "^4.0.9", "@types/jspdf": "^2.0.0", "@types/uuid": "^10.0.0", "aws-amplify": "^6.14.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "exceljs": "^4.4.0", "handlebars": "^4.7.8", "html-to-pdfmake": "^2.5.28", "html2pdf.js": "^0.10.3", "input-otp": "^1.4.2", "js-yaml": "^4.1.0", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-themes": "^0.4.6", "pdfmake": "^0.2.20", "postcss": "^8.5.3", "puppeteer": "^24.9.0", "react": "^18", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-hook-form": "^7.56.4", "react-oidc-context": "^3.3.0", "sonner": "^2.0.3", "stripe": "^18.3.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.17", "zxcvbn": "^4.4.2"}, "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.7.2", "@types/aws-lambda": "^8.10.149", "@types/handlebars": "^4.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/zxcvbn": "^4.4.5", "aws-cdk-lib": "^2.189.1", "constructs": "^10.4.2", "esbuild": "^0.25.4", "eslint": "^8", "eslint-config-next": "14.2.28", "husky": "^9.1.7", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md,mdx}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}}