// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/verifyEmailToken';
import {
  CognitoIdentityProviderClient,
  AdminConfirmSignUpCommand,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'no region',
});

export const handler = async event => {
  console.log('Verify email token request:', JSON.stringify(event, null, 2));

  try {
    // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
    const { email, token, verificationType, newPassword } =
      event.arguments || event;

    // Validate required fields
    if (!email || !token) {
      throw new Error('Missing required fields: email, token');
    }

    if (!verificationType) {
      throw new Error('Missing required field: verificationType');
    }

    // Validate verificationType
    if (
      ![
        'accountConfirmation',
        'passwordReset',
        'emergencyContactConfirmation',
        'emergencyNotification',
      ].includes(verificationType)
    ) {
      throw new Error(
        'Invalid verificationType. Must be "accountConfirmation", "passwordReset", "emergencyContactConfirmation", or "emergencyNotification"'
      );
    }

    // For password reset, newPassword is required
    if (verificationType === 'passwordReset' && !newPassword) {
      throw new Error('newPassword is required for password reset');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Add a small delay to account for eventual consistency
    await new Promise(resolve => setTimeout(resolve, 1000));

    // First, let's try to get ALL tokens without any filters
    const allTokensNoFilter = await client.models.VerificationTokens.list();

    if (allTokensNoFilter.data && allTokensNoFilter.data.length > 0) {
      // Filter manually for our email and verification type
      // Handle legacy tokens that might not have verificationType
      const tokensForEmail = allTokensNoFilter.data.filter(
        t =>
          t.email === email &&
          (t.verificationType === verificationType ||
            (!t.verificationType && verificationType === 'accountConfirmation'))
      );

      // For emergency notification, don't filter by isUsed
      const filteredTokens =
        verificationType === 'emergencyNotification'
          ? tokensForEmail
          : tokensForEmail.filter(t => !t.isUsed);

      const matchingToken = filteredTokens.find(t => t.token === token);
      if (matchingToken) {
        // Use this token and skip the rest of the filtering logic
        const verificationToken = matchingToken;

        // Additional safety check
        if (!verificationToken || !verificationToken.id) {
          throw new Error('Invalid verification token data');
        }

        // Check if token has expired
        const now = new Date();
        const expiresAt = new Date(verificationToken.expiresAt);
        if (now > expiresAt) {
          throw new Error('Verification token has expired');
        }

        // Mark token as used
        await client.models.VerificationTokens.update({
          id: verificationToken.id,
          isUsed: true,
          usedAt: now.toISOString(),
        });

        // Handle different verification types
        if (verificationType === 'accountConfirmation') {
          // Confirm the user in Cognito for account confirmation
          try {
            await cognitoClient.send(
              new AdminConfirmSignUpCommand({
                UserPoolId: env.USER_POOL_ID,
                Username: email,
              })
            );

            // Also set email_verified attribute to true
            await cognitoClient.send(
              new AdminUpdateUserAttributesCommand({
                UserPoolId: env.USER_POOL_ID,
                Username: email,
                UserAttributes: [
                  {
                    Name: 'email_verified',
                    Value: 'true',
                  },
                ],
              })
            );
          } catch (cognitoError) {
            console.error('Error confirming user in Cognito:', cognitoError);
          }

          return {
            success: true,
            message: 'Account confirmed successfully',
          };
        } else if (verificationType === 'passwordReset') {
          // For password reset, change the password in Cognito
          try {
            await cognitoClient.send(
              new AdminSetUserPasswordCommand({
                UserPoolId: env.USER_POOL_ID,
                Username: email,
                Password: newPassword,
                Permanent: true,
              })
            );
          } catch (cognitoError) {
            console.error('Error changing password in Cognito:', cognitoError);
            throw new Error('Failed to change password');
          }

          return {
            success: true,
            message: 'Password reset successfully',
          };
        } else if (verificationType === 'emergencyContactConfirmation') {
          // For emergency contact confirmation, we just mark the token as used
          // The frontend will handle updating the emergency contact's verification status
          return {
            success: true,
            message: 'Emergency contact confirmed successfully',
          };
        } else if (verificationType === 'emergencyNotification') {
          // For emergency notification, we just mark the token as used
          // The frontend will handle showing the emergency documents
          return {
            success: true,
            message: 'Emergency notification verified successfully',
          };
        } else {
          throw new Error('Invalid verification type');
        }
      }
    }

    // If manual filtering didn't work, continue with the original approach
    // First try with verificationType filter
    let allTokensResult = await client.models.VerificationTokens.list({
      filter: {
        email: { eq: email },
        verificationType: { eq: verificationType },
      },
    });

    // If no tokens found and we're looking for accountConfirmation, try legacy tokens without verificationType
    if (
      (!allTokensResult.data || allTokensResult.data.length === 0) &&
      verificationType === 'accountConfirmation'
    ) {
      allTokensResult = await client.models.VerificationTokens.list({
        filter: {
          email: { eq: email },
        },
      });
    }

    // Now get tokens - for emergencyNotification, don't filter by isUsed
    let listResult;
    if (verificationType === 'emergencyNotification') {
      listResult = await client.models.VerificationTokens.list({
        filter: {
          email: { eq: email },
          verificationType: { eq: verificationType },
        },
      });
    } else {
      listResult = await client.models.VerificationTokens.list({
        filter: {
          email: { eq: email },
          isUsed: { eq: false },
          verificationType: { eq: verificationType },
        },
      });
    }

    // If no unused tokens found and we're looking for accountConfirmation, try legacy tokens
    if (
      (!listResult.data || listResult.data.length === 0) &&
      verificationType === 'accountConfirmation'
    ) {
      listResult = await client.models.VerificationTokens.list({
        filter: {
          email: { eq: email },
          isUsed: { eq: false },
        },
      });
    }

    if (!listResult || !listResult.data || listResult.data.length === 0) {
      throw new Error('Verification token not found or expired');
    }

    // Find the token that matches
    const verificationToken = listResult.data.find(t => t.token === token);

    if (!verificationToken) {
      throw new Error('Invalid verification token');
    }

    // Additional safety check
    if (!verificationToken.id) {
      throw new Error('Invalid verification token data');
    }

    // Check if token is already used - skip this check for emergencyNotification
    if (
      verificationType !== 'emergencyNotification' &&
      verificationToken.isUsed
    ) {
      throw new Error('Verification token has already been used');
    }

    // Check if token has expired
    const now = new Date();
    const expiresAt = new Date(verificationToken.expiresAt);
    if (now > expiresAt) {
      throw new Error('Verification token has expired');
    }

    // Check if token matches
    if (verificationToken.token !== token) {
      throw new Error('Invalid verification token');
    }

    // Mark token as used
    await client.models.VerificationTokens.update({
      id: verificationToken.id,
      isUsed: true,
      usedAt: now.toISOString(),
    });

    // Handle different verification types
    if (verificationType === 'accountConfirmation') {
      // Confirm the user in Cognito for account confirmation
      try {
        await cognitoClient.send(
          new AdminConfirmSignUpCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: email,
          })
        );

        // Also set email_verified attribute to true
        await cognitoClient.send(
          new AdminUpdateUserAttributesCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: email,
            UserAttributes: [
              {
                Name: 'email_verified',
                Value: 'true',
              },
            ],
          })
        );
      } catch (cognitoError) {
        console.error('Error confirming user in Cognito:', cognitoError);
        // Don't throw here - the token verification was successful
        // The user might already be confirmed or there might be other issues
      }

      return {
        success: true,
        message: 'Account confirmed successfully',
      };
    } else if (verificationType === 'passwordReset') {
      // For password reset, change the password in Cognito
      try {
        await cognitoClient.send(
          new AdminSetUserPasswordCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: email,
            Password: newPassword,
            Permanent: true,
          })
        );
      } catch (cognitoError) {
        console.error('Error changing password in Cognito:', cognitoError);
        throw new Error(cognitoError);
      }

      return {
        success: true,
        message: 'Password reset successfully',
      };
    } else if (verificationType === 'emergencyNotification') {
      // For emergency notification
      return {
        success: true,
        message: 'Emergency notification verified successfully',
      };
    } else if (verificationType === 'emergencyContactConfirmation') {
      // For emergency contact confirmation
      return {
        success: true,
        message: 'Emergency contact confirmed successfully',
      };
    } else {
      throw new Error('Invalid verification type');
    }
  } catch (error) {
    console.error('Error verifying email token:', error);

    return {
      success: false,
      error: error.message || 'Failed to verify email token',
    };
  }
};
