import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
// @ts-ignore
import { env } from '$amplify/env/sendMedicalIncident';
import {
  AdminGetUserCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['getMedicalDataForReview']['functionHandler'] =
  async event => {
    console.log(
      'Get medical data for review request:',
      JSON.stringify(event, null, 2)
    );

    try {
      // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
      const { userId } = event.arguments || event;

      console.log('User ID:', userId);

      // Validate required fields
      if (!userId) {
        throw new Error('Missing required field: userId');
      }

      // Fetch the user data
      const { data: users, errors: userErrors } = await client.models.User.list(
        {
          filter: { id: { eq: userId } },
        }
      );

      if (userErrors) {
        throw new Error(
          `Failed to fetch user data: ${JSON.stringify(userErrors)}`
        );
      }

      if (!users || users.length === 0) {
        throw new Error('User not found');
      }

      const user = users[0];

      const medicalData = await getUserMedicalData(user);

      return {
        success: true,
        data: {
          medicalData,
        },
        message: 'Medical data retrieved successfully',
      };
    } catch (error: any) {
      console.error('Error getting medical data for review:', error);

      return {
        success: false,
        error: error.message || 'Failed to get medical data for review',
      };
    }
  };

const getUserMedicalData = async (user: Schema['User']['type']) => {
  try {
    console.log('Getting user medical data');

    const interviewData = user.interviewProgressV2;

    if (!interviewData) {
      console.warn('Interview data not found');
    }

    const userCognitoData = await cognitoClient.send(
      new AdminGetUserCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: user.email,
      })
    );

    const externalId = userCognitoData.UserAttributes?.find(
      a => a.Name === 'sub'
    )?.Value;

    const { data: careDocuments, errors: careDocumentsErrors } =
      await client.models.CareDocumentNew.list({
        filter: externalId
          ? { userId: { eq: user.id } }
          : { owner: { contains: user.email } },
      });

    if (careDocumentsErrors) {
      console.error(
        'Failed to fetch care documents:',
        JSON.stringify(careDocumentsErrors)
      );
    }

    const careDocument = careDocuments?.[0];
    if (!careDocument) {
      console.warn('Care documents not found');
    }

    const processedInterviewData = processInterviewData(interviewData);
    const processedCareDocuments = processCareDocuments(careDocument);

    const medicalData = {
      interviewData: processedInterviewData,
      careDocuments: processedCareDocuments,
    };

    return medicalData;
  } catch (error) {
    console.error('Error getting user medical data:', error);
    throw error;
  }
};

const processInterviewData = (interviewData: any) => {
  if (!interviewData) return 'No medical data provided.';

  const parsedStepsData =
    typeof interviewData.stepsData === 'string'
      ? JSON.parse(interviewData.stepsData)
      : interviewData.stepsData;
  const medical = parsedStepsData?.medical;
  if (!medical) return 'No medical data provided.';

  let emailBody = 'Patient Medical Information\n\n';

  // Proxy (medical decision maker)
  if (medical.proxy?.primary) {
    const p = medical.proxy.primary;
    emailBody += `Primary Proxy: ${p.firstName} ${p.lastName}, ${p.relationshipType || ''}\n`;
    emailBody += `Phone: ${p.phoneNumber}\nAddress: ${p.address}\nDOB: ${p.dateOfBirth}\n\n`;
  }
  if (medical.proxy?.successors?.length) {
    emailBody += 'Successor Proxies:\n';
    medical.proxy.successors.forEach((s: any) => {
      emailBody += `- ${s.firstName} ${s.lastName}, Phone: ${s.phoneNumber}, Address: ${s.address}, DOB: ${s.dateOfBirth}\n`;
    });
    emailBody += '\n';
  }

  // Consultation preferences
  if (medical.wantConsultation) {
    emailBody += 'Consultation Allowed With:\n';
    medical.consultationPeople.forEach((c: any) => {
      emailBody += `- ${c.firstName} ${c.lastName}, Phone: ${c.phoneNumber}, Address: ${c.address}\n`;
    });
    emailBody += '\n';
  }

  // Organ donation
  if (medical.organDonation?.enabled) {
    emailBody += 'Organ Donation: YES\n';
    emailBody += `Specific Organs: ${medical.organDonation.specificOrgans.join(', ')}\n`;
    if (medical.organDonation.limitations) {
      emailBody += `Limitations: ${medical.organDonation.limitations}\n`;
    }
    emailBody += '\n';
  } else {
    emailBody += 'Organ Donation: NO\n\n';
  }

  // Directives (life-sustaining treatments etc.)
  if (medical.directives) {
    emailBody += 'Medical Directives:\n';
    for (const [key, value] of Object.entries(medical.directives)) {
      emailBody += `- ${key.replace(/_/g, ' ')}: ${value ? 'Yes' : 'No'}\n`;
    }
    emailBody += '\n';
  }

  // Authority
  if (medical.proxyAuthority) {
    emailBody += `Proxy Authority: ${medical.proxyAuthority}\n`;
  }

  return emailBody.trim();
};

const processCareDocuments = (careDocuments: any) => {
  if (!careDocuments) return 'No medical data provided.';

  const medicalSection = careDocuments?.data?.find(
    (s: any) => s.sectionType === 'Medical'
  );

  if (!medicalSection) return 'No medical data available.';

  let content: any;
  try {
    content = JSON.parse(medicalSection.content);
  } catch {
    return 'Invalid medical data format.';
  }

  let emailBody = 'Care Document – Medical Information\n\n';

  // Professionals
  if (content.professionals?.length) {
    emailBody += 'Healthcare Professionals:\n';
    content.professionals.forEach((p: any) => {
      emailBody += `- ${p.name} ${p.isPrimary ? '(Primary)' : ''} ${
        p.isSpecialist ? `(Specialist: ${p.specialty || ''})` : ''
      } | Hospital: ${p.hospital || ''} | Phone: ${p.phone || ''}\n`;
    });
    emailBody += '\n';
  }

  // Medications
  if (content.medications?.length) {
    emailBody += 'Medications:\n';
    content.medications.forEach((m: any) => {
      emailBody += `- ${m.name}, Dosage: ${m.dosage}, Diagnosed: ${m.diagnosisDate}, Providers: ${m.providers?.join(', ')}, Notes: ${m.notes}\n`;
    });
    emailBody += '\n';
  }

  // Conditions
  if (content.conditions?.length) {
    emailBody += 'Medical Conditions:\n';
    content.conditions.forEach((c: any) => {
      emailBody += `- ${c.name}, Diagnosed: ${c.diagnosisDate}, Providers: ${c.providers?.join(', ')}, Notes: ${c.notes}\n`;
    });
    emailBody += '\n';
  }

  // Surgeries
  if (content.surgeries?.length) {
    emailBody += 'Surgeries:\n';
    content.surgeries.forEach((s: any) => {
      emailBody += `- ${s.type}, Date: ${s.date}, Providers: ${s.providers?.join(', ')}, Notes: ${s.notes}\n`;
    });
    emailBody += '\n';
  }

  // Allergies
  if (content.allergies?.length) {
    emailBody += 'Allergies:\n';
    content.allergies.forEach((a: any) => {
      emailBody += `- ${a.name}, Diagnosed: ${a.diagnosisDate}, Providers: ${a.providers?.join(', ')}, Notes: ${a.notes}\n`;
    });
    emailBody += '\n';
  }

  // Pharmacy
  if (content.pharmacy) {
    emailBody += `Preferred Pharmacy: ${content.pharmacy.name}, ${content.pharmacy.address1} ${content.pharmacy.city}, ${content.pharmacy.state}, ${content.pharmacy.zip}\n`;
    emailBody += `Phone: ${content.pharmacy.phone}, Website: ${content.pharmacy.website}\n\n`;
  }

  // Blood Type
  if (content.bloodType) {
    emailBody += `Blood Type: ${content.bloodType}\n\n`;
  }

  // Organ donation
  if (content.organDonation) {
    emailBody += `Organ Donation: YES\nDetails: ${content.organDonationDetails}\n\n`;
  } else {
    emailBody += 'Organ Donation: NO\n\n';
  }

  // Directives / wishes
  if (content.medicalDirective) {
    emailBody += `Medical Directive: ${content.medicalDirective}\n`;
  }
  if (content.coreWishes) {
    emailBody += `Core Wishes: ${content.coreWishes}\n`;
  }
  if (content.dignityMeaning) {
    emailBody += `Dignity Meaning: ${content.dignityMeaning}\n`;
  }
  if (content.noMedical) {
    emailBody += `No Medical Instructions: ${content.noMedical}\n`;
  }
  if (content.foodPreferences) {
    emailBody += `Food Preferences: ${content.foodPreferences}\n`;
  }
  if (content.additionalMedical) {
    emailBody += `Additional Medical Notes: ${content.additionalMedical}\n`;
  }

  return emailBody.trim();
};
