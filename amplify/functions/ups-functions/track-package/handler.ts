// @ts-nocheck

import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../../data/resource';
import axios from 'axios';
import { env } from '$amplify/env/trackUPSPackage';

// UPS API Endpoints
const UPS_ENDPOINTS = {
  OAUTH: '/security/v1/oauth/token',
  TRACK: '/api/track/v1/details',
  // Alternative tracking endpoint if needed
  TRACK_ALT: '/track/v1/details',
} as const;

interface TrackingStatus {
  trackingNumber: string;
  status:
    | 'label_created'
    | 'pending'
    | 'in_transit'
    | 'delivered'
    | 'exception'
    | 'unknown';
  statusDescription: string;
  lastUpdate: string;
  currentLocation?: string;
  estimatedDelivery?: string;
  activities?: Array<{
    date: string;
    time: string;
    location: string;
    status: string;
    description: string;
  }>;
}

// Get UPS configuration from environment variables
function getUPSConfig() {
  let clientSecret = env.UPS_CLIENT_SECRET || '';

  const config = {
    clientId: process.env.UPS_CLIENT_ID || '',
    clientSecret,
    accountNumber: process.env.UPS_ACCOUNT_NUMBER || '',
    baseUrl: process.env.UPS_BASE_URL || '',
  };

  if (!config.clientId || !config.clientSecret || !config.accountNumber) {
    throw new Error('UPS credentials not configured properly');
  }

  return config;
}

// UPS authentication function
async function getUPSAccessToken(): Promise<string> {
  console.log('🔐 Starting UPS Authentication for tracking...');
  const config = getUPSConfig();

  const authString = Buffer.from(
    `${config.clientId}:${config.clientSecret}`
  ).toString('base64');

  try {
    console.log(
      '🔧 Track Auth URL:',
      `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`
    );
    console.log('🔧 Client ID length:', config.clientId.length);
    console.log('🔧 Client Secret length:', config.clientSecret.length);

    const response = await axios.post(
      `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`,
      'grant_type=client_credentials',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${authString}`,
        },
      }
    );

    console.log('✅ UPS Authentication successful for tracking');
    console.log('🔧 Token type:', response.data.token_type);
    console.log('🔧 Expires in:', response.data.expires_in);
    return response.data.access_token;
  } catch (error: any) {
    console.error('❌ UPS tracking authentication failed:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`,
      clientIdPresent: !!config.clientId,
      clientSecretPresent: !!config.clientSecret,
    });
    throw new Error('Failed to authenticate with UPS API for tracking');
  }
}

// Parse UPS tracking response to simplified format
function parseTrackingResponse(upsResponse: any): TrackingStatus {
  console.log('🔍 Parsing UPS tracking response');
  console.log('🔧 Raw UPS response:', JSON.stringify(upsResponse, null, 2));

  const trackResponse = upsResponse?.trackResponse;
  const shipment = trackResponse?.shipment?.[0];
  const pkg = shipment?.package?.[0];

  if (!pkg) {
    throw new Error('No package information found in tracking response');
  }

  const trackingNumber = pkg.trackingNumber;
  const currentStatus = pkg.currentStatus;
  const activities = pkg.activity || [];

  console.log('🔧 Current status from UPS:', {
    statusCode: currentStatus?.statusCode,
    description: currentStatus?.description,
    simplifiedTextDescription: currentStatus?.simplifiedTextDescription,
    type: currentStatus?.type,
  });

  // Map UPS status to our simplified status
  let status: TrackingStatus['status'] = 'unknown';
  let statusDescription = 'Unknown';

  if (currentStatus) {
    const statusCode = currentStatus.statusCode;
    const description = currentStatus.description || '';
    const simplifiedDescription = currentStatus.simplifiedTextDescription || '';
    statusDescription = description || simplifiedDescription || 'Unknown';

    console.log(
      '🔧 Mapping status code:',
      statusCode,
      'description:',
      description
    );

    // Map UPS status codes based on official UPS documentation
    // Reference: https://developer.ups.com/api/reference/track/product-info
    if (statusCode === 'D' || description.toLowerCase().includes('delivered')) {
      status = 'delivered';
    } else if (
      statusCode === 'X' ||
      description.toLowerCase().includes('exception')
    ) {
      status = 'exception';
    } else if (
      // Label created / manifest statuses
      statusCode === 'MP' ||
      statusCode === 'M' ||
      description.toLowerCase().includes('label created') ||
      description.toLowerCase().includes('shipment ready for ups') ||
      description.toLowerCase().includes('billing information received') ||
      description.toLowerCase().includes('order processed') ||
      simplifiedDescription.toLowerCase().includes('label created')
    ) {
      status = 'label_created';
    } else if (
      // Pre-pickup statuses
      statusCode === 'P' ||
      description.toLowerCase().includes('pickup') ||
      description.toLowerCase().includes('ready for pickup') ||
      description.toLowerCase().includes('scheduled for pickup')
    ) {
      status = 'pending';
    } else if (
      // In transit statuses
      statusCode === 'I' ||
      statusCode === 'T' ||
      description.toLowerCase().includes('in transit') ||
      description.toLowerCase().includes('on the way') ||
      description.toLowerCase().includes('departed') ||
      description.toLowerCase().includes('arrived') ||
      description.toLowerCase().includes('origin scan') ||
      description.toLowerCase().includes('destination scan')
    ) {
      status = 'in_transit';
    } else {
      // For unknown status codes, be more conservative
      console.log('⚠️ Unknown status code, defaulting to unknown:', statusCode);
      status = 'unknown';
    }

    console.log('🔧 Mapped status:', status);
  }

  // Get current location from latest activity
  let currentLocation = '';
  if (activities.length > 0) {
    const latestActivity = activities[0];
    if (latestActivity.location?.address) {
      const addr = latestActivity.location.address;
      const locationParts = [];

      if (addr.city && addr.city.trim() && addr.city !== '003') {
        locationParts.push(addr.city.trim());
      }
      if (addr.stateProvinceCode && addr.stateProvinceCode.trim()) {
        locationParts.push(addr.stateProvinceCode.trim());
      }
      if (addr.countryCode && addr.countryCode.trim()) {
        locationParts.push(addr.countryCode.trim());
      }

      currentLocation = locationParts.join(', ');
    }
  }

  console.log('🔧 Current location parsed:', currentLocation);

  // Parse activities
  const parsedActivities = activities.map((activity: any) => {
    let activityLocation = '';
    if (activity.location?.address) {
      const addr = activity.location.address;
      const locationParts = [];

      if (addr.city && addr.city.trim() && addr.city !== '003') {
        locationParts.push(addr.city.trim());
      }
      if (addr.stateProvinceCode && addr.stateProvinceCode.trim()) {
        locationParts.push(addr.stateProvinceCode.trim());
      }
      if (addr.countryCode && addr.countryCode.trim()) {
        locationParts.push(addr.countryCode.trim());
      }

      activityLocation = locationParts.join(', ');
    }

    return {
      date: activity.date || '',
      time: activity.time || '',
      location: activityLocation,
      status: activity.status?.statusCode || '',
      description: activity.status?.description || activity.status?.type || '',
    };
  });

  return {
    trackingNumber,
    status,
    statusDescription,
    lastUpdate: new Date().toISOString(),
    currentLocation,
    estimatedDelivery: shipment?.deliveryDate?.[0]?.date,
    activities: parsedActivities,
  };
}

export const handler: Schema['trackUPSPackage']['functionHandler'] =
  async event => {
    const requestId = Date.now().toString();

    try {
      console.log(`🔍 [${requestId}] UPS Track Package Lambda called`);

      const { trackingNumber } = event.arguments;

      console.log(`📋 [${requestId}] Tracking request:`, {
        originalTrackingNumber: trackingNumber,
      });

      // Validate tracking number format
      if (!trackingNumber || trackingNumber.length < 10) {
        console.log(`❌ [${requestId}] Invalid tracking number format:`, {
          trackingNumber,
          length: trackingNumber?.length,
        });
        return JSON.stringify({
          success: false,
          error: 'Invalid tracking number format',
        });
      }

      // Clean tracking number (remove spaces and special characters)
      const cleanTrackingNumber = trackingNumber
        .replace(/[^A-Z0-9]/gi, '')
        .toUpperCase();

      console.log(`🧹 [${requestId}] Cleaned tracking number:`, {
        original: trackingNumber,
        cleaned: cleanTrackingNumber,
      });

      console.log(`🚚 [${requestId}] Calling UPS tracking API...`);

      const config = getUPSConfig();
      const accessToken = await getUPSAccessToken();

      const trackingUrl = `${config.baseUrl}${UPS_ENDPOINTS.TRACK}/${cleanTrackingNumber}`;
      console.log(`🔧 [${requestId}] Tracking URL:`, trackingUrl);
      console.log(
        `🔧 [${requestId}] Clean tracking number:`,
        cleanTrackingNumber
      );

      let response;
      try {
        response = await axios.get(trackingUrl, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            transId: Date.now().toString(),
            transactionSrc: 'childfree-trust',
          },
          params: {
            locale: 'en_US',
            returnSignature: 'false',
            returnMilestones: 'false',
          },
        });
      } catch (axiosError: any) {
        console.error(`❌ [${requestId}] UPS tracking API request failed:`, {
          message: axiosError.message,
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          url: trackingUrl,
          trackingNumber: cleanTrackingNumber,
          fullUrl: `${trackingUrl}?locale=en_US&returnSignature=false&returnMilestones=false`,
        });

        // Log the detailed error response if available
        if (axiosError.response?.data?.response?.errors) {
          console.error(
            '🔍 UPS API Errors:',
            JSON.stringify(axiosError.response.data.response.errors, null, 2)
          );
        }

        throw axiosError;
      }

      console.log(
        `📥 [${requestId}] UPS tracking response received, parsing...`
      );
      console.log(`🔧 [${requestId}] Response status:`, response.status);
      console.log(
        `🔧 [${requestId}] Response data:`,
        JSON.stringify(response.data, null, 2)
      );

      // Parse response to simplified format
      const trackingStatus = parseTrackingResponse(response.data);

      console.log(`✅ [${requestId}] UPS Tracking retrieved successfully:`, {
        trackingNumber: cleanTrackingNumber,
        status: trackingStatus.status,
        statusDescription: trackingStatus.statusDescription,
        lastUpdate: trackingStatus.lastUpdate,
        currentLocation: trackingStatus.currentLocation,
        activitiesCount: trackingStatus.activities?.length || 0,
      });

      return JSON.stringify({
        success: true,
        data: trackingStatus,
      });
    } catch (error) {
      console.error(`❌ [${requestId}] Track UPS package error:`, {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
      });

      // Handle specific UPS API errors
      if (error instanceof Error) {
        if (error.message.includes('Authentication')) {
          console.log(`🔐 [${requestId}] Authentication error detected`);
          return JSON.stringify({
            success: false,
            error: 'UPS API authentication failed',
          });
        }

        if (
          error.message.includes('not found') ||
          error.message.includes('404')
        ) {
          console.log(`📍 [${requestId}] Tracking number not found`);
          return JSON.stringify({
            success: false,
            error: 'Tracking number not found',
          });
        }
      }

      console.log(`💥 [${requestId}] Returning generic error response`);

      return JSON.stringify({
        success: false,
        error: 'Failed to track package',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  };
