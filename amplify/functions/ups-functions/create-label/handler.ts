// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../../data/resource';
import axios from 'axios';
import { env } from '$amplify/env/createUPSLabel';

// UPS API Types
interface UPSAddress {
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
  city: string;
  stateProvinceCode: string;
  postalCode: string;
  countryCode: string;
}

interface CreateLabelRequest {
  fromAddress: UPSAddress & { name: string; phone?: string };
  toAddress: UPSAddress & { name: string; phone?: string };
  serviceCode?: string;
  packageOptions?: {
    description?: string;
    weight?: string;
    dimensions?: {
      length?: string;
      width?: string;
      height?: string;
    };
  };
}

interface ShippingLabelResult {
  trackingNumber: string;
  labelUrl: string;
  cost: {
    amount: string;
    currency: string;
  };
  createdAt: string;
  estimatedDelivery?: string;
}

// UPS API Endpoints
const UPS_ENDPOINTS = {
  OAUTH: '/security/v1/oauth/token',
  SHIP: '/api/shipments/v1/ship',
} as const;

// UPS Service Codes
const UPS_SERVICE_CODES = {
  GROUND: '03',
  NEXT_DAY_AIR: '01',
  NEXT_DAY_AIR_SAVER: '13',
  NEXT_DAY_AIR_EARLY: '14',
  SECOND_DAY_AIR: '02',
  SECOND_DAY_AIR_AM: '59',
  THREE_DAY_SELECT: '12',
  STANDARD: '11',
} as const;

// UPS Package Types
const UPS_PACKAGE_TYPES = {
  CUSTOMER_SUPPLIED: '02',
  UPS_LETTER: '01',
  UPS_TUBE: '03',
  UPS_PAK: '04',
  UPS_EXPRESS_BOX: '21',
  UPS_25KG_BOX: '24',
  UPS_10KG_BOX: '25',
} as const;

// Default package dimensions
const DEFAULT_PACKAGE_DIMENSIONS = {
  length: '12',
  width: '9',
  height: '3',
  weight: '1',
} as const;

// Get UPS configuration from environment variables
function getUPSConfig() {
  let clientSecret = env.UPS_CLIENT_SECRET || '';

  const config = {
    clientId: process.env.UPS_CLIENT_ID || '',
    clientSecret,
    accountNumber: process.env.UPS_ACCOUNT_NUMBER || '',
    baseUrl: process.env.UPS_BASE_URL || '',
  };

  if (!config.clientId || !config.clientSecret || !config.accountNumber) {
    throw new Error('UPS credentials not configured properly');
  }

  return config;
}

// UPS authentication function
async function getUPSAccessToken(): Promise<string> {
  console.log('🔐 Starting UPS Authentication...');
  const config = getUPSConfig();

  const authString = Buffer.from(
    `${config.clientId}:${config.clientSecret}`
  ).toString('base64');

  try {
    console.log('🔧 Auth URL:', `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`);
    console.log('🔧 Client ID length:', config.clientId.length);
    console.log('🔧 Client Secret length:', config.clientSecret.length);

    const response = await axios.post(
      `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`,
      'grant_type=client_credentials',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${authString}`,
        },
      }
    );

    console.log('✅ UPS Authentication successful');
    console.log('🔧 Token type:', response.data.token_type);
    console.log('🔧 Expires in:', response.data.expires_in);
    return response.data.access_token;
  } catch (error: any) {
    console.error('❌ UPS authentication failed:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: `${config.baseUrl}${UPS_ENDPOINTS.OAUTH}`,
      clientIdPresent: !!config.clientId,
      clientSecretPresent: !!config.clientSecret,
    });
    throw new Error('Failed to authenticate with UPS API');
  }
}

// Validate UPS address
function validateAddress(address: UPSAddress): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!address.addressLine1?.trim()) errors.push('Address line 1 is required');
  if (!address.city?.trim()) errors.push('City is required');
  if (!address.stateProvinceCode?.trim())
    errors.push('State/Province code is required');
  if (!address.postalCode?.trim()) errors.push('Postal code is required');
  if (!address.countryCode?.trim()) errors.push('Country code is required');

  if (address.addressLine1 && address.addressLine1.length > 35) {
    errors.push('Address line 1 must be 35 characters or less');
  }
  if (address.city && address.city.length > 30) {
    errors.push('City must be 30 characters or less');
  }
  if (address.countryCode && address.countryCode.length !== 2) {
    errors.push('Country code must be exactly 2 characters');
  }

  // Basic US postal code validation
  if (address.countryCode === 'US' && address.postalCode) {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    if (!zipRegex.test(address.postalCode)) {
      errors.push('Invalid US postal code format');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Get service description by code
function getServiceDescription(serviceCode: string): string {
  const descriptions: Record<string, string> = {
    [UPS_SERVICE_CODES.GROUND]: 'UPS Ground',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR]: 'UPS Next Day Air',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_SAVER]: 'UPS Next Day Air Saver',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_EARLY]: 'UPS Next Day Air Early',
    [UPS_SERVICE_CODES.SECOND_DAY_AIR]: 'UPS 2nd Day Air',
    [UPS_SERVICE_CODES.SECOND_DAY_AIR_AM]: 'UPS 2nd Day Air AM',
    [UPS_SERVICE_CODES.THREE_DAY_SELECT]: 'UPS 3 Day Select',
    [UPS_SERVICE_CODES.STANDARD]: 'UPS Standard',
  };

  return descriptions[serviceCode] || 'UPS Service';
}

// Create UPS shipment request from simplified input
function createShipmentRequest(
  fromAddress: CreateLabelRequest['fromAddress'],
  toAddress: CreateLabelRequest['toAddress'],
  serviceCode: string = UPS_SERVICE_CODES.GROUND,
  packageOptions?: CreateLabelRequest['packageOptions']
) {
  const shipper = {
    name: fromAddress.name,
    attentionName: fromAddress.name,
    phone: fromAddress.phone,
    address: {
      addressLine1: fromAddress.addressLine1,
      addressLine2: fromAddress.addressLine2,
      addressLine3: fromAddress.addressLine3,
      city: fromAddress.city,
      stateProvinceCode: fromAddress.stateProvinceCode,
      postalCode: fromAddress.postalCode,
      countryCode: fromAddress.countryCode,
    },
  };

  const shipTo = {
    name: toAddress.name,
    attentionName: toAddress.name,
    phone: toAddress.phone,
    address: {
      addressLine1: toAddress.addressLine1,
      addressLine2: toAddress.addressLine2,
      addressLine3: toAddress.addressLine3,
      city: toAddress.city,
      stateProvinceCode: toAddress.stateProvinceCode,
      postalCode: toAddress.postalCode,
      countryCode: toAddress.countryCode,
    },
  };

  const defaultPackage = {
    description: 'Legal Documents',
    packaging: {
      code: UPS_PACKAGE_TYPES.CUSTOMER_SUPPLIED,
      description: 'Customer Supplied Package',
    },
    dimensions: {
      unitOfMeasurement: {
        code: 'IN',
        description: 'Inches',
      },
      length: DEFAULT_PACKAGE_DIMENSIONS.length,
      width: DEFAULT_PACKAGE_DIMENSIONS.width,
      height: DEFAULT_PACKAGE_DIMENSIONS.height,
    },
    packageWeight: {
      unitOfMeasurement: {
        code: 'LBS',
        description: 'Pounds',
      },
      weight: DEFAULT_PACKAGE_DIMENSIONS.weight,
    },
  };

  const packageInfo = packageOptions
    ? {
        ...defaultPackage,
        description: packageOptions.description || defaultPackage.description,
        dimensions: packageOptions.dimensions
          ? {
              ...defaultPackage.dimensions,
              length:
                packageOptions.dimensions.length ||
                defaultPackage.dimensions.length,
              width:
                packageOptions.dimensions.width ||
                defaultPackage.dimensions.width,
              height:
                packageOptions.dimensions.height ||
                defaultPackage.dimensions.height,
            }
          : defaultPackage.dimensions,
        packageWeight: packageOptions.weight
          ? {
              ...defaultPackage.packageWeight,
              weight: packageOptions.weight,
            }
          : defaultPackage.packageWeight,
      }
    : defaultPackage;

  return {
    shipper,
    shipTo,
    service: {
      code: serviceCode,
      description: getServiceDescription(serviceCode),
    },
    package: [packageInfo],
    labelImageFormat: {
      code: 'GIF',
      description: 'GIF',
    },
    labelStockSize: {
      height: '6',
      width: '4',
    },
  };
}

// UPS shipment creation function
async function createUPSShipment(shipmentRequest: any) {
  console.log('📦 Creating UPS Shipment...');

  const config = getUPSConfig();
  const accessToken = await getUPSAccessToken();

  const requestData = {
    ShipmentRequest: {
      Request: {
        SubVersion: '1801',
        RequestOption: 'nonvalidate',
      },
      Shipment: {
        Description: 'Legal Documents Shipment',
        Shipper: {
          Name: shipmentRequest.shipper.name,
          AttentionName:
            shipmentRequest.shipper.attentionName ||
            shipmentRequest.shipper.name,
          Phone: {
            Number: shipmentRequest.shipper.phone || '',
          },
          ShipperNumber: config.accountNumber,
          Address: {
            AddressLine: [
              shipmentRequest.shipper.address.addressLine1,
              shipmentRequest.shipper.address.addressLine2,
              shipmentRequest.shipper.address.addressLine3,
            ].filter(Boolean),
            City: shipmentRequest.shipper.address.city,
            StateProvinceCode:
              shipmentRequest.shipper.address.stateProvinceCode,
            PostalCode: shipmentRequest.shipper.address.postalCode,
            CountryCode: shipmentRequest.shipper.address.countryCode,
          },
        },
        ShipTo: {
          Name: shipmentRequest.shipTo.name,
          AttentionName:
            shipmentRequest.shipTo.attentionName || shipmentRequest.shipTo.name,
          Phone: {
            Number: shipmentRequest.shipTo.phone || '',
          },
          Address: {
            AddressLine: [
              shipmentRequest.shipTo.address.addressLine1,
              shipmentRequest.shipTo.address.addressLine2,
              shipmentRequest.shipTo.address.addressLine3,
            ].filter(Boolean),
            City: shipmentRequest.shipTo.address.city,
            StateProvinceCode: shipmentRequest.shipTo.address.stateProvinceCode,
            PostalCode: shipmentRequest.shipTo.address.postalCode,
            CountryCode: shipmentRequest.shipTo.address.countryCode,
          },
        },
        Service: {
          Code: shipmentRequest.service.code,
          Description: shipmentRequest.service.description || 'UPS Service',
        },
        Package: shipmentRequest.package.map((pkg: any) => ({
          Description: pkg.description || 'Legal Documents',
          Packaging: {
            Code: pkg.packaging.code,
            Description:
              pkg.packaging.description || 'Customer Supplied Package',
          },
          Dimensions: {
            UnitOfMeasurement: {
              Code: pkg.dimensions.unitOfMeasurement.code,
              Description:
                pkg.dimensions.unitOfMeasurement.description || 'Inches',
            },
            Length: pkg.dimensions.length,
            Width: pkg.dimensions.width,
            Height: pkg.dimensions.height,
          },
          PackageWeight: {
            UnitOfMeasurement: {
              Code: pkg.packageWeight.unitOfMeasurement.code,
              Description:
                pkg.packageWeight.unitOfMeasurement.description || 'Pounds',
            },
            Weight: pkg.packageWeight.weight,
          },
        })),
        PaymentInformation: {
          ShipmentCharge: {
            Type: '01', // Transportation charges
            BillShipper: {
              AccountNumber: config.accountNumber,
            },
          },
        },
      },
      LabelSpecification: {
        LabelImageFormat: {
          Code: 'GIF',
          Description: 'GIF',
        },
        HTTPUserAgent: 'Mozilla/4.0',
        LabelStockSize: {
          Height: '6',
          Width: '4',
        },
      },
    },
  };

  try {
    const response = await axios.post(
      `${config.baseUrl}${UPS_ENDPOINTS.SHIP}?additionaladdressvalidation=string`,
      requestData,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          transId: Date.now().toString(),
          transactionSrc: 'childfree-trust',
        },
      }
    );

    console.log('📥 UPS Shipment Response received');
    return response.data;
  } catch (error: any) {
    console.error('❌ UPS Shipment Error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });

    if (error.response?.data?.response?.errors) {
      console.error(
        '🔍 UPS API Errors:',
        JSON.stringify(error.response.data.response.errors, null, 2)
      );
    }

    throw error;
  }
}

// Parse UPS shipment response to simplified format
function parseShipmentResponse(upsResponse: any): ShippingLabelResult {
  console.log('🔍 Parsing UPS response');
  console.log('🔧 Full UPS Response:', JSON.stringify(upsResponse, null, 2));

  // UPS returns ShipmentResponse.ShipmentResults (note the capitalization)
  const shipmentResults = upsResponse?.ShipmentResponse?.ShipmentResults;
  console.log('🔧 Shipment Results:', JSON.stringify(shipmentResults, null, 2));

  // PackageResults is not an array, it's a single object
  const packageResults = shipmentResults?.PackageResults;
  console.log('🔧 Package Results:', JSON.stringify(packageResults, null, 2));

  const trackingNumber = packageResults?.TrackingNumber;
  const labelImage = packageResults?.ShippingLabel?.GraphicImage;
  const totalCharges = shipmentResults?.ShipmentCharges?.TotalCharges;

  console.log('🔧 Extracted values:', {
    trackingNumber,
    hasLabelImage: !!labelImage,
    labelImageLength: labelImage?.length || 0,
    totalCharges,
  });

  if (!trackingNumber) {
    console.error('❌ No tracking number found in response');
    console.error('🔧 Available package results:', packageResults);
    throw new Error('No tracking number in UPS response');
  }

  if (!labelImage) {
    console.error('❌ No label image found in response');
    console.error(
      '🔧 Available shipping label:',
      packageResults?.ShippingLabel
    );
    throw new Error('No label image in UPS response');
  }

  return {
    trackingNumber,
    labelUrl: `data:image/gif;base64,${labelImage}`,
    cost: {
      amount: totalCharges?.MonetaryValue || '0.00',
      currency: totalCharges?.CurrencyCode || 'USD',
    },
    createdAt: new Date().toISOString(),
  };
}

export const handler: Schema['createUPSLabel']['functionHandler'] =
  async event => {
    const requestId = Date.now().toString();

    try {
      console.log(`🚀 [${requestId}] UPS Create Label Lambda called`);

      const {
        fromAddress,
        toAddress,
        serviceCode = '03', // Default to UPS Ground
        packageOptions,
      } = event.arguments as CreateLabelRequest;

      // Validate required fields
      if (!fromAddress || !toAddress) {
        console.log(`❌ [${requestId}] Validation failed: Missing addresses`);
        return JSON.stringify({
          success: false,
          error: 'From address and to address are required',
        });
      }

      // Validate addresses
      console.log(`🔍 [${requestId}] Validating addresses...`);

      const fromValidation = validateAddress(fromAddress);
      if (!fromValidation.isValid) {
        console.log(
          `❌ [${requestId}] From address validation failed:`,
          fromValidation.errors
        );
        return JSON.stringify({
          success: false,
          error: 'Invalid from address',
          details: fromValidation.errors,
        });
      }

      const toValidation = validateAddress(toAddress);
      if (!toValidation.isValid) {
        console.log(
          `❌ [${requestId}] To address validation failed:`,
          toValidation.errors
        );
        return JSON.stringify({
          success: false,
          error: 'Invalid to address',
          details: toValidation.errors,
        });
      }

      console.log(`✅ [${requestId}] Address validation passed`);

      // Create shipment request
      const packageOptionsFormatted = packageOptions
        ? {
            description: packageOptions.description || 'Legal Documents',
            weight: packageOptions.weight || '1',
            dimensions: packageOptions.dimensions
              ? {
                  length: packageOptions.dimensions.length || '12',
                  width: packageOptions.dimensions.width || '9',
                  height: packageOptions.dimensions.height || '3',
                }
              : undefined,
          }
        : undefined;

      const shipmentRequest = createShipmentRequest(
        fromAddress,
        toAddress,
        serviceCode,
        packageOptionsFormatted
      );

      console.log(`🚚 [${requestId}] Calling UPS API to create shipment...`);

      // Create shipment with UPS
      const upsResponse = await createUPSShipment(shipmentRequest);

      console.log(`📥 [${requestId}] UPS API response received, parsing...`);

      // Parse response to simplified format
      const labelResult = parseShipmentResponse(upsResponse);

      console.log(`✅ [${requestId}] UPS Label created successfully:`, {
        trackingNumber: labelResult.trackingNumber,
        cost: labelResult.cost,
        labelUrlLength: labelResult.labelUrl?.length || 0,
      });

      return JSON.stringify({
        success: true,
        data: labelResult,
        environment: {
          environment:
            process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
          baseUrl: process.env.UPS_BASE_URL,
        },
      });
    } catch (error) {
      console.error(`❌ [${requestId}] Create UPS label error:`, {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
      });

      // Handle specific UPS API errors
      if (error instanceof Error) {
        if (error.message.includes('Authentication')) {
          console.log(`🔐 [${requestId}] Authentication error detected`);
          return JSON.stringify({
            success: false,
            error: 'UPS API authentication failed',
          });
        }

        if (error.message.includes('Address')) {
          console.log(`📍 [${requestId}] Address validation error detected`);
          return JSON.stringify({
            success: false,
            error: 'Invalid address information',
            details: error.message,
          });
        }
      }

      console.log(`💥 [${requestId}] Returning generic error response`);

      return JSON.stringify({
        success: false,
        error: 'Failed to create shipping label',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  };
