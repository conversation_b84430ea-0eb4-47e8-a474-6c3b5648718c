// @ts-nocheck

import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

const AWS_REGION = process.env.AWS_REGION || 'no-region';

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/initiatePhoneVerification';
import { CognitoIdentityProviderClient } from '@aws-sdk/client-cognito-identity-provider';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

const cognitoClient = new CognitoIdentityProviderClient({
  region: AWS_REGION,
});

const snsClient = new SNSClient({
  region: AWS_REGION,
});

// Generate a 6-digit verification code
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export const handler: Schema['initiatePhoneVerification']['functionHandler'] =
  async event => {
    console.log(
      '===> Initiate phone verification event:',
      JSON.stringify(event, null, 2)
    );

    const { phoneNumber, email } = event.arguments;
    console.log('===> Extracted parameters:', { phoneNumber, email });

    if (!phoneNumber || !email) {
      console.error('===> Missing required parameters:', {
        phoneNumber: !!phoneNumber,
        email: !!email,
      });
      return {
        success: false,
        error: 'Phone number and email are required',
      };
    }

    try {
      console.log('===> Starting phone verification process...');

      // Validate phone number format (should be in E.164 format)
      const phoneRegex = /^\+1[0-9]{10}$/;
      console.log('===> Phone number validation:', {
        phoneNumber,
        matches: phoneRegex.test(phoneNumber),
      });
      // if (!phoneRegex.test(phoneNumber)) {
      //   return {
      //     success: false,
      //     error: 'Phone number must be in format +1XXXXXXXXXX',
      //   };
      // }

      // Generate verification code
      const verificationCode = generateVerificationCode();
      console.log(
        '===> Generated verification code for user:',
        email,
        'Code length:',
        verificationCode.length
      );

      // Store the verification code in DynamoDB (using VerificationTokens table)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 10); // 10 minutes expiry
      console.log('===> Token expiry time:', expiresAt.toISOString());

      const tokenData = {
        email: email, // Store user's email
        token: verificationCode,
        expiresAt: expiresAt.toISOString(),
        isUsed: false,
        createdAt: new Date().toISOString(),
        verificationType: 'phoneVerification',
      };
      console.log('===> Creating verification token:', tokenData);

      const createResult =
        await client.models.VerificationTokens.create(tokenData);
      console.log('===> Token creation result:', createResult);

      // Send SMS using SNS
      const message = `Your Childfree verification code is: ${verificationCode}. This code will expire in 10 minutes.`;
      console.log('===> Preparing to send SMS:', {
        phoneNumber,
        messageLength: message.length,
      });

      const publishCommand = new PublishCommand({
        PhoneNumber: phoneNumber,
        Message: message,
      });
      console.log('===> SNS Publish command created');

      const smsResult = await snsClient.send(publishCommand);
      console.log(
        '===> SMS sent successfully to:',
        phoneNumber,
        'MessageId:',
        smsResult.MessageId
      );

      console.log('===> Phone verification initiated successfully');
      return {
        success: true,
        message: 'Verification code sent successfully',
      };
    } catch (error) {
      console.error('===> Error initiating phone verification:', error);
      console.error('===> Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      return {
        success: false,
        error: 'Failed to send verification code. Please try again.',
      };
    }
  };
