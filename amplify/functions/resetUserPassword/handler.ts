// @ts-nocheck

import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../data/resource';
import { generateClient } from 'aws-amplify/data';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { env } from '$amplify/env/resetUserPassword';
import {
  AdminSetUserPasswordCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'no region',
});

export const handler: Schema['resetUserPassword']['functionHandler'] =
  async event => {
    console.log(
      '===> RESET USER PASSWORD EVENT',
      JSON.stringify(event, null, 2)
    );

    try {
      const { email, token, newPassword } = event.arguments;

      if (!token || !email || !newPassword) {
        console.error('RESET PASSWORD: NO REQUIRED DATA');
        throw new Error('No required data');
      }

      let isValid: boolean;

      try {
        const { data } = await client.queries.checkIsValid({ token });
        console.log('RESET PASSWORD: checkIsValid RESULT PARAMS:', data);
        isValid = Boolean(data);
      } catch (e) {
        isValid = false;
        throw new Error('Failed to validate token');
      }

      if (!isValid) {
        throw new Error('Invalid token');
      }

      try {
        await cognitoClient.send(
          new AdminSetUserPasswordCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: email,
            Password: newPassword,
            Permanent: true,
          })
        );
      } catch (error) {
        console.error('Error changing password in Cognito:', error);
        if (
          error instanceof Error &&
          error.name === 'PasswordHistoryPolicyViolationException'
        ) {
          throw new Error(
            'Password has been used recently. Please choose a different password.'
          );
        } else {
          console.error('Error changing password in Cognito:', error);
          throw new Error('Failed to change password');
        }
      }

      try {
        await client.mutations.markTokenAsUsed({ token });
        console.log('===> RESET PASSWORD: TOKEN MARKED AS USED: ', token);
      } catch (error) {
        console.log('Error marking token as used:', error);
      }

      return { success: true };
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('verifyPromoCode: unexpected error', errorMessage);
      return { success: false, error: errorMessage };
    }
  };
