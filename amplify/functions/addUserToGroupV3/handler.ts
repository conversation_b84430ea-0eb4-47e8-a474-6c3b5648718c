// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/addUserToGroupV3';

import {
  CognitoIdentityProviderClient,
  AdminAddUserToGroupCommand,
  AdminRemoveUserFromGroupCommand,
  AdminListGroupsForUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  // @ts-ignore
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const GROUP_MAPPING = {
  member: {
    cognitoGroup: null,
    databaseRole: 'Member',
  },
  welontrust: {
    cognitoGroup: 'WELONTRUST',
    databaseRole: 'WelonTrust',
  },
  administrator: {
    cognitoGroup: 'ADMINS',
    databaseRole: 'Administrator',
  },
  callcenter: {
    cognitoGroup: 'CALLCENTER',
    databaseRole: 'CallCenter',
  },
  medicalreview: {
    cognitoGroup: 'MEDICALREVIEW',
    databaseRole: 'MedicalReview',
  },
} as const;

// Simple helper to build consistent JSON responses
const respond = (
  success: boolean,
  message?: string,
  extras?: Record<string, any>
) => {
  return JSON.stringify({ success, message, ...(extras || {}) });
};

type GroupName = keyof typeof GROUP_MAPPING;

// Helper function to validate inputs
const validateInputs = (userId: string, groupName: string) => {
  if (!userId?.trim()) {
    return 'User ID is required';
  }

  if (!groupName?.trim()) {
    return 'Group name is required';
  }

  if (!Object.keys(GROUP_MAPPING).includes(groupName.toLowerCase())) {
    const validGroups = Object.keys(GROUP_MAPPING).join(', ');
    return `Invalid group name. Valid groups are: ${validGroups}`;
  }

  return null;
};

// Helper function to remove user from all managed Cognito groups
const removeFromAllCognitoGroups = async (userEmail: string) => {
  const allCognitoGroups = [
    'ADMINS',
    'WELONTRUST',
    'CALLCENTER',
    'MEDICALREVIEW',
  ];
  type RemoveResult = {
    group: string;
    action: 'removed';
    success: boolean;
    error?: unknown;
  };
  const results: RemoveResult[] = [];

  // Get current groups first
  let currentGroups: string[] = [];
  try {
    const listGroupsCommand = new AdminListGroupsForUserCommand({
      UserPoolId: env.USER_POOL_ID,
      Username: userEmail,
    });
    const groupsResponse = await cognitoClient.send(listGroupsCommand);
    currentGroups =
      groupsResponse.Groups?.map(group => group.GroupName || '') || [];
    console.log('===> Current Cognito groups:', currentGroups);
  } catch (error) {
    console.warn('===> Warning: Could not fetch current groups:', error);
    // Continue anyway - we'll try to remove from all possible groups
  }

  // Remove from all managed groups
  for (const group of allCognitoGroups) {
    if (currentGroups.length === 0 || currentGroups.includes(group)) {
      try {
        const removeCommand = new AdminRemoveUserFromGroupCommand({
          UserPoolId: env.USER_POOL_ID,
          Username: userEmail,
          GroupName: group,
        });
        await cognitoClient.send(removeCommand);
        console.log(`===> Removed user from Cognito group: ${group}`);
        results.push({ group, action: 'removed', success: true });
      } catch (error) {
        console.error(`===> Error removing user from group ${group}:`, error);
        results.push({ group, action: 'removed', success: false, error });
      }
    }
  }

  return results;
};

// Helper function to add user to a Cognito group
const addToCognitoGroup = async (userEmail: string, groupName: string) => {
  try {
    const addCommand = new AdminAddUserToGroupCommand({
      UserPoolId: env.USER_POOL_ID,
      Username: userEmail,
      GroupName: groupName,
    });
    await cognitoClient.send(addCommand);
    console.log(`===> Added user to Cognito group: ${groupName}`);
    return { success: true };
  } catch (error) {
    console.error(`===> Error adding user to group ${groupName}:`, error);
    return { success: false, error };
  }
};

// Helper function to update database role
const updateDatabaseRole = async (
  userId: string,
  role:
    | 'Member'
    | 'WelonTrust'
    | 'Administrator'
    | 'Professional'
    | 'CallCenter'
    | 'MedicalReview'
    | null
    | undefined
) => {
  try {
    const { errors, data } = await client.models.User.update({
      id: userId,
      role: role,
    });

    if (errors && errors.length > 0) {
      console.error('===> Database update errors:', errors);
      return {
        success: false,
        errors: errors.map(e => e.message || 'Unknown database error'),
      };
    }

    console.log('===> Database role updated successfully:', { userId, role });
    return { success: true, data };
  } catch (error) {
    console.error('===> Database update exception:', error);
    return {
      success: false,
      errors: [
        'Database update failed: ' +
          (error instanceof Error ? error.message : 'Unknown error'),
      ],
    };
  }
};

export const handler: Schema['addUserToGroupV3']['functionHandler'] =
  async event => {
    console.log('===> FULL EVENT:', JSON.stringify(event, null, 2));

    try {
      const { userId, groupName } = event.arguments;
      console.log('===> Processing user group update:', { userId, groupName });

      // Validate inputs
      const validationError = validateInputs(userId, groupName);
      if (validationError) {
        return respond(false, validationError);
      }

      const normalizedGroupName = groupName.toLowerCase() as GroupName;
      const mapping = GROUP_MAPPING[normalizedGroupName];

      // Find user in database
      const { data: users, errors: userFetchErrors } =
        await client.models.User.list({
          filter: { id: { eq: userId } },
        });

      if (userFetchErrors && userFetchErrors.length > 0) {
        console.error('===> Error fetching user:', userFetchErrors);
        return respond(false, 'Error fetching user from database');
      }

      if (!users || users.length === 0) {
        return respond(false, `User with ID ${userId} not found`);
      }

      const user = users[0];
      const userEmail = user.email;

      if (!userEmail) {
        return respond(false, 'User email is required for group management');
      }

      console.log('===> Found user:', {
        id: user.id,
        email: userEmail,
        currentRole: user.role,
        newRole: mapping.databaseRole,
      });

      // Track operations for rollback if needed
      let cognitoOperationSuccess = true;
      let cognitoErrorDetails: string[] = [];

      // Step 1: Remove from all existing groups
      const removeResults = await removeFromAllCognitoGroups(userEmail);
      const removeFailures = removeResults.filter(r => !r.success);
      if (removeFailures.length > 0) {
        cognitoOperationSuccess = false;
        cognitoErrorDetails.push(
          ...removeFailures.map(f => `Failed to remove from ${f.group}`)
        );
      }

      // Step 2: Add to new group (if specified)
      if (mapping.cognitoGroup) {
        const addResult = await addToCognitoGroup(
          userEmail,
          mapping.cognitoGroup
        );
        if (!addResult.success) {
          cognitoOperationSuccess = false;
          cognitoErrorDetails.push(`Failed to add to ${mapping.cognitoGroup}`);
        }
      }

      // Step 3: Update database role
      const dbUpdateResult = await updateDatabaseRole(
        user.id,
        mapping.databaseRole
      );

      // Determine overall success
      if (!cognitoOperationSuccess && !dbUpdateResult.success) {
        return respond(false, 'Both Cognito and database operations failed', {
          cognitoErrors: cognitoErrorDetails,
          databaseErrors: dbUpdateResult.errors,
        });
      } else if (!cognitoOperationSuccess) {
        return respond(
          false,
          'Cognito operations failed, but database was updated',
          {
            cognitoErrors: cognitoErrorDetails,
            warning:
              'User role updated in database but Cognito group membership may be inconsistent',
          }
        );
      } else if (!dbUpdateResult.success) {
        return respond(
          false,
          'Database update failed, but Cognito operations succeeded',
          {
            databaseErrors: dbUpdateResult.errors,
            warning:
              'Cognito group membership updated but database role may be inconsistent',
          }
        );
      }

      // Full success
      return respond(true, `User successfully assigned to ${groupName} group`, {
        previousRole: user.role,
        newRole: mapping.databaseRole,
        cognitoGroup: mapping.cognitoGroup || 'none',
      });
    } catch (error) {
      console.error('===> UNEXPECTED ERROR:', error);
      return respond(
        false,
        'An unexpected error occurred during group assignment'
      );
    }
  };
