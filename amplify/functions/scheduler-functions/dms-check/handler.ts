import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { generateClient } from 'aws-amplify/api';
import type { Schema } from '../../../data/resource';
// @ts-ignore
import { env } from '$amplify/env/dmsCheck';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

// Type for update result tracking
interface UpdateResult {
  userId: string;
  success: boolean;
  error?: string | any;
  welonTrustNotified?: boolean;
}

// Type for user with DMS status tracking
interface UserWithDMSStatus {
  id: string;
  cognitoId: string;
  firstName: string;
  lastName: string;
  email: string;
  assignedWelonTrustId: string | null;
  isDmsCheckSuccessful: boolean;
}

/**
 * Check if a DMS entry is overdue for check-in
 */
const isCheckInOverdue = (nextCheckIn: string | null | undefined): boolean => {
  if (!nextCheckIn) return false;

  const now = new Date();
  const checkInDate = new Date(nextCheckIn);

  return now > checkInDate;
};

/**
 * Check if email notification was already sent for this member and notification type
 */
const checkEmailNotificationSent = async (
  memberUserId: string,
  welonTrustUserId: string,
  notificationType: 'DMS_FAILURE' | 'DMS_RECOVERY'
): Promise<boolean> => {
  try {
    // We use the author field to identify email notifications and message to identify type
    const emailAuthor = `system-dms-email-${notificationType.toLowerCase()}`;

    const { data: existingNotifications, errors } =
      await client.models.Notification.list({
        filter: {
          and: [
            { recipient: { eq: welonTrustUserId } },
            { author: { eq: emailAuthor } },
            { message: { contains: memberUserId } }, // Member ID should be in the message
          ],
        },
      });

    if (errors) {
      console.error('Error checking email notification log:', errors);
      return false; // If we can't check, assume not sent to be safe
    }

    return existingNotifications && existingNotifications.length > 0;
  } catch (error) {
    console.error('Error checking email notification log:', error);
    return false; // If we can't check, assume not sent to be safe
  }
};

/**
 * Send email notification to Welon Trust user about assigned user's DMS status
 */
const sendWelonTrustEmailNotification = async (
  user: UserWithDMSStatus,
  welonTrustUser: any,
  isDmsSuccessful: boolean
): Promise<boolean> => {
  try {
    const notificationType = isDmsSuccessful ? 'DMS_RECOVERY' : 'DMS_FAILURE';

    if (!user.assignedWelonTrustId) {
      console.error(
        `Cannot send notification: user ${user.cognitoId} has no assigned Welon Trust ID`
      );
      return false;
    }

    // Check if email was already sent for this member and notification type
    const alreadySent = await checkEmailNotificationSent(
      user.cognitoId,
      user.assignedWelonTrustId,
      notificationType
    );
    if (alreadySent) {
      console.log(
        `Email notification already sent for member ${user.cognitoId} (${notificationType}). Skipping.`
      );
      return true; // Return true as the notification was already handled
    }

    const subject = isDmsSuccessful
      ? `Member Recovery: ${user.firstName} ${user.lastName} - Dead Man's Switch Active`
      : `Member Alert: ${user.firstName} ${user.lastName} - Dead Man's Switch Requires Attention`;

    const message = isDmsSuccessful
      ? `Dear ${welonTrustUser.firstName} ${welonTrustUser.lastName},

${user.firstName} ${user.lastName} has successfully completed their check-in and their Dead Man's Switch is now active.

This means the member has recovered from their previous missed check-in and their estate planning documents are secure.

Member Details:
- Name: ${user.firstName} ${user.lastName}
- Email: ${user.email}
- Status: Active

No action is required at this time.

Best regards,
ChildFree Trust® Team`
      : `Dear ${welonTrustUser.firstName} ${welonTrustUser.lastName},

${user.firstName} ${user.lastName} has missed their scheduled check-in. Their Dead Man's Switch status requires attention.

This notification is sent to inform you that the member you are assigned to monitor has not completed their required check-in within the specified timeframe.

Member Details:
- Name: ${user.firstName} ${user.lastName}
- Email: ${user.email}
- Status: Requires Attention

Please review the member's status and take appropriate action as needed.

Best regards,
ChildFree Trust® Team`;

    // Send email notification
    const emailResult = await client.mutations.sendEmail({
      to: welonTrustUser.email,
      subject: subject,
      message: message,
      emailType: 'notification',
    });

    // Parse email result
    let emailSuccess = false;
    let errorMessage = '';

    if (emailResult.data) {
      const emailData =
        typeof emailResult.data === 'string'
          ? JSON.parse(emailResult.data)
          : emailResult.data;
      emailSuccess = emailData.success === true;
      if (!emailSuccess) {
        errorMessage = emailData.error || 'Unknown email error';
      }
    } else if (emailResult.errors) {
      errorMessage = JSON.stringify(emailResult.errors);
    }

    // Log the email notification attempt using Notification model
    // Log the email notification attempt using Notification model
    if (emailSuccess) {
      // Add null check for assignedWelonTrustId
      if (!user.assignedWelonTrustId) {
        console.error(
          `Cannot log notification: user ${user.cognitoId} has no assigned Welon Trust ID`
        );
        return emailSuccess;
      }

      const emailAuthor = `system-dms-email-${notificationType.toLowerCase()}`;
      const logMessage = `EMAIL_SENT: ${notificationType} for member ${user.cognitoId} (${user.firstName} ${user.lastName}) - Subject: ${subject}`;

      await client.models.Notification.create({
        message: logMessage,
        recipient: user.assignedWelonTrustId,
        author: emailAuthor,
        createdAt: new Date().toISOString(),
        isRead: true, // Mark as read since it's just a log entry
      });
    }

    if (emailSuccess) {
      console.log(
        `Successfully sent email notification to Welon Trust user ${user.assignedWelonTrustId} about member ${user.cognitoId}`
      );
    } else {
      console.error(
        `Failed to send email notification to Welon Trust user ${user.assignedWelonTrustId}:`,
        errorMessage
      );
    }

    return emailSuccess;
  } catch (error) {
    console.error(
      `Error sending email notification for member ${user.cognitoId}:`,
      error
    );

    // Log error in console only - no need to create notification for failed attempts

    return false;
  }
};

/**
 * Create notification for Welon Trust user about assigned user's DMS status
 */
const createWelonTrustNotification = async (
  user: UserWithDMSStatus,
  isDmsSuccessful: boolean
): Promise<boolean> => {
  try {
    if (!user.assignedWelonTrustId) {
      console.log(`User ${user.cognitoId} has no assigned Welon Trust user`);
      return false;
    }

    const message = isDmsSuccessful
      ? `${user.firstName} ${user.lastName} has successfully completed their check-in and their Dead Man's Switch is now active.`
      : `${user.firstName} ${user.lastName} has missed their scheduled check-in. Their Dead Man's Switch status requires attention.`;

    // Create in-app notification
    const { data: notification, errors } =
      await client.models.Notification.create({
        message,
        recipient: user.assignedWelonTrustId,
        author: 'system-dms-check',
        createdAt: new Date().toISOString(),
        isRead: false,
      });

    if (errors) {
      console.error(
        `Failed to create notification for Welon Trust user ${user.assignedWelonTrustId}:`,
        errors
      );
      return false;
    }

    console.log(
      `Successfully created in-app notification for Welon Trust user ${user.assignedWelonTrustId} about user ${user.cognitoId}`
    );

    // Get Welon Trust user details for email notification
    const { data: welonTrustUsers, errors: welonTrustErrors } =
      await client.models.User.listUserByCognitoId(
        { cognitoId: user.assignedWelonTrustId },
        {
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'email',
          ] as const,
        }
      );

    if (welonTrustErrors || !welonTrustUsers || welonTrustUsers.length === 0) {
      console.error(
        `Failed to get Welon Trust user details for ${user.assignedWelonTrustId}:`,
        welonTrustErrors
      );
      return true; // In-app notification was successful, email failed
    }

    const welonTrustUser = welonTrustUsers[0];

    // Send email notification
    const emailSent = await sendWelonTrustEmailNotification(
      user,
      welonTrustUser,
      isDmsSuccessful
    );

    return emailSent;
  } catch (error) {
    console.error(
      `Error creating Welon Trust notification for user ${user.cognitoId}:`,
      error
    );
    return false;
  }
};

/**
 * Create in-app notification for the member who missed their check-in
 */
const createMemberMissedCheckInNotification = async (
  user: UserWithDMSStatus
): Promise<boolean> => {
  try {
    const message =
      "You missed your scheduled check-in. Your Dead Man's Switch requires attention. Please log in to complete your check-in.";

    const { data, errors } = await client.models.Notification.create({
      message,
      recipient: user.cognitoId,
      author: 'system-dms-check',
      createdAt: new Date().toISOString(),
      isRead: false,
    });

    if (errors) {
      console.error(
        `Failed to create in-app notification for member ${user.cognitoId}:`,
        errors
      );
      return false;
    }

    console.log(
      `Successfully created in-app notification for member ${user.cognitoId} about missed check-in`
    );

    return true;
  } catch (error) {
    console.error(
      `Error creating in-app notification for member ${user.cognitoId}:`,
      error
    );
    return false;
  }
};

export const handler: EventBridgeHandler<
  'Scheduled Event',
  null,
  void
> = async event => {
  console.log('DMS Check triggered at:', new Date().toISOString());
  console.log('DMS CHECK EVENT:', JSON.stringify(event, null, 2));

  try {
    // Fetch ALL DMS entries without any filters for debugging
    console.log('Fetching ALL DeadMansSwitch entries without filters...');
    const { data: dmsEntries, errors } =
      await client.models.DeadMansSwitch.list({
        filter: {
          status: { eq: 'ACTIVE' },
          isEnable: { eq: true },
        },
      });

    if (errors && errors.length > 0) {
      console.error('Error fetching DMS entries:', errors);
      return;
    }

    console.log(`Found ${dmsEntries?.length || 0} total DMS entries`);

    const now = new Date();
    const overdueUsers: string[] = [];
    const processedEntries: Array<{
      userId: string | null | undefined;
      nextCheckIn: string | null | undefined;
      lastCheckIn: string | null | undefined;
      isOverdue: boolean;
    }> = [];

    // Process each enabled and active DMS entry
    for (const entry of dmsEntries) {
      const { userId, nextCheckIn, lastCheckIn } = entry;

      if (!userId) {
        console.warn('DMS entry missing userId:', entry.id);
        continue;
      }

      const isOverdue = isCheckInOverdue(nextCheckIn);

      processedEntries.push({
        userId,
        nextCheckIn,
        lastCheckIn,
        isOverdue,
      });

      if (isOverdue) {
        overdueUsers.push(userId);
        console.log(
          `User ${userId} is overdue for check-in. Next check-in was: ${nextCheckIn}`
        );
      } else {
        console.log(
          `User ${userId} is up to date. Next check-in: ${nextCheckIn}`
        );
      }
    }

    console.log(
      `Processing complete. ${overdueUsers.length} users are overdue for check-in.`
    );

    // Get all users who are up to date (not overdue) for potential recovery notifications
    const upToDateUsers = processedEntries
      .filter(entry => !entry.isOverdue && entry.userId)
      .map(entry => entry.userId as string);

    console.log(
      `Found ${upToDateUsers.length} users who are up to date for potential recovery notifications.`
    );

    // Update isDmsCheckSuccessful for overdue users
    if (overdueUsers.length > 0) {
      console.log('Updating isDmsCheckSuccessful for overdue users...');

      const updatePromises = overdueUsers.map(
        async (userId): Promise<UpdateResult> => {
          try {
            // First, fetch the user to get their current data including Welon Trust assignment
            const userResponse = await client.models.User.listUserByEmail(
              {
                email: userId,
              },
              {
                selectionSet: [
                  'id',
                  'cognitoId',
                  'firstName',
                  'lastName',
                  'email',
                  'assignedWelonTrustId',
                  'isDmsCheckSuccessful',
                ] as const,
              }
            );

            if (!userResponse.data || userResponse.data.length === 0) {
              console.warn(`User not found for userId: ${userId}`);
              return { userId, success: false, error: 'User not found' };
            }

            const user = userResponse.data[0] as UserWithDMSStatus;
            const previousDmsStatus = user.isDmsCheckSuccessful;

            // Update the user's isDmsCheckSuccessful field
            const updateResult = await client.models.User.update({
              id: user.id,
              isDmsCheckSuccessful: false,
            });

            if (updateResult.data) {
              console.log(
                `Successfully updated isDmsCheckSuccessful for user: ${userId}`
              );

              // Create notifications if status changed from true to false
              let welonTrustNotified = false;
              if (previousDmsStatus === true) {
                // Notify the member about the missed check-in
                await createMemberMissedCheckInNotification(user);

                // Notify the assigned Welon Trust user
                welonTrustNotified = await createWelonTrustNotification(
                  user,
                  false
                );
              }

              return { userId, success: true, welonTrustNotified };
            } else {
              console.error(
                `Failed to update user: ${userId}`,
                updateResult.errors
              );
              return { userId, success: false, error: updateResult.errors };
            }
          } catch (error) {
            console.error(`Error updating user ${userId}:`, error);
            return { userId, success: false, error: (error as Error).message };
          }
        }
      );

      const updateResults = await Promise.all(updatePromises);
      const successfulUpdates = updateResults.filter(result => result.success);
      const failedUpdates = updateResults.filter(result => !result.success);
      const welonTrustNotifications = updateResults.filter(
        result => result.welonTrustNotified
      ).length;

      console.log(
        `User updates complete. ${successfulUpdates.length} successful, ${failedUpdates.length} failed.`
      );
      console.log(`Welon Trust notifications sent: ${welonTrustNotifications}`);

      if (failedUpdates.length > 0) {
        console.error('Failed updates:', failedUpdates);
      }
    }

    // Check for users who have recovered (up to date but previously had failed DMS checks)
    if (upToDateUsers.length > 0) {
      console.log(
        'Checking for users who have recovered from failed DMS checks...'
      );

      const recoveryPromises = upToDateUsers.map(
        async (userId): Promise<UpdateResult> => {
          try {
            // Fetch the user to check their current DMS status
            const userResponse = await client.models.User.listUserByEmail(
              { email: userId },
              {
                selectionSet: [
                  'id',
                  'cognitoId',
                  'firstName',
                  'lastName',
                  'email',
                  'assignedWelonTrustId',
                  'isDmsCheckSuccessful',
                ] as const,
              }
            );

            if (!userResponse.data || userResponse.data.length === 0) {
              console.warn(`User not found for recovery check: ${userId}`);
              return { userId, success: false, error: 'User not found' };
            }

            const user = userResponse.data[0] as UserWithDMSStatus;

            // If user was previously failed but is now up to date, update and notify
            if (user.isDmsCheckSuccessful === false) {
              const updateResult = await client.models.User.update({
                id: user.id,
                isDmsCheckSuccessful: true,
              });

              if (updateResult.data) {
                console.log(
                  `Successfully updated isDmsCheckSuccessful to true for recovered user: ${userId}`
                );

                // Create recovery notification for Welon Trust user
                let welonTrustNotified = false;
                welonTrustNotified = await createWelonTrustNotification(
                  user,
                  true
                );

                return { userId, success: true, welonTrustNotified };
              } else {
                console.error(
                  `Failed to update recovered user: ${userId}`,
                  updateResult.errors
                );
                return { userId, success: false, error: updateResult.errors };
              }
            } else {
              // User was already marked as successful, no action needed
              return { userId, success: true, welonTrustNotified: false };
            }
          } catch (error) {
            console.error(
              `Error processing recovery for user ${userId}:`,
              error
            );
            return { userId, success: false, error: (error as Error).message };
          }
        }
      );

      const recoveryResults = await Promise.all(recoveryPromises);
      const successfulRecoveries = recoveryResults.filter(
        result => result.success
      );
      const failedRecoveries = recoveryResults.filter(
        result => !result.success
      );
      const recoveryNotifications = recoveryResults.filter(
        result => result.welonTrustNotified
      ).length;

      console.log(
        `Recovery processing complete. ${successfulRecoveries.length} successful, ${failedRecoveries.length} failed.`
      );
      console.log(`Recovery notifications sent: ${recoveryNotifications}`);

      if (failedRecoveries.length > 0) {
        console.error('Failed recoveries:', failedRecoveries);
      }
    }

    // Summary logging
    console.log('DMS Check Summary:', {
      timestamp: now.toISOString(),
      totalDmsEntries: dmsEntries?.length || 0,
      overdueUsers: overdueUsers.length,
      upToDateUsers: upToDateUsers.length,
      processedEntries: processedEntries.length,
    });
  } catch (error) {
    console.error('Error in DMS Check handler:', error);
    throw error;
  }
};
