import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { generateClient } from 'aws-amplify/api';
import type { Schema } from '../../../data/resource';
// @ts-ignore
import { env } from '$amplify/env/documentSigningReminder';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

interface DocumentWithUser {
  id: string | null;
  title: string;
  userId: string;
  status: string | null;
  dateCreated: string;
}

interface UserData {
  id: string;
  cognitoId: string;
  email: string;
  firstName: string;
  lastName: string;
}

/**
 * Check if notification was already sent for this document today
 */
const checkNotificationSentToday = async (
  userId: string,
  documentId: string
): Promise<boolean> => {
  try {
    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );

    const { data: existingNotifications, errors } =
      await client.models.Notification.list({
        filter: {
          and: [
            { recipient: { eq: userId } },
            { author: { eq: 'system-document-signing-reminder' } },
            { message: { contains: documentId } },
            { createdAt: { ge: startOfDay.toISOString() } },
          ],
        },
      });

    if (errors) {
      console.error('Error checking notification log:', errors);
      return false;
    }

    return existingNotifications && existingNotifications.length > 0;
  } catch (error) {
    console.error('Error checking notification log:', error);
    return false;
  }
};

/**
 * Send notification to user about approved document awaiting signature
 */
const sendDocumentSigningNotification = async (
  document: DocumentWithUser,
  user: UserData
): Promise<boolean> => {
  try {
    if (!document.id) {
      console.error('Document ID is missing');
      return false;
    }

    // Check if notification was already sent today
    const alreadySent = await checkNotificationSentToday(
      user.cognitoId,
      document.id
    );
    if (alreadySent) {
      console.log(
        `Notification already sent today for document ${document.id} to user ${user.cognitoId}. Skipping.`
      );
      return true;
    }

    const message = `Your document "${document.title}" has been approved and is ready for signing. Please sign it to complete the process.`;
    const emailSubject = 'Document Ready for Signing - ChildFree Trust®';

    // Create in-app notification
    const { data: notification, errors: notificationErrors } =
      await client.models.Notification.create({
        message: `Document signing reminder: ${document.id} - ${message}`,
        recipient: user.cognitoId,
        author: 'system-document-signing-reminder',
        createdAt: new Date().toISOString(),
        isRead: false,
      });

    if (notificationErrors) {
      console.error(
        `Failed to create in-app notification for user ${user.cognitoId}:`,
        notificationErrors
      );
      return false;
    }

    console.log(
      `Successfully created in-app notification for user ${user.cognitoId} about document ${document.id}`
    );

    // Send email notification
    const emailResult = await client.mutations.sendEmail({
      to: user.email,
      subject: emailSubject,
      message: `Dear ${user.firstName} ${user.lastName},

${message}

Document Details:
- Title: ${document.title}
- Status: Approved
- Created: ${new Date(document.dateCreated).toLocaleDateString()}

Please log in to your account to review and sign the document.

Best regards,
ChildFree Trust® Team`,
      emailType: 'notification',
    });

    // Parse email result
    let emailSuccess = false;
    if (emailResult.data) {
      const emailData =
        typeof emailResult.data === 'string'
          ? JSON.parse(emailResult.data)
          : emailResult.data;
      emailSuccess = emailData.success === true;
    }

    if (emailSuccess) {
      console.log(
        `Successfully sent email notification to user ${user.email} about document ${document.id}`
      );
    } else {
      console.error(
        `Failed to send email notification to user ${user.email} about document ${document.id}`
      );
    }

    return emailSuccess;
  } catch (error) {
    console.error(
      `Error sending notification for document ${document.id} to user ${user.cognitoId}:`,
      error
    );
    return false;
  }
};

export const handler: EventBridgeHandler<
  'Scheduled Event',
  null,
  void
> = async event => {
  console.log(
    'Document Signing Reminder triggered at:',
    new Date().toISOString()
  );
  console.log('Event details:', JSON.stringify(event, null, 2));

  try {
    // Fetch all documents with status 'approved'
    console.log('Fetching documents with status "approved"...');
    const { data: approvedDocuments, errors } =
      await client.models.Document.list({
        filter: { status: { eq: 'approved' } },
        selectionSet: ['id', 'title', 'userId', 'status', 'dateCreated'],
      });

    if (errors && errors.length > 0) {
      console.error('Error fetching approved documents:', errors);
      return;
    }

    console.log(`Found ${approvedDocuments?.length || 0} approved documents`);

    if (!approvedDocuments || approvedDocuments.length === 0) {
      console.log('No approved documents found. Exiting.');
      return;
    }

    // Get unique user IDs
    const userIds = [...new Set(approvedDocuments.map(doc => doc.userId))];
    console.log(`Found ${userIds.length} unique users with approved documents`);

    // Fetch user details for all users
    const userPromises = userIds.map(userId =>
      client.models.User.get(
        {
          id: userId,
        },
        {
          selectionSet: ['id', 'cognitoId', 'email', 'firstName', 'lastName'],
        }
      )
    );

    const userResults = await Promise.all(userPromises);
    const users = userResults
      .filter(result => result.data && !result.errors)
      .map(result => result.data!);

    if (users.length === 0) {
      console.error('No valid users found');
      return;
    }

    // Create user lookup map
    const userMap = new Map<string, UserData>();
    users.forEach(user => {
      if (user.cognitoId) {
        userMap.set(user.id, {
          id: user.id,
          cognitoId: user.cognitoId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        });
      }
    });

    // Send notifications for each approved document
    const notificationPromises = approvedDocuments.map(async document => {
      if (!document.id) {
        console.warn(`Document missing ID:`, document);
        return {
          documentId: 'unknown',
          success: false,
          error: 'Document ID missing',
        };
      }

      const user = userMap.get(document.userId);
      if (!user) {
        console.warn(
          `User not found for document ${document.id}, userId: ${document.userId}`
        );
        return {
          documentId: document.id,
          success: false,
          error: 'User not found',
        };
      }

      try {
        const success = await sendDocumentSigningNotification(document, user);
        return { documentId: document.id, success, userId: user.cognitoId };
      } catch (error) {
        console.error(`Error processing document ${document.id}:`, error);
        return {
          documentId: document.id,
          success: false,
          error: (error as Error).message,
        };
      }
    });

    const results = await Promise.all(notificationPromises);
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log(
      `Notification processing complete. ${successCount} successful, ${failureCount} failed.`
    );

    if (failureCount > 0) {
      const failures = results.filter(r => !r.success);
      console.error('Failed notifications:', failures);
    }

    // Summary logging
    console.log('Document Signing Reminder Summary:', {
      timestamp: new Date().toISOString(),
      totalApprovedDocuments: approvedDocuments.length,
      uniqueUsers: userIds.length,
      successfulNotifications: successCount,
      failedNotifications: failureCount,
    });
  } catch (error) {
    console.error('Error in Document Signing Reminder handler:', error);
    throw error;
  }
};
