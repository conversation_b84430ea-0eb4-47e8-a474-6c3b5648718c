// @ts-nocheck

import type { Schema } from '../../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import Strip<PERSON> from 'stripe';
import { env } from '$amplify/env/adminActivateSubscription';

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

// Initialize Stripe client
let stripe: Stripe | null = null;

function getStripeClient(): Stripe {
  if (!stripe) {
    const secretKey = env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    stripe = new Stripe(secretKey);
    console.log('✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Stripe client initialized');
  }
  return stripe;
}

export const handler: Schema['adminActivateSubscription']['functionHandler'] =
  async event => {
    console.log('🚀 [ADMIN-ACTIVATE-SUBSCRIPTION] Lambda started');
    console.log(
      '📥 [ADMIN-ACTIVATE-SUBSCRIPTION] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { arguments: args, identity } = event;
      const {
        userId,
        cognitoId,
        plan,
        email,
        currency = 'usd',
        trialDays,
      } = args;

      // Check if user is admin
      if (!identity.groups?.includes('ADMINS')) {
        console.error(
          '❌ [ADMIN-ACTIVATE-SUBSCRIPTION] Unauthorized: user is not admin'
        );
        throw new Error('Unauthorized: admin access required');
      }

      // Validate required fields
      if (!userId || !cognitoId || !plan || !email || !trialDays) {
        console.error(
          '❌ [ADMIN-ACTIVATE-SUBSCRIPTION] Missing required fields'
        );
        throw new Error(
          'Missing required fields: userId, cognitoId, plan, email, trialDays'
        );
      }

      // Validate plan
      if (!['BASIC', 'PRO'].includes(plan)) {
        console.error('❌ [ADMIN-ACTIVATE-SUBSCRIPTION] Invalid plan:', plan);
        throw new Error('Invalid plan. Must be BASIC or PRO');
      }

      console.log('✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Input validation passed');
      console.log('🔍 [ADMIN-ACTIVATE-SUBSCRIPTION] Processing activation:', {
        userId,
        cognitoId,
        plan,
        email,
        trialDays,
        adminUser: identity.claims.sub,
      });

      const stripe = getStripeClient();

      // Calculate amount based on plan
      const amount = plan === 'BASIC' ? 1000 : 2000; // $10 and $20 in cents

      // Create or get Stripe customer
      let stripeCustomer;
      const existingCustomers = await stripe.customers.list({
        email: email,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        stripeCustomer = existingCustomers.data[0];
        console.log(
          '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Found existing Stripe customer'
        );
      } else {
        stripeCustomer = await stripe.customers.create({
          email: email,
          metadata: {
            userId: userId,
            cognitoId: cognitoId,
            admin_activated: 'true',
          },
        });
        console.log(
          '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Created new Stripe customer'
        );
      }

      // Create Stripe product first
      const stripeProduct = await stripe.products.create({
        name: `${plan} Plan`,
        metadata: {
          plan: plan,
        },
      });
      console.log('✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Created Stripe product');

      // Calculate trial end date
      const trialEnd = new Date();
      trialEnd.setDate(trialEnd.getDate() + trialDays);

      let stripeSubscription;
      let stripePrice = null;

      if (amount > 0) {
        // For paid subscriptions, create price and subscription with payment
        stripePrice = await stripe.prices.create({
          product: stripeProduct.id,
          unit_amount: amount,
          currency: currency,
          recurring: {
            interval: 'month',
          },
        });

        stripeSubscription = await stripe.subscriptions.create({
          customer: stripeCustomer.id,
          items: [
            {
              price: stripePrice.id,
            },
          ],
          trial_end: Math.floor(trialEnd.getTime() / 1000),
          metadata: {
            userId: userId,
            cognitoId: cognitoId,
            plan: plan,
            admin_activated: 'true',
          },
        });
        console.log(
          '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Created paid Stripe subscription'
        );
      } else {
        // For trial-only subscriptions (amount = 0), create subscription without price
        stripeSubscription = await stripe.subscriptions.create({
          customer: stripeCustomer.id,
          items: [
            {
              price_data: {
                currency: currency,
                product: stripeProduct.id,
                recurring: {
                  interval: 'month',
                },
                unit_amount: 0, // Free trial
              },
            },
          ],
          trial_end: Math.floor(trialEnd.getTime() / 1000),
          metadata: {
            userId: userId,
            cognitoId: cognitoId,
            plan: plan,
            admin_activated: 'true',
            trial_only: 'true',
          },
        });
        console.log(
          '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Created trial-only Stripe subscription'
        );
      }

      // Cancel any existing active subscriptions for this user
      const { data: existingSubscriptions } =
        await client.models.UserSubscription.list({
          filter: {
            cognitoId: {
              eq: cognitoId,
            },
            status: {
              eq: 'ACTIVE',
            },
          },
          authMode: 'iam',
        });

      console.log(
        '🔍 [ADMIN-ACTIVATE-SUBSCRIPTION] Found existing subscriptions:',
        existingSubscriptions?.length || 0
      );

      // Cancel existing active subscriptions (both in Stripe and database)
      if (existingSubscriptions && existingSubscriptions.length > 0) {
        for (const subscription of existingSubscriptions) {
          // Cancel in Stripe first (if it has a valid Stripe subscription ID)
          if (
            subscription.stripeSubscriptionId &&
            !subscription.stripeSubscriptionId.startsWith('admin-sub-')
          ) {
            try {
              await stripe.subscriptions.cancel(
                subscription.stripeSubscriptionId
              );
              console.log(
                '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Canceled Stripe subscription:',
                subscription.stripeSubscriptionId
              );
            } catch (error) {
              console.error(
                '❌ [ADMIN-ACTIVATE-SUBSCRIPTION] Error canceling Stripe subscription:',
                subscription.stripeSubscriptionId,
                error
              );
              // Continue with database update even if Stripe cancellation fails
            }
          }

          // Update in database
          await client.models.UserSubscription.update(
            {
              id: subscription.id,
              status: 'CANCELED' as const,
              canceledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            {
              authMode: 'iam',
            }
          );

          console.log(
            '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Canceled database subscription:',
            subscription.id
          );
        }
      }

      // Calculate subscription period (fallback if Stripe doesn't provide dates)
      const currentPeriodStart = new Date();
      const currentPeriodEnd = new Date();
      currentPeriodEnd.setDate(currentPeriodEnd.getDate() + trialDays); // Use trial period instead of 1 month

      // Create new subscription with Stripe data
      const subscriptionData = {
        userId: userId,
        cognitoId: cognitoId,
        stripeCustomerId: stripeCustomer.id,
        stripeSubscriptionId: stripeSubscription.id,
        plan: plan as 'BASIC' | 'PRO',
        status: 'ACTIVE' as const,
        amount: amount,
        currency: currency,
        currentPeriodStart: (stripeSubscription as any).current_period_start
          ? new Date(
              (stripeSubscription as any).current_period_start * 1000
            ).toISOString()
          : currentPeriodStart.toISOString(),
        currentPeriodEnd: (stripeSubscription as any).trial_end
          ? new Date((stripeSubscription as any).trial_end * 1000).toISOString()
          : (stripeSubscription as any).current_period_end
            ? new Date(
                (stripeSubscription as any).current_period_end * 1000
              ).toISOString()
            : currentPeriodEnd.toISOString(),
        cancelAtPeriodEnd:
          (stripeSubscription as any).cancel_at_period_end || false,
        createdAt: new Date().toISOString(),
      };

      // Create subscription in database
      const { data: newSubscription } =
        await client.models.UserSubscription.create(subscriptionData, {
          authMode: 'iam',
        });

      console.log(
        '✅ [ADMIN-ACTIVATE-SUBSCRIPTION] Admin-activated subscription created for user:',
        cognitoId,
        'Plan:',
        plan,
        'by admin:',
        identity.claims.sub
      );

      return {
        success: true,
        subscription: newSubscription,
        message: `Successfully activated ${plan} subscription for user`,
      };
    } catch (error) {
      console.error('❌ [ADMIN-ACTIVATE-SUBSCRIPTION] Error:', error);
      throw error;
    }
  };
