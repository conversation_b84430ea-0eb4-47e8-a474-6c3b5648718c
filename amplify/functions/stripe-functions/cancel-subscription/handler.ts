// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import Stripe from 'stripe';
import { env } from '$amplify/env/cancelSubscription';

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

// Initialize Stripe client
let stripe: Stripe | null = null;

function getStripeClient(): Stripe {
  if (!stripe) {
    const secretKey = env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    stripe = new Stripe(secretKey);
    console.log('✅ Stripe client initialized');
  }
  return stripe;
}

// Helper function to safely convert timestamp to ISO string
function safeTimestampToISO(timestamp: number | null | undefined): string {
  if (timestamp === null || timestamp === undefined || isNaN(timestamp)) {
    return new Date().toISOString();
  }
  return new Date(timestamp * 1000).toISOString();
}

export const handler: Schema['cancelSubscription']['functionHandler'] =
  async event => {
    console.log('🚀 [CANCEL-SUBSCRIPTION] Lambda started');
    console.log(
      '📥 [CANCEL-SUBSCRIPTION] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { subscriptionId, userId, cognitoId } = event.arguments;

      console.log('🔍 [CANCEL-SUBSCRIPTION] Processing cancellation request:', {
        subscriptionId,
        userId,
        cognitoId,
        timestamp: new Date().toISOString(),
      });

      console.log('✅ [CANCEL-SUBSCRIPTION] Validating inputs...');
      if (!subscriptionId) {
        console.error('❌ [CANCEL-SUBSCRIPTION] Missing subscription ID');
        throw new Error('Subscription ID is required');
      }

      if (!userId || !cognitoId) {
        console.error('❌ [CANCEL-SUBSCRIPTION] Missing authentication data:', {
          userId,
          cognitoId,
        });
        throw new Error('User authentication required');
      }

      console.log('✅ [CANCEL-SUBSCRIPTION] Input validation passed');

      const stripe = getStripeClient();

      // Cancel subscription in Stripe
      console.log(
        '🔄 [CANCEL-SUBSCRIPTION] Updating Stripe subscription to cancel at period end...'
      );
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });

      console.log('✅ [CANCEL-SUBSCRIPTION] Stripe subscription updated:', {
        id: subscription.id,
        status: subscription.status,
        cancel_at_period_end: subscription.cancel_at_period_end,
        current_period_end: safeTimestampToISO(subscription.current_period_end),
      });

      // Update subscription in database
      console.log(
        '💾 [CANCEL-SUBSCRIPTION] Updating subscription in database...'
      );
      try {
        const { data: subscriptions } =
          await client.models.UserSubscription.list({
            filter: {
              stripeSubscriptionId: {
                eq: subscriptionId,
              },
            },
          });

        console.log(
          '🔍 [CANCEL-SUBSCRIPTION] Database subscription search result:',
          {
            found: subscriptions && subscriptions.length > 0,
            count: subscriptions?.length || 0,
            subscriptionIds: subscriptions?.map(s => s.id) || [],
          }
        );

        if (subscriptions && subscriptions.length > 0) {
          const userSubscription = subscriptions[0];
          console.log(
            '🔄 [CANCEL-SUBSCRIPTION] Updating database subscription:',
            {
              id: userSubscription.id,
              currentStatus: userSubscription.status,
              stripeSubscriptionId: userSubscription.stripeSubscriptionId,
            }
          );

          await client.models.UserSubscription.update({
            id: userSubscription.id,
            status: 'CANCELED',
            cancelAtPeriodEnd: true,
            canceledAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

          console.log(
            '✅ [CANCEL-SUBSCRIPTION] Database subscription updated successfully:',
            {
              id: userSubscription.id,
              status: 'CANCELED',
              cancelAtPeriodEnd: true,
              canceledAt: new Date().toISOString(),
            }
          );
        } else {
          console.warn(
            '⚠️ [CANCEL-SUBSCRIPTION] No matching subscription found in database'
          );
        }
      } catch (dbError) {
        console.error(
          '❌ [CANCEL-SUBSCRIPTION] Error updating subscription in database:',
          {
            error: dbError instanceof Error ? dbError.message : 'Unknown error',
            stack: dbError instanceof Error ? dbError.stack : undefined,
            subscriptionId,
            cognitoId,
          }
        );
        // Continue with Stripe response even if DB update fails
      }

      const result = {
        success: true,
        data: {
          cancelAtPeriodEnd: subscription.cancel_at_period_end || false,
          currentPeriodEnd: safeTimestampToISO(subscription.current_period_end),
        },
      };

      console.log(
        '✅ [CANCEL-SUBSCRIPTION] Success result:',
        JSON.stringify(result, null, 2)
      );
      return JSON.stringify(result);
    } catch (error) {
      console.error('❌ [CANCEL-SUBSCRIPTION] Lambda error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.log(
        '❌ [CANCEL-SUBSCRIPTION] Error result:',
        JSON.stringify(errorResult, null, 2)
      );
      return JSON.stringify(errorResult);
    }
  };
