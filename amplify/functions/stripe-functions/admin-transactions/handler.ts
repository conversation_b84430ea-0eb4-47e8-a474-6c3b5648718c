// @ts-nocheck

import type { Schema } from '../../../data/resource';
import Stripe from 'stripe';
import { env } from '$amplify/env/adminTransactions';

// Initialize Stripe client
let stripe: Stripe | null = null;

function getStripeClient(): Stripe {
  if (!stripe) {
    const secretKey = env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    stripe = new Stripe(secretKey);
    console.log('✅ [ADMIN-TRANSACTIONS] Stripe client initialized');
  }
  return stripe;
}

// Helper function to map Stripe payment status to admin page status
function mapPaymentStatus(stripeStatus: string): string {
  switch (stripeStatus) {
    case 'succeeded':
      return 'Successful';
    case 'failed':
      return 'Failed';
    case 'canceled':
      return 'Canceled';
    case 'processing':
      return 'Processing';
    case 'requires_payment_method':
    case 'requires_confirmation':
    case 'requires_action':
      return 'Pending';
    default:
      return 'Unknown';
  }
}

export const handler: Schema['getAdminTransactions']['functionHandler'] =
  async event => {
    console.log('🚀 [ADMIN-TRANSACTIONS] Lambda started');
    console.log(
      '📥 [ADMIN-TRANSACTIONS] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { arguments: args, identity } = event;
      const { limit = 200 } = args;

      // Check if user is admin
      const cognitoIdentity = identity as any;
      if (!cognitoIdentity?.groups?.includes('ADMINS')) {
        console.error(
          '❌ [ADMIN-TRANSACTIONS] Unauthorized: user is not admin'
        );
        throw new Error('Unauthorized: admin access required');
      }

      console.log('✅ [ADMIN-TRANSACTIONS] Admin access verified');

      const stripe = getStripeClient();
      console.log('✅ [ADMIN-TRANSACTIONS] Stripe client initialized');

      // Get payment intents from Stripe (these represent transactions)
      const paymentIntents = await stripe.paymentIntents.list({
        limit: Math.min(limit || 200, 200), // Cap at 200 for performance
      });

      console.log(
        '✅ [ADMIN-TRANSACTIONS] Found payment intents:',
        paymentIntents.data.length
      );

      // Transform payment intents to match admin page format
      const transformedTransactions = paymentIntents.data.map(intent => ({
        id: intent.id,
        amount: intent.amount / 100, // Convert from cents to dollars
        status: mapPaymentStatus(intent.status),
        date: new Date(intent.created * 1000).toISOString(),
        description:
          intent.description || `Payment for ${intent.amount / 100} USD`,
        currency: intent.currency.toUpperCase(),
      }));

      console.log(
        '✅ [ADMIN-TRANSACTIONS] Transactions transformed successfully'
      );

      return JSON.stringify({
        success: true,
        transactions: transformedTransactions,
      });
    } catch (error) {
      console.error('❌ [ADMIN-TRANSACTIONS] Error:', error);
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };
