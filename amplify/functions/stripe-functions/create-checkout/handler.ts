// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../../data/resource';
import Stripe from 'stripe';
import { env } from '$amplify/env/createCheckout';

// Initialize Stripe client
let stripe: Stripe | null = null;

function getStripeClient(): Stripe {
  if (!stripe) {
    const secretKey = process.env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    stripe = new Stripe(secretKey);
    console.log('✅ Stripe client initialized');
  }
  return stripe;
}

export const handler: Schema['createCheckout']['functionHandler'] =
  async event => {
    console.log('🚀 [CREATE-CHECKOUT] Lambda started');
    console.log(
      '📥 [CREATE-CHECKOUT] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { plan, userEmail, userId, cognitoId } = event.arguments;

      console.log('🔍 [CREATE-CHECKOUT] Processing checkout request:', {
        plan,
        userEmail,
        userId,
        cognitoId,
        timestamp: new Date().toISOString(),
      });

      // Validate inputs
      console.log('✅ [CREATE-CHECKOUT] Validating inputs...');
      if (!plan || !['BASIC', 'PRO'].includes(plan)) {
        console.error('❌ [CREATE-CHECKOUT] Invalid plan:', {
          plan,
          validPlans: ['BASIC', 'PRO'],
        });
        throw new Error('Invalid plan selected');
      }

      if (!userEmail || !userId || !cognitoId) {
        console.error('❌ [CREATE-CHECKOUT] Missing authentication data:', {
          userEmail,
          userId,
          cognitoId,
        });
        throw new Error('User authentication required');
      }

      console.log('✅ [CREATE-CHECKOUT] Input validation passed');

      const stripe = getStripeClient();

      // Set price based on plan
      const priceAmount = plan === 'BASIC' ? 1000 : 2000; // $10 or $20 in cents
      const planName = plan === 'BASIC' ? 'Basic Plan' : 'Pro Plan';

      console.log('💰 [CREATE-CHECKOUT] Plan details:', {
        plan,
        planName,
        priceAmount,
        priceInDollars: priceAmount / 100,
      });

      // Create or get Stripe customer
      console.log(
        '👤 [CREATE-CHECKOUT] Looking for existing Stripe customer...'
      );
      let customer;
      try {
        const customers = await stripe.customers.list({
          email: userEmail,
          limit: 1,
        });

        console.log('🔍 [CREATE-CHECKOUT] Customer search result:', {
          found: customers.data.length > 0,
          count: customers.data.length,
        });

        if (customers.data.length > 0) {
          customer = customers.data[0];
          console.log('✅ [CREATE-CHECKOUT] Found existing customer:', {
            customerId: customer.id,
            email: customer.email,
            created: new Date(customer.created * 1000).toISOString(),
          });
        } else {
          console.log('➕ [CREATE-CHECKOUT] Creating new Stripe customer...');
          customer = await stripe.customers.create({
            email: userEmail,
            metadata: {
              cognitoId: cognitoId,
              userId: userId,
            },
          });
          console.log('✅ [CREATE-CHECKOUT] Created new customer:', {
            customerId: customer.id,
            email: customer.email,
            metadata: customer.metadata,
          });
        }
      } catch (error) {
        console.error('❌ [CREATE-CHECKOUT] Error creating/finding customer:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          userEmail,
        });
        throw new Error('Failed to create customer');
      }

      // Create checkout session
      console.log('🛒 [CREATE-CHECKOUT] Creating Stripe checkout session...');
      const sessionConfig = {
        customer: customer.id,
        payment_method_types: ['card' as const],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: planName,
                description: `Monthly subscription to ${planName}`,
              },
              unit_amount: priceAmount,
              recurring: {
                interval: 'month' as const,
              },
            },
            quantity: 1,
          },
        ],
        mode: 'subscription' as const,
        success_url: `${env.NEXT_PUBLIC_APP_URL}/member/billing?success=true`,
        cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/member/billing/subscribe?canceled=true`,
        metadata: {
          cognitoId: cognitoId,
          userId: userId,
          plan: plan,
        },
      };

      console.log('🔧 [CREATE-CHECKOUT] Session configuration:', {
        customerId: customer.id,
        planName,
        priceAmount,
        successUrl: sessionConfig.success_url,
        cancelUrl: sessionConfig.cancel_url,
        metadata: sessionConfig.metadata,
      });

      const session = await stripe.checkout.sessions.create(sessionConfig);

      console.log(
        '✅ [CREATE-CHECKOUT] Checkout session created successfully:',
        {
          sessionId: session.id,
          url: session.url,
          customerId: session.customer,
          mode: session.mode,
          status: session.status,
        }
      );

      const result = {
        success: true,
        data: {
          sessionId: session.id,
          url: session.url,
        },
      };

      console.log(
        '✅ [CREATE-CHECKOUT] Success result:',
        JSON.stringify(result, null, 2)
      );
      return JSON.stringify(result);
    } catch (error) {
      console.error('❌ [CREATE-CHECKOUT] Lambda error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.log(
        '❌ [CREATE-CHECKOUT] Error result:',
        JSON.stringify(errorResult, null, 2)
      );
      return JSON.stringify(errorResult);
    }
  };
