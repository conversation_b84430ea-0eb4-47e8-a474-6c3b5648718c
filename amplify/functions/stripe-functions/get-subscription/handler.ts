// @ts-nocheck

import type { Schema } from '../../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/getSubscription';

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

export const handler: Schema['getSubscription']['functionHandler'] =
  async event => {
    console.log('🚀 [GET-SUBSCRIPTION] Lambda started');
    console.log(
      '📥 [GET-SUBSCRIPTION] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { userId, cognitoId } = event.arguments;

      console.log(
        '🔍 [GET-SUBSCRIPTION] Processing subscription request for:',
        {
          userId,
          cognitoId,
          timestamp: new Date().toISOString(),
        }
      );

      if (!userId || !cognitoId) {
        console.error('❌ [GET-SUBSCRIPTION] Missing authentication data:', {
          userId,
          cognitoId,
        });
        throw new Error('User authentication required');
      }

      console.log(
        '✅ [GET-SUBSCRIPTION] Authentication validated successfully'
      );

      // Get user subscription from database
      console.log(
        '🔍 [GET-SUBSCRIPTION] Querying subscriptions by cognitoId:',
        cognitoId
      );
      const { data: subscriptions, errors } =
        await client.models.UserSubscription.list({
          filter: {
            cognitoId: {
              eq: cognitoId,
            },
          },
        });

      console.log('📊 [GET-SUBSCRIPTION] Subscriptions by cognitoId result:', {
        count: subscriptions?.length || 0,
        hasErrors: !!errors,
        errors: errors ? JSON.stringify(errors) : null,
      });

      // Also try to find by userId in case there are old records
      console.log(
        '🔍 [GET-SUBSCRIPTION] Querying subscriptions by userId:',
        userId
      );
      const { data: subscriptionsByUserId } =
        await client.models.UserSubscription.list({
          filter: {
            userId: {
              eq: userId,
            },
          },
        });

      console.log('📊 [GET-SUBSCRIPTION] Subscriptions by userId result:', {
        count: subscriptionsByUserId?.length || 0,
      });

      // Combine both results
      const allSubscriptions = [
        ...(subscriptions || []),
        ...(subscriptionsByUserId || []),
      ];

      console.log('📊 [GET-SUBSCRIPTION] Combined subscriptions:', {
        totalCount: allSubscriptions.length,
        subscriptionIds: allSubscriptions.map(s => s.id),
      });

      if (errors) {
        console.error(
          '❌ [GET-SUBSCRIPTION] Database errors:',
          JSON.stringify(errors, null, 2)
        );
        throw new Error('Failed to fetch subscription data');
      }

      // Find the most recent active subscription
      console.log(
        '🔍 [GET-SUBSCRIPTION] Filtering for active subscriptions...'
      );
      const activeSubscriptions = allSubscriptions.filter(
        sub => sub.status === 'ACTIVE'
      );
      console.log('📊 [GET-SUBSCRIPTION] Active subscriptions found:', {
        count: activeSubscriptions.length,
        subscriptions: activeSubscriptions.map(s => ({
          id: s.id,
          status: s.status,
          createdAt: s.createdAt,
          stripeSubscriptionId: s.stripeSubscriptionId,
        })),
      });

      const activeSubscription = activeSubscriptions.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      const hasActiveSubscription = !!activeSubscription;

      console.log('🎯 [GET-SUBSCRIPTION] Selected subscription:', {
        hasActiveSubscription,
        selectedSubscription: activeSubscription
          ? {
              id: activeSubscription.id,
              status: activeSubscription.status,
              plan: activeSubscription.plan,
              stripeSubscriptionId: activeSubscription.stripeSubscriptionId,
            }
          : null,
      });

      const result = {
        success: true,
        data: {
          subscription: activeSubscription || null,
          hasActiveSubscription,
        },
      };

      console.log(
        '✅ [GET-SUBSCRIPTION] Success result:',
        JSON.stringify(result, null, 2)
      );
      return JSON.stringify(result);
    } catch (error) {
      console.error('❌ [GET-SUBSCRIPTION] Lambda error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.log(
        '❌ [GET-SUBSCRIPTION] Error result:',
        JSON.stringify(errorResult, null, 2)
      );
      return JSON.stringify(errorResult);
    }
  };
