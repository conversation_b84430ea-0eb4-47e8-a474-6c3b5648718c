// @ts-nocheck
import type { Schema } from '../../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import Stripe from 'stripe';
import { env } from '$amplify/env/processWebhook';

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

// Initialize Stripe client
let stripe: Stripe | null = null;

function getStripeClient(): Stripe {
  if (!stripe) {
    const secretKey = env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    stripe = new Stripe(secretKey);
    console.log('✅ Stripe client initialized');
  }
  return stripe;
}

export const handler: Schema['processWebhook']['functionHandler'] =
  async event => {
    console.log('🔔 [PROCESS-WEBHOOK] Lambda started');
    console.log(
      '📥 [PROCESS-WEBHOOK] Event received:',
      JSON.stringify(event, null, 2)
    );

    try {
      const { body, signature } = event.arguments;

      console.log('🔐 [PROCESS-WEBHOOK] Validating Stripe signature...');

      // Validate Stripe webhook signature
      const stripe = getStripeClient();
      const webhookSecret = env.STRIPE_WEBHOOK_SECRET;

      if (!webhookSecret) {
        throw new Error(
          'STRIPE_WEBHOOK_SECRET environment variable is not set'
        );
      }

      let stripeEvent: any;
      try {
        stripeEvent = stripe.webhooks.constructEvent(
          body,
          signature,
          webhookSecret
        );
        console.log(
          '✅ [PROCESS-WEBHOOK] Stripe signature validated successfully'
        );
        console.log('📋 [PROCESS-WEBHOOK] Event type:', stripeEvent.type);
        console.log('📋 [PROCESS-WEBHOOK] Event ID:', stripeEvent.id);
      } catch (err) {
        console.error(
          '❌ [PROCESS-WEBHOOK] Stripe signature validation failed:',
          err
        );
        throw new Error('Invalid Stripe signature');
      }

      console.log(
        '🔄 [PROCESS-WEBHOOK] Processing event type:',
        stripeEvent.type
      );

      // Handle different event types
      switch (stripeEvent.type) {
        case 'checkout.session.completed':
          await handleCheckoutCompleted(stripeEvent.data.object);
          break;
        case 'invoice.payment_succeeded':
          await handlePaymentSucceeded(stripeEvent.data.object);
          break;
        case 'customer.subscription.updated':
          await handleSubscriptionUpdated(stripeEvent.data.object);
          break;
        case 'customer.subscription.deleted':
          await handleSubscriptionDeleted(stripeEvent.data.object);
          break;
        default:
          console.log(
            '⚠️ [PROCESS-WEBHOOK] Unhandled event type:',
            stripeEvent.type
          );
      }

      const result = {
        success: true,
        message: `Successfully processed ${stripeEvent.type}`,
      };

      console.log(
        '✅ [PROCESS-WEBHOOK] Success result:',
        JSON.stringify(result, null, 2)
      );
      return JSON.stringify(result);
    } catch (error) {
      console.error('❌ [PROCESS-WEBHOOK] Lambda error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.log(
        '❌ [PROCESS-WEBHOOK] Error result:',
        JSON.stringify(errorResult, null, 2)
      );
      return JSON.stringify(errorResult);
    }
  };

// Helper function to safely convert timestamp to ISO string
const safeTimestampToISO = (timestamp: number | null | undefined): string => {
  if (timestamp === null || timestamp === undefined || isNaN(timestamp)) {
    return new Date().toISOString();
  }
  return new Date(timestamp * 1000).toISOString();
};

async function handleCheckoutCompleted(sessionData: any) {
  console.log('=== [PROCESS-WEBHOOK] CHECKOUT COMPLETED PROCESSING START ===');
  console.log('Processing checkout completed:', sessionData.id);
  console.log('Session metadata:', sessionData.metadata);

  if (
    !sessionData.subscription ||
    !sessionData.metadata?.cognitoId ||
    !sessionData.metadata?.userId
  ) {
    console.log(
      'Missing subscription, cognitoId, or userId in session metadata',
      {
        hasSubscription: !!sessionData.subscription,
        hasCognitoId: !!sessionData.metadata?.cognitoId,
        hasUserId: !!sessionData.metadata?.userId,
        metadata: sessionData.metadata,
      }
    );
    return;
  }

  const cognitoId = sessionData.metadata.cognitoId;
  const userId = sessionData.metadata.userId;
  const plan = sessionData.metadata.plan;
  const amount = plan === 'BASIC' ? 1000 : 2000;

  console.log(
    `Processing subscription for user: ${cognitoId}, plan: ${plan}, amount: ${amount}`
  );

  try {
    // Get subscription details from Stripe
    const stripe = getStripeClient();
    const subscriptionResponse = await stripe.subscriptions.retrieve(
      sessionData.subscription as string
    );
    const subscription = subscriptionResponse as Stripe.Subscription;

    // Cancel any existing active subscriptions before creating the new one
    console.log(
      'Canceling existing active subscriptions before creating new subscription'
    );
    await cancelExistingActiveSubscriptions(cognitoId);

    const subscriptionData = {
      userId: userId,
      cognitoId: cognitoId,
      stripeCustomerId: sessionData.customer as string,
      stripeSubscriptionId: subscription.id,
      plan: plan as 'BASIC' | 'PRO',
      status: 'ACTIVE' as const,
      amount: amount,
      currency: 'usd',
      currentPeriodStart: safeTimestampToISO(
        (subscription as any).current_period_start
      ),
      currentPeriodEnd: safeTimestampToISO(
        (subscription as any).current_period_end
      ),
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
      createdAt: new Date().toISOString(),
    };

    // Create subscription in database
    await client.models.UserSubscription.create(subscriptionData);
    console.log(
      'New subscription created in database for user:',
      cognitoId,
      'Plan:',
      plan
    );
    console.log('=== [PROCESS-WEBHOOK] CHECKOUT COMPLETED PROCESSING END ===');
  } catch (error) {
    console.error('Error creating subscription in database:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    console.log(
      '=== [PROCESS-WEBHOOK] CHECKOUT COMPLETED PROCESSING FAILED ==='
    );
    throw error; // Re-throw to be handled by main handler
  }
}

async function handlePaymentSucceeded(invoiceData: any) {
  if (!invoiceData.subscription) return;

  const stripe = getStripeClient();
  const subscriptionResponse = await stripe.subscriptions.retrieve(
    invoiceData.subscription as string
  );
  const subscription = subscriptionResponse as Stripe.Subscription;
  const cognitoId = subscription.metadata?.cognitoId;

  if (!cognitoId) return;

  console.log('Payment succeeded for user:', cognitoId);

  try {
    // Find and update subscription in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscription.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      await client.models.UserSubscription.update({
        id: userSubscription.id,
        status: 'ACTIVE' as const,
        currentPeriodStart: safeTimestampToISO(
          (subscription as any).current_period_start
        ),
        currentPeriodEnd: safeTimestampToISO(
          (subscription as any).current_period_end
        ),
        updatedAt: new Date().toISOString(),
      });
      console.log('Subscription updated in database for user:', cognitoId);
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
    throw error;
  }
}

async function handleSubscriptionUpdated(subscriptionData: any) {
  const cognitoId = subscriptionData.metadata?.cognitoId;
  if (!cognitoId) return;

  let status:
    | 'ACTIVE'
    | 'INACTIVE'
    | 'PAST_DUE'
    | 'CANCELED'
    | 'INCOMPLETE'
    | 'TRIALING' = 'ACTIVE';
  if (subscriptionData.status === 'past_due') status = 'PAST_DUE';
  else if (subscriptionData.status === 'canceled') status = 'CANCELED';
  else if (
    subscriptionData.status === 'incomplete' ||
    subscriptionData.status === 'incomplete_expired'
  )
    status = 'INCOMPLETE';
  else if (subscriptionData.status === 'trialing') status = 'TRIALING';

  console.log('Subscription updated for user:', cognitoId, 'Status:', status);

  try {
    // Find and update subscription in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscriptionData.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      const updateData: any = {
        id: userSubscription.id,
        status: status,
        currentPeriodStart: safeTimestampToISO(
          subscriptionData.current_period_start
        ),
        currentPeriodEnd: safeTimestampToISO(
          subscriptionData.current_period_end
        ),
        cancelAtPeriodEnd: subscriptionData.cancel_at_period_end || false,
        updatedAt: new Date().toISOString(),
      };

      if (subscriptionData.canceled_at) {
        updateData.canceledAt = safeTimestampToISO(
          subscriptionData.canceled_at
        );
      }

      await client.models.UserSubscription.update(updateData);
      console.log('Subscription updated in database for user:', cognitoId);
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
    throw error;
  }
}

async function handleSubscriptionDeleted(subscriptionData: any) {
  const cognitoId = subscriptionData.metadata?.cognitoId;
  if (!cognitoId) return;

  console.log('Subscription deleted for user:', cognitoId);

  try {
    // Find and update subscription status in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscriptionData.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      await client.models.UserSubscription.update({
        id: userSubscription.id,
        status: 'CANCELED' as const,
        canceledAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      console.log(
        'Subscription marked as canceled in database for user:',
        cognitoId
      );
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
    throw error;
  }
}

// Helper function to cancel existing active subscriptions for a user
async function cancelExistingActiveSubscriptions(cognitoId: string) {
  console.log(
    'Checking for existing active subscriptions for user:',
    cognitoId
  );

  try {
    // Find all active subscriptions for the user
    const { data: existingSubscriptions } =
      await client.models.UserSubscription.list({
        filter: {
          cognitoId: {
            eq: cognitoId,
          },
          status: {
            eq: 'ACTIVE',
          },
        },
      });

    if (!existingSubscriptions || existingSubscriptions.length === 0) {
      console.log(
        'No existing active subscriptions found for user:',
        cognitoId
      );
      return;
    }

    console.log(
      `Found ${existingSubscriptions.length} active subscription(s) to cancel for user:`,
      cognitoId
    );

    // Cancel each existing subscription
    for (const subscription of existingSubscriptions) {
      try {
        console.log(
          `Canceling subscription ${subscription.stripeSubscriptionId} for user:`,
          cognitoId
        );

        // Cancel in Stripe
        const stripe = getStripeClient();
        await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
        console.log(
          `Successfully canceled Stripe subscription: ${subscription.stripeSubscriptionId}`
        );

        // Update status in database
        await client.models.UserSubscription.update({
          id: subscription.id,
          status: 'CANCELED' as const,
          canceledAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
        console.log(
          `Successfully updated database status for subscription: ${subscription.id}`
        );
      } catch (error) {
        console.error(
          `Error canceling subscription ${subscription.stripeSubscriptionId}:`,
          error
        );
        // Continue with other subscriptions even if one fails
      }
    }

    console.log(
      'Finished canceling existing subscriptions for user:',
      cognitoId
    );
  } catch (error) {
    console.error('Error finding existing subscriptions:', error);
    throw error; // Re-throw to handle in calling function
  }
}
