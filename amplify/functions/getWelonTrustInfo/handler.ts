// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { env } from '$amplify/env/getWelonTrustInfo';

// Initialize Amplify client
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

export const handler: Schema['getWelonTrustInfo']['functionHandler'] =
  async event => {
    const requestId = Date.now().toString();
    console.log(
      `🔍 [${requestId}] Getting WelonTrust info for user:`,
      event.arguments.userId
    );

    try {
      const { userId: assignedWelonTrustId } = event.arguments;

      console.log(
        `📋 [${requestId}] Looking for WelonTrust with cognitoId:`,
        assignedWelonTrustId
      );

      // Get WelonTrust user data directly by cognitoId
      const { data: welonUserList, errors: welonListErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: assignedWelonTrustId } },
          selectionSet: [
            'id',
            'firstName',
            'lastName',
            'cognitoId',
            'phoneNumber',
            'shippingAddress.*',
          ],
        });

      if (welonListErrors) {
        console.error(
          `❌ [${requestId}] WelonTrust user query errors:`,
          welonListErrors
        );
        throw new Error(
          `Failed to get WelonTrust user: ${JSON.stringify(welonListErrors)}`
        );
      }

      const welonUser = welonUserList?.[0];
      if (!welonUser) {
        throw new Error('WelonTrust user not found');
      }

      if (!welonUser.shippingAddress) {
        throw new Error('WelonTrust user has no shipping address configured');
      }

      console.log(`✅ [${requestId}] Found WelonTrust user:`, {
        id: welonUser.id,
        firstName: welonUser.firstName,
        lastName: welonUser.lastName,
        phoneNumber: welonUser.phoneNumber,
        shippingAddress: welonUser.shippingAddress,
      });

      return {
        success: true,
        welonTrust: {
          id: welonUser.id,
          cognitoId: welonUser.cognitoId,
          name: `${welonUser.firstName} ${welonUser.lastName}`,
          firstName: welonUser.firstName,
          lastName: welonUser.lastName,
          phoneNumber: welonUser.phoneNumber,
          address: {
            addressLine1: welonUser.shippingAddress.addressLine1 || '',
            addressLine2: welonUser.shippingAddress.addressLine2 || '',
            city: welonUser.shippingAddress.city || '',
            stateProvinceCode:
              welonUser.shippingAddress.stateProvinceCode || '',
            postalCode: welonUser.shippingAddress.postalCode || '',
            countryCode: welonUser.shippingAddress.countryCode || '',
          },
        },
      };
    } catch (error) {
      console.error(`❌ [${requestId}] Error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };
