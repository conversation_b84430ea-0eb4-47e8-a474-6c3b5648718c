// @ts-nocheck

import {
  PreTokenGenerationTriggerHandler,
  PreTokenGenerationTriggerEvent,
} from 'aws-lambda';

export const handler: PreTokenGenerationTriggerHandler = async (
  event: PreTokenGenerationTriggerEvent
) => {
  console.log('Pre Token Generation event:', JSON.stringify(event, null, 2));

  const userAttrs = event.request.userAttributes;

  try {
    // Check if the custom:externalId attribute exists
    const externalId = userAttrs['custom:externalId'];

    if (externalId) {
      event.response = {
        claimsAndScopeOverrideDetails: {
          idTokenGeneration: {},
          accessTokenGeneration: {
            claimsToAddOrOverride: {
              externalId: externalId,
            },
          },
        },
      };

      console.log('Successfully added externalId to access token:', externalId);
    } else {
      console.log('No custom:externalId found in user attributes');
    }

    console.log('===> EVENT RESULT', event);

    return event;
  } catch (error) {
    console.error('Error processing pre token generation:', error);
    // Return the event even on error to avoid blocking token generation
    return event;
  }
};
