import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
// @ts-ignore
import { env } from '$amplify/env/sendMedicalIncident';
import {
  AdminGetUserCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['sendMedicalIncident']['functionHandler'] =
  async event => {
    console.log(
      'Medical incident send request:',
      JSON.stringify(event, null, 2)
    );

    try {
      // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
      const { incidentId, emailType, clientData } = event.arguments || event;

      console.log('Incident ID:', incidentId);
      console.log('Email type:', emailType);
      console.log('Client data:', clientData);

      let deliveryAttempt: Schema['MedicalIncidentDelivery']['type'] | null = null;

      // Validate required fields
      if (!incidentId) {
        throw new Error('Missing required field: incidentId');
      }

      // Fetch the incident data
      const { data: incident, errors: incidentErrors } =
        await client.models.MedicalIncident.get({
          id: incidentId,
        });

      console.log('Incident data:', incident);

      if (incidentErrors) {
        throw new Error(
          `Failed to fetch incident data: ${JSON.stringify(incidentErrors)}`
        );
      }

      if (!incident) {
        throw new Error('Incident not found');
      }

      if (emailType === 'support') {
        if (!clientData) {
          throw new Error('Missing required field: clientData');
        }

        const { data } = await sendSupportEmail(clientData);
        deliveryAttempt = data;
        if (deliveryAttempt) {
          // Add the delivery attempt to the incident
          await client.models.MedicalIncident.update({
            id: incidentId,
            deliveryAttempts: incident.deliveryAttempts ? [...incident.deliveryAttempts, deliveryAttempt] : [deliveryAttempt],
          });
        }

        return {
          success: true,
          message: 'Support email sent successfully',
        };
      }

      if (!incident.selectedClient) {
        throw new Error('Client not selected');
      }

      // Fetch the user data
      const { data: users, errors: userErrors } = await client.models.User.list(
        {
          filter: { id: { eq: incident.selectedClient.id } },
        }
      );

      if (userErrors) {
        throw new Error(
          `Failed to fetch user data: ${JSON.stringify(userErrors)}`
        );
      }

      if (!users || users.length === 0) {
        throw new Error('User not found');
      }

      const user = users[0];

      // Send the emails
      if (emailType === 'fyi') {
        const { data } = await sendFYIEmails(incident, user);
        deliveryAttempt = data;
      } else if (emailType === 'hospital') {
        const { data } = await sendHospitalEmail(incident, user);
        deliveryAttempt = data;
      }

      if (deliveryAttempt) {
        // Add the delivery attempt to the incident
        await client.models.MedicalIncident.update({
          id: incidentId,
          deliveryAttempts: incident.deliveryAttempts ? [...incident.deliveryAttempts, deliveryAttempt] : [deliveryAttempt],
        });
      }

      return {
        success: true,
        message: 'Emails sent successfully',
      };
    } catch (error: any) {
      console.error('Error sending medical incident emails:', error);

      return {
        success: false,
        error: error.message || 'Failed to send medical incident emails',
      };
    }
  };

const sendHospitalEmail = async (
  incident: Schema['MedicalIncident']['type'],
  user: Schema['User']['type']
) => {
  try {
    console.log('Sending hospital email');

    if (!incident.hospitalEmail) {
      throw new Error('Hospital email not provided');
    }

    const interviewData = user.interviewProgressV2;
    if (!interviewData) {
      console.warn('Interview data not found');
    }

    const userCognitoData = await cognitoClient.send(
      new AdminGetUserCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: user.email,
      })
    );

    const externalId = userCognitoData.UserAttributes?.find(
      a => a.Name === 'sub'
    )?.Value;

    const { data: careDocuments, errors: careDocumentsErrors } =
      await client.models.CareDocumentNew.list({
        filter: externalId
          ? { userId: { eq: user.id } }
          : { owner: { contains: user.email } },
      });

    if (careDocumentsErrors) {
      console.error(
        'Failed to fetch care documents:',
        JSON.stringify(careDocumentsErrors)
      );
    }

    const careDocument = careDocuments?.[0];
    if (!careDocument) {
      console.warn('Care documents not found');
    }

    const processedInterviewData = processInterviewData(interviewData);
    const processedCareDocuments = processCareDocuments(careDocument);

    if (!processedInterviewData && !processedCareDocuments) {
      throw new Error('No medical data available');
    }

    const emailBody = `
        Hello,

        This is a notification from Childfree Trust®. 
        ${user.firstName} ${user.lastName} has been hospitalized and we need your assistance. 

        Please find the patient's medical information below:

        ${processedInterviewData || ''}

        ${processedCareDocuments || ''}

        Please contact us immediately if you have any questions or concerns.

        Thank you,
        Childfree Trust® Team
    `.trim();

    await client.mutations.sendEmail({
      to: incident.hospitalEmail,
      subject: 'Medical Incident Notification',
      message: emailBody,
      emailType: 'medicalIncident',
    });

    const deliveryAttempt = {
      timestamp: new Date().toISOString(),
      recipients: [incident.hospitalEmail],
      method: 'email' as Schema['MedicalIncidentDelivery']['type']['method'],
      content: JSON.stringify(emailBody),
      delivered: true,
      type: 'hospital' as Schema['MedicalIncidentDelivery']['type']['type'],
    }

    return { success: true, data: deliveryAttempt, message: 'Hospital email sent successfully' };
  } catch (error) {
    console.error('Error sending hospital email:', error);
    throw error;
  }
};

const processInterviewData = (interviewData: any) => {
  if (!interviewData) return;

  const parsedStepsData =
    typeof interviewData.stepsData === 'string'
      ? JSON.parse(interviewData.stepsData)
      : interviewData.stepsData;
  const medical = parsedStepsData?.medical;
  if (!medical) return;

  let emailBody = 'Patient Medical Information\n\n';

  // Proxy (medical decision maker)
  if (medical.proxy?.primary) {
    const p = medical.proxy.primary;
    emailBody += `Primary Proxy: ${p.firstName} ${p.lastName}, ${p.relationshipType || ''}\n`;
    emailBody += `Phone: ${p.phoneNumber}\nAddress: ${p.address}\nDOB: ${p.dateOfBirth}\n\n`;
  }
  if (medical.proxy?.successors?.length) {
    emailBody += 'Successor Proxies:\n';
    medical.proxy.successors.forEach((s: any) => {
      emailBody += `- ${s.firstName} ${s.lastName}, Phone: ${s.phoneNumber}, Address: ${s.address}, DOB: ${s.dateOfBirth}\n`;
    });
    emailBody += '\n';
  }

  // Consultation preferences
  if (medical.wantConsultation) {
    emailBody += 'Consultation Allowed With:\n';
    medical.consultationPeople.forEach((c: any) => {
      emailBody += `- ${c.firstName} ${c.lastName}, Phone: ${c.phoneNumber}, Address: ${c.address}\n`;
    });
    emailBody += '\n';
  }

  // Organ donation
  if (medical.organDonation?.enabled) {
    emailBody += 'Organ Donation: YES\n';
    emailBody += `Specific Organs: ${medical.organDonation.specificOrgans.join(', ')}\n`;
    if (medical.organDonation.limitations) {
      emailBody += `Limitations: ${medical.organDonation.limitations}\n`;
    }
    emailBody += '\n';
  } else {
    emailBody += 'Organ Donation: NO\n\n';
  }

  // Directives (life-sustaining treatments etc.)
  if (medical.directives) {
    emailBody += 'Medical Directives:\n';
    for (const [key, value] of Object.entries(medical.directives)) {
      emailBody += `- ${key.replace(/_/g, ' ')}: ${value ? 'Yes' : 'No'}\n`;
    }
    emailBody += '\n';
  }

  // Authority
  if (medical.proxyAuthority) {
    emailBody += `Proxy Authority: ${medical.proxyAuthority}\n`;
  }

  return emailBody.trim();
};

const processCareDocuments = (careDocuments: any) => {
  if (!careDocuments) return;

  const medicalSection = careDocuments?.data?.find(
    (s: any) => s.sectionType === 'Medical'
  );

  if (!medicalSection) return;

  let content: any;
  try {
    content = JSON.parse(medicalSection.content);
  } catch {
    return;
  }

  let emailBody = 'Care Document – Medical Information\n\n';

  // Professionals
  if (content.professionals?.length) {
    emailBody += 'Healthcare Professionals:\n';
    content.professionals.forEach((p: any) => {
      emailBody += `- ${p.name} ${p.isPrimary ? '(Primary)' : ''} ${
        p.isSpecialist ? `(Specialist: ${p.specialty || ''})` : ''
      } | Hospital: ${p.hospital || ''} | Phone: ${p.phone || ''}\n`;
    });
    emailBody += '\n';
  }

  // Medications
  if (content.medications?.length) {
    emailBody += 'Medications:\n';
    content.medications.forEach((m: any) => {
      emailBody += `- ${m.name}, Dosage: ${m.dosage}, Diagnosed: ${m.diagnosisDate}, Providers: ${m.providers?.join(', ')}, Notes: ${m.notes}\n`;
    });
    emailBody += '\n';
  }

  // Conditions
  if (content.conditions?.length) {
    emailBody += 'Medical Conditions:\n';
    content.conditions.forEach((c: any) => {
      emailBody += `- ${c.name}, Diagnosed: ${c.diagnosisDate}, Providers: ${c.providers?.join(', ')}, Notes: ${c.notes}\n`;
    });
    emailBody += '\n';
  }

  // Surgeries
  if (content.surgeries?.length) {
    emailBody += 'Surgeries:\n';
    content.surgeries.forEach((s: any) => {
      emailBody += `- ${s.type}, Date: ${s.date}, Providers: ${s.providers?.join(', ')}, Notes: ${s.notes}\n`;
    });
    emailBody += '\n';
  }

  // Allergies
  if (content.allergies?.length) {
    emailBody += 'Allergies:\n';
    content.allergies.forEach((a: any) => {
      emailBody += `- ${a.name}, Diagnosed: ${a.diagnosisDate}, Providers: ${a.providers?.join(', ')}, Notes: ${a.notes}\n`;
    });
    emailBody += '\n';
  }

  // Pharmacy
  if (content.pharmacy) {
    emailBody += `Preferred Pharmacy: ${content.pharmacy.name}, ${content.pharmacy.address1} ${content.pharmacy.city}, ${content.pharmacy.state}, ${content.pharmacy.zip}\n`;
    emailBody += `Phone: ${content.pharmacy.phone}, Website: ${content.pharmacy.website}\n\n`;
  }

  // Blood Type
  if (content.bloodType) {
    emailBody += `Blood Type: ${content.bloodType}\n\n`;
  }

  // Organ donation
  if (content.organDonation) {
    emailBody += `Organ Donation: YES\nDetails: ${content.organDonationDetails}\n\n`;
  } else {
    emailBody += 'Organ Donation: NO\n\n';
  }

  // Directives / wishes
  if (content.medicalDirective) {
    emailBody += `Medical Directive: ${content.medicalDirective}\n`;
  }
  if (content.coreWishes) {
    emailBody += `Core Wishes: ${content.coreWishes}\n`;
  }
  if (content.dignityMeaning) {
    emailBody += `Dignity Meaning: ${content.dignityMeaning}\n`;
  }
  if (content.noMedical) {
    emailBody += `No Medical Instructions: ${content.noMedical}\n`;
  }
  if (content.foodPreferences) {
    emailBody += `Food Preferences: ${content.foodPreferences}\n`;
  }
  if (content.additionalMedical) {
    emailBody += `Additional Medical Notes: ${content.additionalMedical}\n`;
  }

  return emailBody.trim();
};

const sendFYIEmails = async (
  incident: Schema['MedicalIncident']['type'],
  user: Schema['User']['type']
) => {
  console.log('Sending FYI emails');
  try {
    const {
      firstName,
      lastName,
      email: clientEmail,
      assignedWelonTrustId,
    } = user;
    const { hospitalName, hospitalAddress } = incident;

    const subject = 'Medical Incident FYI';
    const message = `Medical incident FYI for ${firstName} ${lastName}.
                    Hospital: ${hospitalName}
                    Location: ${hospitalAddress}`;

    // Recipients list
    const recipients: string[] = [clientEmail, '<EMAIL>'];

    // Add Welon Trust if assigned
    if (assignedWelonTrustId) {
      const welonTrustEmail = await getWelonTrustEmail(assignedWelonTrustId);
      if (welonTrustEmail) {
        recipients.push(welonTrustEmail);
      }
    }

    console.log('Sending FYI emails to:', recipients);

    // Send emails in parallel
    await Promise.all(
      recipients.map(to =>
        client.mutations.sendEmail({
          to,
          subject,
          message,
          emailType: 'medicalIncidentFYI',
        })
      )
    );

    const deliveryAttempt = {
      timestamp: new Date().toISOString(),
      recipients,
      method: 'email' as Schema['MedicalIncidentDelivery']['type']['method'],
      content: JSON.stringify(message),
      delivered: true,
      type: 'fyi' as Schema['MedicalIncidentDelivery']['type']['type'],
    }

    return { success: true, data: deliveryAttempt, message: 'FYI emails sent successfully' };
  } catch (error) {
    console.error('Error sending FYI emails:', error);
    throw error;
  }
};

const getWelonTrustEmail = async (
  welonTrustId: string
): Promise<string | null> => {
  try {
    const { data, errors } = await client.models.User.list({
      filter: { cognitoId: { eq: welonTrustId } },
      selectionSet: ['email'],
    });

    if (errors) {
      console.warn('Failed to fetch Welon Trust user:', JSON.stringify(errors));
      return null;
    }

    if (!data || data.length === 0) {
      console.warn(`Welon Trust user not found for ID: ${welonTrustId}`);
      return null;
    }

    return data[0].email ?? null;
  } catch (error) {
    console.error('Error fetching Welon Trust user email:', error);
    return null;
  }
};

const sendSupportEmail = async (clientData: { firstName?: string | null; lastName?: string | null, birthdate?: string | null }) => {
  try {
    const { firstName, lastName, birthdate } = clientData;
    const message = `Client: ${firstName} ${lastName} with birthdate ${birthdate} does not exist in the system.`;
    await client.mutations.sendEmail({
      to: '<EMAIL>',
      subject: 'Medical Incident Notification',
      message: message,
      emailType: 'medicalIncidentSupport',
    });

    const deliveryAttempt = {
      timestamp: new Date().toISOString(),
      recipients: ['<EMAIL>'],
      method: 'email' as Schema['MedicalIncidentDelivery']['type']['method'],
      content: JSON.stringify(message),
      delivered: true,
      type: 'support' as Schema['MedicalIncidentDelivery']['type']['type'],
    }

    return { success: true, data: deliveryAttempt, message: 'Support email sent successfully' };
  } catch (error) {
    console.error('Error sending support email:', error);
    throw error;
  }
}
