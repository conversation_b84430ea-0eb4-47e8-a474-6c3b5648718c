import type { Schema } from '../../data/resource';

export const handler: Schema['getSecrets']['functionHandler'] = async event => {
  try {
    const { secretName } = event.arguments;

    if (!secretName) {
      throw new Error('secretName is required');
    }

    // Get secret from environment (Amplify will inject from secret store)
    let secretValue: string | undefined;

    switch (secretName) {
      case 'GOOGLE_API_KEY':
        secretValue = process.env.GOOGLE_API_KEY;
        break;
      default:
        throw new Error('Invalid secret name');
    }

    if (!secretValue) {
      throw new Error('Secret not found');
    }

    return {
      value: secretValue,
    };
  } catch (error) {
    console.error('Error getting secret:', error);
    throw error;
  }
};
