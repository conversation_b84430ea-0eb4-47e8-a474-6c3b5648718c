// @ts-nocheck

import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/markTokenAsUsed';
import { Schema } from '../../../data/resource';

// Initialize clients outside handler for connection reuse
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

const getTokenByFilter = async (tokenValue: string) => {
  try {
    const result = await client.models.VerificationTokens.list({
      filter: {
        token: { eq: tokenValue },
        isUsed: { eq: false }, // Only fetch unused tokens
        expiresAt: { gt: new Date().toISOString() }, // Only fetch non-expired tokens
      },
    });

    console.log('checkIsValid: filtered query result:', result);

    return result?.data?.[0] || null;
  } catch (error) {
    console.warn('checkIsValid: filtered query failed:', error);
    return null;
  }
};

const getTokenByValueOnly = async (tokenValue: string) => {
  try {
    const result = await client.models.VerificationTokens.list({
      filter: {
        token: { eq: tokenValue },
      },
    });

    console.log('checkIsValid: token-only query result:', result);

    return result?.data?.[0] || null;
  } catch (error) {
    console.warn('checkIsValid: token-only query failed:', error);
    return null;
  }
};

// TODO REFACTOR THIS HANDLER AND VerificationTokens MODEL TO ALLOW GET IT BY TOKEN (WITHOUT LIST)
export const handler: Schema['markTokenAsUsed']['functionHandler'] =
  async event => {
    console.log('MARK TOKEN AS USED EVENT:', JSON.stringify(event, null, 2));

    try {
      const now = new Date();

      const { token } = event.arguments || event;

      if (!token) {
        console.log('checkIsValid: missing token parameter');
        return false;
      }

      // Primary approach: Use filtered query with all conditions
      let tokenRecord = await getTokenByFilter(token);

      // If filtered query didn't work, try simpler query
      if (!tokenRecord) {
        console.log(
          'checkIsValid: filtered query returned no results, trying fallback'
        );
        tokenRecord = await getTokenByValueOnly(token);
      }

      if (!tokenRecord) {
        console.log('checkIsValid: token not found in database');
        return false;
      }

      const { errors } = await client.models.VerificationTokens.update({
        id: tokenRecord.id,
        isUsed: true,
        usedAt: now.toISOString(),
      });

      if (errors) {
        console.error('checkIsValid: errors updating token:', errors);
        return false;
      }

      return true;
    } catch (error) {
      console.error('checkIsValid: unexpected error:', error);
      return false;
    }
  };
