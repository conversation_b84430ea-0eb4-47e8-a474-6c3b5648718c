// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/checkIsValid';

// Initialize clients outside handler for connection reuse
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

interface TokenValidationResult {
  isValid: boolean;
  reason?: string;
}

/**
 * Validates a verification token
 */
const validateToken = (token: any): TokenValidationResult => {
  if (!token) {
    return { isValid: false, reason: 'missing token' };
  }

  if (token.isUsed) {
    return { isValid: false, reason: 'token already used' };
  }

  const now = new Date();
  const expiresAt = new Date(token.expiresAt);

  if (isNaN(expiresAt.getTime())) {
    return { isValid: false, reason: 'invalid expiration date' };
  }

  if (now > expiresAt) {
    return { isValid: false, reason: 'token expired' };
  }

  return { isValid: true };
};

/**
 * Fetches token using filtered query (primary method)
 */
const getTokenByFilter = async (tokenValue: string) => {
  try {
    const result = await client.models.VerificationTokens.list({
      filter: {
        token: { eq: tokenValue },
        isUsed: { eq: false }, // Only fetch unused tokens
        expiresAt: { gt: new Date().toISOString() }, // Only fetch non-expired tokens
      },
    });

    console.log('checkIsValid: filtered query result:', result);

    return result?.data?.[0] || null;
  } catch (error) {
    console.warn('checkIsValid: filtered query failed:', error);
    return null;
  }
};

/**
 * Fallback method: fetch by token value only
 */
const getTokenByValueOnly = async (tokenValue: string) => {
  try {
    const result = await client.models.VerificationTokens.list({
      filter: {
        token: { eq: tokenValue },
      },
    });

    console.log('checkIsValid: token-only query result:', result);

    return result?.data?.[0] || null;
  } catch (error) {
    console.warn('checkIsValid: token-only query failed:', error);
    return null;
  }
};

export const handler: Schema['checkIsValid']['functionHandler'] =
  async event => {
    console.log('checkIsValid request:', JSON.stringify(event, null, 2));

    try {
      const { token } = event.arguments || event;

      console.log('checkIsValid: extracted token:', token);

      if (!token) {
        console.log('checkIsValid: missing token parameter');
        return false;
      }

      // Primary approach: Use filtered query with all conditions
      let tokenRecord = await getTokenByFilter(token);

      // If filtered query didn't work, try simpler query
      if (!tokenRecord) {
        console.log(
          'checkIsValid: filtered query returned no results, trying fallback'
        );
        tokenRecord = await getTokenByValueOnly(token);
      }

      if (!tokenRecord) {
        console.log('checkIsValid: token not found in database');
        return false;
      }

      // Validate the token
      const validation = validateToken(tokenRecord);

      console.log(
        `checkIsValid: token validation result - ${validation.isValid ? 'valid' : `invalid (${validation.reason})`}`
      );

      return validation.isValid;
    } catch (error) {
      console.error('checkIsValid: unexpected error:', error);
      return false;
    }
  };
