// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/deleteUserComplete';
import {
  CognitoIdentityProviderClient,
  AdminDeleteUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['deleteUserComplete']['functionHandler'] =
  async event => {
    const { userId } = event.arguments;

    console.log('===> START DELETE USER LOGIC:', { userId });
    console.log('===> Full event:', JSON.stringify(event, null, 2));

    try {
      // Step 1: Get user data including cognitoId
      console.log('===> Fetching user with ID:', userId);
      const { data: users, errors: getUserErrors } =
        await client.models.User.list({
          filter: { id: { eq: userId } },
        });

      console.log('===> User fetch result:', {
        usersCount: users?.length || 0,
        hasErrors: !!getUserErrors,
        errors: getUserErrors,
      });

      if (getUserErrors) {
        console.error('===> Error fetching user:', getUserErrors);
        return {
          success: false,
          message: 'Failed to fetch user data',
          data: null,
        };
      }

      if (!users || users.length === 0) {
        console.error('===> User not found with id:', userId);
        return {
          success: false,
          message: `User not found with id: ${userId}`,
          data: null,
        };
      }

      const user = users[0];
      console.log('===> Found user:', {
        id: user.id,
        email: user.email,
        cognitoId: user.cognitoId,
      });

      // Step 2: Delete all related records from DynamoDB
      console.log('===> Starting deletion of related records...');
      await deleteUserRelatedRecords(userId, user.email);
      console.log('===> Finished deletion of related records');

      // Step 3: Delete user from Cognito (if cognitoId exists)
      if (user.cognitoId) {
        try {
          console.log('===> Deleting user from Cognito:', user.cognitoId);
          const deleteCommand = new AdminDeleteUserCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: user.email,
          });
          await cognitoClient.send(deleteCommand);
          console.log('===> Successfully deleted user from Cognito');
        } catch (cognitoError) {
          console.error('===> Error deleting user from Cognito:', cognitoError);
          // Continue with DynamoDB deletion even if Cognito fails
        }
      } else {
        console.log('===> No cognitoId found, skipping Cognito deletion');
      }

      // Step 4: Finally delete the user record from DynamoDB
      console.log('===> Deleting user record from DynamoDB:', user.id);
      const { errors: deleteUserErrors } = await client.models.User.delete({
        id: user.id,
      });

      if (deleteUserErrors) {
        console.error(
          '===> Errors deleting user from DynamoDB:',
          deleteUserErrors
        );
        throw new Error('Failed to delete user from database');
      }

      console.log('===> Successfully deleted user record from DynamoDB');
      console.log('===> User deletion completed successfully');

      const result = {
        success: true,
        message: 'User deleted successfully',
        data: {
          userId: user.id,
          email: user.email,
          deletedFromCognito: !!user.cognitoId,
        },
      };

      console.log('===> Returning result:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('===> Error in deleteUser:', error);
      const errorResult = {
        success: false,
        message:
          error instanceof Error ? error.message : 'Failed to delete user',
        data: null,
      };

      console.log(
        '===> Returning error result:',
        JSON.stringify(errorResult, null, 2)
      );
      return errorResult;
    }
  };

// Helper function to delete all user-related records
async function deleteUserRelatedRecords(
  userId: string,
  userEmail: string
): Promise<void> {
  console.log('===> Starting deletion of related records for user:', userId);

  try {
    // Execute all list operations in parallel
    console.log('===> Fetching all related records in parallel...');
    const [
      linkedAccountsResult,
      welonTrustAssignmentsResult,
      documentsResult,
      subscriptionsResult,
      careDocumentsResult,
      addressesResult,
      shippingLabelsResult,
      memberNotesResult,
      userInvitesResult,
    ] = await Promise.allSettled([
      client.models.LinkedAccount.list({ filter: { userId: { eq: userId } } }),
      client.models.WelonTrustAssignment.list({
        filter: { userId: { eq: userId } },
      }),
      client.models.Document.list({ filter: { userId: { eq: userId } } }),
      client.models.UserSubscription.list({
        filter: { userId: { eq: userId } },
      }),
      client.models.ShippingLabel.list({ filter: { userId: { eq: userId } } }),
      client.models.MemberNote.list({ filter: { userId: { eq: userId } } }),
      client.models.UserInvite.list({ filter: { email: { eq: userEmail } } }),
    ]);
    console.log('===> Finished fetching all related records');

    // Helper function to process deletion results
    const addDeletionPromises = (
      result: any,
      modelName: string,
      deleteFunction: (id: string) => Promise<any>
    ) => {
      if (result.status === 'fulfilled' && result.value.data?.length) {
        const items = result.value.data;
        console.log(`===> Found ${items.length} ${modelName} to delete`);
        items.forEach((item: any) => {
          deletionPromises.push(deleteFunction(item.id));
        });
      }
    };

    // Collect all deletion promises
    const deletionPromises: Promise<any>[] = [];

    // Process all record types using helper function
    const modelConfigs = [
      {
        result: linkedAccountsResult,
        name: 'LinkedAccounts',
        deleteFunc: (id: string) => client.models.LinkedAccount.delete({ id }),
      },
      {
        result: welonTrustAssignmentsResult,
        name: 'WelonTrustAssignments',
        deleteFunc: (id: string) =>
          client.models.WelonTrustAssignment.delete({ id }),
      },
      {
        result: documentsResult,
        name: 'Documents',
        deleteFunc: (id: string) => client.models.Document.delete({ id }),
      },
      {
        result: subscriptionsResult,
        name: 'UserSubscriptions',
        deleteFunc: (id: string) =>
          client.models.UserSubscription.delete({ id }),
      },
      {
        result: shippingLabelsResult,
        name: 'ShippingLabels',
        deleteFunc: (id: string) => client.models.ShippingLabel.delete({ id }),
      },
      {
        result: memberNotesResult,
        name: 'MemberNotes',
        deleteFunc: (id: string) => client.models.MemberNote.delete({ id }),
      },
      {
        result: userInvitesResult,
        name: 'UserInvites',
        deleteFunc: (id: string) => client.models.UserInvite.delete({ id }),
      },
    ];

    // Process all models in a loop
    modelConfigs.forEach(config => {
      addDeletionPromises(config.result, config.name, config.deleteFunc);
    });

    // Execute all deletions in parallel with chunking for better performance
    console.log(
      '===> Executing',
      deletionPromises.length,
      'deletions in parallel...'
    );

    // Process deletions in chunks of 25 to avoid overwhelming DynamoDB
    const CHUNK_SIZE = 25;
    const chunks = [];
    for (let i = 0; i < deletionPromises.length; i += CHUNK_SIZE) {
      chunks.push(deletionPromises.slice(i, i + CHUNK_SIZE));
    }

    const results = [];
    for (const chunk of chunks) {
      const chunkResults = await Promise.allSettled(chunk);
      results.push(...chunkResults);
      // Small delay between chunks to be gentle on DynamoDB
      if (chunks.length > 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Count results
    const failureCount = results.filter(r => r.status === 'rejected').length;
    const successCount = results.length - failureCount;

    if (failureCount > 0) {
      console.error(
        '===> Some deletions failed:',
        failureCount,
        'out of',
        results.length
      );
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`===> Deletion ${index} failed:`, result.reason);
        }
      });
    }

    console.log(
      `===> Completed: ${successCount}/${results.length} deletions successful`
    );
  } catch (error) {
    console.error('===> Error in deleteUserRelatedRecords:', error);
    // Don't throw - continue with user deletion
  }
}
