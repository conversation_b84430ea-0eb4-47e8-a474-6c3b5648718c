// @ts-nocheck

import type { Schema } from '../../data/resource';
import { generateClient } from 'aws-amplify/data';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { env } from '$amplify/env/verifyPromoCode';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

export const handler: Schema['verifyPromoCode']['functionHandler'] =
  async event => {
    console.log('===> VERIFY PROMO CODE EVENT', JSON.stringify(event, null, 2));

    try {
      const { promoCode } = event.arguments;
      const searchCode = (promoCode || '').toUpperCase().trim();

      if (!searchCode) {
        console.warn('verifyPromoCode: empty promoCode argument');
        return false;
      }

      const { data: promoCodes, errors } =
        await client.models.PromoCode.listPromoCodeByCode({
          code: searchCode,
        });

      if (errors && errors.length) {
        console.error('verifyPromoCode: error fetching promo code', errors);
        return false;
      }

      if (!promoCodes || promoCodes.length === 0) {
        console.log(`verifyPromoCode: promo code not found: ${searchCode}`);
        return false;
      }

      const promo = promoCodes[0];

      // MUST BE ACTIVE
      if (promo.status !== 'active') {
        console.log(
          `verifyPromoCode: promo code not active, status=${promo.status}`
        );
        return false;
      }

      // MUST NOT EXPIRED
      if (promo.expiresAt) {
        const expiresAt = new Date(promo.expiresAt);
        const now = new Date();
        if (!isNaN(expiresAt.getTime()) && expiresAt < now) {
          console.log(
            `verifyPromoCode: promo code expired at ${promo.expiresAt}, now=${now.toISOString()}`
          );
          return false;
        }
      }

      return true;
    } catch (err) {
      console.error('verifyPromoCode: unexpected error', err);
      return false;
    }
  };
