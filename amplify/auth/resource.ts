import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import { referenceAuth } from '@aws-amplify/backend';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';
import { signOutDevice } from '../functions/signOutDevice/resource';
import { inviteUser } from '../functions/inviteUser/inviteUser/resource';
import { acceptInvite } from '../functions/inviteUser/acceptInvite/resource';
import { deleteUserComplete } from '../functions/deleteUserComplete/resource';
import { initiatePhoneVerification } from '../functions/initiatePhoneVerification/resource';
import { verifyPhoneCode } from '../functions/verifyPhoneCode/resource';
import { addUserToGroupV3 } from '../functions/addUserToGroupV3/resource';
import { getWelonTrustInfo } from '../functions/getWelonTrustInfo/resource';
import { sendMedicalIncident } from '../functions/sendMedicalIncident/resource';
import { getMedicalDataForReview } from '../functions/getMedicalDataForReview/resource';
import { createUPSLabel } from '../functions/ups-functions/create-label/resource';
import { trackUPSPackage } from '../functions/ups-functions/track-package/resource';
import { getSubscription } from '../functions/stripe-functions/get-subscription/resource';
import { createCheckout } from '../functions/stripe-functions/create-checkout/resource';
import { cancelSubscription } from '../functions/stripe-functions/cancel-subscription/resource';
import { resetUserPassword } from '../functions/resetUserPassword/resource';

const userPoolId = process.env.CFLegacy_UserPoolID;
const identityPoolId = process.env.CFLegacy_IdentityPoolID;
const authRoleArn = process.env.CFLegacy_authRoleARN;
const unauthRoleArn = process.env.CFLegacy_unauthRoleARN;
const userPoolClientId = process.env.CFLegacy_userPoolClientID;

// This is here to throw an error during deployment to know which one is undefined or missing
if (
  !userPoolId ||
  !identityPoolId ||
  !authRoleArn ||
  !unauthRoleArn ||
  !userPoolClientId
) {
  throw new Error(
    'Missing required Cognito environment variables. Current values:\n\tuserPoolId: ' +
      userPoolId +
      '\n\tidentityPoolId: ' +
      identityPoolId +
      '\n\tauthRoleArn: ' +
      authRoleArn +
      '\n\tunauthRoleArn: ' +
      unauthRoleArn +
      '\n\tuserPoolClientId: ' +
      userPoolClientId
  );
}

export const auth = referenceAuth({
  userPoolId,
  identityPoolId,
  authRoleArn,
  unauthRoleArn,
  userPoolClientId,
  groups: {
    ADMINS: process.env.Admins_Role || 'not found',
    WELONTRUST: process.env.WelonTrust_Role || 'not found',
    CALLCENTER: process.env.CALLCENTER_ROLE || 'not found',
    MEDICALREVIEW: process.env.MEDICALREVIEW_ROLE || 'not found',
  },
  access: allow => [
    allow.resource(activateDeactivateUser).to(['manageUsers', 'listUsers']),
    allow.resource(inviteUser).to(['manageUsers', 'listUsers']),
    allow.resource(acceptInvite).to(['manageUsers', 'listUsers']),
    allow.resource(postSignUp).to(['manageUsers', 'listUsers']),
    allow
      .resource(addUserToGroupV3)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
    allow.resource(checkEmail).to(['manageUsers', 'listUsers']),
    allow.resource(trackLoginAttempt).to(['manageUsers', 'listUsers']),
    allow.resource(generateVerificationToken).to(['manageUsers', 'listUsers']),
    allow
      .resource(verifyEmailToken)
      .to(['manageUsers', 'listUsers', 'managePasswordRecovery']),
    allow.resource(initiatePhoneVerification).to(['manageUsers', 'listUsers']),
    allow.resource(verifyPhoneCode).to(['manageUsers', 'listUsers']),
    allow.resource(signOutDevice).to(['manageUserDevices', 'listUsers']),
    allow.resource(deleteUserComplete).to(['manageUsers', 'listUsers']),
    allow.resource(getWelonTrustInfo).to(['manageUsers', 'listUsers']),
    allow.resource(sendMedicalIncident).to(['manageUsers', 'listUsers']),
    allow.resource(getMedicalDataForReview).to(['manageUsers', 'listUsers']),
    allow.resource(createUPSLabel).to(['manageUsers', 'listUsers']),
    allow.resource(trackUPSPackage).to(['manageUsers', 'listUsers']),
    allow.resource(getSubscription).to(['manageUsers', 'listUsers']),
    allow.resource(createCheckout).to(['manageUsers', 'listUsers']),
    allow.resource(cancelSubscription).to(['manageUsers', 'listUsers']),
    allow
      .resource(resetUserPassword)
      .to(['manageUsers', 'listUsers', 'managePasswordRecovery']),
  ],
});
