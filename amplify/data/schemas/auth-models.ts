import { a } from '@aws-amplify/backend';

/**
 * Authentication and user onboarding related models
 */

export const UserOnboarding = a
  .model({
    cognitoId: a.string().required(),
    currentStep: a.string().required(),
    metadata: a.json(),
    lastUpdated: a.datetime(),
    isComplete: a.boolean().default(false),
  })
  .identifier(['cognitoId'])
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const LoginAttempt = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    lockoutUntil: a.datetime(),
    failedAttempts: a.integer().default(0),
  })
  .identifier(['email'])
  .authorization(allow => [allow.group('ADMINS')])
  .disableOperations(['subscriptions']);

export const LoginHistory = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    sessionId: a.string(),
    userId: a.string(), // COGNITO EXTERNAL USER ID
  })
  .secondaryIndexes(index => [index('userId'), index('email')])
  .authorization(allow => [
    allow.ownerDefinedIn('userId').identityClaim('externalId'),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const VerificationTokens = a
  .model({
    id: a.id(),
    email: a.string().required(),
    token: a.string().required(),
    expiresAt: a.datetime().required(),
    isUsed: a.boolean().default(false),
    createdAt: a.datetime().required(),
    usedAt: a.datetime(),
    verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
  })
  .disableOperations(['subscriptions'])
  .authorization(allow => [allow.group('ADMINS').to(['read'])]);

export const AdminResource = a
  .model({
    id: a.id(),
    title: a.string().required(),
    description: a.string(),
    url: a.string().required(),
    resourceType: a.string().required(), // 'website', 'person', 'organization', 'other'
    isActive: a.boolean().default(true),
    displayOrder: a.integer().default(0),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    createdBy: a.string().required(), // Admin user ID who created this resource
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.authenticated().to(['read']),
  ])
  .disableOperations(['subscriptions']);
