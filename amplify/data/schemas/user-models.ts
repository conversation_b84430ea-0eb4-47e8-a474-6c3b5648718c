import { a } from '@aws-amplify/backend';

/**
 * User management related models
 */

export const UserAnswer = a.customType({
  questionId: a.string().required(),
  answer: a.string(),
  answeredAt: a.datetime().required(),
});

export const UserInterviewProgressNew = a.customType({
  interviewVersionId: a.id().required(),
  isCompleted: a.boolean().required(),
  startedAt: a.datetime().required(),
  completedAt: a.datetime(),
  currentQuestionId: a.string(),
  answers: a.ref('UserAnswer').array(),
});

// User status enum
export const UserStatus = a.enum([
  'active',
  'inactive',
  'pending',
  'deceased',
  'invited',
]);

// Master role enum
export const MasterRole = a.enum([
  'Member',
  'Administrator',
  'WelonTrust',
  'Professional',
  'CallCenter',
  'MedicalReview',
]);

// Invite status enum
export const InviteStatus = a.enum([
  'pending',
  'accepted',
  'expired',
  'cancelled',
]);

// Promo code status enum
export const PromoCodeStatus = a.enum(['active', 'inactive', 'expired']);

// TODO FIX AUTH RULES IN THIS MODEL
// User Invite model
export const UserInvite = a
  .model({
    email: a.string().required(),
    firstName: a.string().required(),
    middleName: a.string(),
    lastName: a.string().required(),
    role: MasterRole,
    subrole: a.string(),
    invitedBy: a.string().required(), // Admin user ID who sent the invite
    invitedByEmail: a.string().required(),
    token: a.string().required(),
    status: InviteStatus,
    expiresAt: a.datetime().required(),
    acceptedAt: a.datetime(),
    createdAt: a.datetime().required(),
    sharedFields: a.string(),
    inviterEmail: a.string(),
    userId: a.id(), // id of the user that sends invite
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.ownerDefinedIn('userId').identityClaim('externalId'),
  ])
  .disableOperations(['subscriptions']);

// Promo Code model
export const PromoCode = a
  .model({
    code: a.string().required(),
    discountPercentage: a.integer().required(),
    status: PromoCodeStatus,
    description: a.string(),
    expiresAt: a.datetime(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    createdBy: a.string().required(),
    createdByEmail: a.string().required(),
  })
  .secondaryIndexes(index => [index('code')])
  .authorization(allow => [allow.group('ADMINS')])
  .disableOperations(['subscriptions']);

// Link type enum
export const LinkType = a.enum([
  'primary',
  'secondary',
  'delegate',
  'emergency',
]);

// Link status enum
export const LinkStatus = a.enum(['pending', 'active', 'revoked']);

// WelonTrustAssignment status enum
export const WelonTrustAssignmentStatus = a.enum([
  'active',
  'pending',
  'revoked',
]);

export const InterviewProgressV2 = a.customType({
  status: a.string(),
  currentStep: a.string(),
  stepsData: a.json(),
  createdAt: a.datetime(),
  updatedAt: a.datetime(),
});

// Address custom type for embedding in models
export const Address = a.customType({
  addressLine1: a.string().required(),
  addressLine2: a.string(),
  city: a.string().required(),
  stateProvinceCode: a.string().required(),
  postalCode: a.string().required(),
  countryCode: a.string().required(),
});

// Shipping Label model for tracking UPS labels
export const ShippingLabel = a
  .model({
    userId: a.id().required(),
    assignedWelonTrustId: a.id(), // ID of the WelonTrust user receiving the package
    documentIds: a.string().array(), // Array of document IDs included in this shipment
    trackingNumber: a.string().required(),
    labelUrl: a.string().required(),
    fromAddress: a.string().required(), // JSON string of address
    toAddress: a.string().required(), // JSON string of address
    serviceCode: a.string().required(),
    cost: a.string().required(), // JSON string with amount and currency
    estimatedDelivery: a.string(),
    status: a.string().default('created'),
    createdAt: a.datetime().required(),
    // Additional fields for multiple documents
    packageDescription: a.string(), // Description of all documents in the package
    totalDocuments: a.integer(), // Number of documents in the package
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrust: a.belongsTo('User', 'assignedWelonTrustId'),
  })
  .authorization(allow => [
    allow.owner(),
    allow.ownerDefinedIn('assignedWelonTrustId').identityClaim('externalId'), // Allow WelonTrust to see packages sent to them
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

// MAIN USER MODEL
export const User = a
  .model({
    email: a
      .string()
      .required()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    firstName: a
      .string()
      .required()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    middleName: a
      .string()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    lastName: a
      .string()
      .required()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    phoneNumber: a
      .string()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    birthdate: a
      .string()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    state: a
      .string()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    cognitoId: a.string(),
    role: MasterRole,
    subrole: a.string(),
    status: UserStatus,
    journeyStatus: a.string(),
    createdAt: a
      .datetime()
      .required()
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
    // Dead Man's Switch fields. TRUE - if all ok, FALSE - if user didn't check in
    isDmsCheckSuccessful: a.boolean().default(true),
    // Referral source field - optional (can be regular referral or promo code)
    howDidYouHearAboutUs: a.string(),
    // Gender field - optional
    gender: a.string(),
    // Promo code information - JSON object with activation status and discount info
    promoCodeInfo: a.json(),
    // People Library - central repository for all people, charities, and businesses
    peopleLibrary: a.json(),
    // Primary shipping address embedded as a custom type
    shippingAddress: a.ref('Address'),

    // Relationships
    linkedAccounts: a.hasMany('LinkedAccount', 'userId'),
    linkedAccountsWithSharedFields: a.hasMany(
      'LinkedAccountWithSharedFields',
      'userId'
    ),
    assignedWelonTrust: a.hasOne('WelonTrustAssignment', 'userId'),
    assignedWelonTrustId: a.string(),
    documents: a.hasMany('Document', 'userId'),
    subscriptions: a.hasMany('UserSubscription', 'userId'),
    interviewProgress: a.ref('UserInterviewProgressNew').array(),
    careDocumentsNew: a.hasMany('CareDocumentNew', 'userId'),
    shippingLabels: a.hasMany('ShippingLabel', 'userId'),
    assignedShippingLabels: a.hasMany('ShippingLabel', 'assignedWelonTrustId'),
    memberNotes: a.hasMany('MemberNote', 'userId'),
    interviewProgressV2: a.ref('InterviewProgressV2'),
    medicalIncidents: a.hasMany('MedicalIncident', 'userId'),
    medicalReports: a.hasMany('MedicalReport', 'userId'),
    userInvites: a.hasMany('UserInvite', 'userId'),
    // USER PRIMARY ADDRESS DATA
    addressLine1: a.string(),
    addressLine2: a.string(),
    city: a.string(),
    stateProvinceCode: a.string(),
    postalCode: a.string(),
    countryCode: a
      .string()
      .required()
      .default('US')
      .authorization(allow => [
        allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
        allow
          .ownerDefinedIn('assignedWelonTrustId')
          .identityClaim('externalId'),
        allow.group('ADMINS'),
        allow.groups(['MEDICALREVIEW', 'CALLCENTER']).to(['read']),
      ]),
  })
  .secondaryIndexes(index => [index('cognitoId'), index('email')])
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId').identityClaim('externalId'),
    allow.ownerDefinedIn('assignedWelonTrustId').identityClaim('externalId'),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

// Welon Trust assignment model
export const WelonTrustAssignment = a
  .model({
    userId: a.id().required(),
    welonTrustUserId: a.string().required(),
    welonTrustName: a.string().required(),
    welonTrustEmail: a.string().required(),
    welonTrustCognitoId: a.string().required().default(''),
    assignedAt: a.datetime().required(),
    assignedBy: a.string().required(), // Admin user ID who made the assignment
    status: WelonTrustAssignmentStatus,
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('welonTrustCognitoId').identityClaim('externalId'),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

// Member Note model for internal notes by Welon Trust users
export const MemberNote = a
  .model({
    userId: a.id().required(), // ID of the member this note is about
    authorId: a.string().required(), // Cognito ID of the Welon Trust user who created the note
    authorName: a.string().required(), // Name of the author for display purposes
    content: a.string().required(), // The note content
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    // Only Welon Trust users can create, read, update, and delete notes
    allow.groups(['WELONTRUST']).to(['create', 'read', 'update', 'delete']),
    // Admins can also manage notes
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
  ])
  .disableOperations(['subscriptions']);

// Linked account model
export const LinkedAccount = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    linkedUserId: a.string().required(), // The ID of the user who is linked
    linkType: LinkType, // The type of link
    status: LinkStatus, // The status of the link
    permissions: a.string().array().required(), // Permissions granted to the linked user
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    expiresAt: a.datetime(), // Optional expiration date
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.owner()])
  .disableOperations(['subscriptions']);

export const LinkedAccountWithSharedFields = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    userEmail: a.string().required(),
    linkedUserId: a.string().required(), // The ID of the user who is linked
    linkedUserEmail: a.string().required(),
    sharedFields: a.string().required(),
    isAccepted: a.boolean().required().default(false),
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.owner(),
    allow.group('ADMINS'),
    allow.ownerDefinedIn('userId').identityClaim('externalId'),
    allow.ownerDefinedIn('linkedUserId').identityClaim('externalId'),
  ])
  .disableOperations(['subscriptions']);

export const MedicalIncidentClient = a.customType({
  id: a.string().required(),
  firstName: a.string().required(),
  lastName: a.string().required(),
  birthdate: a.string(),
  email: a.string().required(),
  phoneNumber: a.string(),
});

export const MedicalIncidentEmailType = a.enum(['fyi', 'hospital', 'support']);
export const MedicalIncidentDeliveryMethod = a.enum(['email', 'fax']);

export const MedicalIncidentDelivery = a.customType({
  timestamp: a.datetime().required(),
  recipients: a.string().array().required(),
  method: MedicalIncidentDeliveryMethod,
  content: a.string().required(),
  delivered: a.boolean().required(),
  type: MedicalIncidentEmailType
});

export const MedicalIncident = a
  .model({
    id: a.id().required(),
    userId: a.string().required(), // The ID of the user who owns this medical incident
    contactFirstName: a.string(),
    contactLastName: a.string(),
    contactPhone: a.string(),
    relationshipToClient: a.string(),
    relationshipToClientOther: a.string(),
    clientStatus: a.string(),
    hospitalName: a.string(),
    hospitalAddress: a.string(),
    hospitalFax: a.string(),
    hospitalEmail: a.string(),
    hospitalConfirmed: a.boolean().default(false),
    clientFirstName: a.string(),
    clientLastName: a.string(),
    clientDateOfBirth: a.string(),
    selectedClient: a.ref('MedicalIncidentClient'),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    sendFYIEmails: a.boolean().default(false),
    sendHospitalEmail: a.boolean().default(false),
    clientDoesNotExist: a.boolean().default(false),
    deliveryAttempts: a.ref('MedicalIncidentDelivery').array(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.owner(),
    allow.groups(['CALLCENTER']).to(['create', 'read', 'update', 'delete']),
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
  ])
  .disableOperations(['subscriptions']);

export const MedicalReport = a
  .model({
    id: a.id().required(),
    userId: a.string().required(),
    clientFirstName: a.string(),
    clientLastName: a.string(),
    clientDateOfBirth: a.string(),
    selectedClient: a.ref('MedicalIncidentClient'),
    medicalNotes: a.string().required(),
    medicalPOAReviewed: a.boolean().default(false),
    medicalCareReportReviewed: a.boolean().default(false),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.owner(),
    allow.groups(['MEDICALREVIEW']).to(['create', 'read', 'update', 'delete']),
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
  ])
  .disableOperations(['subscriptions']);
