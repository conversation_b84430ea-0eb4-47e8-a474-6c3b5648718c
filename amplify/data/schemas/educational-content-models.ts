import { a } from '@aws-amplify/backend';

export const EducationalContent = a
  .model({
    title: a.string().required(),
    type: a.enum(['video', 'article', 'infographic', 'avatar', 'tooltip']),
    status: a.enum(['draft', 'published', 'archived']),
    description: a.string(),
    tags: a.string().array(),
    contentUrl: a.string(), // URL for video, article content, infographic image, etc.
    thumbnailUrl: a.string(),
    duration: a.integer(), // For videos - duration in seconds
    readingTime: a.integer(), // For articles - reading time in minutes
    metadata: a.json(), // Additional metadata specific to content type
    // Display location flags for video content
    showInEducationalContent: a.boolean().default(false), // Show in educational-content page
    showInInterviewHints: a.boolean().default(false), // Show as hints in interview questions
    showInOnboarding: a.boolean().default(false), // Show in onboarding page (only one video can have this)
    // Interview questions/sections where this video should appear as hint
    interviewQuestions: a.string().array(), // Array of question/section IDs
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    version: a.integer().default(1),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
    allow.group('WELONTRUST').to(['create', 'read', 'update']),
  ])
  .disableOperations(['subscriptions']);
