import { a } from '@aws-amplify/backend';

/**
 * Subscription management related models
 */

// Subscription plan enum
export const SubscriptionPlan = a.enum(['BASIC', 'PRO']);

// Subscription status enum
export const SubscriptionStatus = a.enum([
  'ACTIVE',
  'INACTIVE',
  'PAST_DUE',
  'CANCELED',
  'INCOMPLETE',
  'TRIALING',
]);

// User subscription model
export const UserSubscription = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    cognitoId: a.string().required(),
    stripeCustomerId: a.string().required(),
    stripeSubscriptionId: a.string().required(),
    plan: SubscriptionPlan,
    status: SubscriptionStatus,
    amount: a.integer().required(), // Amount in cents
    currency: a.string().required().default('usd'),
    currentPeriodStart: a.datetime().required(),
    currentPeriodEnd: a.datetime().required(),
    cancelAtPeriodEnd: a.boolean().default(false),
    canceledAt: a.datetime(),
    trialStart: a.datetime(),
    trialEnd: a.datetime(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .identifier(['id'])
  .authorization(allow => [allow.group('ADMINS').to(['read'])]);

// .authorization(allow => [allow.guest()]); // for tests
// .authorization(allow => [
//   allow.ownerDefinedIn('cognitoId'),
//   allow.group('ADMINS'),
// ]);
