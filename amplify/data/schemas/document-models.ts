import { a } from '@aws-amplify/backend';

const DocumentStatus = a.enum([
  'draft',
  'sent',
  'approved',
  'rejected',
  'signed',
  'shipped',
  'received',
  'archived',
]);

export const Document = a
  .model({
    id: a.id(),
    title: a.string().required(),
    type: a.enum([
      'Will',
      'Trust',
      'Financial_POA',
      'Healthcare_POA',
      'Advance_Directive',
      'Other',
    ]),
    status: DocumentStatus,
    dateCreated: a.datetime().required(),
    lastModified: a.datetime(),
    version: a.string().required(),
    content: a.string().required(),
    userId: a.string().required(),
    fileUrl: a.string(),
    trackingNumber: a.string(),
    signatureType: a.enum(['manual', 'electronic', 'notarized']),
    executionDate: a.datetime(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    // Template information
    templateId: a.string(),
    templateVersion: a.integer(), // Version of template used when document was created
    documentState: a.string(),
    // Signed document storage
    signedDocumentUrl: a.string(),
    signedAt: a.datetime(),
    documentHash: a.string(),
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrustId: a.string(),
    rejectionReason: a.string(),
  })
  .identifier(['id'])
  .secondaryIndexes(index => [index('userId'), index('templateId')])
  .authorization(allow => [
    allow.owner().identityClaim('externalId'),
    allow
      .ownerDefinedIn('assignedWelonTrustId')
      .to(['read', 'update'])
      .identityClaim('externalId'),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const DocumentUpdateLog = a
  .model({
    id: a.id(),
    documentId: a.string().required(),
    userId: a.string().required(),
    changeType: a.enum([
      'Created',
      'Updated',
      'Reviewed',
      'Archived',
      'Deleted',
    ]),
    changeDescription: a.string(),
    previousVersion: a.integer(),
    newVersion: a.integer(),
    timestamp: a.datetime().required(),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()])
  .disableOperations(['subscriptions']);

// Define the section types for the CareDocumentNew model
const CareDocumentSectionType = a.enum([
  'PersonalIdentification',
  'Employment',
  'Medical',
  'EmergencyContacts',
  'EmergencyIncidents',
  'Pets',
  'Financial',
  'Insurance',
  'Property',
  'AssetInformation',
  'ImportantToMe',
  'LongTermCare',
  'LastWishes',
  'Legacy',
  'Uploads',
]);

export const UploadedFile = a.customType({
  fileKey: a.string(),
  fileName: a.string(),
  fileType: a.string(),
  fileSize: a.integer(),
  reference: a.string(),
});

export const CareDocumentSectionData = a.customType({
  sectionType: CareDocumentSectionType,
  content: a.string(), // JSON string for section data
  uploadedFiles: a.ref('UploadedFile').array(),
});

// New comprehensive care document model
export const CareDocumentNew = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    version: a.integer().default(1),
    data: a.ref('CareDocumentSectionData').array(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrustId: a.string(),
  })
  .identifier(['id'])
  .authorization(allow => [
    allow.owner(), 
    allow.group('ADMINS'),
    allow.ownerDefinedIn('assignedWelonTrustId').to(['read']).identityClaim('externalId')
  ])
  .disableOperations(['subscriptions']);
