import { a } from '@aws-amplify/backend';

/**
 * Template management related models
 */

export const Template = a
  .model({
    id: a.string().required(),
    createdBy: a.string().required(),
    createdByEmail: a.string(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    type: a.string().required(),
    templateState: a.string(),
    templateStates: a.string().array(),
    isGeneral: a.boolean().default(false),
    templateName: a.string().required(),
    isActive: a.boolean().default(true),
    isDraft: a.boolean().default(true),
    isAdditional: a.boolean().default(false),
    startPageTemplateId: a.string(),
    endPageTemplateId: a.string(),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const TemplateVersion = a
  .model({
    templateId: a.string().required(),
    versionNumber: a.integer().required(),
    content: a.string().required(),
    createdAt: a.datetime().required(),
  })
  .identifier(['templateId', 'versionNumber'])
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const TemplateSection = a
  .model({
    id: a.string().required(),
    templateId: a.string().required(),
    name: a.string().required(),
    description: a.string(),
    sectionType: a.enum(['text', 'conditional', 'repeatable', 'custom']),
    orderIndex: a.integer().required(),
    isRequired: a.boolean().default(false),
    conditionalLogic: a.json(), // Store conditional mapping rules
    createdBy: a.string().required(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    isActive: a.boolean().default(true),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);

export const SectionVersion = a
  .model({
    sectionId: a.string().required(),
    versionNumber: a.integer().required(),
    content: a.string(), // For small content, stored in DB
    s3ContentKey: a.string(), // For large content, stored in S3 with KMS encryption
    contentType: a.enum(['inline', 's3_encrypted']),
    createdAt: a.datetime().required(),
    createdBy: a.string().required(),
  })
  .identifier(['sectionId', 'versionNumber'])
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ])
  .disableOperations(['subscriptions']);
