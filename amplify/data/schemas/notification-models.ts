import { a } from '@aws-amplify/backend';

// TODO REFACTOR NOTIFICATION AUTH RULES
export const Notification = a
  .model({
    id: a.id(),
    message: a.string().required(),
    recipient: a.string().required(),
    author: a.string().required(),
    createdAt: a.datetime().required(),
    isRead: a.boolean().default(false),
  })
  .secondaryIndexes(index => [index('recipient'), index('createdAt')])
  .authorization(allow => [
    allow
      .ownerDefinedIn('recipient')
      .to(['read', 'update'])
      .identityClaim('externalId'),
    allow.authenticated().to(['create']),
    allow
      .groups(['ADMINS', 'WELONTRUST'])
      .to(['create', 'read', 'update', 'delete']),
  ]);
