import { a } from '@aws-amplify/backend';

/**
 * Attorney management related models
 */

// Attorney status enum
export const AttorneyStatus = a.enum(['active', 'inactive', 'pending']);

// Main Attorney model
export const Attorney = a
  .model({
    name: a.string().required(),
    firm: a.string(),
    phone: a.string().required(),
    email: a.string().required(),
    address: a.string(),
    city: a.string(),
    state: a.string().required(),
    zipCode: a.string(),
    specialties: a.string().array(), // Array of specialty strings
    barNumber: a.string(),
    yearsExperience: a.integer().default(0),
    rating: a.float().default(0),
    isPreferred: a.boolean().default(false),
    isActive: a.boolean().default(true),
    status: a.enum(['active', 'inactive', 'pending']),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime(),
  })
  .authorization(allow => [allow.authenticated()])
  .disableOperations(['subscriptions']);
