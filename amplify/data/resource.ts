import {
  type ClientSchema,
  a,
  defineData,
  defineFunction,
} from '@aws-amplify/backend';

// Import all model definitions from schemas
import {
  UserOnboarding,
  AdminResource,
  LoginAttempt,
  LoginHistory,
  VerificationTokens,
  Template,
  TemplateVersion,
  EmergencyContact,
  CareDocumentNew,
  CareDocumentSectionData,
  UploadedFile,
  Document,
  DocumentUpdateLog,
  VaultAccess,
  User,
  WelonTrustAssignment,
  LinkedAccount,
  LinkedAccountWithSharedFields,
  MedicalIncident,
  MedicalReport,
  Attorney,
  EvidenceSubmission,
  EducationalContent,
  DeadMansSwitch,
  CommunicationMethod,
  EscalationProtocol,
  DMSStatus,
  CheckInFrequency,
  CheckInHistory,
  EscalationEvent,
  UserInterviewProgressNew,
  MedicalIncidentClient,
  MedicalIncidentDelivery,
  Address,
  UserAnswer,
  UserInvite,
  UserSubscription,
  Notification,
  ShippingLabel,
  MemberNote,
  TemplateSection,
  SectionVersion,
  InterviewProgressV2,
  PromoCode,
} from './schemas';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generatePdfDoc } from '../functions/generatePdfDoc/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { sendEmail } from '../functions/sendEmail/resource';
import { initiatePhoneVerification } from '../functions/initiatePhoneVerification/resource';
import { verifyPhoneCode } from '../functions/verifyPhoneCode/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';
import { exportDocumentUpdateLogs } from '../functions/exportDocumentUpdateLogs/resource';
import { signOutDevice } from '../functions/signOutDevice/resource';
import { getSecrets } from '../functions/get-secrets/resource';
import { inviteUser } from '../functions/inviteUser/inviteUser/resource';
import { readInvite } from '../functions/inviteUser/readInvite/resource';
import { acceptInvite } from '../functions/inviteUser/acceptInvite/resource';
import { verifyAndFetchDocuments } from '../functions/verifyAndFetchDocuments/resource';
import { deleteUserComplete } from '../functions/deleteUserComplete/resource';
import { dmsCheck } from '../functions/scheduler-functions/dms-check/resource';
import { documentSigningReminder } from '../functions/scheduler-functions/document-signing-reminder/resource';
import { getEmergencyFileUrl } from '../functions/getEmergencyFileUrl/resource';
import { addUserToGroupV3 } from '../functions/addUserToGroupV3/resource';
import { checkIsValid } from '../functions/emailToken/checkIsValid/resource';
import { getWelonTrustInfo } from '../functions/getWelonTrustInfo/resource';
import { sendMedicalIncident } from '../functions/sendMedicalIncident/resource';
import { getMedicalDataForReview } from '../functions/getMedicalDataForReview/resource';
import { createUPSLabel } from '../functions/ups-functions/create-label/resource';
import { trackUPSPackage } from '../functions/ups-functions/track-package/resource';
import { getSubscription } from '../functions/stripe-functions/get-subscription/resource';
import { createCheckout } from '../functions/stripe-functions/create-checkout/resource';
import { cancelSubscription } from '../functions/stripe-functions/cancel-subscription/resource';
import { processWebhook } from '../functions/stripe-functions/process-webhook/resource';
import { adminTransactions } from '../functions/stripe-functions/admin-transactions/resource';
import { adminActivateSubscription } from '../functions/stripe-functions/admin-activate-subscription/resource';
import { verifyPromoCode } from '../functions/verifyPromoCode/resource';
import { resetUserPassword } from '../functions/resetUserPassword/resource';
import { markTokenAsUsed } from '../functions/emailToken/markTokenAsUsed/resource';

/**
 * You can use the secret SQL_CONNECTION_STRING to get access to the
 * DB for any code push. If you want to use a differencet user or non-default DB
 * then let me know. The default DB is call cfl_dev_db and the credentials will
 * be sent outside of the code.
 */

const schema = a
  .schema({
    // Authentication and user onboarding models
    UserOnboarding,
    AdminResource,
    LoginAttempt,
    LoginHistory,
    VerificationTokens,

    // Template management models
    Template,
    TemplateVersion,
    TemplateSection,
    SectionVersion,

    // Emergency contact models
    EmergencyContact,

    // Care document models
    CareDocumentNew,
    CareDocumentSectionData,
    UploadedFile,
    DocumentUpdateLog,
    Document,

    // Vault access models
    VaultAccess,

    // User management models
    User,
    Address,
    ShippingLabel,
    WelonTrustAssignment,
    MemberNote,
    LinkedAccount,
    LinkedAccountWithSharedFields,
    MedicalIncident,
    MedicalReport,
    UserInterviewProgressNew,
    MedicalIncidentClient,
    MedicalIncidentDelivery,
    UserAnswer,
    InterviewProgressV2,
    // TABLES AND CUSTOM RESOURCES FOR CREATION AND WORK WITH USER INVITES
    UserInvite,
    inviteUser: a
      .mutation()
      .arguments({
        firstName: a.string().required(),
        middleName: a.string(),
        lastName: a.string().required(),
        email: a.string().required(),
        role: a.string().required(),
        subrole: a.string().required(),
        sharedFields: a.string(),
        userId: a.id(),
        baseUrl: a.string(),
        inviterEmail: a.string(),
      })
      .authorization(allow => [allow.group('ADMINS'), allow.authenticated()])
      .handler(a.handler.function(inviteUser))
      .returns(a.json()),
    readInvite: a
      .query()
      .arguments({
        token: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(readInvite))
      .returns(a.ref('UserInvite')),
    acceptInvite: a
      .mutation()
      .arguments({
        inviteToken: a.string().required(),
        email: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(acceptInvite))
      .returns(a.json()),
    deleteUserComplete: a
      .mutation()
      .arguments({
        userId: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(deleteUserComplete))
      .returns(a.json()),

    // Attorney management models
    Attorney,

    // Evidence submission models
    EvidenceSubmission,

    // Educational content models
    EducationalContent,

    // Notification models
    Notification,

    // Subscription models
    UserSubscription,

    // Promo code models
    PromoCode,

    addUserToGroupV3: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        groupName: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(addUserToGroupV3))
      .returns(a.json()),

    checkEmail: a
      .query()
      .arguments({
        email: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(checkEmail))
      .returns(a.json()),

    trackLoginAttempt: a
      .mutation()
      .arguments({
        email: a.string().required(),
        success: a.boolean().required(),
        action: a.string().required(),
        ipAddress: a.string(),
        userAgent: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    checkAccountLockout: a
      .query()
      .arguments({
        email: a.string().required(),
        action: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    generatePdfDoc: a
      .mutation()
      .arguments({
        documentId: a.string().required(),
        html: a.string().required(),
        isSignedVersion: a.boolean(),
        signatureData: a.string(),
        userId: a.string(), // Optional user ID for S3 path
        signatureType: a.string(), // Add signature type parameter
        memberId: a.string(), // Add member ID for metadata
        documentVersion: a.string(), // Add document version for metadata
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(generatePdfDoc))
      .returns(a.json()),

    sendEmail: a
      .mutation()
      .arguments({
        to: a.string().required(),
        subject: a.string().required(),
        message: a.string().required(),
        verificationLink: a.string(),
        emailType: a.string(),
        isNewAccount: a.boolean(),
        baseUrl: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(sendEmail))
      .returns(a.json()),

    generateVerificationToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
        baseUrl: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(generateVerificationToken))
      .returns(a.json()),

    verifyEmailToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
        newPassword: a.string(), // Required for passwordReset
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyEmailToken))
      .returns(a.json()),

    resetUserPassword: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        newPassword: a.string(), // Required for passwordReset
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(resetUserPassword))
      .returns(a.json()),

    verifyPromoCode: a
      .query()
      .arguments({
        promoCode: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyPromoCode))
      .returns(a.boolean()),

    checkIsValid: a
      .query()
      .arguments({
        token: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(checkIsValid))
      .returns(a.boolean()),

    markTokenAsUsed: a
      .mutation()
      .arguments({
        token: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(markTokenAsUsed))
      .returns(a.boolean()),

    initiatePhoneVerification: a
      .mutation()
      .arguments({
        phoneNumber: a.string().required(),
        email: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(initiatePhoneVerification))
      .returns(a.json()),

    verifyPhoneCode: a
      .mutation()
      .arguments({
        phoneNumber: a.string().required(),
        verificationCode: a.string().required(),
        email: a.string().required(),
        userId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(verifyPhoneCode))
      .returns(a.json()),

    verifyAndFetchDocuments: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyAndFetchDocuments))
      .returns(a.json()),

    getEmergencyFileUrl: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        fileKey: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(getEmergencyFileUrl))
      .returns(a.json()),

    activateDeactivateUser: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        activate: a.boolean().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(activateDeactivateUser))
      .returns(a.json()),

    exportDocumentUpdateLogs: a
      .query()
      .arguments({
        startDate: a.datetime(),
        endDate: a.datetime(),
        userId: a.string(),
        changeType: a.string(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(exportDocumentUpdateLogs))
      .returns(a.json()),

    signOutDevice: a
      .mutation()
      .arguments({
        deviceId: a.string().required(),
        username: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(signOutDevice))
      .returns(a.json()),

    getSecrets: a
      .query()
      .arguments({
        secretName: a.string().required(),
      })
      .authorization(allow => [allow.publicApiKey()])
      .handler(a.handler.function(getSecrets))
      .returns(a.json()),

    getWelonTrustInfo: a
      .query()
      .arguments({
        userId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(getWelonTrustInfo))
      .returns(a.json()),

    sendMedicalIncident: a
      .mutation()
      .arguments({
        incidentId: a.string().required(),
        emailType: a.string().required(),
        clientData: a.customType({
          firstName: a.string(),
          lastName: a.string(),
          birthdate: a.string(),
        }),
      })
      .authorization(allow => [
        allow.group('CALLCENTER'),
        allow.group('ADMINS'),
      ])
      .handler(a.handler.function(sendMedicalIncident))
      .returns(a.json()),

    getMedicalDataForReview: a
      .query()
      .arguments({
        userId: a.string().required(),
      })
      .authorization(allow => [
        allow.group('MEDICALREVIEW'),
        allow.group('ADMINS'),
      ])
      .handler(a.handler.function(getMedicalDataForReview))
      .returns(a.json()),

    createUPSLabel: a
      .mutation()
      .arguments({
        fromAddress: a.json().required(),
        toAddress: a.json().required(),
        serviceCode: a.string(),
        packageOptions: a.json(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(createUPSLabel))
      .returns(a.json()),

    trackUPSPackage: a
      .query()
      .arguments({
        trackingNumber: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(trackUPSPackage))
      .returns(a.json()),

    getSubscription: a
      .query()
      .arguments({
        userId: a.string().required(),
        cognitoId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(getSubscription))
      .returns(a.json()),

    createCheckout: a
      .mutation()
      .arguments({
        plan: a.string().required(),
        userEmail: a.string().required(),
        userId: a.string().required(),
        cognitoId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(createCheckout))
      .returns(a.json()),

    cancelSubscription: a
      .mutation()
      .arguments({
        subscriptionId: a.string().required(),
        userId: a.string().required(),
        cognitoId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(cancelSubscription))
      .returns(a.json()),

    processWebhook: a
      .mutation()
      .arguments({
        body: a.string().required(), // Сирий body від Stripe
        signature: a.string().required(), // Stripe signature header
      })
      .authorization(allow => [allow.guest()]) // Дозволяємо guest доступ для API route
      .handler(a.handler.function(processWebhook))
      .returns(a.json()),

    getAdminTransactions: a
      .query()
      .arguments({
        limit: a.integer(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(adminTransactions))
      .returns(a.json()),

    adminActivateSubscription: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        cognitoId: a.string().required(),
        plan: a.string().required(), // 'BASIC' or 'PRO'
        email: a.string().required(),
        currency: a.string(),
        trialDays: a.integer().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(adminActivateSubscription))
      .returns(a.json()),

    DeadMansSwitch,
    CheckInFrequency,
    CommunicationMethod,
    EscalationProtocol,
    DMSStatus,
    CheckInHistory,
    EscalationEvent,
  })
  .authorization(allow => [
    allow.resource(dmsCheck).to(['query', 'mutate']),
    allow.resource(documentSigningReminder).to(['query', 'mutate']),
    allow.resource(inviteUser).to(['query', 'mutate']),
    allow.resource(readInvite).to(['query']),
    allow.resource(acceptInvite).to(['query', 'mutate']),
    allow.resource(postSignUp).to(['query', 'mutate']),
    allow.resource(exportDocumentUpdateLogs).to(['query']),
    allow.resource(addUserToGroupV3).to(['query', 'mutate']),
    allow.resource(checkEmail).to(['query']),
    allow.resource(trackLoginAttempt).to(['query', 'mutate']),
    allow.resource(generatePdfDoc).to(['query', 'mutate']),
    allow.resource(sendEmail).to(['mutate']),
    allow.resource(generateVerificationToken).to(['query', 'mutate']),
    allow.resource(verifyEmailToken).to(['query', 'mutate']),
    allow.resource(checkIsValid).to(['query']),
    allow.resource(initiatePhoneVerification).to(['mutate']),
    allow.resource(verifyPhoneCode).to(['query', 'mutate']),
    allow.resource(activateDeactivateUser).to(['query', 'mutate']),
    allow.resource(getSecrets).to(['query']),
    allow.resource(createUPSLabel).to(['mutate']),
    allow.resource(trackUPSPackage).to(['query']),
    allow.resource(getSubscription).to(['query', 'mutate']),
    allow.resource(createCheckout).to(['query', 'mutate']),
    allow.resource(cancelSubscription).to(['query', 'mutate']),
    allow.resource(processWebhook).to(['query', 'mutate']),
    allow.resource(adminTransactions).to(['query', 'mutate']),
    allow.resource(adminActivateSubscription).to(['query', 'mutate']),
    allow.resource(verifyAndFetchDocuments).to(['query', 'mutate']),
    allow.resource(getEmergencyFileUrl).to(['query', 'mutate']),
    allow.resource(deleteUserComplete).to(['query', 'mutate']),
    allow.resource(getWelonTrustInfo).to(['query']),
    allow.resource(sendMedicalIncident).to(['query', 'mutate']),
    allow.resource(getMedicalDataForReview).to(['query']),
    allow.resource(verifyPromoCode).to(['query']),
    allow.resource(resetUserPassword).to(['query', 'mutate']),
    allow.resource(markTokenAsUsed).to(['query', 'mutate']),
  ]);

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: 'userPool',
    lambdaAuthorizationMode: {
      function: defineFunction({
        entry: './custom-authorizer.ts',
      }),
    },
    apiKeyAuthorizationMode: {
      expiresInDays: 30,
    },
  },
});
