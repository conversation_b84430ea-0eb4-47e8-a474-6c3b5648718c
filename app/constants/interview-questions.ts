/**
 * Interview Questions and Sections for Video Hints
 *
 * This file defines all available questions and sections where video hints can be displayed
 */

export interface InterviewQuestionOption {
  id: string;
  label: string;
  section: string;
}

export const INTERVIEW_QUESTIONS: InterviewQuestionOption[] = [
  // Profile Section
  {
    id: 'profile.personal-info',
    label: 'Personal Information',
    section: 'Profile',
  },
  { id: 'profile.address', label: 'Address Information', section: 'Profile' },
  { id: 'profile.marital-status', label: 'Marital Status', section: 'Profile' },
  {
    id: 'profile.spouse-info',
    label: 'Spouse Information',
    section: 'Profile',
  },
  {
    id: 'profile.sound-mind',
    label: 'Sound Mind Declaration',
    section: 'Profile',
  },
  {
    id: 'profile.document-selection',
    label: 'Document Selection',
    section: 'Profile',
  },

  // Emergency Section
  { id: 'emergency.dependents', label: 'Dependents', section: 'Emergency' },
  { id: 'emergency.pets', label: 'Pet Care', section: 'Emergency' },

  // After You Die Section
  {
    id: 'afterYouDie.executor',
    label: 'Executor Selection',
    section: 'After You Die',
  },
  {
    id: 'afterYouDie.successor-trustee',
    label: 'Successor Trustee',
    section: 'After You Die',
  },
  {
    id: 'afterYouDie.properties',
    label: 'Property Distribution',
    section: 'After You Die',
  },
  {
    id: 'afterYouDie.specific-gifts',
    label: 'Specific Gifts',
    section: 'After You Die',
  },
  {
    id: 'afterYouDie.main-beneficiaries',
    label: 'Main Beneficiaries',
    section: 'After You Die',
  },
  {
    id: 'afterYouDie.last-resort',
    label: 'Beneficiary of Last Resort',
    section: 'After You Die',
  },

  // Financial Decisions Section
  {
    id: 'financial.agent-selection',
    label: 'Financial Agent Selection',
    section: 'Financial Decisions',
  },
  {
    id: 'financial.powers',
    label: 'Financial Powers',
    section: 'Financial Decisions',
  },

  // Medical Decisions Section
  {
    id: 'medical.proxy-selection',
    label: 'Medical Proxy Selection',
    section: 'Medical Decisions',
  },
  {
    id: 'medical.directives',
    label: 'Medical Directives',
    section: 'Medical Decisions',
  },
  {
    id: 'medical.proxy-authority',
    label: 'Proxy Authority',
    section: 'Medical Decisions',
  },
  {
    id: 'medical.organ-donation',
    label: 'Organ Donation',
    section: 'Medical Decisions',
  },

  // Additional Considerations Section
  {
    id: 'additional.personal-property',
    label: 'Personal Property',
    section: 'Additional Considerations',
  },
  {
    id: 'additional.burial-preferences',
    label: 'Burial Preferences',
    section: 'Additional Considerations',
  },
  {
    id: 'additional.guardian',
    label: 'Guardian Selection',
    section: 'Additional Considerations',
  },
  {
    id: 'additional.legal-provisions',
    label: 'Legal Provisions',
    section: 'Additional Considerations',
  },
];

// Group questions by section for easier display
export const QUESTIONS_BY_SECTION = INTERVIEW_QUESTIONS.reduce(
  (acc, question) => {
    if (!acc[question.section]) {
      acc[question.section] = [];
    }
    acc[question.section].push(question);
    return acc;
  },
  {} as Record<string, InterviewQuestionOption[]>
);

// Get question by ID
export const getQuestionById = (
  id: string
): InterviewQuestionOption | undefined => {
  return INTERVIEW_QUESTIONS.find(q => q.id === id);
};

// Get questions for a specific section
export const getQuestionsBySection = (
  section: string
): InterviewQuestionOption[] => {
  return QUESTIONS_BY_SECTION[section] || [];
};
