'use client';

import { useCallback, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { saveInterviewProgressV2 } from '@/app/utils/interviewV2Progress';

interface UseUserActionSaveOptions {
  data: any;
  currentStep: string;
  onSaveSuccess?: (data: any) => void; // Callback when save is successful
  shouldMarkAsCompleted?: (data: any) => boolean; // Function to check if interview should be completed
}

export function useUserActionSave({
  data,
  currentStep,
  onSaveSuccess,
  shouldMarkAsCompleted,
}: UseUserActionSaveOptions) {
  const queryClient = useQueryClient();
  const lastSavedDataRef = useRef<string>('');
  const pendingSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const dataRef = useRef(data);
  const currentStepRef = useRef(currentStep);

  // Keep refs updated
  dataRef.current = data;
  currentStepRef.current = currentStep;

  const saveMutation = useMutation({
    mutationFn: async (payload: { stepsData: any; currentStep: string }) => {
      console.log('useUserActionSave: Starting save mutation:', {
        currentStep: payload.currentStep,
        stepsDataKeys: Object.keys(payload.stepsData || {}),
        profileData: payload.stepsData?.profile,
        timestamp: new Date().toLocaleTimeString(),
      });

      // Check if interview should be marked as completed
      const shouldComplete = shouldMarkAsCompleted
        ? shouldMarkAsCompleted(payload.stepsData)
        : false;
      const status = shouldComplete ? 'completed' : 'in_progress';

      console.log('useUserActionSave: Status check:', {
        shouldComplete,
        status,
        hasCompletionChecker: !!shouldMarkAsCompleted,
        timestamp: new Date().toLocaleTimeString(),
      });

      const result = await saveInterviewProgressV2({
        status,
        currentStep: payload.currentStep,
        stepsData: payload.stepsData,
      });

      console.log('useUserActionSave: Save mutation completed:', {
        resultStatus: result.status,
        resultCurrentStep: result.currentStep,
        timestamp: new Date().toLocaleTimeString(),
      });

      return result;
    },
    onSuccess: savedData => {
      queryClient.setQueryData(['interviewV2Progress'], savedData);
      // Invalidate user profile cache to sync changes across the app
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      // Invalidate mental capacity check cache to ensure Document Management page reflects changes
      queryClient.invalidateQueries({ queryKey: ['mentalCapacityCheck'] });
      onSaveSuccess?.(savedData);
      console.log(
        'User action save successful at',
        new Date().toLocaleTimeString(),
        'Saved data:',
        savedData
      );
    },
    onError: error => {
      console.error('User action save failed:', {
        error,
        timestamp: new Date().toLocaleTimeString(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });
    },
  });

  // Save after user action with debounce
  const saveAfterUserAction = useCallback(() => {
    // Clear any pending save
    if (pendingSaveTimeoutRef.current) {
      clearTimeout(pendingSaveTimeoutRef.current);
    }

    console.log('useUserActionSave: Scheduling save after user action');

    // Schedule save with shorter debounce
    pendingSaveTimeoutRef.current = setTimeout(() => {
      // Get the latest data at save time
      const currentData = dataRef.current;
      const currentStepValue = currentStepRef.current;

      if (!currentData || Object.keys(currentData).length === 0) {
        console.log('useUserActionSave: No data to save at execution time');
        return;
      }

      // Check if data has actually changed at the time of save
      const currentDataString = JSON.stringify(currentData);
      if (currentDataString === lastSavedDataRef.current) {
        console.log(
          'useUserActionSave: No changes detected at save time, skipping save'
        );
        return;
      }

      console.log('useUserActionSave: Executing scheduled save with data:', {
        currentStep: currentStepValue,
        dataKeys: Object.keys(currentData),
        profileData: currentData.profile,
      });

      lastSavedDataRef.current = currentDataString;
      saveMutation.mutate({
        stepsData: currentData,
        currentStep: currentStepValue,
      });
    }, 1000); // 1 second debounce (reduced from 2 seconds)
  }, [saveMutation]);

  // Force save immediately (useful for navigation)
  const forceSave = useCallback(async () => {
    // Clear any pending save
    if (pendingSaveTimeoutRef.current) {
      clearTimeout(pendingSaveTimeoutRef.current);
    }

    const currentData = dataRef.current;
    const currentStepValue = currentStepRef.current;

    if (currentData && Object.keys(currentData).length > 0) {
      const currentDataString = JSON.stringify(currentData);
      lastSavedDataRef.current = currentDataString;

      return saveMutation.mutateAsync({
        stepsData: currentData,
        currentStep: currentStepValue,
      });
    }
  }, [saveMutation]);

  return {
    isSaving: saveMutation.isPending,
    isError: saveMutation.isError,
    error: saveMutation.error,
    forceSave,
    saveAfterUserAction,
  };
}
