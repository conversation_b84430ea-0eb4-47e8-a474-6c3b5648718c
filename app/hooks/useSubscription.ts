'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { StripeUserSubscription } from '@/components/billing/types';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import * as Sentry from '@sentry/nextjs';

const client = generateClient<Schema>();

interface UseSubscriptionReturn {
  subscription: StripeUserSubscription | null;
  hasActiveSubscription: boolean;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useSubscription(): UseSubscriptionReturn {
  const { user, userId } = useAuth(); // userId is the database ID, user.userId is cognitoId
  const [subscription, setSubscription] =
    useState<StripeUserSubscription | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    console.log('🚀 [HOOK] fetchSubscription started');
    console.log('🔍 [HOOK] User authentication check:', {
      hasUser: !!user,
      userUserId: user?.userId,
      userId: userId,
      userEmail: user?.signInDetails?.loginId,
    });

    if (!user?.userId || !userId) {
      console.log('⚠️ [HOOK] User not authenticated, stopping fetch');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('📡 [HOOK] Making getSubscription Lambda call with params:', {
        userId: userId,
        cognitoId: user.userId,
      });

      console.log('🔍 [HOOK] Raw user data:', user);

      const { data: subscriptionResult, errors: subscriptionErrors } =
        await client.queries.getSubscription({
          userId: userId,
          cognitoId: user.userId,
        });

      console.log('📥 [HOOK] Lambda response received:', {
        hasData: !!subscriptionResult,
        hasErrors: !!subscriptionErrors,
        rawResult: subscriptionResult,
        errors: subscriptionErrors ? JSON.stringify(subscriptionErrors) : null,
      });

      if (subscriptionErrors) {
        console.error(
          '❌ [HOOK] Subscription Lambda errors:',
          subscriptionErrors
        );
        throw new Error('Failed to fetch subscription');
      }

      // Parse the result - Lambda returns JSON string
      console.log('🔧 [HOOK] Parsing Lambda result...');
      let parsedResult;
      try {
        console.log('🔧 [HOOK] Raw result type:', typeof subscriptionResult);
        console.log('🔧 [HOOK] Raw result value:', subscriptionResult);

        const parsed = JSON.parse(String(subscriptionResult));
        console.log('🔧 [HOOK] First parse result type:', typeof parsed);

        // If it's still a string, parse again (double-encoded JSON)
        if (typeof parsed === 'string') {
          console.log('🔧 [HOOK] Result is still string, parsing again...');
          parsedResult = JSON.parse(parsed);
          console.log(
            '🔧 [HOOK] Double parse result type:',
            typeof parsedResult
          );
        } else {
          parsedResult = parsed;
        }

        console.log('🔧 [HOOK] Final parsed result:', parsedResult);
      } catch (error) {
        console.error('❌ [HOOK] Failed to parse subscription result:', error);
        Sentry.captureException(error);
        // If it's already an object, use it directly
        parsedResult = subscriptionResult;
        console.log('🔧 [HOOK] Using raw result as fallback:', parsedResult);
      }

      console.log('✅ [HOOK] Checking parsed result success:', {
        hasSuccess: 'success' in parsedResult,
        success: parsedResult.success,
        hasData: 'data' in parsedResult,
        error: parsedResult.error,
      });

      if (!parsedResult.success) {
        console.error('❌ [HOOK] Lambda returned failure:', parsedResult.error);
        throw new Error(parsedResult.error || 'Failed to fetch subscription');
      }

      console.log('📊 [HOOK] Setting subscription data:', {
        subscription: parsedResult.data.subscription,
        hasActiveSubscription: parsedResult.data.hasActiveSubscription,
      });

      setSubscription(parsedResult.data.subscription);
      setHasActiveSubscription(parsedResult.data.hasActiveSubscription);

      console.log('✅ [HOOK] fetchSubscription completed successfully');
    } catch (err) {
      console.error('❌ [HOOK] Error in fetchSubscription:', {
        error: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
      });
      Sentry.captureException(err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      setSubscription(null);
      setHasActiveSubscription(false);
    } finally {
      setLoading(false);
      console.log('🏁 [HOOK] fetchSubscription finished');
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [user?.userId]);

  return {
    subscription,
    hasActiveSubscription,
    loading,
    error,
    refetch: fetchSubscription,
  };
}

// Helper function to create checkout session
export async function createCheckoutSession(
  plan: 'BASIC' | 'PRO',
  userEmail: string,
  userId: string,
  cognitoId: string
) {
  console.log('🚀 [HELPER] createCheckoutSession started');
  console.log('🔍 [HELPER] Checkout parameters:', {
    plan,
    userEmail,
    userId,
    cognitoId,
  });

  try {
    console.log('📡 [HELPER] Calling createCheckout Lambda...');
    const { data: checkoutResult, errors: checkoutErrors } =
      await client.mutations.createCheckout({
        plan,
        userEmail,
        userId,
        cognitoId,
      });

    console.log('📥 [HELPER] Lambda response received:', {
      hasData: !!checkoutResult,
      hasErrors: !!checkoutErrors,
      rawResult: checkoutResult,
      errors: checkoutErrors ? JSON.stringify(checkoutErrors) : null,
    });

    if (checkoutErrors) {
      console.error('❌ [HELPER] Checkout Lambda errors:', checkoutErrors);
      throw new Error('Failed to create checkout session');
    }

    // Parse the result - Lambda returns JSON string
    console.log('🔧 [HELPER] Parsing checkout result...');
    let parsedResult;
    try {
      console.log(
        '🔧 [HELPER] Raw checkout result type:',
        typeof checkoutResult
      );
      const parsed = JSON.parse(String(checkoutResult));
      console.log('🔧 [HELPER] First parse result type:', typeof parsed);

      // If it's still a string, parse again (double-encoded JSON)
      if (typeof parsed === 'string') {
        console.log('🔧 [HELPER] Result is still string, parsing again...');
        parsedResult = JSON.parse(parsed);
        console.log(
          '🔧 [HELPER] Double parse result type:',
          typeof parsedResult
        );
      } else {
        parsedResult = parsed;
      }

      console.log('🔧 [HELPER] Final parsed checkout result:', parsedResult);
    } catch (error) {
      console.error('❌ [HELPER] Failed to parse checkout result:', error);
      Sentry.captureException(error);
      // If it's already an object, use it directly
      parsedResult = checkoutResult;
      console.log(
        '🔧 [HELPER] Using raw checkout result as fallback:',
        parsedResult
      );
    }

    console.log('✅ [HELPER] Checking checkout result success:', {
      hasSuccess: 'success' in parsedResult,
      success: parsedResult.success,
      hasData: 'data' in parsedResult,
      error: parsedResult.error,
    });

    if (!parsedResult.success) {
      console.error(
        '❌ [HELPER] Checkout Lambda returned failure:',
        parsedResult.error
      );
      throw new Error(
        parsedResult.error || 'Failed to create checkout session'
      );
    }

    console.log(
      '✅ [HELPER] createCheckoutSession completed successfully:',
      parsedResult.data
    );
    return parsedResult.data;
  } catch (error) {
    console.error('❌ [HELPER] Error in createCheckoutSession:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    Sentry.captureException(error);
    throw error;
  }
}

// Helper function to cancel subscription
export async function cancelSubscription(
  subscriptionId: string,
  userId: string,
  cognitoId: string
) {
  console.log('🚀 [HELPER] cancelSubscription started');
  console.log('🔍 [HELPER] Cancel parameters:', {
    subscriptionId,
    userId,
    cognitoId,
  });

  try {
    console.log('📡 [HELPER] Calling cancelSubscription Lambda...');
    const { data: cancelResult, errors: cancelErrors } =
      await client.mutations.cancelSubscription({
        subscriptionId,
        userId,
        cognitoId,
      });

    console.log('📥 [HELPER] Lambda response received:', {
      hasData: !!cancelResult,
      hasErrors: !!cancelErrors,
      rawResult: cancelResult,
      errors: cancelErrors ? JSON.stringify(cancelErrors) : null,
    });

    if (cancelErrors) {
      console.error(
        '❌ [HELPER] Cancel subscription Lambda errors:',
        cancelErrors
      );
      throw new Error('Failed to cancel subscription');
    }

    // Parse the result - Lambda returns JSON string
    console.log('🔧 [HELPER] Parsing cancel result...');
    let parsedResult;
    try {
      console.log('🔧 [HELPER] Raw cancel result type:', typeof cancelResult);
      const parsed = JSON.parse(String(cancelResult));
      console.log('🔧 [HELPER] First parse result type:', typeof parsed);

      // If it's still a string, parse again (double-encoded JSON)
      if (typeof parsed === 'string') {
        console.log('🔧 [HELPER] Result is still string, parsing again...');
        parsedResult = JSON.parse(parsed);
        console.log(
          '🔧 [HELPER] Double parse result type:',
          typeof parsedResult
        );
      } else {
        parsedResult = parsed;
      }

      console.log('🔧 [HELPER] Final parsed cancel result:', parsedResult);
    } catch (error) {
      console.error('❌ [HELPER] Failed to parse cancel result:', error);
      Sentry.captureException(error);
      // If it's already an object, use it directly
      parsedResult = cancelResult;
      console.log(
        '🔧 [HELPER] Using raw cancel result as fallback:',
        parsedResult
      );
    }

    console.log('✅ [HELPER] Checking cancel result success:', {
      hasSuccess: 'success' in parsedResult,
      success: parsedResult.success,
      hasData: 'data' in parsedResult,
      error: parsedResult.error,
    });

    if (!parsedResult.success) {
      console.error(
        '❌ [HELPER] Cancel Lambda returned failure:',
        parsedResult.error
      );
      throw new Error(parsedResult.error || 'Failed to cancel subscription');
    }

    console.log(
      '✅ [HELPER] cancelSubscription completed successfully:',
      parsedResult.data
    );
    return parsedResult.data;
  } catch (error) {
    console.error('❌ [HELPER] Error in cancelSubscription:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    Sentry.captureException(error);
    throw error;
  }
}
