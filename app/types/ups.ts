// UPS API Types and Interfaces

export interface UPSAddress {
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
  city: string;
  stateProvinceCode: string;
  postalCode: string;
  countryCode: string;
}

export interface UPSContact {
  name: string;
  phone?: string;
  email?: string;
}

export interface UPSShipper {
  name: string;
  attentionName?: string;
  phone?: string;
  shipperNumber?: string;
  address: UPSAddress;
}

export interface UPSShipTo {
  name: string;
  attentionName?: string;
  phone?: string;
  address: UPSAddress;
}

export interface UPSPackage {
  description?: string;
  packaging: {
    code: string; // "02" for Customer Supplied Package
    description?: string;
  };
  dimensions: {
    unitOfMeasurement: {
      code: string; // "IN" for inches
      description?: string;
    };
    length: string;
    width: string;
    height: string;
  };
  packageWeight: {
    unitOfMeasurement: {
      code: string; // "LBS" for pounds
      description?: string;
    };
    weight: string;
  };
}

export interface UPSService {
  code: string; // "03" for Ground, "01" for Next Day Air, etc.
  description?: string;
}

export interface UPSLabelImageFormat {
  code: string; // "GIF", "PNG", "PDF"
  description?: string;
}

export interface UPSShipmentRequest {
  shipper: UPSShipper;
  shipTo: UPSShipTo;
  shipFrom?: UPSShipper; // Optional, defaults to shipper
  service: UPSService;
  package: UPSPackage[];
  labelImageFormat?: UPSLabelImageFormat;
  labelStockSize?: {
    height: string;
    width: string;
  };
}

export interface UPSShipmentResponse {
  shipmentResults: {
    shipmentIdentificationNumber: string;
    packageResults: Array<{
      trackingNumber: string;
      shippingLabel: {
        imageFormat: {
          code: string;
          description: string;
        };
        graphicImage: string; // Base64 encoded image
      };
    }>;
    shipmentCharges: {
      totalCharges: {
        currencyCode: string;
        monetaryValue: string;
      };
    };
  };
}

export interface UPSTrackingRequest {
  trackingNumber: string;
}

export interface UPSTrackingActivity {
  location: {
    address: {
      city?: string;
      stateProvinceCode?: string;
      countryCode?: string;
      postalCode?: string;
    };
  };
  status: {
    type: string;
    description: string;
    code: string;
  };
  date: string;
  time: string;
}

export interface UPSTrackingResponse {
  trackResponse: {
    shipment: Array<{
      inquiryNumber: string;
      package: Array<{
        trackingNumber: string;
        deliveryDate?: Array<{
          type: string;
          date: string;
        }>;
        activity: UPSTrackingActivity[];
        packageAddress: Array<{
          type: string;
          address: UPSAddress;
        }>;
        currentStatus: {
          type: string;
          description: string;
          simplifiedTextDescription: string;
          code: string;
          statusDate: string;
          statusTime: string;
        };
      }>;
    }>;
  };
}

export interface UPSError {
  response: {
    errors: Array<{
      code: string;
      message: string;
    }>;
  };
}

// Configuration interface
export interface UPSConfig {
  clientId: string;
  clientSecret: string;
  accountNumber: string;
  baseUrl: string; // sandbox or production URL
  version: string;
}

// Simplified tracking status for UI
export interface TrackingStatus {
  trackingNumber: string;
  status:
    | 'label_created'
    | 'pending'
    | 'in_transit'
    | 'delivered'
    | 'exception'
    | 'unknown';
  statusDescription: string;
  lastUpdate: string;
  estimatedDelivery?: string;
  currentLocation?: string;
  activities: Array<{
    status: string;
    location: string;
    date: string;
    time: string;
    description: string;
  }>;
}

// Shipping label result for UI
export interface ShippingLabelResult {
  trackingNumber: string;
  labelUrl: string; // URL to download/view the label
  cost: {
    amount: string;
    currency: string;
  };
  estimatedDelivery?: string;
  createdAt?: string; // When the label was created
}
