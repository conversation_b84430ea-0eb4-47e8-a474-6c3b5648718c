import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';
import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import {
  ResetPasswordContent,
  ResetPasswordError,
} from '@/app/auth/reset-password/ResetPasswordForm';

// Configure Amplify once in the server runtime
Amplify.configure(outputs);

const client = generateClient<Schema>({
  authMode: 'iam',
});

interface PageProps {
  searchParams: Promise<{
    email?: string;
    token?: string;
  }>;
}

const Page = async ({ searchParams }: PageProps) => {
  const { token, email } = await searchParams;

  if (!token || !email) {
    console.error('RESET PASSWORD: NO REQUIRED PARAMS');
    return <ResetPasswordError message='Invalid reset link' />;
  }

  let isValid = false;

  try {
    const { data } = await client.queries.checkIsValid({ token });
    console.log('RESET PASSWORD: checkIsValid RESULT PARAMS:', data);
    isValid = Boolean(data);
  } catch (e) {
    isValid = false;
  }

  if (!isValid) {
    return <ResetPasswordError message='Invalid reset link' />;
  }

  return <ResetPasswordContent email={email} token={token} />;
};

export default Page;
