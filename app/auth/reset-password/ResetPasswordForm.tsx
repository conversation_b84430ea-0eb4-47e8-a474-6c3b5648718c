'use client';

import { useState, useEffect, FC } from 'react';
import { useRouter } from 'next/navigation';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2, Eye, EyeOff } from 'lucide-react';
import routes from '@/utils/routes';
import PasswordStrengthIndicator from '@/app/components/PasswordStrengthIndicator';
import PasswordRequirements from '@/app/components/PasswordRequirements';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { resetUserPassword } from '@/lib/utils/auth-utils/reset-user-password';

type ResetPasswordFormProps = {
  email: string;
  token: string;
};

export const ResetPasswordError = ({ message }: { message?: string }) => {
  return (
    <div className='h-full flex justify-center items-center px-4 py-8'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <div className='flex justify-center mb-4'>
            <XCircle className='h-12 w-12 text-red-500' />
          </div>
          <CardTitle>Reset Failed</CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button className='w-full'>
            <Link href={routes.login}>Back to Sign In</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export const ResetPasswordContent: FC<ResetPasswordFormProps> = ({
  email,
  token,
}) => {
  const { logout } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordMatchError, setPasswordMatchError] = useState<string | null>(
    null
  );

  useEffect(() => {
    logout();
  }, []);

  // Live validate password match before submit
  useEffect(() => {
    if (!password || !confirmPassword) {
      setPasswordMatchError(null);
      return;
    }
    if (password !== confirmPassword) {
      setPasswordMatchError('Passwords do not match');
    } else {
      setPasswordMatchError(null);
    }
  }, [password, confirmPassword]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token || !email) {
      setError('Invalid reset link. Missing token or email.');
      return;
    }

    if (!password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (password.length < 12) {
      setError('Password must be at least 12 characters long');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await resetUserPassword(email, token, password);

      setSuccess(true);
    } catch (err: any) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    router.push(routes.login);
  };

  if (success) {
    return (
      <div className='h-full flex justify-center items-center px-4 py-8'>
        <Card className='w-full max-w-md'>
          <CardHeader className='text-center'>
            <div className='flex justify-center mb-4'>
              <CheckCircle className='h-12 w-12 text-green-500' />
            </div>
            <CardTitle>Password Reset Successfully!</CardTitle>
            <CardDescription>
              Your password has been reset. You can now sign in with your new
              password.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleContinue} className='w-full'>
              Continue to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='h-full flex justify-center items-center px-4 py-8'>
      <Card className='w-full max-w-md'>
        <CardHeader>
          <CardTitle>Reset Your Password</CardTitle>
          <CardDescription>Enter your new password below.</CardDescription>
        </CardHeader>
        <form onSubmit={handleResetPassword}>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='password'>New Password</Label>
              <div className='relative'>
                <Input
                  id='password'
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={e => {
                    setPassword(e.target.value);
                    if (error) setError('');
                  }}
                  placeholder='Enter new password'
                  required
                />
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <Eye className='h-4 w-4' />
                  ) : (
                    <EyeOff className='h-4 w-4' />
                  )}
                </Button>
              </div>
              <PasswordStrengthIndicator password={password} />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='confirmPassword'>Confirm New Password</Label>
              <div className='relative'>
                <Input
                  id='confirmPassword'
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={e => {
                    setConfirmPassword(e.target.value);
                    if (error) setError('');
                  }}
                  placeholder='Confirm new password'
                  required
                />
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <Eye className='h-4 w-4' />
                  ) : (
                    <EyeOff className='h-4 w-4' />
                  )}
                </Button>
              </div>
              {passwordMatchError && (
                <p className='text-sm text-red-600'>{passwordMatchError}</p>
              )}
            </div>

            <PasswordRequirements password={password} />

            {error && (
              <div className='text-sm text-red-600 bg-red-50 p-3 rounded-md'>
                {error}
              </div>
            )}

            <Button
              type='submit'
              className='w-full'
              disabled={loading || !!passwordMatchError}
            >
              {loading ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Resetting Password...
                </>
              ) : (
                'Reset Password'
              )}
            </Button>
          </CardContent>
        </form>
      </Card>
    </div>
  );
};
