'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, User, Building, Heart } from 'lucide-react';
import { Person, PersonType } from '@/app/(protected)/member/interviewV2/page';
import PersonModal from './PersonModal';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import { toast } from 'sonner';

interface PeopleLibraryProps {
  people: Person[];
  onChange: (people: Person[]) => void;
  interviewData?: any; // All interview data to check if person is used
  readOnly?: boolean; // Flag to make all actions read-only when interview is submitted
}

export function PeopleLibrary({
  people,
  onChange,
  interviewData,
  readOnly = false,
}: PeopleLibraryProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editing<PERSON><PERSON>, setEditing<PERSON>erson] = useState<Person | null>(null);

  const {
    people: globalPeople,
    isLoading,
    addPerson,
    update<PERSON>erson,
    deletePerson,
    isUpdating,
  } = usePeopleLibrary();

  // Use global people library
  const peopleArray = Array.isArray(globalPeople) ? globalPeople : [];

  // Function to check if a person is used in the interview
  const isPersonUsedInInterview = (personId: string): boolean => {
    if (!interviewData) return false;

    // Convert interview data to JSON string and search for the person ID
    // This is more efficient than deep object traversal
    const dataString = JSON.stringify(interviewData);

    // Check if the person ID appears in the data
    // We use a more specific pattern to avoid false positives
    const personIdPattern = new RegExp(`"id"\\s*:\\s*"${personId}"`, 'g');

    return personIdPattern.test(dataString);
  };

  const handleAddPerson = async (person: Omit<Person, 'id'>) => {
    try {
      await addPerson(person);
      setIsModalOpen(false);
      toast.success('Person added successfully');
    } catch (error) {
      toast.error('Failed to add person');
    }
  };

  const handleEditPerson = async (person: Person) => {
    try {
      await updatePerson(person);
      setEditingPerson(null);
      toast.success('Person updated successfully');
    } catch (error) {
      toast.error('Failed to update person');
    }
  };

  const handleSave = async (person: Person | Omit<Person, 'id'>) => {
    if (editingPerson) {
      await handleEditPerson(person as Person);
    } else {
      await handleAddPerson(person as Omit<Person, 'id'>);
    }
  };

  const handleDeletePerson = async (personId: string) => {
    // Check if person is used in interview and warn user
    const isUsed = isPersonUsedInInterview(personId);
    const confirmMessage = isUsed
      ? 'Are you sure you want to delete this person? This will also remove them from all your interview answers where they were selected. This action cannot be undone.'
      : 'Are you sure you want to delete this person? This action cannot be undone.';

    if (confirm(confirmMessage)) {
      try {
        await deletePerson(personId);
        toast.success(
          isUsed
            ? 'Person deleted successfully and removed from all interview answers'
            : 'Person deleted successfully'
        );
      } catch (error) {
        toast.error('Failed to delete person');
      }
    }
  };

  const getPersonIcon = (type: PersonType) => {
    switch (type) {
      case 'individual':
        return <User className='h-4 w-4' />;
      case 'charity':
        return <Heart className='h-4 w-4' />;
      case 'business':
        return <Building className='h-4 w-4' />;
      default:
        return <User className='h-4 w-4' />;
    }
  };

  const getPersonName = (person: Person) => {
    if (person.type === 'individual') {
      const firstName = person.firstName || '';
      const middleName = person.middleName || '';
      const lastName = person.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return person.entityName || 'Unnamed Entity';
  };

  const getPersonTypeLabel = (type: PersonType) => {
    switch (type) {
      case 'individual':
        return 'Person';
      case 'charity':
        return 'Charity';
      case 'business':
        return 'Business';
      default:
        return 'Unknown';
    }
  };

  return (
    <>
      <Card className='h-fit'>
        <CardHeader className='pb-3'>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>People Library</CardTitle>
            <Button
              size='sm'
              onClick={() => setIsModalOpen(true)}
              className='flex items-center gap-2'
              disabled={isUpdating}
            >
              <Plus className='h-4 w-4' />
              Add
            </Button>
          </div>
          <p className='text-sm text-muted-foreground'>
            Manage all people, charities, and businesses in your estate plan.
          </p>
        </CardHeader>
        <CardContent className='space-y-3'>
          {peopleArray.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              <User className='h-12 w-12 mx-auto mb-3 opacity-50' />
              <p className='text-sm'>No people added yet</p>
              <p className='text-xs'>Click "Add" to get started</p>
            </div>
          ) : (
            peopleArray.map((person: Person) => (
              <div
                key={person.id}
                className='flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors'
              >
                <div className='flex items-center gap-3 flex-1 min-w-0'>
                  <div className='flex-shrink-0'>
                    {getPersonIcon(person.type)}
                  </div>
                  <div className='flex-1 min-w-0'>
                    <div className='font-medium text-sm truncate'>
                      {getPersonName(person)}
                    </div>
                    <div className='flex items-center gap-2 mt-1'>
                      <Badge variant='secondary' className='text-xs'>
                        {getPersonTypeLabel(person.type)}
                      </Badge>

                      {person.type === 'individual' && person.dateOfBirth && (
                        <span className='text-xs text-muted-foreground'>
                          DOB:{' '}
                          {new Date(person.dateOfBirth).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-1 flex-shrink-0'>
                  {(() => {
                    const isEditDisabled = readOnly; // Allow editing if not read-only
                    const isDeleteDisabled = readOnly; // Allow deletion if not read-only (with confirmation)

                    return (
                      <>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => {
                            if (readOnly) {
                              toast.error(
                                'Cannot edit people after interview submission.'
                              );
                              return;
                            }
                            setEditingPerson(person);
                          }}
                          className={`h-8 w-8 p-0 ${isEditDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                          disabled={isEditDisabled}
                        >
                          <Edit className='h-3 w-3' />
                        </Button>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => {
                            if (readOnly) {
                              toast.error(
                                'Cannot delete people after interview submission.'
                              );
                              return;
                            }
                            handleDeletePerson(person.id);
                          }}
                          className={`h-8 w-8 p-0 text-destructive hover:text-destructive ${isDeleteDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                          disabled={isDeleteDisabled}
                        >
                          <Trash2 className='h-3 w-3' />
                        </Button>
                      </>
                    );
                  })()}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      <PersonModal
        isOpen={isModalOpen || !!editingPerson}
        onClose={() => {
          setIsModalOpen(false);
          setEditingPerson(null);
        }}
        onSave={handleSave}
        person={editingPerson}
      />
    </>
  );
}

export default PeopleLibrary;
