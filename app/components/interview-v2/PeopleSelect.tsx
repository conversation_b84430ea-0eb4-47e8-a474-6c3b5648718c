'use client';

import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus } from 'lucide-react';
import { Person, PersonType } from '@/app/(protected)/member/interviewV2/page';
import PersonModal from './PersonModal';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';

interface PeopleSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  placeholder?: string;
  filterType?: PersonType | 'all';
  excludeIds?: string[];
  includeNoneOption?: boolean;
  noneOptionText?: string;
  className?: string;
}

export function PeopleSelect({
  value,
  onValueChange,
  people,
  onPeopleChange,
  placeholder = 'Select person',
  filterType = 'all',
  excludeIds = [],
  includeNoneOption = true,
  noneOptionText = 'No selection',
  className,
}: PeopleSelectProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Use global People Library
  const { people: globalPeople, addPerson } = usePeopleLibrary();

  // Use global people library instead of local people
  const peopleArray = Array.isArray(globalPeople) ? globalPeople : [];

  // Filter people based on type and exclusions
  const filteredPeople = peopleArray.filter(person => {
    if (excludeIds.includes(person.id)) return false;
    if (filterType === 'all') return true;
    return person.type === filterType;
  });

  const getPersonName = (personId: string) => {
    // Always get the most up-to-date person data from People Library
    const person = peopleArray.find(p => p.id === personId);
    if (!person) return 'Unknown';

    if (person.type === 'individual') {
      const firstName = person.firstName || '';
      const middleName = person.middleName || '';
      const lastName = person.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return person.entityName || 'Unknown';
  };

  const handleAddPerson = async (person: Omit<Person, 'id'>): Promise<void> => {
    try {
      const newPerson = await addPerson(person);
      onValueChange(newPerson.id);
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error adding person:', error);
    }
  };

  const handleValueChange = (selectedValue: string) => {
    if (selectedValue === 'create-new') {
      setIsModalOpen(true);
    } else {
      onValueChange(selectedValue === 'none' ? '' : selectedValue);
    }
  };

  return (
    <>
      <Select value={value || 'none'} onValueChange={handleValueChange}>
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {includeNoneOption && (
            <SelectItem value='none'>{noneOptionText}</SelectItem>
          )}

          {filteredPeople.map(person => (
            <SelectItem key={person.id} value={person.id}>
              {getPersonName(person.id)}
            </SelectItem>
          ))}

          <SelectItem value='create-new' className='text-primary font-medium'>
            <div className='flex items-center gap-2'>
              <Plus className='h-4 w-4' />
              Create New Person/Entity
            </div>
          </SelectItem>
        </SelectContent>
      </Select>

      <PersonModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleAddPerson}
        person={null}
        defaultType={filterType === 'all' ? 'individual' : filterType}
      />
    </>
  );
}

export default PeopleSelect;
