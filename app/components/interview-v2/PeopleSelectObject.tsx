'use client';

import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus } from 'lucide-react';
import { Person, PersonType } from '@/app/(protected)/member/interviewV2/page';
import PersonModal from './PersonModal';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';

interface PeopleSelectObjectProps {
  value?: Person;
  onValueChange: (value: Person | undefined) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  placeholder?: string;
  filterType?: PersonType | 'all';
  excludeIds?: string[];
  includeNoneOption?: boolean;
  noneOptionText?: string;
  className?: string;
  disabled?: boolean;
}

export function PeopleSelectObject({
  value,
  onValueChange,
  people,
  onPeopleChange,
  placeholder = 'Select person',
  filterType = 'all',
  excludeIds = [],
  includeNoneOption = true,
  noneOptionText = 'No selection',
  className,
  disabled = false,
}: PeopleSelectObjectProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Use global People Library
  const { people: globalPeople, addPerson } = usePeopleLibrary();

  // Use global people library instead of local people
  const peopleArray = Array.isArray(globalPeople) ? globalPeople : [];

  // Filter people based on type and exclusions
  const filteredPeople = peopleArray.filter(person => {
    if (excludeIds.includes(person.id)) return false;
    if (filterType === 'all') return true;
    return person.type === filterType;
  });

  const getPersonName = (person: Person) => {
    // Get the most up-to-date person data from People Library
    const currentPerson = peopleArray.find(p => p.id === person.id) || person;

    if (currentPerson.type === 'individual') {
      const firstName = currentPerson.firstName || '';
      const middleName = currentPerson.middleName || '';
      const lastName = currentPerson.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return currentPerson.entityName || 'Unknown';
  };

  const handleAddPerson = async (person: Omit<Person, 'id'>): Promise<void> => {
    try {
      const newPerson = await addPerson(person);
      onValueChange(newPerson);
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error adding person:', error);
    }
  };

  const handleValueChange = (selectedValue: string) => {
    if (disabled) return;

    if (selectedValue === 'create-new') {
      setIsModalOpen(true);
    } else if (selectedValue === 'none') {
      onValueChange(undefined);
    } else {
      const selectedPerson = peopleArray.find(p => p.id === selectedValue);
      onValueChange(selectedPerson);
    }
  };

  return (
    <>
      <Select
        value={value?.id || 'none'}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder}>
            {value ? getPersonName(value) : placeholder}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {includeNoneOption && (
            <SelectItem value='none'>{noneOptionText}</SelectItem>
          )}

          {filteredPeople.map(person => (
            <SelectItem key={person.id} value={person.id}>
              {getPersonName(person)}
            </SelectItem>
          ))}

          {!disabled && (
            <SelectItem value='create-new'>
              <div className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Create new person
              </div>
            </SelectItem>
          )}
        </SelectContent>
      </Select>

      <PersonModal
        isOpen={isModalOpen && !disabled}
        onClose={() => setIsModalOpen(false)}
        onSave={handleAddPerson}
        filterType={filterType}
      />
    </>
  );
}

export default PeopleSelectObject;
