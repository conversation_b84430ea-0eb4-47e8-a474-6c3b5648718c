'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { HelpCircle, Play } from 'lucide-react';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { getSafeVideoInfo, getPlayerSizeClasses } from '@/utils/video-utils';

interface QuestionVideoHintProps {
  questionId: string;
  className?: string;
}

// Simple video player component using existing utilities
function SimpleVideoPlayer({ video }: { video: any }) {
  const videoInfo = getSafeVideoInfo(video.contentUrl, false);

  return (
    <div className={getPlayerSizeClasses('medium')}>
      <div className='relative w-full aspect-video rounded-lg overflow-hidden bg-black'>
        <iframe
          src={videoInfo.embedUrl}
          title={video.title}
          className='w-full h-full'
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          allowFullScreen
        />
      </div>
    </div>
  );
}

export function QuestionVideoHint({
  questionId,
  className = '',
}: QuestionVideoHintProps) {
  const { getContentForInterviewHints } = useEducationalContent();
  const [isOpen, setIsOpen] = useState(false);

  // Get videos for this specific question
  const questionVideos = getContentForInterviewHints().filter(video =>
    video.interviewQuestions?.includes(questionId)
  );

  if (questionVideos.length === 0) return null;

  return (
    <div className={`mt-3 ${className}`}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant='outline'
            size='sm'
            className='flex items-center space-x-2 text-blue-600 border-blue-200 hover:bg-blue-50'
          >
            <HelpCircle className='h-4 w-4' />
            <span>View Hint</span>
            <Play className='h-3 w-3' />
          </Button>
        </DialogTrigger>
        <DialogContent
          className='!max-w-[38vw] !w-[38vw] max-h-[58vh] h-auto overflow-y-auto'
          style={{ width: '38vw', maxWidth: '38vw' }}
        >
          <DialogHeader>
            <DialogTitle className='flex items-center space-x-2'>
              <HelpCircle className='h-5 w-5 text-blue-600' />
              <span>Video Hints</span>
            </DialogTitle>
            <DialogDescription>
              Helpful videos for completing this section
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-6'>
            {questionVideos.map((video, index) => (
              <div key={video.id} className='space-y-3'>
                {questionVideos.length > 1 && (
                  <h3 className='text-lg font-medium'>{video.title}</h3>
                )}
                {video.title && (
                  <p className='text-sm text-[var(--foreground)]'>
                    {video.title}
                  </p>
                )}
                <SimpleVideoPlayer video={video} />
                {index < questionVideos.length - 1 && (
                  <hr className='border-gray-200' />
                )}
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Compact version that shows just an icon
export function CompactQuestionVideoHint({
  questionId,
  className = '',
}: QuestionVideoHintProps) {
  const { getContentForInterviewHints } = useEducationalContent();
  const [isOpen, setIsOpen] = useState(false);

  const questionVideos = getContentForInterviewHints().filter(video =>
    video.interviewQuestions?.includes(questionId)
  );

  if (questionVideos.length === 0) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className={`p-1 h-6 w-6 text-blue-600 hover:bg-blue-50 ${className}`}
          title='View video hint'
        >
          <HelpCircle className='h-4 w-4' />
        </Button>
      </DialogTrigger>
      <DialogContent
        className='!max-w-[38vw] !w-[38vw] max-h-[38vh] h-[38vh] overflow-y-auto'
        style={{ width: '38vw', maxWidth: '38vw' }}
      >
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <HelpCircle className='h-5 w-5 text-blue-600' />
            <span>Video Hints</span>
          </DialogTitle>
          <DialogDescription>
            Helpful videos for completing this section
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {questionVideos.map((video, index) => (
            <div key={video.id} className='space-y-3'>
              {questionVideos.length > 1 && (
                <h3 className='text-lg font-medium'>{video.title}</h3>
              )}
              {video.title && (
                <p className='text-sm text-[var(--foreground)]'>
                  {video.title}
                </p>
              )}
              <SimpleVideoPlayer video={video} />
              {index < questionVideos.length - 1 && (
                <hr className='border-gray-200' />
              )}
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
