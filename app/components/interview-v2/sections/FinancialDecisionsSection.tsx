'use client';

import React, { useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  CheckboxRadioGroup,
  CheckboxRadioItem,
} from '@/components/ui/checkbox-radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import PeopleSelectObject from '../PeopleSelectObject';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DollarSign, Users, Settings, Trash2, Info } from 'lucide-react';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import { AlertTriangle } from 'lucide-react';

interface FinancialData {
  agent?: {
    useCompany?: boolean;
    primary?: Person;
    successors?: Person[];
  };
  powers?: Record<string, boolean>;
}

interface FinancialDecisionsSectionProps {
  value: FinancialData;
  onChange: (data: Partial<FinancialData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function FinancialDecisionsSection({
  value,
  onChange,
  people,
  onPeopleChange,
  readOnly = false,
}: FinancialDecisionsSectionProps) {
  const { people: globalPeople } = usePeopleLibrary();

  const getPersonName = (person: Person | undefined) => {
    if (!person) return 'Unknown';

    // Get the most up-to-date person data from People Library
    const currentPerson = globalPeople.find(p => p.id === person.id) || person;

    if (currentPerson.type === 'individual') {
      const firstName = currentPerson.firstName || '';
      const middleName = currentPerson.middleName || '';
      const lastName = currentPerson.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return currentPerson.entityName || 'Unknown';
  };

  const updateAgent = useCallback(
    (field: string, fieldValue: any) => {
      // Prevent unnecessary updates if value hasn't changed
      if (value.agent?.[field as keyof typeof value.agent] === fieldValue) {
        return;
      }
      onChange({
        agent: {
          ...value.agent,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.agent]
  );

  const updatePowers = (power: string, enabled: boolean) => {
    onChange({
      powers: {
        ...value.powers,
        [power]: enabled,
      },
    });
  };

  const addSuccessor = (person: Person) => {
    const currentSuccessors = value.agent?.successors || [];
    updateAgent('successors', [...currentSuccessors, person]);
  };

  const removeSuccessor = (index: number) => {
    const currentSuccessors = value.agent?.successors || [];
    updateAgent(
      'successors',
      currentSuccessors.filter((_, i) => i !== index)
    );
  };

  // Common financial powers
  const financialPowers = [
    {
      id: 'banking',
      label: 'Banking and Financial Transactions',
      description:
        'Access bank accounts, make deposits, withdrawals, and transfers',
    },
    {
      id: 'investments',
      label: 'Investment Management',
      description: 'Buy, sell, and manage investments and securities',
    },
    {
      id: 'real_estate',
      label: 'Real Estate Transactions',
      description: 'Buy, sell, lease, and manage real estate properties',
    },
    {
      id: 'business',
      label: 'Business Operations',
      description:
        'Operate businesses, sign contracts, and make business decisions',
    },
    {
      id: 'insurance',
      label: 'Insurance Matters',
      description: 'Purchase, modify, and manage insurance policies',
    },
    {
      id: 'taxes',
      label: 'Tax Matters',
      description: 'File tax returns, pay taxes, and handle tax-related issues',
    },
    {
      id: 'gifts',
      label: 'Making Gifts',
      description: 'Make gifts on your behalf to individuals or charities',
    },
    {
      id: 'legal',
      label: 'Legal Proceedings',
      description: 'Initiate or defend legal actions on your behalf',
    },
    {
      id: 'retirement',
      label: 'Retirement Benefits',
      description: 'Manage retirement accounts and benefits',
    },
    {
      id: 'government',
      label: 'Government Benefits',
      description: 'Apply for and manage government benefits and programs',
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Financial Agent Appointment */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              <CardTitle>Financial Agent Appointment</CardTitle>
            </div>
            <QuestionVideoHint questionId='financial.agent-selection' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <Label>
              Do you want to pick an individual as your financial agent?
            </Label>
            <CheckboxRadioGroup
              value={
                value.agent?.useCompany === undefined
                  ? ''
                  : value.agent.useCompany === false
                    ? 'individual'
                    : 'company'
              }
              onValueChange={val => {
                if (val === '') {
                  updateAgent('useCompany', undefined);
                } else {
                  const newValue = val === 'company';
                  if (newValue !== value.agent?.useCompany) {
                    updateAgent('useCompany', newValue);
                  }
                }
              }}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='individual'
                id='agentIndividual'
                disabled={readOnly}
              >
                <Label htmlFor='agentIndividual'>
                  Yes, I want to pick an individual
                </Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='company'
                id='agentCompany'
                disabled={readOnly}
              >
                <Label htmlFor='agentCompany'>No, appoint Our Company</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          {value.agent?.useCompany === false && (
            <div className='space-y-4 border-t pt-4'>
              <div className='space-y-2'>
                <Label>Primary Financial Agent *</Label>
                <PeopleSelectObject
                  value={value.agent?.primary}
                  onValueChange={val => updateAgent('primary', val)}
                  people={people}
                  onPeopleChange={onPeopleChange}
                  placeholder='Select primary financial agent'
                  noneOptionText='Select primary financial agent'
                  filterType='individual'
                  includeNoneOption={false}
                  disabled={readOnly}
                />
                {value.agent?.useCompany === false && !value.agent?.primary && (
                  <Alert>
                    <AlertTriangle className='h-4 w-4' />
                    <AlertDescription>
                      Please select a primary financial agent to continue.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className='space-y-3'>
                <Label>Successor Financial Agents (in order)</Label>
                {(value.agent?.successors || []).map((successor, index) => (
                  <div key={index} className='flex items-center gap-2'>
                    <Badge variant='outline'>{index + 1}</Badge>
                    <span className='flex-1'>{getPersonName(successor)}</span>
                    <Button
                      size='sm'
                      variant='ghost'
                      onClick={() => removeSuccessor(index)}
                      className='h-8 w-8 p-0 text-destructive'
                      disabled={readOnly}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                ))}

                <PeopleSelectObject
                  value={undefined}
                  onValueChange={person => person && addSuccessor(person)}
                  people={people}
                  onPeopleChange={onPeopleChange}
                  placeholder='Add successor agent'
                  noneOptionText='Add successor agent'
                  filterType='individual'
                  excludeIds={
                    [
                      value.agent?.primary?.id,
                      ...(value.agent?.successors || []).map(s => s.id),
                    ].filter(Boolean) as string[]
                  }
                  includeNoneOption={false}
                  disabled={readOnly}
                />
              </div>
            </div>
          )}

          <Alert>
            <Info className='h-4 w-4' />
            <AlertDescription>
              Our Company will be appointed as the final successor financial
              agent.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Agent's Powers */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Settings className='h-5 w-5' />
              <CardTitle>Agent's Powers</CardTitle>
            </div>
            <QuestionVideoHint questionId='financial.powers' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-4'>
            <Label className='text-base'>
              What powers should your financial agent have?
            </Label>

            {financialPowers.map(power => (
              <div
                key={power.id}
                className='flex items-start space-x-3 p-3 border rounded-lg'
              >
                <Checkbox
                  id={power.id}
                  checked={value.powers?.[power.id] || false}
                  onCheckedChange={checked => updatePowers(power.id, !!checked)}
                  className='mt-1'
                  disabled={readOnly}
                />
                <div className='flex-1 space-y-1'>
                  <Label
                    htmlFor={power.id}
                    className={`font-medium ${
                      readOnly ? 'cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  >
                    {power.label}
                  </Label>
                  <p className='text-sm text-muted-foreground'>
                    {power.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <Alert>
            <Info className='h-4 w-4' />
            <AlertDescription>
              You can grant specific powers or all powers to your financial
              agent. Consider carefully which powers are necessary for your
              situation.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}

export default FinancialDecisionsSection;
