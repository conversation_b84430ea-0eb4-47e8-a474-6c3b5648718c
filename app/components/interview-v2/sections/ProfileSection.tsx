'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  CheckboxRadioGroup,
  CheckboxRadioItem,
} from '@/components/ui/checkbox-radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import { PeopleSelectObject } from '@/components/interview-v2/PeopleSelectObject';
import {
  syncInterviewV2ToSettings,
  syncInterviewV2ToShipping,
} from '@/app/utils/dataSyncUtils';
import { StreetAutocomplete } from '@/components/address-autocomplete/StreetAutocomplete';
import { CityAutocomplete } from '@/components/address-autocomplete/CityAutocomplete';
import { ZipCodeAutocomplete } from '@/components/address-autocomplete/ZipCodeAutocomplete';
import { CountyAutocomplete } from '@/components/address-autocomplete/CountyAutocomplete';
import { getUserDataForMyProfileInterviewStep } from '@/lib/data/users';
import { useAuth } from '@/context/AuthContext';
import { getMemberAvailableTemplates } from '@/lib/api/member-documents';
import { normalizeStateName } from '@/app/utils/states';
import {
  validateAddress,
  shouldValidateAddress,
  formatValidationIssues,
  type AddressValidationResult,
  type AddressToValidate,
} from '@/app/utils/addressValidation';

interface ProfileData {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: {
    street?: string;
    street2?: string;
    county?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  maritalStatus?: 'married' | 'single' | 'widowed';
  spouse?: Person; // Reference to spouse in People Library
  soundMind?: boolean;
  selectedDocuments?: {
    will?: boolean;
    trust?: boolean;
    financialPOA?: boolean;
    medicalPOA?: boolean;
  };
}

interface ProfileSectionProps {
  value: ProfileData;
  onChange: (data: Partial<ProfileData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  dataInitialized?: boolean; // Flag to indicate if parent data has been loaded
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function ProfileSection({
  value,
  onChange,
  people,
  onPeopleChange,
  dataInitialized = false,
  readOnly = false,
}: ProfileSectionProps) {
  const { userExternalId } = useAuth();
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [availableDocumentTypes, setAvailableDocumentTypes] = useState<
    string[]
  >([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  // Address validation state
  const [addressValidationResult, setAddressValidationResult] =
    useState<AddressValidationResult | null>(null);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [localAddress, setLocalAddress] = useState(value.address || {});

  // Track if auto-population has been attempted to prevent multiple runs
  const autoPopulationAttemptedRef = useRef(false);

  // Use global People Library
  const {
    people: globalPeople,
    addPerson,
    updatePerson,
    deletePerson,
  } = usePeopleLibrary();

  // Sync local address with prop value when it changes from parent
  useEffect(() => {
    setLocalAddress(value.address || {});
  }, [value.address]);

  const formatPhoneInput = (value: string): string => {
    // If user tries to delete the +1 prefix, restore it
    if (!value.startsWith('+1 ')) {
      return '+1 ';
    }

    const afterPrefix = value.substring(3);

    // Remove all non-numeric characters including spaces
    const cleaned = afterPrefix.replace(/[^0-9]/g, '');

    if (cleaned.length > 10) {
      return value.substring(0, value.length - 1);
    }

    return '+1 ' + cleaned;
  };

  // Function to load available document types based on user's state
  const loadAvailableDocumentTypes = async (userState: string) => {
    try {
      setIsLoadingTemplates(true);
      const normalizedState = normalizeStateName(userState || 'California');
      const templates = await getMemberAvailableTemplates(normalizedState);

      // Map template types to interview document keys
      const mapTemplateTypeToInterviewKey = (
        templateType: string
      ): string | null => {
        const typeMapping: Record<string, string> = {
          Will: 'will',
          Trust: 'trust',
          'Healthcare POA': 'medicalPOA',
          'Financial POA': 'financialPOA',
          'Advance Directive': 'medicalPOA',
          POA: 'medicalPOA',
          Medical: 'medicalPOA',
          Financial: 'financialPOA',
          Healthcare: 'medicalPOA',
        };
        return typeMapping[templateType] || null;
      };

      const availableTypes = templates
        .map(template => mapTemplateTypeToInterviewKey(template.type))
        .filter((type): type is string => type !== null)
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      setAvailableDocumentTypes(availableTypes);
      console.log('Available document types for user:', availableTypes);
    } catch (error) {
      console.error('Error loading available document types:', error);
      // Fallback to all document types if there's an error
      setAvailableDocumentTypes([
        'will',
        'trust',
        'financialPOA',
        'medicalPOA',
      ]);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  // Load user data for pre-population only after parent data is initialized
  useEffect(() => {
    const loadUserData = async () => {
      // Wait for parent data to be initialized
      if (!dataInitialized) {
        return;
      }

      // Prevent multiple auto-population attempts
      if (autoPopulationAttemptedRef.current) {
        return;
      }

      // Check if profile has any meaningful data (not just empty objects)
      const hasAnyData = !!(
        value.firstName ||
        value.lastName ||
        value.email ||
        value.phone ||
        value.dateOfBirth ||
        value.maritalStatus ||
        value.soundMind !== undefined ||
        value.selectedDocuments?.will ||
        value.selectedDocuments?.trust ||
        value.selectedDocuments?.financialPOA ||
        value.selectedDocuments?.medicalPOA ||
        (value.address &&
          (value.address.street ||
            value.address.city ||
            value.address.state ||
            value.address.zip ||
            value.address.county)) ||
        value.spouse
      );

      if (hasAnyData) {
        autoPopulationAttemptedRef.current = true; // Mark as attempted even if skipped
        return; // Already has data, don't overwrite
      }

      autoPopulationAttemptedRef.current = true; // Mark as attempted
      setIsLoadingUserData(true);

      try {
        if (!userExternalId) {
          throw new Error('User not authenticated');
        }

        const userData =
          await getUserDataForMyProfileInterviewStep(userExternalId);

        // Pre-populate fields according to task.md requirements
        const updates: Partial<ProfileData> = {};

        if (userData.firstName) updates.firstName = userData.firstName;
        if (userData.middleName) updates.middleName = userData.middleName;
        if (userData.lastName) updates.lastName = userData.lastName;
        if (userData.email) updates.email = userData.email;
        if (userData.phoneNumber) updates.phone = userData.phoneNumber;
        if (userData.birthdate) updates.dateOfBirth = userData.birthdate;

        // Always set up address structure from primary user address fields
        const addr = userData.address;
        updates.address = {
          street: addr?.street || '',
          street2: addr?.street2 || '',
          city: addr?.city || '',
          state: addr?.state || '',
          zip: addr?.zip || '',
          county: addr?.county || '',
          country: 'USA',
        };

        if (Object.keys(updates).length > 0) {
          onChange(updates);
        }

        // Load available document types based on user's state
        const userState =
          userData.address?.state || userData.state || 'California';
        await loadAvailableDocumentTypes(userState);
      } catch (error) {
        console.error('Failed to load user data for pre-population:', error);
        // Load available document types with fallback state
        await loadAvailableDocumentTypes('California');
        Sentry.captureException(error);
      } finally {
        setIsLoadingUserData(false);
      }
    };

    loadUserData();
  }, [dataInitialized]); // Only run when dataInitialized changes

  // Load available document types when user's state changes
  useEffect(() => {
    if (value.address?.state && dataInitialized) {
      loadAvailableDocumentTypes(value.address.state);
    }
  }, [value.address?.state, dataInitialized]);

  // Function to get available spouses from People Library
  const getAvailableSpouses = () => {
    return globalPeople.filter(person => person.type === 'individual');
  };

  // Sync spouse selection with People Library
  useEffect(() => {
    // Find current spouse in People Library
    const currentSpouseInLibrary = globalPeople.find(
      p => p.relationshipType === 'Spouse'
    );

    if (currentSpouseInLibrary) {
      // If someone is marked as Spouse in People Library but not selected as spouse
      if (!value.spouse || value.spouse.id !== currentSpouseInLibrary.id) {
        onChange({
          maritalStatus: 'married',
          spouse: currentSpouseInLibrary,
        });
      } else if (
        value.spouse &&
        value.spouse.id === currentSpouseInLibrary.id
      ) {
        // Update spouse data if it changed in People Library
        if (
          JSON.stringify(value.spouse) !==
          JSON.stringify(currentSpouseInLibrary)
        ) {
          onChange({
            spouse: currentSpouseInLibrary,
          });
        }
      }
    } else {
      // No one is marked as Spouse in People Library
      // Only clear if marital status is married and we have a spouse selected
      if (value.spouse && value.maritalStatus === 'married') {
        onChange({
          spouse: undefined,
        });
      }
    }
  }, [globalPeople, value.spouse, value.maritalStatus, onChange]);

  // Sync profile fields to settings when they change
  const syncProfileFieldToSettings = useCallback(
    async (field: string, fieldValue: any) => {
      // Only sync specific profile fields that exist in settings
      const syncableFields = [
        'firstName',
        'middleName',
        'lastName',
        'phone',
        'dateOfBirth',
      ];
      if (!syncableFields.includes(field)) {
        return;
      }

      try {
        if (!userExternalId) {
          throw new Error('User not authenticated');
        }

        const syncData: any = {};

        // Map interview fields to settings fields
        if (field === 'firstName') syncData.firstName = fieldValue;
        if (field === 'middleName') syncData.middleName = fieldValue;
        if (field === 'lastName') syncData.lastName = fieldValue;
        if (field === 'phone') syncData.phone = fieldValue;
        if (field === 'dateOfBirth') syncData.dateOfBirth = fieldValue;

        await syncInterviewV2ToSettings(syncData, userExternalId);
        console.log(`Successfully synced ${field} to settings`);
      } catch (error) {
        console.error(`Error syncing ${field} to settings:`, error);
        Sentry.captureException(error);
        // Don't fail the main operation if sync fails
      }
    },
    []
  );

  const updateField = useCallback(
    async (field: string, fieldValue: any) => {
      // If changing marital status, handle spouse relationship updates
      if (field === 'maritalStatus') {
        if (fieldValue === 'married') {
          // User selected married - just update the field, spouse sync will handle the rest
          onChange({ [field]: fieldValue });
        } else {
          // User selected single/widowed - clear spouse and update relationship type
          if (value.spouse) {
            try {
              // Update the current spouse's relationship type to 'Other'
              await updatePerson({
                ...value.spouse,
                relationshipType:
                  fieldValue === 'widowed' ? 'Other' : undefined,
              });
            } catch (error) {
              console.error('Error updating spouse relationship type:', error);
            }
          }

          // Clear spouse selection and update marital status
          onChange({
            [field]: fieldValue,
            spouse: undefined,
          });
        }
      } else {
        // Update the field locally
        onChange({ [field]: fieldValue });
      }

      // Sync to settings if it's a profile field
      syncProfileFieldToSettings(field, fieldValue);
    },
    [onChange, syncProfileFieldToSettings, value.spouse, updatePerson]
  );

  // Sync address changes to shipping
  const syncAddressToShipping = useCallback(async (updatedAddress: any) => {
    try {
      if (!userExternalId) {
        throw new Error('User not authenticated');
      }

      await syncInterviewV2ToShipping(
        {
          street: updatedAddress.street,
          street2: updatedAddress.street2,
          city: updatedAddress.city,
          state: updatedAddress.state,
          zip: updatedAddress.zip,
          county: updatedAddress.county,
        },
        userExternalId
      );
      console.log('Successfully synced address to shipping');
    } catch (error) {
      console.error('Error syncing address to shipping:', error);
      Sentry.captureException(error);
      // Don't fail the main operation if sync fails
    }
  }, []);

  // Validate address function
  const validateAddressData = useCallback(async () => {
    if (
      !shouldValidateAddress({
        addressLine1: localAddress.street || '',
        city: localAddress.city || '',
        state: localAddress.state || '',
        postalCode: localAddress.zip || '',
      })
    ) {
      setAddressValidationResult({
        isValid: false,
        issues: [
          'Please fill in all required address fields before validating',
        ],
        confidence: 'LOW',
        deliveryConfidence: 'UNLIKELY',
      });
      return;
    }

    setIsValidatingAddress(true);
    setAddressValidationResult(null);

    try {
      const result = await validateAddress({
        addressLine1: localAddress.street || '',
        city: localAddress.city || '',
        state: localAddress.state || '',
        postalCode: localAddress.zip || '',
      });

      setAddressValidationResult(result);

      // If validation is successful, save the address
      if (result.isValid) {
        // Use corrected address if available
        const addressToSave = result.correctedAddress
          ? {
              ...localAddress,
              street: result.correctedAddress.addressLine1,
              city: result.correctedAddress.city,
              state: result.correctedAddress.state,
              zip: result.correctedAddress.postalCode,
            }
          : localAddress;

        // Update parent state (triggers autosave)
        onChange({ address: addressToSave });

        // Sync to shipping
        await syncAddressToShipping(addressToSave);
      }
    } catch (error) {
      console.error('Address validation failed:', error);
      setAddressValidationResult({
        isValid: false,
        issues: ['Address validation failed. Please try again.'],
        confidence: 'LOW',
        deliveryConfidence: 'UNLIKELY',
      });
    } finally {
      setIsValidatingAddress(false);
    }
  }, [localAddress, onChange, syncAddressToShipping]);

  // Update local address only (no autosave)
  const updateLocalAddress = useCallback(
    (field: string, fieldValue: string) => {
      // Apply English-only filter for street2 field
      let processedValue = fieldValue;
      if (field === 'street2') {
        processedValue = fieldValue.replace(/[^\x00-\x7F]/g, '');
        if (processedValue !== fieldValue) {
          console.log(
            '⚠️ Address Line 2: Non-English characters detected and removed:',
            {
              original: fieldValue,
              filtered: processedValue,
            }
          );
        }
      }

      setLocalAddress(prev => ({
        ...prev,
        [field]: processedValue,
      }));

      // Clear validation result when address changes
      if (addressValidationResult) {
        setAddressValidationResult(null);
      }
    },
    [addressValidationResult]
  );

  // Handle spouse selection from People Library
  const handleSpouseSelection = useCallback(
    async (selectedSpouse: Person | undefined) => {
      if (selectedSpouse) {
        // Clear any existing spouse relationship type
        const existingSpouse = globalPeople.find(
          p => p.relationshipType === 'Spouse' && p.id !== selectedSpouse.id
        );
        if (existingSpouse) {
          try {
            await updatePerson({
              ...existingSpouse,
              relationshipType: undefined,
            });
          } catch (error) {
            console.error(
              'Error clearing existing spouse relationship:',
              error
            );
          }
        }

        // Set the selected person as Spouse
        try {
          await updatePerson({
            ...selectedSpouse,
            relationshipType: 'Spouse',
          });

          onChange({
            spouse: { ...selectedSpouse, relationshipType: 'Spouse' },
            maritalStatus: 'married',
          });
        } catch (error) {
          console.error('Error setting spouse relationship:', error);
        }
      } else {
        // Clear spouse selection
        onChange({
          spouse: undefined,
          maritalStatus: undefined,
        });
      }
    },
    [onChange, globalPeople, updatePerson]
  );

  const updateSelectedDocuments = (doc: string, checked: boolean) => {
    const newDocs = {
      ...value.selectedDocuments,
      [doc]: checked,
    };

    // Allow unchecking all documents - user can choose not to create any documents
    onChange({
      selectedDocuments: newDocs,
    });
  };

  // Function to get document display information
  const getDocumentInfo = (docType: string) => {
    const documentInfo: Record<string, { id: string; label: string }> = {
      will: { id: 'will', label: 'Last Will and Testament' },
      trust: { id: 'trust', label: 'Revocable Trust' },
      financialPOA: {
        id: 'financialPOA',
        label: 'Financial Power of Attorney',
      },
      medicalPOA: { id: 'medicalPOA', label: 'Medical Power of Attorney' },
    };
    return documentInfo[docType] || { id: docType, label: docType };
  };

  const isBlocked = value.soundMind === false;

  return (
    <div className='space-y-6'>
      {/* User Information */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>My Information</CardTitle>
            <QuestionVideoHint questionId='profile.personal-info' />
            {isLoadingUserData && (
              <p className='text-sm text-muted-foreground'>
                Loading your information...
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='firstName'>First Name *</Label>
              <Input
                id='firstName'
                value={value.firstName || ''}
                onChange={e => updateField('firstName', e.target.value)}
                placeholder='John'
                disabled={readOnly}
                className={
                  readOnly
                    ? 'bg-muted text-muted-foreground'
                    : !value.firstName?.trim()
                      ? 'border-red-300 focus:border-red-500'
                      : ''
                }
              />
              {!value.firstName?.trim() && !readOnly && (
                <p className='text-sm text-red-600'>First name is required</p>
              )}
            </div>
            <div className='space-y-2'>
              <Label htmlFor='middleName'>Middle Name</Label>
              <Input
                id='middleName'
                value={value.middleName || ''}
                onChange={e => updateField('middleName', e.target.value)}
                placeholder='Michael'
                disabled={readOnly}
                className={readOnly ? 'bg-muted text-muted-foreground' : ''}
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='lastName'>Last Name *</Label>
              <Input
                id='lastName'
                value={value.lastName || ''}
                onChange={e => updateField('lastName', e.target.value)}
                placeholder='Doe'
                disabled={readOnly}
                className={
                  readOnly
                    ? 'bg-muted text-muted-foreground'
                    : !value.lastName?.trim()
                      ? 'border-red-300 focus:border-red-500'
                      : ''
                }
              />
              {!value.lastName?.trim() && !readOnly && (
                <p className='text-sm text-red-600'>Last name is required</p>
              )}
            </div>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='email'>Email *</Label>
              <Input
                id='email'
                type='email'
                value={value.email || ''}
                placeholder='<EMAIL>'
                disabled
                className='bg-muted'
              />
              <p className='text-xs text-muted-foreground'>
                Email is pre-filled and cannot be changed
              </p>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='phone'>Phone Number</Label>
              <Input
                id='phone'
                value={value.phone || '+1 '}
                onChange={e =>
                  updateField('phone', formatPhoneInput(e.target.value))
                }
                placeholder='+****************'
                disabled={readOnly}
                className={readOnly ? 'bg-muted text-muted-foreground' : ''}
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label>Date of Birth *</Label>
            <DatePicker
              date={value.dateOfBirth ? new Date(value.dateOfBirth) : undefined}
              setDate={date => updateField('dateOfBirth', date?.toISOString())}
              calendarProps={{
                disabled: readOnly,
              }}
            />
            {!value.dateOfBirth && !readOnly && (
              <p className='text-sm text-red-600'>Date of birth is required</p>
            )}
          </div>

          {/* Address with Google Places Autocomplete */}
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <QuestionVideoHint questionId='profile.address' />
            </div>

            <StreetAutocomplete
              label='Street Address *'
              value={localAddress?.street || ''}
              onChange={street => updateLocalAddress('street', street)}
              onAddressSelect={components => {
                // Parse Google Places address components
                const addressData: any = {};

                components.forEach((component: any) => {
                  const types = component.types;
                  if (types.includes('street_number')) {
                    addressData.streetNumber = component.long_name;
                  } else if (types.includes('route')) {
                    addressData.route = component.long_name;
                  } else if (types.includes('locality')) {
                    addressData.city = component.long_name;
                  } else if (types.includes('administrative_area_level_1')) {
                    addressData.state = component.short_name;
                  } else if (types.includes('postal_code')) {
                    addressData.zip = component.long_name;
                  } else if (types.includes('administrative_area_level_2')) {
                    addressData.county = component.long_name;
                  }
                });

                // Update all address fields using Google Places (only update local state, no autosave)
                const newAddress = {
                  ...localAddress,
                  street:
                    addressData.streetNumber && addressData.route
                      ? `${addressData.streetNumber} ${addressData.route}`
                      : addressData.route ||
                        addressData.streetNumber ||
                        localAddress?.street ||
                        '',
                  city: addressData.city || localAddress?.city || '',
                  state: addressData.state || localAddress?.state || '',
                  zip: addressData.zip || localAddress?.zip || '',
                  county: addressData.county || localAddress?.county || '',
                  country: 'USA',
                };

                // Only update local state - no autosave
                setLocalAddress(newAddress);
                // Clear validation result since address changed
                setAddressValidationResult(null);
              }}
              placeholder='Start typing your address...'
              disabled={readOnly}
            />

            {/* Additional address fields */}
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='street2'>Address Line 2</Label>
                <Input
                  id='street2'
                  value={localAddress?.street2 || ''}
                  onChange={e => updateLocalAddress('street2', e.target.value)}
                  placeholder='Apartment, suite, unit, building, floor, etc.'
                  disabled={readOnly}
                />
              </div>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <CityAutocomplete
                  label='City *'
                  value={localAddress?.city || ''}
                  onChange={city => updateLocalAddress('city', city)}
                  placeholder='Start typing a city...'
                  disabled={readOnly}
                />
                <div className='space-y-2'>
                  <Label htmlFor='state'>State *</Label>
                  <Input
                    id='state'
                    value={localAddress?.state || ''}
                    onChange={e =>
                      updateLocalAddress('state', e.target.value.toUpperCase())
                    }
                    placeholder='CA'
                    maxLength={2}
                    disabled={readOnly}
                  />
                </div>
                <ZipCodeAutocomplete
                  label='ZIP Code *'
                  value={localAddress?.zip || ''}
                  onChange={zip => updateLocalAddress('zip', zip)}
                  onZipSelect={zipData => {
                    // Auto-fill city and state when ZIP is selected (only update local state, no autosave)
                    if (zipData.city && zipData.state && !readOnly) {
                      const newAddress = {
                        ...localAddress,
                        zip: zipData.zip,
                        city: zipData.city,
                        state: zipData.state,
                      };
                      setLocalAddress(newAddress);
                      // Clear validation result since address changed
                      setAddressValidationResult(null);
                    }
                  }}
                  placeholder='Enter ZIP code...'
                  disabled={readOnly}
                />
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <CountyAutocomplete
                  label='County *'
                  value={localAddress?.county || ''}
                  onChange={county => updateLocalAddress('county', county)}
                  placeholder='Start typing a county...'
                  disabled={readOnly}
                />
                <div className='space-y-2'>
                  <Label htmlFor='country'>Country *</Label>
                  <Input
                    id='country'
                    value={localAddress?.country || 'USA'}
                    placeholder='USA'
                    disabled
                    className='bg-muted'
                  />
                </div>
              </div>

              {/* Address Validation Section */}
              {!readOnly && (
                <div className='space-y-4 border-t pt-4'>
                  <div className='flex items-center justify-between'>
                    <Label className='text-base font-medium'>
                      Address Validation
                    </Label>
                  </div>

                  <div className='flex flex-col space-y-3'>
                    <Button
                      type='button'
                      onClick={validateAddressData}
                      disabled={
                        isValidatingAddress ||
                        !shouldValidateAddress({
                          addressLine1: localAddress?.street || '',
                          city: localAddress?.city || '',
                          state: localAddress?.state || '',
                          postalCode: localAddress?.zip || '',
                        })
                      }
                      className='w-full sm:w-auto'
                    >
                      {isValidatingAddress ? (
                        <>
                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          Validating Address...
                        </>
                      ) : (
                        'Validate & Save Address'
                      )}
                    </Button>

                    {addressValidationResult && (
                      <Alert
                        className={
                          addressValidationResult.isValid
                            ? 'border-green-200 bg-green-50'
                            : 'border-red-200 bg-red-50'
                        }
                      >
                        <AlertTriangle
                          className={`h-4 w-4 ${addressValidationResult.isValid ? 'text-green-600' : 'text-red-600'}`}
                        />
                        <AlertDescription
                          className={
                            addressValidationResult.isValid
                              ? 'text-green-700'
                              : 'text-red-700'
                          }
                        >
                          <div className='whitespace-pre-line'>
                            {formatValidationIssues(addressValidationResult)}
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}

                    {!shouldValidateAddress({
                      addressLine1: localAddress?.street || '',
                      city: localAddress?.city || '',
                      state: localAddress?.state || '',
                      postalCode: localAddress?.zip || '',
                    }) && (
                      <p className='text-sm text-muted-foreground'>
                        Please fill in all required address fields (Street,
                        City, State, ZIP) to validate your address.
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Marital Status */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Marital Status</CardTitle>
            <QuestionVideoHint questionId='profile.marital-status' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <Label>Are you legally married?</Label>
            <CheckboxRadioGroup
              value={value.maritalStatus || ''}
              onValueChange={val => updateField('maritalStatus', val)}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='married'
                id='married'
                disabled={readOnly}
              >
                <Label htmlFor='married'>Yes</Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem value='single' id='single' disabled={readOnly}>
                <Label htmlFor='single'>No</Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='widowed'
                id='widowed'
                disabled={readOnly}
              >
                <Label htmlFor='widowed'>Widowed</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          {value.maritalStatus === 'married' && (
            <div className='space-y-4 border-t pt-4'>
              <div className='flex items-center justify-between'>
                <Label className='text-base font-medium'>
                  Select Your Spouse
                </Label>
                <QuestionVideoHint questionId='profile.spouse-info' />
              </div>
              <div className='space-y-2'>
                <Label>Choose your spouse from People Library *</Label>
                <PeopleSelectObject
                  value={value.spouse}
                  onValueChange={handleSpouseSelection}
                  people={getAvailableSpouses()}
                  onPeopleChange={onPeopleChange}
                  placeholder='Select your spouse'
                  filterType='individual'
                  includeNoneOption={true}
                  noneOptionText='No spouse selected'
                  disabled={readOnly}
                />
                {value.maritalStatus === 'married' &&
                  !value.spouse &&
                  !readOnly && (
                    <p className='text-sm text-red-600'>
                      Please select your spouse from the People Library
                    </p>
                  )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sound Mind Declaration */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Sound Mind Declaration</CardTitle>
            <QuestionVideoHint questionId='profile.sound-mind' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            <Label>
              Are you of sound mind and capable of making legal decisions?
            </Label>
            <CheckboxRadioGroup
              value={
                value.soundMind === undefined ? '' : value.soundMind.toString()
              }
              onValueChange={val => updateField('soundMind', val === 'true')}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='true'
                id='soundMindYes'
                disabled={readOnly}
              >
                <Label htmlFor='soundMindYes'>Yes</Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='false'
                id='soundMindNo'
                disabled={readOnly}
              >
                <Label htmlFor='soundMindNo'>No</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>

            {value.soundMind === false && (
              <Alert className='border-destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>
                  You must be of sound mind to proceed with creating legal
                  documents. Please consult with a legal professional if you
                  have concerns about your capacity.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Document Selection */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Document Selection</CardTitle>
            <QuestionVideoHint questionId='profile.document-selection' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <Label>Select the documents you would like to create:</Label>
            {isLoadingTemplates ? (
              <div className='flex items-center space-x-2 py-4'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary'></div>
                <span className='text-sm text-muted-foreground'>
                  Loading available documents for your state...
                </span>
              </div>
            ) : (
              <div className='space-y-3'>
                {availableDocumentTypes.length > 0 ? (
                  availableDocumentTypes.map(docType => {
                    const docInfo = getDocumentInfo(docType);
                    return (
                      <div
                        key={docType}
                        className='flex items-center space-x-2'
                      >
                        <Checkbox
                          id={docInfo.id}
                          checked={
                            value.selectedDocuments?.[
                              docType as keyof typeof value.selectedDocuments
                            ] || false
                          }
                          onCheckedChange={checked =>
                            updateSelectedDocuments(docType, !!checked)
                          }
                          disabled={isBlocked || readOnly}
                        />
                        <Label htmlFor={docInfo.id}>{docInfo.label}</Label>
                      </div>
                    );
                  })
                ) : (
                  <div className='text-sm text-muted-foreground py-4'>
                    No document templates are available for your state at this
                    time. Please contact support for assistance.
                  </div>
                )}
              </div>
            )}

            {!Object.values(value.selectedDocuments || {}).some(Boolean) &&
              !isBlocked &&
              availableDocumentTypes.length > 0 && (
                <Alert className='border-yellow-200 bg-yellow-50'>
                  <AlertTriangle className='h-4 w-4 text-yellow-600' />
                  <AlertDescription className='text-yellow-700'>
                    No documents selected. You can continue without selecting
                    any documents, but you won't be able to generate legal
                    documents at the end of the interview.
                  </AlertDescription>
                </Alert>
              )}

            {isBlocked && (
              <Alert className='border-destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>
                  Document selection is disabled. You must declare that you are
                  of sound mind to proceed.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ProfileSection;
