'use client';

import React, { useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  CheckboxRadioGroup,
  CheckboxRadioItem,
} from '@/components/ui/checkbox-radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import PeopleSelectObject from '../PeopleSelectObject';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Package,
  MapPin,
  Shield,
  Scale,
  Users,
  Trash2,
  Info,
  HelpCircle,
  AlertTriangle,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';

interface AdditionalData {
  personalProperty?: 'spouse' | 'list';
  burial?: 'buried' | 'cremated' | 'other';
  burialOther?: string;
  guardian?: {
    useCompany?: boolean;
    primary?: Person;
    successors?: Person[];
  };
  legalProvisions?: {
    disclaimerTrust?: boolean;
    drugAbuse?: boolean;
    mineralRights?: boolean;
  };
}

interface AdditionalConsiderationsSectionProps {
  value: AdditionalData;
  onChange: (data: Partial<AdditionalData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  isMarried?: boolean;
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function AdditionalConsiderationsSection({
  value,
  onChange,
  people,
  onPeopleChange,
  isMarried,
  readOnly = false,
}: AdditionalConsiderationsSectionProps) {
  const getPersonName = (person: Person | undefined) => {
    if (!person) return 'Unknown';

    if (person.type === 'individual') {
      return `${person.firstName || ''} ${person.lastName || ''}`.trim();
    }
    return person.entityName || 'Unknown';
  };

  const updateGuardian = useCallback(
    (field: string, fieldValue: any) => {
      onChange({
        guardian: {
          ...value.guardian,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.guardian]
  );

  const updateLegalProvisions = useCallback(
    (provision: string, enabled: boolean) => {
      onChange({
        legalProvisions: {
          ...value.legalProvisions,
          [provision]: enabled,
        },
      });
    },
    [onChange, value.legalProvisions]
  );

  const addSuccessorToGuardian = (person: Person) => {
    const currentSuccessors = value.guardian?.successors || [];
    updateGuardian('successors', [...currentSuccessors, person]);
  };

  const removeSuccessorFromGuardian = (index: number) => {
    const currentSuccessors = value.guardian?.successors || [];
    updateGuardian(
      'successors',
      currentSuccessors.filter((_, i) => i !== index)
    );
  };

  return (
    <TooltipProvider>
      <div className='space-y-6'>
        {/* Personal Property Distribution */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Package className='h-5 w-5' />
                <CardTitle>Personal Property Distribution</CardTitle>
              </div>
              <QuestionVideoHint questionId='additional.personal-property' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-3'>
              <Label>
                How should your personal property (furniture, jewelry, etc.) be
                distributed?
              </Label>
              <CheckboxRadioGroup
                value={value.personalProperty || ''}
                onValueChange={val =>
                  onChange({ personalProperty: val as 'spouse' | 'list' })
                }
                disabled={readOnly}
              >
                {isMarried && (
                  <CheckboxRadioItem
                    value='spouse'
                    id='propertySpouse'
                    disabled={readOnly}
                  >
                    <Label htmlFor='propertySpouse'>
                      Give all to my spouse
                    </Label>
                  </CheckboxRadioItem>
                )}
                <CheckboxRadioItem
                  value='list'
                  id='propertyList'
                  disabled={readOnly}
                >
                  <Label htmlFor='propertyList'>See my informal list</Label>
                </CheckboxRadioItem>
              </CheckboxRadioGroup>

              {value.personalProperty === 'list' && (
                <Alert>
                  <Info className='h-4 w-4' />
                  <AlertDescription>
                    You can create a separate informal list of personal property
                    items and designate who should receive them. This list can
                    be updated at any time without changing your will. You’ll
                    find this feature under the My Care section, which you’ll
                    complete after your legal documents are notarized.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Burial or Cremation */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <MapPin className='h-5 w-5' />
                <CardTitle>Burial or Cremation</CardTitle>
              </div>
              <QuestionVideoHint questionId='additional.burial-preferences' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-3'>
              <Label>What are your preferences for your remains?</Label>
              <CheckboxRadioGroup
                value={value.burial || ''}
                onValueChange={val =>
                  onChange({ burial: val as 'buried' | 'cremated' | 'other' })
                }
                disabled={readOnly}
              >
                <CheckboxRadioItem
                  value='buried'
                  id='buried'
                  disabled={readOnly}
                >
                  <Label htmlFor='buried'>Buried</Label>
                </CheckboxRadioItem>
                <CheckboxRadioItem
                  value='cremated'
                  id='cremated'
                  disabled={readOnly}
                >
                  <Label htmlFor='cremated'>Cremated</Label>
                </CheckboxRadioItem>
                <CheckboxRadioItem
                  value='other'
                  id='burialOther'
                  disabled={readOnly}
                >
                  <Label htmlFor='burialOther'>Other</Label>
                </CheckboxRadioItem>
              </CheckboxRadioGroup>

              {value.burial === 'other' && (
                <div className='space-y-2 ml-6'>
                  <Label htmlFor='burialOtherText'>Please specify:</Label>
                  <Input
                    id='burialOtherText'
                    value={value.burialOther || ''}
                    onChange={e => onChange({ burialOther: e.target.value })}
                    placeholder='Describe your preferences...'
                    disabled={readOnly}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Guardian and Conservator */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Shield className='h-5 w-5' />
                <CardTitle>Guardian and Conservator</CardTitle>
              </div>
              <QuestionVideoHint questionId='additional.guardian' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-3'>
              <Label>
                If you become incapacitated, who should be your guardian and
                conservator?
              </Label>
              <p className='text-sm text-muted-foreground'>
                A guardian makes personal decisions (healthcare, living
                arrangements) while a conservator manages financial affairs.
              </p>

              <CheckboxRadioGroup
                value={
                  value.guardian?.useCompany === false
                    ? 'individual'
                    : 'company'
                }
                onValueChange={val => {
                  if (val === '') {
                    updateGuardian('useCompany', undefined);
                  } else {
                    updateGuardian('useCompany', val === 'company');
                  }
                }}
                disabled={readOnly}
              >
                <CheckboxRadioItem
                  value='individual'
                  id='guardianIndividual'
                  disabled={readOnly}
                >
                  <Label htmlFor='guardianIndividual'>
                    Yes, I want to pick an individual
                  </Label>
                </CheckboxRadioItem>
                <CheckboxRadioItem
                  value='company'
                  id='guardianCompany'
                  disabled={readOnly}
                >
                  <Label htmlFor='guardianCompany'>
                    No, appoint Our Company
                  </Label>
                </CheckboxRadioItem>
              </CheckboxRadioGroup>
            </div>

            {value.guardian?.useCompany === false && (
              <div className='space-y-4 border-t pt-4'>
                <div className='space-y-2'>
                  <Label>Primary Guardian/Conservator *</Label>
                  <PeopleSelectObject
                    value={value.guardian?.primary}
                    onValueChange={val => updateGuardian('primary', val)}
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select primary guardian/conservator'
                    noneOptionText='Select primary guardian/conservator'
                    filterType='individual'
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                  {value.guardian?.useCompany === false &&
                    !value.guardian?.primary && (
                      <Alert>
                        <AlertTriangle className='h-4 w-4' />
                        <AlertDescription>
                          Please select a primary guardian/conservator to
                          continue.
                        </AlertDescription>
                      </Alert>
                    )}
                </div>

                <div className='space-y-3'>
                  <Label>Successor Guardians/Conservators (in order)</Label>
                  {(value.guardian?.successors || []).map(
                    (successor, index) => (
                      <div key={index} className='flex items-center gap-2'>
                        <Badge variant='outline'>{index + 1}</Badge>
                        <span className='flex-1'>
                          {getPersonName(successor)}
                        </span>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => removeSuccessorFromGuardian(index)}
                          className='h-8 w-8 p-0 text-destructive'
                          disabled={readOnly}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    )
                  )}

                  <PeopleSelectObject
                    value={undefined}
                    onValueChange={person =>
                      person && addSuccessorToGuardian(person)
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Add successor guardian/conservator'
                    noneOptionText='Add successor guardian/conservator'
                    filterType='individual'
                    excludeIds={
                      [
                        value.guardian?.primary?.id,
                        ...(value.guardian?.successors || []).map(s => s.id),
                      ].filter(Boolean) as string[]
                    }
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            <Alert>
              <Info className='h-4 w-4' />
              <AlertDescription>
                Our Company will be appointed as the final successor guardian
                and conservator.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Legal Provisions */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Scale className='h-5 w-5' />
                <CardTitle>Legal Provisions</CardTitle>
              </div>
              <QuestionVideoHint questionId='additional.legal-provisions' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-4'>
              <Label className='text-base'>
                Advanced legal clauses (optional):
              </Label>

              <div className='space-y-4'>
                <div className='flex items-start space-x-3 p-3 border rounded-lg'>
                  <Checkbox
                    id='disclaimerTrust'
                    checked={value.legalProvisions?.disclaimerTrust || false}
                    onCheckedChange={checked =>
                      updateLegalProvisions('disclaimerTrust', !!checked)
                    }
                    className='mt-1'
                    disabled={readOnly}
                  />
                  <div className='flex-1 space-y-1'>
                    <div className='flex items-center gap-2'>
                      <Label
                        htmlFor='disclaimerTrust'
                        className={`font-medium ${
                          readOnly ? 'cursor-not-allowed' : 'cursor-pointer'
                        }`}
                      >
                        Disclaimer Trust
                      </Label>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className='h-4 w-4 text-muted-foreground cursor-help' />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className='max-w-xs'>
                            Allows beneficiaries to disclaim (refuse)
                            inheritances, which can provide tax planning
                            flexibility.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      Provides tax planning flexibility for beneficiaries
                    </p>
                  </div>
                </div>

                <div className='flex items-start space-x-3 p-3 border rounded-lg'>
                  <Checkbox
                    id='drugAbuse'
                    checked={value.legalProvisions?.drugAbuse || false}
                    onCheckedChange={checked =>
                      updateLegalProvisions('drugAbuse', !!checked)
                    }
                    className='mt-1'
                    disabled={readOnly}
                  />
                  <div className='flex-1 space-y-1'>
                    <div className='flex items-center gap-2'>
                      <Label
                        htmlFor='drugAbuse'
                        className={`font-medium ${
                          readOnly ? 'cursor-not-allowed' : 'cursor-pointer'
                        }`}
                      >
                        Drug Abuse Provisions
                      </Label>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className='h-4 w-4 text-muted-foreground cursor-help' />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className='max-w-xs'>
                            Allows withholding of inheritance if a beneficiary
                            has substance abuse issues, encouraging treatment.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      Protects beneficiaries with substance abuse issues
                    </p>
                  </div>
                </div>

                <div className='flex items-start space-x-3 p-3 border rounded-lg'>
                  <Checkbox
                    id='mineralRights'
                    checked={value.legalProvisions?.mineralRights || false}
                    onCheckedChange={checked =>
                      updateLegalProvisions('mineralRights', !!checked)
                    }
                    className='mt-1'
                    disabled={readOnly}
                  />
                  <div className='flex-1 space-y-1'>
                    <div className='flex items-center gap-2'>
                      <Label
                        htmlFor='mineralRights'
                        className={`font-medium ${
                          readOnly ? 'cursor-not-allowed' : 'cursor-pointer'
                        }`}
                      >
                        Mineral Rights
                      </Label>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className='h-4 w-4 text-muted-foreground cursor-help' />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className='max-w-xs'>
                            Special provisions for handling mineral rights, oil,
                            gas, and other subsurface rights.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      Special handling for mineral and subsurface rights
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}

export default AdditionalConsiderationsSection;
