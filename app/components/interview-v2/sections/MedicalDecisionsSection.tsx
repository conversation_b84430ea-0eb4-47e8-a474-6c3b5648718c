'use client';

import React, { useCallback } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  CheckboxRadioGroup,
  CheckboxRadioItem,
} from '@/components/ui/checkbox-radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import PeopleSelectObject from '../PeopleSelectObject';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Heart,
  Users,
  FileText,
  UserCheck,
  Trash2,
  Info,
  X,
  AlertTriangle,
} from 'lucide-react';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';

interface MedicalData {
  proxy?: {
    useCompany?: boolean;
    primary?: Person;
    successors?: Person[];
  };
  directives?: Record<string, boolean>;
  proxyAuthority?: string;
  wantConsultation?: boolean;
  consultationPeople?: Person[];
  organDonation?: {
    enabled?: boolean;
    specificOrgans?: string[];
    limitations?: string;
  };
  medicalInfoAccessPeople?: Person[];
}

interface MedicalDecisionsSectionProps {
  value: MedicalData;
  onChange: (data: Partial<MedicalData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function MedicalDecisionsSection({
  value,
  onChange,
  people,
  onPeopleChange,
  readOnly = false,
}: MedicalDecisionsSectionProps) {
  const { people: globalPeople } = usePeopleLibrary();

  const getPersonName = (person: Person | undefined) => {
    if (!person) return 'Unknown';

    // Get the most up-to-date person data from People Library
    const currentPerson = globalPeople.find(p => p.id === person.id) || person;

    if (currentPerson.type === 'individual') {
      const firstName = currentPerson.firstName || '';
      const middleName = currentPerson.middleName || '';
      const lastName = currentPerson.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return currentPerson.entityName || 'Unknown';
  };

  // Check if all required fields are completed
  const isAllRequiredFieldsCompleted = () => {
    // Check if proxy is properly configured
    const proxyComplete = !!(
      value.proxy?.useCompany === true ||
      (value.proxy?.useCompany === false && value.proxy?.primary)
    );

    // Check if all medical directives are answered
    const medicalDirectives = [
      'life_sustaining',
      'artificial_nutrition',
      'pain_relief',
      'antibiotics',
      'dialysis',
      'ventilator',
    ];
    const directivesComplete = medicalDirectives.every(
      directive => value.directives?.[directive] !== undefined
    );

    // Check if proxy authority is selected
    const proxyAuthorityComplete = !!value.proxyAuthority;

    // Check if consultation preference is answered
    const consultationPreferenceComplete = value.wantConsultation !== undefined;

    // Check if organ donation preference is answered
    const organDonationComplete = value.organDonation?.enabled !== undefined;

    return (
      proxyComplete &&
      directivesComplete &&
      proxyAuthorityComplete &&
      consultationPreferenceComplete &&
      organDonationComplete
    );
  };

  const updateProxy = useCallback(
    (field: string, fieldValue: any) => {
      // Prevent unnecessary updates if value hasn't changed
      if (value.proxy?.[field as keyof typeof value.proxy] === fieldValue) {
        return;
      }
      onChange({
        proxy: {
          ...value.proxy,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.proxy]
  );

  const updateDirectives = useCallback(
    (directive: string, enabled: boolean | undefined) => {
      if (enabled === undefined) {
        // Remove the directive from the object
        const { [directive]: removed, ...rest } = value.directives || {};
        onChange({
          directives: rest,
        });
      } else {
        onChange({
          directives: {
            ...value.directives,
            [directive]: enabled,
          },
        });
      }
    },
    [onChange, value.directives]
  );

  const updateOrganDonation = useCallback(
    (field: string, fieldValue: any) => {
      // Prevent unnecessary updates if value hasn't changed
      if (
        value.organDonation?.[field as keyof typeof value.organDonation] ===
        fieldValue
      ) {
        return;
      }
      onChange({
        organDonation: {
          ...value.organDonation,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.organDonation]
  );

  const addSuccessor = (person: Person) => {
    const currentSuccessors = value.proxy?.successors || [];
    updateProxy('successors', [...currentSuccessors, person]);
  };

  const removeSuccessor = (index: number) => {
    const currentSuccessors = value.proxy?.successors || [];
    updateProxy(
      'successors',
      currentSuccessors.filter((_, i) => i !== index)
    );
  };

  const addMedicalInfoAccessPerson = (person: Person) => {
    const currentPeople = value.medicalInfoAccessPeople || [];
    onChange({
      medicalInfoAccessPeople: [...currentPeople, person],
    });
  };

  const removeMedicalInfoAccessPerson = (personId: string) => {
    const currentPeople = value.medicalInfoAccessPeople || [];
    onChange({
      medicalInfoAccessPeople: currentPeople.filter(p => p.id !== personId),
    });
  };

  const addConsultationPerson = (person: Person) => {
    const currentPeople = value.consultationPeople || [];
    onChange({
      consultationPeople: [...currentPeople, person],
    });
  };

  const removeConsultationPerson = (personId: string) => {
    const currentPeople = value.consultationPeople || [];
    onChange({
      consultationPeople: currentPeople.filter(p => p.id !== personId),
    });
  };

  const toggleOrganDonation = (organ: string, enabled: boolean) => {
    const currentOrgans = value.organDonation?.specificOrgans || [];
    const newOrgans = enabled
      ? [...currentOrgans, organ]
      : currentOrgans.filter(o => o !== organ);

    updateOrganDonation('specificOrgans', newOrgans);
  };

  const selectAllOrgans = () => {
    const allOrgans = [
      'heart',
      'liver',
      'kidneys',
      'lungs',
      'pancreas',
      'corneas',
      'skin',
      'bone',
      'tissue',
    ];
    updateOrganDonation('specificOrgans', allOrgans);
  };

  const clearAllOrgans = () => {
    updateOrganDonation('specificOrgans', []);
  };

  // Medical directives
  const medicalDirectives = [
    {
      id: 'life_sustaining',
      label: 'Life-Sustaining Treatment',
      description:
        'Do you want life-sustaining treatment if you are in a terminal condition?',
    },
    {
      id: 'artificial_nutrition',
      label: 'Artificial Nutrition and Hydration',
      description:
        'Do you want artificial nutrition and hydration if you cannot eat or drink?',
    },
    {
      id: 'pain_relief',
      label: 'Pain Relief',
      description:
        'Do you want pain relief medication even if it might shorten your life?',
    },
    {
      id: 'antibiotics',
      label: 'Antibiotics',
      description: 'Do you want antibiotics to treat infections?',
    },
    {
      id: 'dialysis',
      label: 'Dialysis',
      description: 'Do you want dialysis if your kidneys fail?',
    },
    {
      id: 'ventilator',
      label: 'Mechanical Ventilation',
      description:
        'Do you want to be placed on a ventilator if you cannot breathe on your own?',
    },
  ];

  // Organ donation options
  const organOptions = [
    { id: 'heart', label: 'Heart' },
    { id: 'liver', label: 'Liver' },
    { id: 'kidneys', label: 'Kidneys' },
    { id: 'lungs', label: 'Lungs' },
    { id: 'pancreas', label: 'Pancreas' },
    { id: 'corneas', label: 'Corneas' },
    { id: 'skin', label: 'Skin' },
    { id: 'bone', label: 'Bone' },
    { id: 'tissue', label: 'Tissue' },
  ];

  const allRequiredCompleted = isAllRequiredFieldsCompleted();

  return (
    <div className='space-y-6'>
      {/* Healthcare Proxy Appointment */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              <CardTitle>Healthcare Proxy Appointment</CardTitle>
            </div>
            <QuestionVideoHint questionId='medical.proxy-selection' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <Label>
              Do you want to pick an individual as your healthcare proxy?
            </Label>
            <CheckboxRadioGroup
              value={
                value.proxy?.useCompany === undefined
                  ? ''
                  : value.proxy.useCompany === false
                    ? 'individual'
                    : 'company'
              }
              onValueChange={val => {
                if (val === '') {
                  updateProxy('useCompany', undefined);
                } else {
                  const newValue = val === 'company';
                  if (newValue !== value.proxy?.useCompany) {
                    updateProxy('useCompany', newValue);
                  }
                }
              }}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='individual'
                id='proxyIndividual'
                disabled={readOnly}
              >
                <Label htmlFor='proxyIndividual'>
                  Yes, I want to pick an individual
                </Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='company'
                id='proxyCompany'
                disabled={readOnly}
              >
                <Label htmlFor='proxyCompany'>No, appoint Our Company</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          {value.proxy?.useCompany === false && (
            <div className='space-y-4 border-t pt-4'>
              <div className='space-y-2'>
                <Label>Primary Healthcare Proxy *</Label>
                <PeopleSelectObject
                  value={value.proxy?.primary}
                  onValueChange={val => updateProxy('primary', val)}
                  people={people}
                  onPeopleChange={onPeopleChange}
                  placeholder='Select primary healthcare proxy'
                  noneOptionText='Select primary healthcare proxy'
                  filterType='individual'
                  includeNoneOption={false}
                  disabled={readOnly}
                />
                {value.proxy?.useCompany === false && !value.proxy?.primary && (
                  <Alert>
                    <AlertTriangle className='h-4 w-4' />
                    <AlertDescription>
                      Please select a primary healthcare proxy to continue.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className='space-y-3'>
                <Label>Successor Healthcare Proxies (in order)</Label>
                {(value.proxy?.successors || []).map((successor, index) => (
                  <div key={index} className='flex items-center gap-2'>
                    <Badge variant='outline'>{index + 1}</Badge>
                    <span className='flex-1'>{getPersonName(successor)}</span>
                    <Button
                      size='sm'
                      variant='ghost'
                      onClick={() => removeSuccessor(index)}
                      className='h-8 w-8 p-0 text-destructive'
                      disabled={readOnly}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                ))}

                <PeopleSelectObject
                  value={undefined}
                  onValueChange={person => person && addSuccessor(person)}
                  people={people}
                  onPeopleChange={onPeopleChange}
                  placeholder='Add successor proxy'
                  noneOptionText='Add successor proxy'
                  filterType='individual'
                  excludeIds={
                    [
                      value.proxy?.primary?.id,
                      ...(value.proxy?.successors || []).map(s => s.id),
                    ].filter(Boolean) as string[]
                  }
                  includeNoneOption={false}
                  disabled={readOnly}
                />
              </div>
            </div>
          )}

          <Alert>
            <Info className='h-4 w-4' />
            <AlertDescription>
              Our Company will be appointed as the final successor healthcare
              proxy.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Medical Directive Questions */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              <CardTitle>Medical Directive Questions</CardTitle>
            </div>
            <QuestionVideoHint questionId='medical.directives' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-4'>
            <Label className='text-base'>
              Please indicate your preferences for medical treatment:
            </Label>

            {medicalDirectives.map(directive => (
              <div
                key={directive.id}
                className='space-y-3 p-4 border rounded-lg'
              >
                <Label className='font-medium'>{directive.label}</Label>
                <p className='text-sm text-muted-foreground'>
                  {directive.description}
                </p>
                <CheckboxRadioGroup
                  value={value.directives?.[directive.id]?.toString() || ''}
                  onValueChange={val => {
                    if (val === '') {
                      updateDirectives(directive.id, undefined);
                    } else {
                      updateDirectives(directive.id, val === 'true');
                    }
                  }}
                  disabled={readOnly}
                >
                  <CheckboxRadioItem
                    value='true'
                    id={`${directive.id}_yes`}
                    disabled={readOnly}
                  >
                    <Label htmlFor={`${directive.id}_yes`}>Yes</Label>
                  </CheckboxRadioItem>
                  <CheckboxRadioItem
                    value='false'
                    id={`${directive.id}_no`}
                    disabled={readOnly}
                  >
                    <Label htmlFor={`${directive.id}_no`}>No</Label>
                  </CheckboxRadioItem>
                </CheckboxRadioGroup>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Proxy Authority & Consultation */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <UserCheck className='h-5 w-5' />
              <CardTitle>Proxy Authority & Consultation</CardTitle>
            </div>
            <div className='flex items-center gap-2'>
              <QuestionVideoHint questionId='medical.proxy-authority' />
            </div>
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <Label>How much authority should your healthcare proxy have?</Label>
            <CheckboxRadioGroup
              value={value.proxyAuthority || ''}
              onValueChange={val => onChange({ proxyAuthority: val })}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='follow_exactly'
                id='follow_exactly'
                className='items-start space-x-2'
                disabled={readOnly}
              >
                <div className='space-y-1'>
                  <Label htmlFor='follow_exactly'>
                    Follow this form exactly
                  </Label>
                  <p className='text-sm text-muted-foreground'>
                    Your proxy must follow your specific instructions in this
                    document
                  </p>
                </div>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='make_decisions'
                id='make_decisions'
                className='items-start space-x-2'
                disabled={readOnly}
              >
                <div className='space-y-1'>
                  <Label htmlFor='make_decisions'>
                    Make decisions on uncovered items
                  </Label>
                  <p className='text-sm text-muted-foreground'>
                    Your proxy can make decisions about medical situations not
                    covered in this document
                  </p>
                </div>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='full_authority'
                id='full_authority'
                className='items-start space-x-2'
                disabled={readOnly}
              >
                <div className='space-y-1'>
                  <Label htmlFor='full_authority'>
                    Full decision-making authority
                  </Label>
                  <p className='text-sm text-muted-foreground'>
                    Your proxy can make all medical decisions as they see fit
                  </p>
                </div>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          <div className='space-y-3 border-t pt-4'>
            <Label>
              Would you like doctors to consult with specific individuals or
              organizations?
            </Label>
            <CheckboxRadioGroup
              value={value.wantConsultation?.toString() || ''}
              onValueChange={val => {
                if (val === '') {
                  onChange({ wantConsultation: undefined });
                } else {
                  const wantConsultation = val === 'true';
                  onChange({
                    wantConsultation,
                    // Clear consultation people if they don't want consultation
                    ...(wantConsultation ? {} : { consultationPeople: [] }),
                  });
                }
              }}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='true'
                id='consultationYes'
                disabled={readOnly}
              >
                <Label htmlFor='consultationYes'>Yes</Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='false'
                id='consultationNo'
                disabled={readOnly}
              >
                <Label htmlFor='consultationNo'>No</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          {value.wantConsultation === true && (
            <div className='space-y-3 border-t pt-4'>
              <Label>
                Who should doctors consult when making medical decisions?
              </Label>
              <p className='text-sm text-muted-foreground'>
                Select people from your People Library that doctors should speak
                with.
              </p>

              {/* Selected consultation people */}
              {(value.consultationPeople || []).length > 0 && (
                <div className='space-y-2'>
                  <Label className='text-sm font-medium'>
                    Selected for consultation:
                  </Label>
                  <div className='flex flex-wrap gap-2'>
                    {(value.consultationPeople || []).map(person => (
                      <Badge
                        key={person.id}
                        variant='secondary'
                        className='flex items-center gap-1'
                      >
                        {getPersonName(person)}
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => removeConsultationPerson(person.id)}
                          className='h-4 w-4 p-0 hover:bg-transparent'
                          disabled={readOnly}
                        >
                          <X className='h-3 w-3' />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <PeopleSelectObject
                value={undefined}
                onValueChange={person =>
                  person && addConsultationPerson(person)
                }
                people={people}
                onPeopleChange={onPeopleChange}
                placeholder='Add person for consultation'
                noneOptionText='Add person for consultation'
                filterType='all'
                excludeIds={(value.consultationPeople || []).map(p => p.id)}
                includeNoneOption={false}
                disabled={readOnly}
              />

              {value.wantConsultation === true &&
                (!value.consultationPeople ||
                  value.consultationPeople.length === 0) && (
                  <Alert>
                    <AlertTriangle className='h-4 w-4' />
                    <AlertDescription>
                      Please add at least one person for consultation to
                      continue.
                    </AlertDescription>
                  </Alert>
                )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <UserCheck className='h-5 w-5' />
              <CardTitle>Medical Information Access</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          {
            <div className='space-y-3 border-t pt-4'>
              <Label>
                Who else should access your medical info (HIPAA release)?
              </Label>
              <p className='text-sm text-muted-foreground'>
                Select people from your People Library
              </p>

              {/* Selected consultation people */}
              {(value.medicalInfoAccessPeople || []).length > 0 && (
                <div className='space-y-2'>
                  <Label className='text-sm font-medium'>
                    Selected for access:
                  </Label>
                  <div className='flex flex-wrap gap-2'>
                    {(value.medicalInfoAccessPeople || []).map(person => (
                      <Badge
                        key={person.id}
                        variant='secondary'
                        className='flex items-center gap-1'
                      >
                        {getPersonName(person)}
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() =>
                            removeMedicalInfoAccessPerson(person.id)
                          }
                          disabled={readOnly}
                          className='h-4 w-4 p-0 hover:bg-transparent'
                        >
                          <X className='h-3 w-3' />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <PeopleSelectObject
                value={undefined}
                onValueChange={person =>
                  person && addMedicalInfoAccessPerson(person)
                }
                people={people}
                onPeopleChange={onPeopleChange}
                placeholder='Add person for consultation'
                noneOptionText='Add person for consultation'
                filterType='all'
                excludeIds={(value.medicalInfoAccessPeople || []).map(
                  p => p.id
                )}
                includeNoneOption={false}
                disabled={readOnly}
              />
            </div>
          }
        </CardContent>
      </Card>

      {/* Organ Donation */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Heart className='h-5 w-5' />
              <CardTitle>Organ Donation</CardTitle>
            </div>
            <QuestionVideoHint questionId='medical.organ-donation' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <Label>Do you want to donate your organs and tissues?</Label>
            <CheckboxRadioGroup
              value={value.organDonation?.enabled?.toString() || ''}
              onValueChange={val => {
                if (val === '') {
                  updateOrganDonation('enabled', undefined);
                } else {
                  updateOrganDonation('enabled', val === 'true');
                }
              }}
              disabled={readOnly}
            >
              <CheckboxRadioItem
                value='true'
                id='donateYes'
                disabled={readOnly}
              >
                <Label htmlFor='donateYes'>Yes, I want to donate</Label>
              </CheckboxRadioItem>
              <CheckboxRadioItem
                value='false'
                id='donateNo'
                disabled={readOnly}
              >
                <Label htmlFor='donateNo'>No, I do not want to donate</Label>
              </CheckboxRadioItem>
            </CheckboxRadioGroup>
          </div>

          {value.organDonation?.enabled && (
            <div className='space-y-4 border-t pt-4'>
              <div className='flex items-center justify-between'>
                <Label>Select specific organs and tissues to donate:</Label>
                <div className='flex gap-2'>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={selectAllOrgans}
                    disabled={readOnly}
                  >
                    Select All
                  </Button>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={clearAllOrgans}
                    disabled={readOnly}
                  >
                    Clear All
                  </Button>
                </div>
              </div>

              <div className='space-y-2'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='anyNeeded'
                    checked={
                      (value.organDonation?.specificOrgans || []).length ===
                      organOptions.length
                    }
                    onCheckedChange={checked => {
                      if (checked) {
                        selectAllOrgans();
                      } else {
                        clearAllOrgans();
                      }
                    }}
                    disabled={readOnly}
                  />
                  <Label htmlFor='anyNeeded' className='font-medium'>
                    Any needed organs or tissues
                  </Label>
                </div>

                <div className='grid grid-cols-2 md:grid-cols-3 gap-2 ml-6'>
                  {organOptions.map(organ => (
                    <div key={organ.id} className='flex items-center space-x-2'>
                      <Checkbox
                        id={organ.id}
                        checked={(
                          value.organDonation?.specificOrgans || []
                        ).includes(organ.id)}
                        onCheckedChange={checked =>
                          toggleOrganDonation(organ.id, !!checked)
                        }
                        disabled={readOnly}
                      />
                      <Label htmlFor={organ.id} className='text-sm'>
                        {organ.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='limitations'>
                  Additional limitations or instructions:
                </Label>
                <Textarea
                  id='limitations'
                  value={value.organDonation?.limitations || ''}
                  onChange={e =>
                    updateOrganDonation('limitations', e.target.value)
                  }
                  placeholder='Any specific limitations or instructions for organ donation...'
                  rows={3}
                  disabled={readOnly}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default MedicalDecisionsSection;
