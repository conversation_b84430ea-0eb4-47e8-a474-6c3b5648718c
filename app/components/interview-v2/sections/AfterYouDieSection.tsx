'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  CheckboxRadioGroup,
  CheckboxRadioItem,
} from '@/components/ui/checkbox-radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import PeopleSelectObject from '../PeopleSelectObject';
import TooltipInfo from '../../ui/tooltip-info';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Trash2,
  Home,
  Gift,
  Crown,
  Shield,
  Info,
  User<PERSON>heck,
  ChevronDown,
  ChevronUp,
  Al<PERSON><PERSON>riangle,
  Check,
} from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import MainBeneficiariesCard from './MainBeneficiariesCard';

interface AfterYouDieData {
  executor?: {
    useCompany?: boolean;
    primary?: Person;
    successors?: Person[];
  };
  successorTrustee?: {
    useCompany?: boolean;
    primary?: Person;
    successors?: Person[];
  };
  properties?: Array<{
    id: string;
    type: string;
    address: string;
    title: string;
    ownership: number;
    coOwners?: Array<{ person: Person; percentage: number }>;
    recipient?: Person;
  }>;
  specificGifts?: Array<{
    id: string;
    recipient: Person;
    type: 'financial' | 'other';
    bankName?: string;
    accountNumber?: string;
    accountType?: 'IRA' | 'Brokerage' | 'Savings' | 'Checking' | 'Other';
    distributionMethod?: 'fixed' | 'percentage';
    amount?: number;
    description?: string;
  }>;
  mainBeneficiaries?: Array<{
    id: string;
    primaryBeneficiary: Person;
    percentage: number;
    successors: Array<{ person: Person; isDescendants?: boolean }>;
    distributionStages?: Array<{ age: number; percentage: number }>;
  }>;
  lastResortBeneficiary?: Person;
  noOwnedProperties?: boolean;
}

interface AfterYouDieSectionProps {
  value: AfterYouDieData;
  onChange: (data: Partial<AfterYouDieData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  selectedDocuments?: {
    will?: boolean;
    trust?: boolean;
    financialPOA?: boolean;
    medicalPOA?: boolean;
  };
  markSubsectionComplete?: (
    sectionKey: string,
    subsectionKey: string,
    completed?: boolean
  ) => void;
  isSubsectionCompleted?: (
    sectionKey: string,
    subsectionKey: string
  ) => boolean;
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function AfterYouDieSection({
  value,
  onChange,
  people,
  onPeopleChange,
  selectedDocuments,
  markSubsectionComplete,
  isSubsectionCompleted,
  readOnly = false,
}: AfterYouDieSectionProps) {
  const { people: globalPeople } = usePeopleLibrary();
  const [newProperty, setNewProperty] = useState<Partial<any>>({});
  const [newGift, setNewGift] = useState<Partial<any>>({});

  // Collapsible sections state
  const [collapsedSections, setCollapsedSections] = useState<{
    [key: string]: boolean;
  }>({
    properties: false,
    specificGifts: false,
    mainBeneficiaries: false,
  });

  const toggleSection = (sectionKey: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  // Handle "no owned properties" checkbox
  const handleNoOwnedPropertiesChange = (checked: boolean) => {
    onChange({
      noOwnedProperties: checked,
      properties: checked ? [] : value.properties, // Clear properties if checking "no owned properties"
    });
  };

  const getPersonName = (person: Person | undefined) => {
    if (!person) return 'Unknown';

    // Get the most up-to-date person data from People Library
    const currentPerson = globalPeople.find(p => p.id === person.id) || person;

    if (currentPerson.type === 'individual') {
      const firstName = currentPerson.firstName || '';
      const middleName = currentPerson.middleName || '';
      const lastName = currentPerson.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return currentPerson.entityName || 'Unknown';
  };

  // Track previous completion states to prevent unnecessary updates
  const prevCompletionStates = useRef<Record<string, boolean>>({});

  // Auto-track subsection completion
  useEffect(() => {
    if (!markSubsectionComplete) return;

    // Check Executor subsection completion
    const executorComplete = !!(
      value.executor?.useCompany !== undefined &&
      (value.executor.useCompany || !!value.executor.primary)
    );

    // Check Successor Trustee subsection completion
    const trusteeComplete = !!(
      value.successorTrustee?.useCompany !== undefined &&
      (value.successorTrustee.useCompany || !!value.successorTrustee.primary)
    );

    // Check Properties subsection completion
    const propertiesComplete = !!((value.properties?.length || 0) > 0);

    // Check Specific Gifts subsection completion
    const giftsComplete = !!(
      (value.specificGifts?.length || 0) > 0 &&
      value.specificGifts?.every(gift => {
        if (gift.type === 'financial') {
          return (
            gift.bankName && gift.accountType && gift.amount && gift.amount > 0
          );
        }
        return true; // For 'other' type gifts, no additional validation needed
      })
    );

    // Check Main Beneficiaries subsection completion
    const beneficiariesComplete = !!(
      (value.mainBeneficiaries?.length || 0) > 0 &&
      value.mainBeneficiaries?.reduce(
        (sum, b) => sum + (b.percentage || 0),
        0
      ) === 100
    );

    // Check Last Resort Beneficiary subsection completion
    const lastResortComplete = !!value.lastResortBeneficiary;

    // Only call markSubsectionComplete if the state has actually changed
    const completionStates = {
      executor: executorComplete,
      successorTrustee: trusteeComplete,
      properties: propertiesComplete,
      specificGifts: giftsComplete,
      mainBeneficiaries: beneficiariesComplete,
      lastResort: lastResortComplete,
    };

    Object.entries(completionStates).forEach(([key, isComplete]) => {
      if (prevCompletionStates.current[key] !== isComplete) {
        markSubsectionComplete('afterYouDie', key, isComplete);
        prevCompletionStates.current[key] = isComplete;
      }
    });
  }, [value, markSubsectionComplete]);

  const updateExecutor = useCallback(
    (field: string, fieldValue: any) => {
      // Prevent unnecessary updates if value hasn't changed
      if (
        value.executor?.[field as keyof typeof value.executor] === fieldValue
      ) {
        return;
      }
      onChange({
        executor: {
          ...value.executor,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.executor]
  );

  const updateSuccessorTrustee = useCallback(
    (field: string, fieldValue: any) => {
      // Prevent unnecessary updates if value hasn't changed
      if (
        value.successorTrustee?.[
          field as keyof typeof value.successorTrustee
        ] === fieldValue
      ) {
        return;
      }
      onChange({
        successorTrustee: {
          ...value.successorTrustee,
          [field]: fieldValue,
        },
      });
    },
    [onChange, value.successorTrustee]
  );

  const addSuccessorToExecutor = (person: Person) => {
    const currentSuccessors = value.executor?.successors || [];
    updateExecutor('successors', [...currentSuccessors, person]);
  };

  const removeSuccessorFromExecutor = (index: number) => {
    const currentSuccessors = value.executor?.successors || [];
    updateExecutor(
      'successors',
      currentSuccessors.filter((_, i) => i !== index)
    );
  };

  const addSuccessorToTrustee = (person: Person) => {
    const currentSuccessors = value.successorTrustee?.successors || [];
    updateSuccessorTrustee('successors', [...currentSuccessors, person]);
  };

  const removeSuccessorFromTrustee = (index: number) => {
    const currentSuccessors = value.successorTrustee?.successors || [];
    updateSuccessorTrustee(
      'successors',
      currentSuccessors.filter((_, i) => i !== index)
    );
  };

  return (
    <div className='space-y-6'>
      {/* Executor Section */}
      {selectedDocuments?.will && (
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Crown className='h-5 w-5' />
                <CardTitle className='flex items-center gap-2'>
                  Executor
                  <TooltipInfo content='An executor is the person responsible for carrying out the instructions in your will, including distributing your assets and paying any debts.' />
                </CardTitle>
              </div>
              <QuestionVideoHint questionId='afterYouDie.executor' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-3'>
              <Label>Do you want to pick an individual as your executor?</Label>
              <CheckboxRadioGroup
                value={
                  value.executor?.useCompany === undefined
                    ? ''
                    : value.executor.useCompany === false
                      ? 'individual'
                      : 'company'
                }
                onValueChange={val => {
                  if (val === '') {
                    updateExecutor('useCompany', undefined);
                  } else {
                    const newValue = val === 'company';
                    if (newValue !== value.executor?.useCompany) {
                      updateExecutor('useCompany', newValue);
                    }
                  }
                }}
                disabled={readOnly}
              >
                <CheckboxRadioItem
                  value='individual'
                  id='executorIndividual'
                  disabled={readOnly}
                >
                  <Label htmlFor='executorIndividual'>
                    Yes, I want to pick an individual
                  </Label>
                </CheckboxRadioItem>
                <CheckboxRadioItem
                  value='company'
                  id='executorCompany'
                  disabled={readOnly}
                >
                  <Label htmlFor='executorCompany'>
                    No, appoint Our Company
                  </Label>
                </CheckboxRadioItem>
              </CheckboxRadioGroup>
            </div>

            {value.executor?.useCompany === false && (
              <div className='space-y-4 border-t pt-4'>
                <div className='space-y-2'>
                  <Label>Primary Executor *</Label>
                  <PeopleSelectObject
                    value={value.executor?.primary}
                    onValueChange={val => updateExecutor('primary', val)}
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select primary executor'
                    noneOptionText='Select primary executor'
                    filterType='individual'
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                  {value.executor?.useCompany === false &&
                    !value.executor?.primary && (
                      <Alert>
                        <AlertTriangle className='h-4 w-4' />
                        <AlertDescription>
                          Please select a primary executor to continue.
                        </AlertDescription>
                      </Alert>
                    )}
                </div>

                <div className='space-y-3'>
                  <Label>Successor Executors (in order)</Label>
                  {(value.executor?.successors || []).map(
                    (successor, index) => (
                      <div key={index} className='flex items-center gap-2'>
                        <Badge variant='outline'>{index + 1}</Badge>
                        <span className='flex-1'>
                          {getPersonName(successor)}
                        </span>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => removeSuccessorFromExecutor(index)}
                          className='h-8 w-8 p-0 text-destructive'
                          disabled={readOnly}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    )
                  )}

                  <PeopleSelectObject
                    value={undefined}
                    onValueChange={person =>
                      person && addSuccessorToExecutor(person)
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Add successor executor'
                    noneOptionText='Add successor executor'
                    filterType='individual'
                    excludeIds={
                      [
                        value.executor?.primary?.id,
                        ...(value.executor?.successors || []).map(s => s.id),
                      ].filter(Boolean) as string[]
                    }
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            <Alert>
              <Info className='h-4 w-4' />
              <AlertDescription>
                Our Company will be appointed as the final successor executor.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Successor Trustee Section */}
      {selectedDocuments?.trust && (
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Shield className='h-5 w-5' />
                <CardTitle className='flex items-center gap-2'>
                  Successor Trustee
                  <TooltipInfo content='A successor trustee is the person who will manage your trust if you become unable to do so, or after you pass away.' />
                </CardTitle>
              </div>
              <QuestionVideoHint questionId='afterYouDie.successor-trustee' />
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-3'>
              <Label>
                Do you want to pick an individual as your Successor Trustee?
              </Label>
              <CheckboxRadioGroup
                value={
                  value.successorTrustee?.useCompany === undefined
                    ? ''
                    : value.successorTrustee.useCompany === false
                      ? 'individual'
                      : 'company'
                }
                onValueChange={val => {
                  if (val === '') {
                    updateSuccessorTrustee('useCompany', undefined);
                  } else {
                    const newValue = val === 'company';
                    if (newValue !== value.successorTrustee?.useCompany) {
                      updateSuccessorTrustee('useCompany', newValue);
                    }
                  }
                }}
                disabled={readOnly}
              >
                <CheckboxRadioItem
                  value='individual'
                  id='trusteeIndividual'
                  disabled={readOnly}
                >
                  <Label htmlFor='trusteeIndividual'>
                    Yes, I want to pick an individual
                  </Label>
                </CheckboxRadioItem>
                <CheckboxRadioItem
                  value='company'
                  id='trusteeCompany'
                  disabled={readOnly}
                >
                  <Label htmlFor='trusteeCompany'>
                    No, appoint Our Company
                  </Label>
                </CheckboxRadioItem>
              </CheckboxRadioGroup>
            </div>

            {value.successorTrustee?.useCompany === false && (
              <div className='space-y-4 border-t pt-4'>
                <div className='space-y-2'>
                  <Label>Primary Successor Trustee *</Label>
                  <PeopleSelectObject
                    value={value.successorTrustee?.primary}
                    onValueChange={val =>
                      updateSuccessorTrustee('primary', val)
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select individual'
                    noneOptionText='Select individual'
                    filterType='individual'
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>

                <div className='space-y-3'>
                  <Label>Additional Successor Trustees (in order)</Label>
                  {!value.successorTrustee?.primary && (
                    <Alert>
                      <Info className='h-4 w-4' />
                      <AlertDescription>
                        Please select a Primary Successor Trustee before adding
                        additional trustees.
                      </AlertDescription>
                    </Alert>
                  )}
                  {(value.successorTrustee?.successors || []).map(
                    (successor, index) => (
                      <div key={index} className='flex items-center gap-2'>
                        <Badge variant='outline'>{index + 1}</Badge>
                        <span className='flex-1'>
                          {getPersonName(successor)}
                        </span>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={() => removeSuccessorFromTrustee(index)}
                          className='h-8 w-8 p-0 text-destructive'
                          disabled={readOnly}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    )
                  )}

                  <PeopleSelectObject
                    value={undefined}
                    onValueChange={person =>
                      person && addSuccessorToTrustee(person)
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder={
                      value.successorTrustee?.primary
                        ? 'Add successor trustee'
                        : 'Select Primary Successor Trustee first'
                    }
                    noneOptionText={
                      value.successorTrustee?.primary
                        ? 'Add successor trustee'
                        : 'Select Primary Successor Trustee first'
                    }
                    filterType='individual'
                    excludeIds={
                      [
                        value.successorTrustee?.primary?.id,
                        ...(value.successorTrustee?.successors || []).map(
                          s => s.id
                        ),
                      ].filter(Boolean) as string[]
                    }
                    includeNoneOption={false}
                    disabled={!value.successorTrustee?.primary || readOnly}
                  />
                </div>
              </div>
            )}

            <Alert>
              <Info className='h-4 w-4' />
              <AlertDescription>
                Our Company will be appointed as the final successor trustee.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Property Section */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Home className='h-5 w-5' />
              <CardTitle>Property</CardTitle>
            </div>
            <div className='flex items-center gap-2'>
              <QuestionVideoHint questionId='afterYouDie.properties' />
              <button
                onClick={() => toggleSection('properties')}
                className='p-1 hover:bg-gray-100 rounded'
              >
                {collapsedSections.properties ? (
                  <ChevronDown className='h-4 w-4' />
                ) : (
                  <ChevronUp className='h-4 w-4' />
                )}
              </button>
            </div>
          </div>
        </CardHeader>
        {!collapsedSections.properties && (
          <CardContent className='space-y-4'>
            {/* No Owned Properties Checkbox */}
            <div className='flex items-center space-x-2 p-3 bg-muted/50 rounded-lg'>
              <Checkbox
                id='no-owned-properties'
                checked={value.noOwnedProperties || false}
                onCheckedChange={handleNoOwnedPropertiesChange}
                disabled={readOnly}
              />
              <Label
                htmlFor='no-owned-properties'
                className='text-sm font-medium'
              >
                I don't have any owned properties
              </Label>
            </div>

            {/* Existing Properties - only show if not checked "no owned properties" */}
            {!value.noOwnedProperties &&
              (value.properties || []).map(property => (
                <div
                  key={property.id}
                  className='border rounded-lg p-4 space-y-3'
                >
                  <div className='flex items-center justify-between'>
                    <h4 className='font-medium'>Property: {property.type}</h4>
                    <Button
                      size='sm'
                      variant='ghost'
                      onClick={() => {
                        const updatedProperties = (
                          value.properties || []
                        ).filter(p => p.id !== property.id);
                        onChange({ properties: updatedProperties });
                      }}
                      className='h-8 w-8 p-0 text-destructive'
                      disabled={readOnly}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                    <div className='space-y-2'>
                      <Label>Property Type</Label>
                      <Select
                        value={property.type}
                        onValueChange={val => {
                          const updatedProperties = (
                            value.properties || []
                          ).map(p =>
                            p.id === property.id ? { ...p, type: val } : p
                          );
                          onChange({ properties: updatedProperties });
                        }}
                        disabled={readOnly}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Primary Residence'>
                            Primary Residence
                          </SelectItem>
                          <SelectItem value='Secondary Residence'>
                            Secondary Residence
                          </SelectItem>
                          <SelectItem value='Investment Property'>
                            Investment Property
                          </SelectItem>
                          <SelectItem value='Commercial Property'>
                            Commercial Property
                          </SelectItem>
                          <SelectItem value='Land'>Land</SelectItem>
                          <SelectItem value='Other'>Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className='space-y-2'>
                      <Label>Address</Label>
                      <Input
                        value={property.address}
                        onChange={e => {
                          const updatedProperties = (
                            value.properties || []
                          ).map(p =>
                            p.id === property.id
                              ? { ...p, address: e.target.value }
                              : p
                          );
                          onChange({ properties: updatedProperties });
                        }}
                        placeholder='Property address'
                        disabled={readOnly}
                      />
                    </div>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                    <div className='space-y-2'>
                      <Label>How it's Titled</Label>
                      <Select
                        value={property.title}
                        onValueChange={val => {
                          const updatedProperties = (
                            value.properties || []
                          ).map(p =>
                            p.id === property.id
                              ? {
                                  ...p,
                                  title: val,
                                  ownership:
                                    val === 'Sole Ownership'
                                      ? 100
                                      : p.ownership,
                                }
                              : p
                          );
                          onChange({ properties: updatedProperties });
                        }}
                        disabled={readOnly}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Sole Ownership'>
                            Sole Ownership
                          </SelectItem>
                          <SelectItem value='Joint Tenancy'>
                            Joint Tenancy
                          </SelectItem>
                          <SelectItem value='Tenancy in Common'>
                            Tenancy in Common
                          </SelectItem>
                          <SelectItem value='Community Property'>
                            Community Property
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className='space-y-2'>
                      <Label>Ownership Percentage</Label>
                      <Input
                        type='number'
                        min='0'
                        max='100'
                        value={property.ownership}
                        onChange={e => {
                          const updatedProperties = (
                            value.properties || []
                          ).map(p =>
                            p.id === property.id
                              ? {
                                  ...p,
                                  ownership: parseInt(e.target.value) || 0,
                                }
                              : p
                          );
                          onChange({ properties: updatedProperties });
                        }}
                        disabled={
                          property.title === 'Sole Ownership' || readOnly
                        }
                        placeholder='100'
                      />
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label>Who should receive it</Label>
                    <PeopleSelectObject
                      value={property.recipient}
                      onValueChange={val => {
                        const updatedProperties = (value.properties || []).map(
                          p =>
                            p.id === property.id ? { ...p, recipient: val } : p
                        );
                        onChange({ properties: updatedProperties });
                      }}
                      people={people}
                      onPeopleChange={onPeopleChange}
                      placeholder='Select recipient'
                      noneOptionText='Select recipient'
                      filterType='all'
                      disabled={readOnly}
                    />
                  </div>
                </div>
              ))}

            {/* Add New Property - only show if not checked "no owned properties" */}
            {!value.noOwnedProperties && (
              <div className='border-2 border-dashed rounded-lg p-4 space-y-3'>
                <h4 className='font-medium text-muted-foreground'>
                  Add New Property
                </h4>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                  <div className='space-y-2'>
                    <Label>Property Type</Label>
                    <Select
                      value={newProperty.type || 'none'}
                      onValueChange={val =>
                        setNewProperty(prev => ({
                          ...prev,
                          type: val === 'none' ? '' : val,
                        }))
                      }
                      disabled={readOnly}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select type' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='none'>Select type</SelectItem>
                        <SelectItem value='Primary Residence'>
                          Primary Residence
                        </SelectItem>
                        <SelectItem value='Secondary Residence'>
                          Secondary Residence
                        </SelectItem>
                        <SelectItem value='Investment Property'>
                          Investment Property
                        </SelectItem>
                        <SelectItem value='Commercial Property'>
                          Commercial Property
                        </SelectItem>
                        <SelectItem value='Land'>Land</SelectItem>
                        <SelectItem value='Other'>Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='space-y-2'>
                    <Label>Address</Label>
                    <Input
                      value={newProperty.address || ''}
                      onChange={e =>
                        setNewProperty(prev => ({
                          ...prev,
                          address: e.target.value,
                        }))
                      }
                      placeholder='Property address'
                      disabled={readOnly}
                    />
                  </div>
                </div>
                <Button
                  onClick={() => {
                    if (!newProperty.type || !newProperty.address) {
                      alert('Please fill in property type and address.');
                      return;
                    }

                    const property = {
                      id: Date.now().toString(),
                      type: newProperty.type,
                      address: newProperty.address,
                      title: 'Sole Ownership',
                      ownership: 100,
                      recipient: undefined,
                    };

                    onChange({
                      properties: [...(value.properties || []), property],
                      noOwnedProperties: false, // Clear the "no owned properties" flag when adding
                    });

                    setNewProperty({});
                  }}
                  className='w-full'
                  disabled={readOnly}
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Property
                </Button>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* Specific Gifts Section */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Gift className='h-5 w-5' />
              <CardTitle>Specific Gifts</CardTitle>
            </div>
            <div className='flex items-center gap-2'>
              <QuestionVideoHint questionId='afterYouDie.specific-gifts' />
              <button
                onClick={() => toggleSection('specificGifts')}
                className='p-1 hover:bg-gray-100 rounded'
              >
                {collapsedSections.specificGifts ? (
                  <ChevronDown className='h-4 w-4' />
                ) : (
                  <ChevronUp className='h-4 w-4' />
                )}
              </button>
            </div>
          </div>
        </CardHeader>
        {!collapsedSections.specificGifts && (
          <CardContent className='space-y-4'>
            {/* Existing Gifts */}
            {(value.specificGifts || []).map(gift => (
              <div key={gift.id} className='border rounded-lg p-4 space-y-3'>
                <div className='flex items-center justify-between'>
                  <h4 className='font-medium'>
                    Gift to {getPersonName(gift.recipient)}
                  </h4>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => {
                      const updatedGifts = (value.specificGifts || []).filter(
                        g => g.id !== gift.id
                      );
                      onChange({ specificGifts: updatedGifts });
                    }}
                    className='h-8 w-8 p-0 text-destructive'
                    disabled={readOnly}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                  <div className='space-y-2'>
                    <Label>Recipient</Label>
                    <PeopleSelectObject
                      value={gift.recipient}
                      onValueChange={val => {
                        if (val) {
                          const updatedGifts = (value.specificGifts || []).map(
                            g =>
                              g.id === gift.id ? { ...g, recipient: val } : g
                          );
                          onChange({ specificGifts: updatedGifts });
                        }
                      }}
                      people={people}
                      onPeopleChange={onPeopleChange}
                      placeholder='Select recipient'
                      noneOptionText='Select recipient'
                      filterType='all'
                      includeNoneOption={false}
                      disabled={readOnly}
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label>Gift Type</Label>
                    <Select
                      value={gift.type}
                      onValueChange={val => {
                        const updatedGifts = (value.specificGifts || []).map(
                          g =>
                            g.id === gift.id
                              ? { ...g, type: val as 'financial' | 'other' }
                              : g
                        );
                        onChange({ specificGifts: updatedGifts });
                      }}
                      disabled={readOnly}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='financial'>
                          Financial Account
                        </SelectItem>
                        <SelectItem value='other'>
                          Other Specific Item
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {gift.type === 'financial' ? (
                  <div className='space-y-3'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                      <div className='space-y-2'>
                        <Label>Bank Name</Label>
                        <Input
                          value={gift.bankName || ''}
                          onChange={e => {
                            const updatedGifts = (
                              value.specificGifts || []
                            ).map(g =>
                              g.id === gift.id
                                ? { ...g, bankName: e.target.value }
                                : g
                            );
                            onChange({ specificGifts: updatedGifts });
                          }}
                          placeholder='Bank name'
                          disabled={readOnly}
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label>Account Type</Label>
                        <Select
                          value={gift.accountType || ''}
                          onValueChange={val => {
                            const updatedGifts = (
                              value.specificGifts || []
                            ).map(g =>
                              g.id === gift.id
                                ? {
                                    ...g,
                                    accountType: val as
                                      | 'IRA'
                                      | 'Brokerage'
                                      | 'Savings'
                                      | 'Checking'
                                      | 'Other',
                                  }
                                : g
                            );
                            onChange({ specificGifts: updatedGifts });
                          }}
                          disabled={readOnly}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select account type' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='IRA'>IRA</SelectItem>
                            <SelectItem value='Brokerage'>Brokerage</SelectItem>
                            <SelectItem value='Savings'>Savings</SelectItem>
                            <SelectItem value='Checking'>Checking</SelectItem>
                            <SelectItem value='Other'>Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                      <div className='space-y-2'>
                        <Label>Account Number (Optional)</Label>
                        <Input
                          value={gift.accountNumber || ''}
                          onChange={e => {
                            const updatedGifts = (
                              value.specificGifts || []
                            ).map(g =>
                              g.id === gift.id
                                ? { ...g, accountNumber: e.target.value }
                                : g
                            );
                            onChange({ specificGifts: updatedGifts });
                          }}
                          placeholder='Account number'
                          disabled={readOnly}
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label>Distribution Method</Label>
                        <Select
                          value={gift.distributionMethod || 'none'}
                          onValueChange={val => {
                            const updatedGifts = (
                              value.specificGifts || []
                            ).map(g =>
                              g.id === gift.id
                                ? {
                                    ...g,
                                    distributionMethod:
                                      val === 'none'
                                        ? undefined
                                        : (val as 'fixed' | 'percentage'),
                                  }
                                : g
                            );
                            onChange({ specificGifts: updatedGifts });
                          }}
                          disabled={readOnly}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select method' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>Select method</SelectItem>
                            <SelectItem value='fixed'>Fixed Amount</SelectItem>
                            <SelectItem value='percentage'>
                              Percentage
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    {gift.distributionMethod && (
                      <div className='grid grid-cols-1 gap-3'>
                        <div className='space-y-2'>
                          <Label>
                            {gift.distributionMethod === 'percentage'
                              ? 'Percentage'
                              : 'Amount'}
                          </Label>
                          <Input
                            type='number'
                            value={gift.amount || ''}
                            onChange={e => {
                              const updatedGifts = (
                                value.specificGifts || []
                              ).map(g =>
                                g.id === gift.id
                                  ? {
                                      ...g,
                                      amount: parseFloat(e.target.value) || 0,
                                    }
                                  : g
                              );
                              onChange({ specificGifts: updatedGifts });
                            }}
                            placeholder={
                              gift.distributionMethod === 'percentage'
                                ? '10'
                                : '1000'
                            }
                            disabled={readOnly}
                          />
                        </div>
                      </div>
                    )}{' '}
                    {gift.type === 'financial' &&
                      (!gift.bankName || !gift.amount || gift.amount <= 0) && (
                        <Alert>
                          <Info className='h-4 w-4' />
                          <AlertDescription>
                            Please fill in Bank Name and Amount to complete this
                            financial gift.
                          </AlertDescription>
                        </Alert>
                      )}
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <Label>Item Description</Label>
                    <Textarea
                      value={gift.description || ''}
                      onChange={e => {
                        const updatedGifts = (value.specificGifts || []).map(
                          g =>
                            g.id === gift.id
                              ? { ...g, description: e.target.value }
                              : g
                        );
                        onChange({ specificGifts: updatedGifts });
                      }}
                      placeholder='e.g., Rolex watch, family heirloom, artwork'
                      rows={3}
                      disabled={readOnly}
                    />
                  </div>
                )}
              </div>
            ))}

            {/* Add New Gift */}
            <div className='border-2 border-dashed rounded-lg p-4 space-y-3'>
              <h4 className='font-medium text-muted-foreground'>
                Add New Specific Gift
              </h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>Recipient</Label>
                  <PeopleSelectObject
                    value={newGift.recipient}
                    onValueChange={val =>
                      setNewGift(prev => ({
                        ...prev,
                        recipient: val,
                      }))
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select recipient'
                    noneOptionText='Select recipient'
                    filterType='all'
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Gift Type</Label>
                  <Select
                    value={newGift.type || 'none'}
                    onValueChange={val =>
                      setNewGift(prev => ({
                        ...prev,
                        type: val === 'none' ? '' : val,
                      }))
                    }
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='none'>Select type</SelectItem>
                      <SelectItem value='financial'>
                        Financial Account
                      </SelectItem>
                      <SelectItem value='other'>Other Specific Item</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button
                onClick={() => {
                  if (!newGift.recipient || !newGift.type) {
                    alert('Please select recipient and gift type.');
                    return;
                  }

                  const gift = {
                    id: Date.now().toString(),
                    recipient: newGift.recipient,
                    type: newGift.type as 'financial' | 'other',
                  };

                  onChange({
                    specificGifts: [...(value.specificGifts || []), gift],
                  });

                  setNewGift({});
                }}
                className='w-full'
                disabled={readOnly}
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Specific Gift
              </Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Main Beneficiaries Section */}
      <MainBeneficiariesCard
        beneficiaries={value.mainBeneficiaries || []}
        onChange={beneficiaries =>
          onChange({ mainBeneficiaries: beneficiaries })
        }
        people={people}
        onPeopleChange={onPeopleChange}
        isSubsectionCompleted={isSubsectionCompleted}
        collapsed={collapsedSections.mainBeneficiaries}
        onToggleCollapse={() => toggleSection('mainBeneficiaries')}
        readOnly={readOnly}
      />

      {/* Beneficiary of Last Resort */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <UserCheck className='h-5 w-5' />
              <CardTitle>Beneficiary of Last Resort</CardTitle>
            </div>
            <QuestionVideoHint questionId='afterYouDie.last-resort' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            <Label>
              If all main beneficiaries and their successors fail, who should
              receive your estate?
            </Label>
            <PeopleSelectObject
              value={value.lastResortBeneficiary}
              onValueChange={val => onChange({ lastResortBeneficiary: val })}
              people={people}
              onPeopleChange={onPeopleChange}
              placeholder='Select beneficiary of last resort'
              noneOptionText='Select beneficiary of last resort'
              filterType='all'
              excludeIds={
                (value.mainBeneficiaries || [])
                  .map(b => b.primaryBeneficiary?.id)
                  .filter(Boolean) as string[]
              }
              disabled={readOnly}
            />
            <p className='text-sm text-muted-foreground'>
              This person/entity will receive your estate only if all other
              beneficiaries are unable to inherit.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AfterYouDieSection;
