'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import PeopleSelectObject from '../PeopleSelectObject';
import {
  Plus,
  Trash2,
  ChevronUp,
  ChevronDown,
  Users,
  Heart,
  Check,
} from 'lucide-react';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import { Checkbox } from '@/components/ui/checkbox';

interface Dependent {
  id: string;
  person: Person; // Reference to person in People Library (relationshipType is stored in person)
  careInstructions: string;
}

interface Pet {
  id: string;
  name: string;
  primaryGuardian?: Person; // Required field
  secondaryGuardian?: Person; // Optional backup
  petTrustSupport?: number; // Dollar amount for pet care
  takerOfLastResort?: Person; // Final guardian field
}

interface EmergencyData {
  dependents?: Dependent[];
  pets?: Pet[];
  noDependents?: boolean;
  noPets?: boolean;
}

interface EmergencySectionProps {
  value: EmergencyData;
  onChange: (data: Partial<EmergencyData>) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  readOnly?: boolean; // Flag to make all fields read-only when interview is submitted
}

export function EmergencySection({
  value,
  onChange,
  people,
  onPeopleChange,
  readOnly = false,
}: EmergencySectionProps) {
  const [newDependent, setNewDependent] = useState<{
    person?: Person;
    careInstructions?: string;
  }>({});
  const [newPet, setNewPet] = useState<Partial<Pet>>({});

  // States to track the display text for pet trust support inputs
  const [petTrustInputs, setPetTrustInputs] = useState<Record<string, string>>(
    {}
  );
  const [newPetTrustInput, setNewPetTrustInput] = useState<string>('');

  const { addPerson } = usePeopleLibrary();

  const dependents = value.dependents || [];
  const pets = value.pets || [];

  const addDependent = async () => {
    if (!newDependent.person) {
      alert('Please select a person for the dependent.');
      return;
    }

    if (!newDependent.person.relationshipType) {
      alert(
        'Please set a relationship type for this person in the People Library.'
      );
      return;
    }

    const dependent: Dependent = {
      id: Date.now().toString(),
      person: newDependent.person,
      careInstructions: newDependent.careInstructions || '',
    };

    onChange({
      dependents: [...dependents, dependent],
      noDependents: false, // Clear the "no dependents" flag when adding
    });

    setNewDependent({});
  };

  const removeDependent = (id: string) => {
    if (confirm('Are you sure you want to remove this dependent?')) {
      onChange({
        dependents: dependents.filter(d => d.id !== id),
      });
    }
  };

  const updateDependent = (
    id: string,
    field: keyof Dependent,
    value: string | Person
  ) => {
    onChange({
      dependents: dependents.map(d =>
        d.id === id ? { ...d, [field]: value } : d
      ),
    });
  };

  const moveDependentUp = (index: number) => {
    if (index > 0) {
      const newDependents = [...dependents];
      [newDependents[index - 1], newDependents[index]] = [
        newDependents[index],
        newDependents[index - 1],
      ];
      onChange({ dependents: newDependents });
    }
  };

  const moveDependentDown = (index: number) => {
    if (index < dependents.length - 1) {
      const newDependents = [...dependents];
      [newDependents[index], newDependents[index + 1]] = [
        newDependents[index + 1],
        newDependents[index],
      ];
      onChange({ dependents: newDependents });
    }
  };

  const addPet = () => {
    if (!newPet.name) {
      alert('Please enter a name for the pet.');
      return;
    }

    if (!newPet.primaryGuardian) {
      alert('Please select a primary guardian for the pet.');
      return;
    }

    const pet: Pet = {
      id: Date.now().toString(),
      name: newPet.name,
      primaryGuardian: newPet.primaryGuardian,
      secondaryGuardian: newPet.secondaryGuardian,
      petTrustSupport: newPet.petTrustSupport,
      takerOfLastResort: newPet.takerOfLastResort,
    };

    onChange({
      pets: [...pets, pet],
      noPets: false, // Clear the "no pets" flag when adding
    });

    setNewPet({});
    setNewPetTrustInput(''); // Clear the input text
  };

  // Handle "no dependents" checkbox
  const handleNoDependentsChange = (checked: boolean) => {
    onChange({
      noDependents: checked,
      dependents: checked ? [] : dependents, // Clear dependents if checking "no dependents"
    });
  };

  // Handle "no pets" checkbox
  const handleNoPetsChange = (checked: boolean) => {
    onChange({
      noPets: checked,
      pets: checked ? [] : pets, // Clear pets if checking "no pets"
    });
  };

  const removePet = (id: string) => {
    if (confirm('Are you sure you want to remove this pet?')) {
      onChange({
        pets: pets.filter(p => p.id !== id),
      });

      // Clear the input text for this pet
      setPetTrustInputs(prev => {
        const newInputs = { ...prev };
        delete newInputs[id];
        return newInputs;
      });
    }
  };

  // Updated updatePet function to handle Person objects and numbers
  const updatePet = (
    id: string,
    field: keyof Pet,
    value: string | Person | number | undefined
  ) => {
    onChange({
      pets: pets.map(p => (p.id === id ? { ...p, [field]: value } : p)),
    });
  };

  const getPersonName = (person: Person | undefined) => {
    if (!person) return 'Unknown';

    if (person.type === 'individual') {
      return `${person.firstName || ''} ${person.lastName || ''}`.trim();
    }
    return person.entityName || 'Unknown';
  };

  // Helper function to filter input to only allow digits and dots
  const handlePetTrustSupportInput = (value: string) => {
    // Remove any characters that are not digits or dots
    const filteredValue = value.replace(/[^0-9.]/g, '');

    // Ensure only one dot is allowed
    const parts = filteredValue.split('.');
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('');
    }

    return filteredValue;
  };

  // Helper function to check if a string represents a valid number for storage
  const isValidNumberForStorage = (value: string) => {
    if (!value || value === '.' || value === '') return false;
    const num = parseFloat(value);
    return !isNaN(num) && isFinite(num);
  };

  // Helper function to format number for display (always use dot as decimal separator)
  const formatPetTrustSupportForDisplay = (
    value: number | undefined,
    petId?: string
  ) => {
    // If we have a stored input text for this pet, use it
    if (petId && petTrustInputs[petId] !== undefined) {
      return petTrustInputs[petId];
    }

    if (value === undefined || value === null || isNaN(value)) return '';
    // Convert to string and ensure dot is used as decimal separator and add two decimal places
    return value.toFixed(2).replace(',', '.');
  };

  return (
    <div className='space-y-6'>
      {/* Dependents Section */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              <CardTitle>Dependents</CardTitle>
            </div>
            <QuestionVideoHint questionId='emergency.dependents' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Existing Dependents */}
          {dependents.map((dependent, index) => (
            <div key={dependent.id} className='border rounded-lg p-4 space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium'>Dependent {index + 1}</h4>
                <div className='flex items-center gap-1'>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => moveDependentUp(index)}
                    disabled={index === 0 || readOnly}
                    className='h-8 w-8 p-0'
                  >
                    <ChevronUp className='h-4 w-4' />
                  </Button>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => moveDependentDown(index)}
                    disabled={index === dependents.length - 1 || readOnly}
                    className='h-8 w-8 p-0'
                  >
                    <ChevronDown className='h-4 w-4' />
                  </Button>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => removeDependent(dependent.id)}
                    disabled={readOnly}
                    className='h-8 w-8 p-0 text-destructive hover:text-destructive'
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>Person</Label>
                  <PeopleSelectObject
                    value={dependent.person}
                    onValueChange={person =>
                      updateDependent(dependent.id, 'person', person!)
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select person'
                    filterType='individual'
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Relationship</Label>
                  <div className='flex items-center gap-2'>
                    <Input
                      value={dependent.person?.relationshipType || 'Not set'}
                      disabled={true}
                      className='bg-muted text-muted-foreground'
                    />
                  </div>
                </div>
              </div>

              <div className='space-y-2'>
                <Label>Care Instructions</Label>
                <Textarea
                  value={dependent.careInstructions}
                  onChange={e =>
                    updateDependent(
                      dependent.id,
                      'careInstructions',
                      e.target.value
                    )
                  }
                  placeholder='Special care instructions, medical needs, preferences, etc.'
                  rows={3}
                  disabled={readOnly}
                />
              </div>
            </div>
          ))}

          {/* No Dependents Checkbox */}
          <div className='flex items-center space-x-2 p-3 bg-muted/50 rounded-lg'>
            <Checkbox
              id='no-dependents'
              checked={value.noDependents || false}
              onCheckedChange={handleNoDependentsChange}
              disabled={readOnly}
            />
            <Label htmlFor='no-dependents' className='text-sm font-medium'>
              I don't have any dependents
            </Label>
          </div>

          {/* Add New Dependent - only show if not checked "no dependents" */}
          {!value.noDependents && (
            <div className='border-2 border-dashed rounded-lg p-4 space-y-3'>
              <h4 className='font-medium text-muted-foreground'>
                Add New Dependent
              </h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>Person</Label>
                  <PeopleSelectObject
                    value={newDependent.person}
                    onValueChange={person =>
                      setNewDependent(prev => ({ ...prev, person }))
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select or create person'
                    filterType='individual'
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Relationship</Label>
                  <div className='flex items-center gap-2'>
                    <Input
                      value={
                        newDependent.person?.relationshipType ||
                        'Select person first'
                      }
                      disabled={true}
                      className='bg-muted text-muted-foreground'
                    />
                  </div>
                </div>
              </div>
              <div className='space-y-2'>
                <Label>Care Instructions</Label>
                <Textarea
                  value={newDependent.careInstructions || ''}
                  onChange={e =>
                    setNewDependent(prev => ({
                      ...prev,
                      careInstructions: e.target.value,
                    }))
                  }
                  placeholder='Special care instructions, medical needs, preferences, etc.'
                  rows={3}
                  disabled={readOnly}
                  className={readOnly ? 'bg-muted text-muted-foreground' : ''}
                />
              </div>
              <Button
                onClick={addDependent}
                className='w-full'
                disabled={readOnly}
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Dependent
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pets Section */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Heart className='h-5 w-5' />
              <CardTitle>Pets</CardTitle>
            </div>
            <QuestionVideoHint questionId='emergency.pets' />
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Existing Pets */}
          {pets.map(pet => (
            <div key={pet.id} className='border rounded-lg p-4 space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium'>Pet: {pet.name}</h4>
                <Button
                  size='sm'
                  variant='ghost'
                  onClick={() => removePet(pet.id)}
                  className='h-8 w-8 p-0 text-destructive hover:text-destructive'
                  disabled={readOnly}
                >
                  <Trash2 className='h-4 w-4' />
                </Button>
              </div>

              <div className='space-y-4'>
                {/* Pet Name */}
                <div className='space-y-2'>
                  <Label>Pet's Name</Label>
                  <Input
                    value={pet.name}
                    onChange={e => updatePet(pet.id, 'name', e.target.value)}
                    placeholder='Pet name'
                    disabled={readOnly}
                  />
                </div>

                {/* Primary and Secondary Guardians */}
                <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                  <div className='space-y-2'>
                    <Label>
                      Primary Guardian <span className='text-red-500'>*</span>
                    </Label>
                    <PeopleSelectObject
                      value={pet.primaryGuardian}
                      onValueChange={value =>
                        updatePet(pet.id, 'primaryGuardian', value)
                      }
                      people={people}
                      onPeopleChange={onPeopleChange}
                      placeholder='Select primary guardian'
                      noneOptionText='No primary guardian selected'
                      filterType='all'
                      disabled={readOnly}
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label>Secondary Guardian (Optional)</Label>
                    <PeopleSelectObject
                      value={pet.secondaryGuardian}
                      onValueChange={value =>
                        updatePet(pet.id, 'secondaryGuardian', value)
                      }
                      people={people}
                      onPeopleChange={onPeopleChange}
                      placeholder='Select secondary guardian'
                      noneOptionText='No secondary guardian selected'
                      filterType='all'
                      disabled={readOnly}
                    />
                  </div>
                </div>

                {/* Pet Trust Support and Taker of Last Resort */}
                <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                  <div className='space-y-2'>
                    <Label>Pet Trust Support ($)</Label>
                    <Input
                      type='text'
                      value={formatPetTrustSupportForDisplay(
                        pet.petTrustSupport,
                        pet.id
                      )}
                      onChange={e => {
                        const filteredValue = handlePetTrustSupportInput(
                          e.target.value
                        );

                        // Store the display text
                        setPetTrustInputs(prev => ({
                          ...prev,
                          [pet.id]: filteredValue,
                        }));

                        // Update the actual numeric value only if it's valid
                        updatePet(
                          pet.id,
                          'petTrustSupport',
                          isValidNumberForStorage(filteredValue)
                            ? parseFloat(filteredValue)
                            : undefined
                        );
                      }}
                      placeholder='0.00'
                      disabled={readOnly}
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label>Taker of Last Resort</Label>
                    <PeopleSelectObject
                      value={pet.takerOfLastResort}
                      onValueChange={value =>
                        updatePet(pet.id, 'takerOfLastResort', value)
                      }
                      people={people}
                      onPeopleChange={onPeopleChange}
                      placeholder='Select taker of last resort'
                      noneOptionText='Our Company (default)'
                      filterType='all'
                      disabled={readOnly}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* No Pets Checkbox */}
          <div className='flex items-center space-x-2 p-3 bg-muted/50 rounded-lg'>
            <Checkbox
              id='no-pets'
              checked={value.noPets || false}
              onCheckedChange={handleNoPetsChange}
              disabled={readOnly}
            />
            <Label htmlFor='no-pets' className='text-sm font-medium'>
              I don't have any pets
            </Label>
          </div>

          {/* Add New Pet - only show if not checked "no pets" */}
          {!value.noPets && (
            <div className='border-2 border-dashed rounded-lg p-4 space-y-4'>
              <h4 className='font-medium text-muted-foreground'>Add New Pet</h4>

              {/* Pet Name */}
              <div className='space-y-2'>
                <Label>Pet's Name</Label>
                <Input
                  value={newPet.name || ''}
                  onChange={e =>
                    setNewPet(prev => ({ ...prev, name: e.target.value }))
                  }
                  placeholder='Pet name'
                  disabled={readOnly}
                />
              </div>

              {/* Primary and Secondary Guardians */}
              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>
                    Primary Guardian <span className='text-red-500'>*</span>
                  </Label>
                  <PeopleSelectObject
                    value={newPet.primaryGuardian}
                    onValueChange={value =>
                      setNewPet(prev => ({
                        ...prev,
                        primaryGuardian: value,
                      }))
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select primary guardian'
                    noneOptionText='No primary guardian selected'
                    filterType='all'
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Secondary Guardian (Optional)</Label>
                  <PeopleSelectObject
                    value={newPet.secondaryGuardian}
                    onValueChange={value =>
                      setNewPet(prev => ({
                        ...prev,
                        secondaryGuardian: value,
                      }))
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select secondary guardian'
                    noneOptionText='No secondary guardian selected'
                    filterType='all'
                    disabled={readOnly}
                  />
                </div>
              </div>

              {/* Pet Trust Support and Taker of Last Resort */}
              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>Pet Trust Support ($)</Label>
                  <Input
                    type='text'
                    value={
                      newPetTrustInput ||
                      formatPetTrustSupportForDisplay(newPet.petTrustSupport)
                    }
                    onChange={e => {
                      const filteredValue = handlePetTrustSupportInput(
                        e.target.value
                      );

                      // Store the display text
                      setNewPetTrustInput(filteredValue);

                      // Update the actual numeric value only if it's valid
                      setNewPet(prev => ({
                        ...prev,
                        petTrustSupport: isValidNumberForStorage(filteredValue)
                          ? parseFloat(filteredValue)
                          : undefined,
                      }));
                    }}
                    placeholder='0.00'
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Taker of Last Resort</Label>
                  <PeopleSelectObject
                    value={newPet.takerOfLastResort}
                    onValueChange={value =>
                      setNewPet(prev => ({
                        ...prev,
                        takerOfLastResort: value,
                      }))
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select taker of last resort'
                    noneOptionText='Our Company (default)'
                    filterType='all'
                    disabled={readOnly}
                  />
                </div>
              </div>

              <Button onClick={addPet} className='w-full' disabled={readOnly}>
                <Plus className='h-4 w-4 mr-2' />
                Add Pet
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default EmergencySection;
