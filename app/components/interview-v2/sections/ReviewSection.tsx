'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  CheckCircle,
  XCircle,
  Edit,
  AlertTriangle,
  User,
  Heart,
  Crown,
  DollarSign,
  Shield,
  RefreshCw,
  Plus,
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { finalizeDocumentsForTemplates } from '@/lib/utils/document-create/finalize-documents';
import { getMemberAvailableTemplates } from '@/lib/api/member-documents';
import {
  fetchUserByCognitoId,
  fetchUserProfileByCognitoId,
} from '@/lib/data/users';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import {
  recreateDocument,
  getUserDocumentsByUserId,
} from '@/lib/api/documents';
import routes from '@/utils/routes';
import { normalizeStateName } from '@/app/utils/states';
import { toast } from 'sonner';

interface ReviewSectionProps {
  stepsData: any;
  onSave: (nextStep?: string) => Promise<void>;
  onNavigateToSection: (section: string) => void;
  onSubmitInterview?: () => Promise<void>; // New prop for submitting the interview
  isInterviewSubmitted?: boolean; // New prop to check if interview is submitted
}

const ReviewSection = ({
  stepsData,
  onSave,
  onNavigateToSection,
  onSubmitInterview,
  isInterviewSubmitted = false,
}: ReviewSectionProps) => {
  const [isCreatingNewDocuments, setIsCreatingNewDocuments] = useState(false);
  const [isRecreatingAllDocuments, setIsRecreatingAllDocuments] =
    useState(false);
  const [existingDocuments, setExistingDocuments] = useState<any[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [recreatingDocuments, setRecreatingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [availableDocumentTypes, setAvailableDocumentTypes] = useState<
    string[]
  >([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const router = useRouter();
  const { user, userExternalId, userId } = useAuth();
  const { people: globalPeople } = usePeopleLibrary();

  const getPersonName = (person: any) => {
    if (!person) return 'Unknown';

    // Get the most up-to-date person data from People Library
    const currentPerson = globalPeople.find(p => p.id === person.id) || person;

    if (currentPerson.type === 'individual') {
      const firstName = currentPerson.firstName || '';
      const middleName = currentPerson.middleName || '';
      const lastName = currentPerson.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return currentPerson.entityName || 'Unknown';
  };

  // Function to load available document types based on user's state
  const loadAvailableDocumentTypes = async () => {
    try {
      setIsLoadingTemplates(true);
      const userData = await fetchUserProfileByCognitoId(userExternalId || '');
      const userState = userData?.state || 'California';
      const normalizedState = normalizeStateName(userState);
      const templates = await getMemberAvailableTemplates(normalizedState);

      // Map template types to interview document keys
      const mapTemplateTypeToInterviewKey = (
        templateType: string
      ): string | null => {
        const typeMapping: Record<string, string> = {
          Will: 'will',
          Trust: 'trust',
          'Healthcare POA': 'medicalPOA',
          'Financial POA': 'financialPOA',
          'Advance Directive': 'medicalPOA',
          POA: 'medicalPOA',
          Medical: 'medicalPOA',
          Financial: 'financialPOA',
          Healthcare: 'medicalPOA',
        };
        return typeMapping[templateType] || null;
      };

      const availableTypes = templates
        .map(template => mapTemplateTypeToInterviewKey(template.type))
        .filter((type): type is string => type !== null)
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      setAvailableDocumentTypes(availableTypes);
      console.log(
        'Available document types for user in review:',
        availableTypes
      );
    } catch (error) {
      Sentry.captureException(error);
      console.error('Error loading available document types in review:', error);
      // Fallback to all document types if there's an error
      setAvailableDocumentTypes([
        'will',
        'trust',
        'financialPOA',
        'medicalPOA',
      ]);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const selectedDocuments = stepsData.profile?.selectedDocuments || {};
  const documentsToGenerate = Object.entries(selectedDocuments)
    .filter(([_, selected]) => selected)
    .map(([doc, _]) => doc)
    .filter(doc => availableDocumentTypes.includes(doc));

  // Map existing documents to interview keys
  const existingDocumentTypes = React.useMemo(() => {
    const existingTypes = new Set<string>();

    existingDocuments.forEach(doc => {
      const typeMapping: Record<string, string> = {
        Will: 'will',
        Trust: 'trust',
        Healthcare_POA: 'medicalPOA',
        Financial_POA: 'financialPOA',
        Advance_Directive: 'medicalPOA',
      };

      const interviewKey = typeMapping[doc.type];

      if (interviewKey) {
        existingTypes.add(interviewKey);
      }
    });

    return existingTypes;
  }, [existingDocuments]);

  const documentsNotGenerated = availableDocumentTypes.filter(
    doc => !selectedDocuments[doc]
  );

  const getDocumentDisplayName = (doc: string) => {
    const names: Record<string, string> = {
      will: 'Last Will and Testament',
      trust: 'Revocable Trust',
      financialPOA: 'Financial Power of Attorney',
      medicalPOA: 'Medical Power of Attorney',
    };
    return names[doc] || doc;
  };

  // Component for rendering individual document status
  const DocumentStatusCard = ({ docType }: { docType: string }) => {
    const hasExisting = existingDocumentTypes.has(docType);
    const isRecreating = recreatingDocuments.has(docType);

    console.log(`📄 ${docType}: ${hasExisting ? '✅ EXISTS' : '❌ NEW'}`);

    return (
      <div
        key={docType}
        className='flex items-center justify-between p-4 border rounded-lg'
      >
        <div className='flex items-center gap-3'>
          <FileText className='h-5 w-5 text-muted-foreground' />
          <div>
            <p className='font-medium'>{getDocumentDisplayName(docType)}</p>
            <p className='text-sm text-muted-foreground'>
              {hasExisting
                ? 'Document exists - can be regenerated'
                : 'Ready to create'}
            </p>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          {hasExisting ? (
            <>
              <Badge variant='secondary'>Exists</Badge>
              <Button
                size='sm'
                variant='outline'
                onClick={() => handleRecreateDocument(docType)}
                disabled={isRecreating}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isRecreating ? 'animate-spin' : ''}`}
                />
                {isRecreating ? 'Regenerating...' : 'Regenerate'}
              </Button>
            </>
          ) : (
            <Badge variant='outline'>New</Badge>
          )}
        </div>
      </div>
    );
  };

  const isProfileComplete = () => {
    const profile = stepsData.profile || {};

    console.log('Profile Data:', profile);

    // Check basic required fields
    const basicFieldsComplete = !!(
      profile.firstName &&
      profile.lastName &&
      profile.email &&
      profile.dateOfBirth &&
      profile.address?.street &&
      profile.address?.city &&
      profile.address?.state &&
      profile.address?.zip &&
      profile.maritalStatus &&
      profile.soundMind !== undefined &&
      profile.selectedDocuments
    );

    // If marital status is married, spouse must be selected
    if (profile.maritalStatus === 'married' && !profile.spouse) {
      return false;
    }

    return basicFieldsComplete;
  };

  const getMissingFields = (stepKey: string): string[] => {
    const stepData = stepsData[stepKey];
    const missing: string[] = [];

    if (!stepData) return ['Section not started'];

    switch (stepKey) {
      case 'profile':
        if (!stepData.firstName) missing.push('First Name');
        if (!stepData.lastName) missing.push('Last Name');
        if (!stepData.email) missing.push('Email');
        if (!stepData.dateOfBirth) missing.push('Date of Birth');
        if (!stepData.address?.street) missing.push('Street Address');
        if (!stepData.address?.city) missing.push('City');
        if (!stepData.address?.state) missing.push('State');
        if (!stepData.address?.zip) missing.push('ZIP Code');
        if (!stepData.maritalStatus) missing.push('Marital Status');
        if (stepData.maritalStatus === 'married' && !stepData.spouse) {
          missing.push('Spouse Selection');
        }
        if (stepData.soundMind === undefined)
          missing.push('Sound Mind Declaration');
        if (!stepData.selectedDocuments) missing.push('Document Selection');
        break;

      case 'emergency':
        // Complete if user has dependents/pets OR has explicitly checked "no dependents/pets"
        if (
          !stepData.dependents?.length &&
          !stepData.pets?.length &&
          !stepData.noDependents &&
          !stepData.noPets
        ) {
          missing.push(
            'At least one dependent or pet, or check "I don\'t have any"'
          );
        }

        // Check if all pets have primary guardians
        if (stepData.pets?.length) {
          const petsWithoutPrimaryGuardians = stepData.pets.filter(
            (pet: any) => !pet.primaryGuardian
          );
          if (petsWithoutPrimaryGuardians.length > 0) {
            missing.push(
              `Primary Guardian for ${petsWithoutPrimaryGuardians.length} pet(s)`
            );
          }
        }
        break;

      case 'afterYouDie':
        if (selectedDocuments.will) {
          if (
            stepData.executor?.useCompany === undefined ||
            (stepData.executor?.useCompany === false &&
              !stepData.executor?.primary)
          ) {
            missing.push('Executor');
          }
        }
        if (selectedDocuments.trust) {
          if (
            stepData.successorTrustee?.useCompany === undefined ||
            (stepData.successorTrustee?.useCompany === false &&
              !stepData.successorTrustee?.primary)
          ) {
            missing.push('Successor Trustee');
          }
        }
        if (selectedDocuments.will || selectedDocuments.trust) {
          if (!stepData.properties?.length && !stepData.noOwnedProperties) {
            missing.push(
              'Properties or check "I don\'t have any owned properties"'
            );
          }
          if (!stepData.mainBeneficiaries?.length)
            missing.push('Main Beneficiaries');

          // Check distribution stages for beneficiaries under 25
          if (stepData.mainBeneficiaries?.length) {
            const needsDistributionStages = stepData.mainBeneficiaries.some(
              (beneficiary: any) => {
                // Check if any beneficiary needs distribution stages but doesn't have them properly configured
                const person = beneficiary.primaryBeneficiary;
                if (person?.dateOfBirth) {
                  // Get the most up-to-date person data from People Library
                  const currentPerson =
                    globalPeople.find(p => p.id === person.id) || person;
                  if (!currentPerson.dateOfBirth) return false;

                  const today = new Date();
                  const birthDate = new Date(currentPerson.dateOfBirth);
                  let age = today.getFullYear() - birthDate.getFullYear();
                  const monthDiff = today.getMonth() - birthDate.getMonth();

                  if (
                    monthDiff < 0 ||
                    (monthDiff === 0 && today.getDate() < birthDate.getDate())
                  ) {
                    age--;
                  }

                  if (age < 25) {
                    const stages = beneficiary.distributionStages || [];
                    const totalPercentage = stages.reduce(
                      (sum: number, stage: any) =>
                        sum + (stage.percentage || 0),
                      0
                    );
                    return stages.length === 0 || totalPercentage !== 100;
                  }
                }
                return false;
              }
            );

            if (needsDistributionStages) {
              missing.push('Distribution Stages for beneficiaries under 25');
            }
          }

          // Check specific gifts validation
          if (stepData.specificGifts?.length) {
            const invalidFinancialGifts = stepData.specificGifts.filter(
              (gift: any) => {
                if (gift.type === 'financial') {
                  return (
                    !gift.bankName ||
                    !gift.accountType ||
                    !gift.amount ||
                    gift.amount <= 0
                  );
                }
                return false;
              }
            );

            if (invalidFinancialGifts.length > 0) {
              const missingFields: string[] = [];
              invalidFinancialGifts.forEach((gift: any) => {
                if (!gift.bankName) missingFields.push('Bank Name');
                if (!gift.accountType) missingFields.push('Account Type');
                if (!gift.amount || gift.amount <= 0)
                  missingFields.push('Amount');
              });

              // Remove duplicates and add to missing
              const uniqueMissingFields = [...new Set(missingFields)];
              missing.push(`Specific Gifts: ${uniqueMissingFields.join(', ')}`);
            }
          }
        }
        break;

      case 'financial':
        if (selectedDocuments.financialPOA) {
          if (
            stepData.agent?.useCompany === undefined ||
            (stepData.agent?.useCompany === false && !stepData.agent?.primary)
          ) {
            missing.push('Financial Agent');
          }
        }
        break;

      case 'medical':
        if (selectedDocuments.medicalPOA) {
          if (
            stepData.proxy?.useCompany === undefined ||
            (stepData.proxy?.useCompany === false && !stepData.proxy?.primary)
          ) {
            missing.push('Healthcare Proxy');
          }
        }
        break;

      case 'additional':
        if (!stepData.personalProperty)
          missing.push('Personal Property Distribution');
        if (!stepData.burial) missing.push('Burial Preferences');
        break;
    }

    return missing;
  };

  const isStepCompleted = (stepKey: string): boolean => {
    return getMissingFields(stepKey).length === 0;
  };

  const getSectionCompletionStatus = () => {
    const sections = [
      { key: 'profile', name: 'My Profile', required: true },
      {
        key: 'emergency',
        name: 'Emergency Key Considerations',
        required: false,
      },
      {
        key: 'afterYouDie',
        name: 'After I Die',
        required: selectedDocuments.will || selectedDocuments.trust,
      },
      {
        key: 'financial',
        name: 'Financial Decisions',
        required: selectedDocuments.financialPOA,
      },
      {
        key: 'medical',
        name: 'Medical Decisions',
        required: selectedDocuments.medicalPOA,
      },
      { key: 'additional', name: 'Additional Considerations', required: false },
    ];

    return sections.map(section => ({
      ...section,
      completed: isStepCompleted(section.key),
      missingFields: getMissingFields(section.key),
    }));
  };

  const canGenerateDocuments = () => {
    const sections = getSectionCompletionStatus();
    const requiredSections = sections.filter(s => s.required);
    const hasSelectedDocuments = Object.values(selectedDocuments).some(Boolean);

    console.log('Required Sections:', requiredSections);
    console.log('Profile Complete:', isProfileComplete());
    console.log('Has Selected Documents:', hasSelectedDocuments);
    console.log(
      'All Required Completed:',
      requiredSections.every(s => s.completed)
    );
    return (
      requiredSections.every(s => s.completed) &&
      isProfileComplete() &&
      hasSelectedDocuments
    );
  };

  // Load existing documents for the user
  const loadExistingDocuments = async () => {
    if (!user?.userId) {
      console.log('❌ No user ID available');
      return;
    }

    try {
      setLoadingDocuments(true);

      if (!userId) {
        console.log('❌ No userExternalId available');
        return;
      }

      const documents = await getUserDocumentsByUserId(userId);

      const activeDocuments =
        documents?.filter((doc: any) => doc.status !== 'archived') || [];

      setExistingDocuments(activeDocuments);
      setRetryCount(0); // Reset on success
    } catch (error: any) {
      console.error('❌ Failed to load documents:', error?.message);
      Sentry.captureException(error);

      if (retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          loadExistingDocuments();
        }, 2000);
      }
    } finally {
      setLoadingDocuments(false);
    }
  };

  // Check which document types already exist
  const getExistingDocumentTypes = () => {
    return existingDocumentTypes;
  };

  // Memoize documents that need to be created
  const documentsToCreate = React.useMemo(() => {
    const toCreate = Object.entries(selectedDocuments)
      .filter(([key, selected]) => selected && !existingDocumentTypes.has(key))
      .map(([key]) => key);

    console.log('Documents to create calculated:', toCreate);
    return toCreate;
  }, [selectedDocuments, existingDocumentTypes]);

  // Get documents that need to be created (not existing)
  const getDocumentsToCreate = () => {
    return documentsToCreate;
  };

  // Handle recreating a specific document type
  const handleRecreateDocument = async (documentType: string) => {
    try {
      setRecreatingDocuments(prev => new Set(prev).add(documentType));

      // Find the existing document of this type
      const typeMapping: Record<string, string> = {
        will: 'Will',
        trust: 'Trust',
        medicalPOA: 'Healthcare_POA',
        financialPOA: 'Financial_POA',
      };

      const mappedType = typeMapping[documentType];
      const existingDoc = existingDocuments.find(
        doc => doc.type === mappedType
      );

      if (existingDoc) {
        await recreateDocument(existingDoc.id);
        // Reload documents to show the new one
        await loadExistingDocuments();
        toast.success(
          `Successfully regenerated ${getDocumentDisplayName(documentType)}!`
        );
      } else {
        toast.error(
          `No existing ${getDocumentDisplayName(documentType)} found to regenerate.`
        );
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error(`Error recreating ${documentType} document:`, error);
    } finally {
      setRecreatingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(documentType);
        return newSet;
      });
    }
  };

  // Handle recreating all existing documents
  const handleRecreateAllExisting = async () => {
    const existingTypes = getExistingDocumentTypes();
    const existingDocumentsList = Array.from(existingTypes) as string[];

    if (existingDocumentsList.length === 0) return;

    try {
      setIsRecreatingAllDocuments(true);

      // Set all existing documents as being recreated
      setRecreatingDocuments(new Set(existingDocumentsList));

      // Recreate all existing documents in parallel
      const recreatePromises = existingDocumentsList.map(
        async (documentType: string) => {
          const typeMapping: Record<string, string> = {
            will: 'Will',
            trust: 'Trust',
            medicalPOA: 'Healthcare_POA',
            financialPOA: 'Financial_POA',
          };

          const mappedType = typeMapping[documentType];
          const existingDoc = existingDocuments.find(
            doc => doc.type === mappedType
          );

          if (existingDoc) {
            await recreateDocument(existingDoc.id);
          }
        }
      );

      await Promise.all(recreatePromises);

      // Reload documents to show the new ones
      await loadExistingDocuments();
      toast.success(
        `Successfully regenerated all ${existingDocumentsList.length} existing documents!`
      );
    } catch (error) {
      Sentry.captureException(error);
      console.error('Error recreating all existing documents:', error);
    } finally {
      setIsRecreatingAllDocuments(false);
      setRecreatingDocuments(new Set());
    }
  };

  // Handle creating new documents (for types that don't exist yet)
  const handleCreateNewDocuments = async () => {
    const docsToCreate = getDocumentsToCreate();
    if (docsToCreate.length === 0) {
      console.log('No documents to create');
      toast.info(
        'All selected documents already exist. Use the regenerate option to update them.'
      );
      return;
    }
    console.log('Creating documents:', docsToCreate);

    // Double-check by reloading existing documents before creating
    await loadExistingDocuments();
    const updatedDocsToCreate = getDocumentsToCreate();
    if (updatedDocsToCreate.length === 0) {
      console.log('No documents to create after refresh');
      toast.info(
        'All selected documents already exist. Use the regenerate option to update them.'
      );
      return;
    }

    try {
      setIsCreatingNewDocuments(true);

      if (!userExternalId) throw new Error('User not authenticated');

      const userData = await fetchUserByCognitoId(userExternalId);
      if (!userData) throw new Error('User data not found');

      console.log('===> User data:', userData);

      // Handle both JSON and plain string state values
      let parsedState: any = {};
      let stateCode = ''; // Default to California

      if (userData.state) {
        try {
          // Try to parse as JSON first
          parsedState = JSON.parse(userData.state);
          stateCode = parsedState.stateProvinceCode || '';
        } catch (jsonError) {
          // If JSON parsing fails, treat it as a plain state code string
          console.log(
            '===> State is not JSON, treating as plain string:',
            userData.state
          );
          stateCode = userData.state;
          parsedState = { stateProvinceCode: userData.state };
        }
      }

      const userState = normalizeStateName(stateCode || 'California');
      console.log('===> User state:', userState);

      const allTemplates = await getMemberAvailableTemplates(userState);

      if (allTemplates.length === 0) {
        throw new Error('No templates found for user state');
      }

      // Filter templates for documents that need to be created
      const mapTemplateTypeToInterviewKey = (
        templateType: string
      ): string | null => {
        const typeMapping: Record<string, string> = {
          Will: 'will',
          Trust: 'trust',
          'Healthcare POA': 'medicalPOA',
          'Financial POA': 'financialPOA',
          'Advance Directive': 'medicalPOA',
          POA: 'medicalPOA',
          Medical: 'medicalPOA',
          Financial: 'financialPOA',
          Healthcare: 'medicalPOA',
        };
        return typeMapping[templateType] || null;
      };

      console.log('===> All templates:', allTemplates);

      const templatesToCreate = allTemplates.filter(template => {
        const interviewKey = mapTemplateTypeToInterviewKey(template.type);
        return interviewKey && updatedDocsToCreate.includes(interviewKey);
      });

      console.log('===> Templates to create:', templatesToCreate);

      if (templatesToCreate.length === 0) {
        throw new Error('No templates found for user state');
      }

      if (templatesToCreate.length > 0) {
        await finalizeDocumentsForTemplates(userExternalId, templatesToCreate);
        // Reload documents to update the UI
        await loadExistingDocuments();
        toast.success(
          `Successfully created ${templatesToCreate.length} document${templatesToCreate.length === 1 ? '' : 's'}!`
        );
        router.push(routes.member.documents);
      } else {
        toast.error('No templates available to create the selected documents.');
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error('===> Error creating new documents:', error);
      toast.error('Failed to create new documents. Please try again.');
    } finally {
      setIsCreatingNewDocuments(false);
    }
  };

  // Load documents when user is authenticated
  React.useEffect(() => {
    if (user?.userId && userExternalId) {
      console.log('🚀 Loading documents for authenticated user');
      loadExistingDocuments();
      loadAvailableDocumentTypes();
    }
  }, [user?.userId, userExternalId]);

  // Reload documents when page becomes visible
  React.useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && user?.userId) {
        loadExistingDocuments();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () =>
      document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user?.userId]);

  const [retryCount, setRetryCount] = React.useState(0);
  const maxRetries = 1;

  return (
    <div className='space-y-6'>
      {/* Document Confirmation */}
      <Card>
        <CardHeader>
          <div className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            <CardTitle>Document Confirmation</CardTitle>
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          {isLoadingTemplates ? (
            <div className='flex items-center space-x-2 py-4'>
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary'></div>
              <span className='text-sm text-muted-foreground'>
                Loading available documents for your state...
              </span>
            </div>
          ) : availableDocumentTypes.length === 0 ? (
            <div className='text-center py-6'>
              <p className='text-sm text-muted-foreground mb-2'>
                No document templates are available for your state at this time.
              </p>
              <p className='text-xs text-muted-foreground'>
                Please contact support for assistance.
              </p>
            </div>
          ) : (
            <div className='space-y-3'>
              <div>
                <h4 className='font-medium text-green-700 mb-2 flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4' />
                  Documents that WILL be produced:
                </h4>
                {documentsToGenerate.length > 0 ? (
                  <div className='space-y-1'>
                    {documentsToGenerate.map(doc => (
                      <div key={doc} className='flex items-center gap-2'>
                        <Badge
                          variant='default'
                          className='bg-green-100 text-green-800'
                        >
                          {getDocumentDisplayName(doc)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className='text-sm text-muted-foreground'>
                    No documents selected
                  </p>
                )}
              </div>

              {documentsNotGenerated.length > 0 && (
                <div>
                  <h4 className='font-medium text-red-700 mb-2 flex items-center gap-2'>
                    <XCircle className='h-4 w-4' />
                    Available documents that WILL NOT be produced:
                  </h4>
                  <div className='space-y-1'>
                    {documentsNotGenerated.map(doc => (
                      <div key={doc} className='flex items-center gap-2'>
                        <Badge
                          variant='secondary'
                          className='bg-red-100 text-red-800'
                        >
                          {getDocumentDisplayName(doc)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                variant='outline'
                size='sm'
                onClick={() => onNavigateToSection('profile')}
                className='mt-3'
                disabled={
                  isInterviewSubmitted || availableDocumentTypes.length === 0
                }
              >
                <Edit className='h-4 w-4 mr-2' />
                Change document selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Submit Interview Button */}
      {!isInterviewSubmitted && canGenerateDocuments() && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <CheckCircle className='h-5 w-5 text-emerald-600' />
              Ready to Submit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <p className='text-sm text-muted-foreground'>
                All required information has been provided. Submit your
                interview to lock your answers and proceed to document
                generation.
              </p>
              <Button onClick={onSubmitInterview} className='w-full' size='lg'>
                Submit Interview
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interview Submitted Message */}
      {isInterviewSubmitted && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <CheckCircle className='h-5 w-5 text-emerald-600' />
              Interview Submitted
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <p className='text-sm text-muted-foreground'>
                Your interview has been submitted and your answers are now
                locked. You can generate documents or unlock your answers to
                make changes.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Section Completion Status */}
      <Card>
        <CardHeader>
          <CardTitle>Section Completion Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {getSectionCompletionStatus().map(section => (
              <div key={section.key} className='border rounded-lg p-3'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-3'>
                    {section.completed ? (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    ) : (
                      <XCircle className='h-5 w-5 text-red-500' />
                    )}
                    <span className='font-medium'>
                      {section.name}
                      {section.required && (
                        <span className='text-red-500 ml-1'>*</span>
                      )}
                    </span>
                  </div>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onNavigateToSection(section.key)}
                    disabled={isInterviewSubmitted}
                  >
                    <Edit className='h-4 w-4' />
                  </Button>
                </div>
                {!section.completed && section.missingFields.length > 0 && (
                  <div className='mt-2 ml-8'>
                    <p className='text-sm text-red-600 font-medium'>Missing:</p>
                    <ul className='text-sm text-red-600 list-disc list-inside'>
                      {section.missingFields.map((field, index) => (
                        <li key={index}>{field}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
          <p className='text-sm text-muted-foreground mt-3'>
            * Required sections based on your document selection
          </p>
        </CardContent>
      </Card>

      {/* Summary of Answers */}
      <Card>
        <CardHeader>
          <CardTitle>Summary of Your Answers</CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* Profile Summary */}
          {stepsData.profile && (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium flex items-center gap-2'>
                  <User className='h-4 w-4' />
                  Your Profile
                </h4>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onNavigateToSection('profile')}
                  disabled={isInterviewSubmitted}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </div>
              <div className='flex flex-col gap-3 text-sm'>
                <div>
                  <span className='font-medium'>Name:</span>{' '}
                  {stepsData.profile.firstName} {stepsData.profile.lastName}
                </div>
                <div>
                  <span className='font-medium'>Email:</span>{' '}
                  {stepsData.profile.email}
                </div>
                <div className='capitalize'>
                  <span className='font-medium'>Marital Status:</span>{' '}
                  {stepsData.profile.maritalStatus}
                </div>
                {stepsData.profile.maritalStatus === 'married' &&
                  stepsData.profile.spouseInfo && (
                    <div>
                      <span className='font-medium'>Spouse:</span>{' '}
                      {stepsData.profile.spouseInfo.firstName}{' '}
                      {stepsData.profile.spouseInfo.lastName}
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* People Library Summary */}
          {stepsData.people && stepsData.people.length > 0 && (
            <div className='space-y-3'>
              <h4 className='font-medium flex items-center gap-2'>
                <User className='h-4 w-4' />
                People in Your Estate Plan ({stepsData.people.length})
              </h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                {stepsData.people.slice(0, 6).map((person: any) => (
                  <div key={person.id} className='text-sm p-2 bg-muted rounded'>
                    {getPersonName(person)} ({person.type})
                  </div>
                ))}
                {stepsData.people.length > 6 && (
                  <div className='text-sm text-muted-foreground'>
                    ... and {stepsData.people.length - 6} more
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Emergency Considerations Summary */}
          {stepsData.emergency && (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium flex items-center gap-2'>
                  <Heart className='h-4 w-4' />
                  Emergency Key Considerations
                </h4>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onNavigateToSection('emergency')}
                  disabled={isInterviewSubmitted}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </div>
              <div className='text-sm space-y-1'>
                {stepsData.emergency.dependents && (
                  <div>Dependents: {stepsData.emergency.dependents.length}</div>
                )}
                {stepsData.emergency.pets && (
                  <div>Pets: {stepsData.emergency.pets.length}</div>
                )}
              </div>
            </div>
          )}

          {/* After You Die Summary */}
          {stepsData.afterYouDie && (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium flex items-center gap-2'>
                  <Crown className='h-4 w-4' />
                  After You Die
                </h4>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onNavigateToSection('afterYouDie')}
                  disabled={isInterviewSubmitted}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </div>
              <div className='text-sm space-y-1'>
                {stepsData.afterYouDie.executor && (
                  <div>
                    <div>
                      Executor:{' '}
                      {stepsData.afterYouDie.executor.useCompany
                        ? 'Our Company'
                        : getPersonName(stepsData.afterYouDie.executor.primary)}
                    </div>
                    {stepsData.afterYouDie.executor.successors &&
                      stepsData.afterYouDie.executor.successors.length > 0 && (
                        <div className='ml-4 text-xs text-[var(--custom-gray-dark)]'>
                          Backup Executors:{' '}
                          {stepsData.afterYouDie.executor.successors
                            .map((successor: any) => getPersonName(successor))
                            .join(', ')}
                        </div>
                      )}
                  </div>
                )}
                {stepsData.afterYouDie.successorTrustee && (
                  <div>
                    <div>
                      Successor Trustee:{' '}
                      {stepsData.afterYouDie.successorTrustee.useCompany
                        ? 'Our Company'
                        : getPersonName(
                            stepsData.afterYouDie.successorTrustee.primary
                          )}
                    </div>
                    {stepsData.afterYouDie.successorTrustee.successors &&
                      stepsData.afterYouDie.successorTrustee.successors.length >
                        0 && (
                        <div className='ml-4 text-xs text-[var(--custom-gray-dark)]'>
                          Additional Successor Trustees:{' '}
                          {stepsData.afterYouDie.successorTrustee.successors
                            .map((successor: any) => getPersonName(successor))
                            .join(', ')}
                        </div>
                      )}
                  </div>
                )}
                {stepsData.afterYouDie.properties && (
                  <div>
                    Properties: {stepsData.afterYouDie.properties.length}
                  </div>
                )}
                {stepsData.afterYouDie.mainBeneficiaries && (
                  <div>
                    Main Beneficiaries:{' '}
                    {stepsData.afterYouDie.mainBeneficiaries.length}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Financial Decisions Summary */}
          {stepsData.financial && (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium flex items-center gap-2'>
                  <DollarSign className='h-4 w-4' />
                  Financial Decisions
                </h4>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onNavigateToSection('financial')}
                  disabled={isInterviewSubmitted}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </div>
              <div className='text-sm'>
                {stepsData.financial.agent && (
                  <div>
                    Financial Agent:{' '}
                    {stepsData.financial.agent.useCompany
                      ? 'Our Company'
                      : getPersonName(stepsData.financial.agent.primary)}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Medical Decisions Summary */}
          {stepsData.medical && (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <h4 className='font-medium flex items-center gap-2'>
                  <Shield className='h-4 w-4' />
                  Medical Decisions
                </h4>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => onNavigateToSection('medical')}
                  disabled={isInterviewSubmitted}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </div>
              <div className='text-sm'>
                {stepsData.medical.proxy && (
                  <div>
                    Healthcare Proxy:{' '}
                    {stepsData.medical.proxy.useCompany
                      ? 'Our Company'
                      : getPersonName(stepsData.medical.proxy.primary)}
                  </div>
                )}
                {stepsData.medical.organDonation?.enabled && (
                  <div>Organ Donation: Yes</div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generate Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Generate Your Documents</CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          {!canGenerateDocuments() && (
            <Alert className='border-destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>
                <div>
                  {!Object.values(selectedDocuments).some(Boolean) ? (
                    <div>
                      <p className='font-medium mb-2'>
                        No documents selected for generation.
                      </p>
                      <p className='text-sm mb-2'>
                        Please go back to the Profile section and select at
                        least one document type to create.
                      </p>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => onNavigateToSection('profile')}
                        className='mt-2'
                        disabled={isInterviewSubmitted}
                      >
                        <Edit className='h-4 w-4 mr-2' />
                        Select Documents
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <p className='font-medium mb-2'>
                        Please complete the following required sections:
                      </p>
                      <ul className='list-disc list-inside space-y-1'>
                        {getSectionCompletionStatus()
                          .filter(s => s.required && !s.completed)
                          .map(section => (
                            <li key={section.key}>
                              <strong>{section.name}</strong>
                              {section.missingFields.length > 0 && (
                                <span className='text-sm'>
                                  {' '}
                                  - Missing: {section.missingFields.join(', ')}
                                </span>
                              )}
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {canGenerateDocuments() && (
            <>
              {/* Document Status Overview */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium'>Document Status</h3>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => {
                      console.log('Manual refresh triggered');
                      setRetryCount(0); // Reset retry count
                      loadExistingDocuments();
                    }}
                    disabled={loadingDocuments}
                  >
                    <RefreshCw
                      className={`h-4 w-4 mr-2 ${loadingDocuments ? 'animate-spin' : ''}`}
                    />
                    Refresh Status
                  </Button>
                </div>

                {loadingDocuments ? (
                  <div className='text-center py-4'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto'></div>
                    <p className='text-sm text-muted-foreground mt-2'>
                      Loading existing documents...
                    </p>
                    {retryCount > 0 && (
                      <p className='text-xs text-orange-600 mt-1'>
                        Retry attempt {retryCount}/{maxRetries}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className='grid gap-3'>
                    {Object.entries(selectedDocuments)
                      .filter(([_, selected]) => selected)
                      .map(([docType, _]) => (
                        <DocumentStatusCard key={docType} docType={docType} />
                      ))}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className='flex flex-col sm:flex-row gap-3 pt-4 border-t'>
                {existingDocumentTypes.size > 0 && (
                  <Button
                    onClick={handleRecreateAllExisting}
                    disabled={
                      isRecreatingAllDocuments || isCreatingNewDocuments
                    }
                    variant='outline'
                    className='flex-1'
                  >
                    <RefreshCw
                      className={`h-4 w-4 mr-2 ${isRecreatingAllDocuments ? 'animate-spin' : ''}`}
                    />
                    {isRecreatingAllDocuments
                      ? 'Regenerating All...'
                      : `Regenerate All ${existingDocumentTypes.size} Existing`}
                  </Button>
                )}

                {documentsToCreate.length > 0 && (
                  <Button
                    onClick={handleCreateNewDocuments}
                    disabled={
                      isCreatingNewDocuments || isRecreatingAllDocuments
                    }
                    className='flex-1'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    {isCreatingNewDocuments
                      ? 'Creating...'
                      : `Create ${documentsToCreate.length} New Document${documentsToCreate.length === 1 ? '' : 's'}`}
                  </Button>
                )}

                <Button
                  onClick={() => router.push(routes.member.documents)}
                  variant='outline'
                  className='flex-1'
                  disabled={isCreatingNewDocuments || isRecreatingAllDocuments}
                >
                  <FileText className='h-4 w-4 mr-2' />
                  View All Documents
                </Button>
              </div>

              <Alert>
                <CheckCircle className='h-4 w-4' />
                <AlertDescription>
                  All required information has been provided. You can create new
                  documents or regenerate existing ones.
                </AlertDescription>
              </Alert>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ReviewSection;
