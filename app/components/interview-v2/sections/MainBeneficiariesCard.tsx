'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import PeopleSelectObject from '../PeopleSelectObject';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Trash2,
  Crown,
  AlertTriangle,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { Person } from '@/app/(protected)/member/interviewV2/page';
import { QuestionVideoHint } from '@/components/interview-v2/question-video-hint';

interface MainBeneficiary {
  id: string;
  primaryBeneficiary: Person;
  percentage: number;
  successors: Array<{ person: Person; isDescendants?: boolean }>;
  distributionStages?: Array<{ age: number; percentage: number }>;
}

interface MainBeneficiariesCardProps {
  beneficiaries: MainBeneficiary[];
  onChange: (beneficiaries: MainBeneficiary[]) => void;
  people: Person[];
  onPeopleChange: (people: Person[]) => void;
  isSubsectionCompleted?: (
    sectionKey: string,
    subsectionKey: string
  ) => boolean;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  readOnly?: boolean;
}

export function MainBeneficiariesCard({
  beneficiaries,
  onChange,
  people,
  onPeopleChange,
  isSubsectionCompleted,
  collapsed = false,
  onToggleCollapse,
  readOnly = false,
}: MainBeneficiariesCardProps) {
  const [newBeneficiary, setNewBeneficiary] = useState<
    Partial<MainBeneficiary>
  >({});

  const getPersonName = (person: Person) => {
    if (!person) return 'Unknown';

    if (person.type === 'individual') {
      return `${person.firstName || ''} ${person.lastName || ''}`.trim();
    }
    return person.entityName || 'Unknown';
  };

  const getPersonAge = (person: Person) => {
    if (!person || person.type !== 'individual' || !person.dateOfBirth)
      return null;

    // Get the most up-to-date person data from People Library
    const currentPerson = people.find(p => p.id === person.id) || person;
    if (!currentPerson.dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(currentPerson.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const getTotalPercentage = () => {
    return beneficiaries.reduce((total, b) => total + (b.percentage || 0), 0);
  };

  const getUsedPrimaryBeneficiaries = () => {
    return beneficiaries.map(b => b.primaryBeneficiary?.id).filter(Boolean);
  };

  const addBeneficiary = () => {
    if (!newBeneficiary.primaryBeneficiary || !newBeneficiary.percentage) {
      alert('Please select a primary beneficiary and enter a percentage.');
      return;
    }

    const totalPercentage = getTotalPercentage() + newBeneficiary.percentage;
    if (totalPercentage > 100) {
      alert('Total percentage cannot exceed 100%.');
      return;
    }

    const beneficiary: MainBeneficiary = {
      id: Date.now().toString(),
      primaryBeneficiary: newBeneficiary.primaryBeneficiary,
      percentage: newBeneficiary.percentage,
      successors: [],
    };

    onChange([...beneficiaries, beneficiary]);
    setNewBeneficiary({});
  };

  const removeBeneficiary = (id: string) => {
    if (confirm('Are you sure you want to remove this beneficiary?')) {
      onChange(beneficiaries.filter(b => b.id !== id));
    }
  };

  const updateBeneficiary = (
    id: string,
    field: keyof MainBeneficiary,
    value: any
  ) => {
    onChange(
      beneficiaries.map(b => (b.id === id ? { ...b, [field]: value } : b))
    );
  };

  const updateBeneficiaryData = (
    id: string,
    updates: Partial<MainBeneficiary>
  ) => {
    onChange(beneficiaries.map(b => (b.id === id ? { ...b, ...updates } : b)));
  };

  const addSuccessor = (beneficiaryId: string, person: Person) => {
    const beneficiary = beneficiaries.find(b => b.id === beneficiaryId);
    if (!beneficiary) return;

    const newSuccessors = [...beneficiary.successors, { person }];
    updateBeneficiary(beneficiaryId, 'successors', newSuccessors);
  };

  const removeSuccessor = (beneficiaryId: string, index: number) => {
    const beneficiary = beneficiaries.find(b => b.id === beneficiaryId);
    if (!beneficiary) return;

    const newSuccessors = beneficiary.successors.filter((_, i) => i !== index);
    updateBeneficiary(beneficiaryId, 'successors', newSuccessors);
  };

  const needsDistributionStages = (beneficiaryId: string) => {
    const beneficiary = beneficiaries.find(b => b.id === beneficiaryId);
    if (!beneficiary) return false;

    // Check primary beneficiary age
    const primaryAge = getPersonAge(beneficiary.primaryBeneficiary);
    if (primaryAge !== null && primaryAge < 25) return true;

    // Check successors ages
    return beneficiary.successors.some(s => {
      if (s.isDescendants) return false; // Descendants don't need age check
      const age = getPersonAge(s.person);
      return age !== null && age < 25;
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Crown className='h-5 w-5' />
            <CardTitle>Main Beneficiaries (Residuary Estate)</CardTitle>
          </div>
          <div className='flex items-center gap-2'>
            <QuestionVideoHint questionId='afterYouDie.main-beneficiaries' />
            {onToggleCollapse && (
              <button
                onClick={onToggleCollapse}
                className='p-1 hover:bg-gray-100 rounded'
              >
                {collapsed ? (
                  <ChevronDown className='h-4 w-4' />
                ) : (
                  <ChevronUp className='h-4 w-4' />
                )}
              </button>
            )}
          </div>
        </div>
      </CardHeader>
      {!collapsed && (
        <CardContent className='space-y-4'>
          {/* Total Percentage Display */}
          <div className='flex items-center justify-between p-3 bg-muted rounded-lg'>
            <span className='font-medium'>Total Allocated:</span>
            <Badge
              variant={getTotalPercentage() === 100 ? 'default' : 'destructive'}
            >
              {getTotalPercentage()}%
            </Badge>
          </div>

          {getTotalPercentage() !== 100 && (
            <Alert className='border-destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>
                The total percentage must equal 100%. Currently allocated:{' '}
                {getTotalPercentage()}%
              </AlertDescription>
            </Alert>
          )}

          {/* Existing Beneficiaries */}
          {beneficiaries.map(beneficiary => (
            <div
              key={beneficiary.id}
              className='border rounded-lg p-4 space-y-4'
            >
              <div className='flex items-center justify-between'>
                <h4 className='font-medium'>
                  Beneficiary - {beneficiary.percentage}%
                </h4>
                <Button
                  size='sm'
                  variant='ghost'
                  onClick={() => removeBeneficiary(beneficiary.id)}
                  className='h-8 w-8 p-0 text-destructive'
                  disabled={readOnly}
                >
                  <Trash2 className='h-4 w-4' />
                </Button>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                <div className='space-y-2'>
                  <Label>Primary Beneficiary</Label>
                  <PeopleSelectObject
                    value={beneficiary.primaryBeneficiary}
                    onValueChange={val =>
                      updateBeneficiary(
                        beneficiary.id,
                        'primaryBeneficiary',
                        val
                      )
                    }
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Select beneficiary'
                    noneOptionText='Select beneficiary'
                    filterType='all'
                    excludeIds={getUsedPrimaryBeneficiaries()}
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
                <div className='space-y-2'>
                  <Label>Percentage Share</Label>
                  <Input
                    type='number'
                    min='0'
                    max='100'
                    value={beneficiary.percentage}
                    onChange={e => {
                      const value = parseInt(e.target.value) || 0;
                      const clampedValue = Math.min(Math.max(value, 0), 100);
                      updateBeneficiary(
                        beneficiary.id,
                        'percentage',
                        clampedValue
                      );
                    }}
                    onInput={e => {
                      const input = e.target as HTMLInputElement;
                      if (parseInt(input.value) > 100) {
                        input.value = '100';
                      }
                    }}
                    placeholder='25'
                    disabled={readOnly}
                  />
                </div>
              </div>

              {/* Successors */}
              <div className='space-y-3'>
                <Label>Successors (in order)</Label>
                {beneficiary.successors.map((successor, index) => (
                  <div key={index} className='flex items-center gap-2'>
                    <Badge variant='outline'>{index + 1}</Badge>
                    <span className='flex-1'>
                      {successor.isDescendants
                        ? 'Their Descendants'
                        : getPersonName(successor.person)}
                    </span>
                    <Button
                      size='sm'
                      variant='ghost'
                      onClick={() => removeSuccessor(beneficiary.id, index)}
                      className='h-8 w-8 p-0 text-destructive'
                      disabled={readOnly}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                ))}

                <div className='space-y-2'>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={() => {
                      const newSuccessors = [
                        ...beneficiary.successors,
                        { person: {} as Person, isDescendants: true },
                      ];
                      updateBeneficiary(
                        beneficiary.id,
                        'successors',
                        newSuccessors
                      );
                    }}
                    className='w-full'
                    disabled={readOnly}
                  >
                    Add Their Descendants
                  </Button>
                  <PeopleSelectObject
                    value={undefined}
                    onValueChange={person => {
                      if (person) {
                        addSuccessor(beneficiary.id, person);
                      }
                    }}
                    people={people}
                    onPeopleChange={onPeopleChange}
                    placeholder='Add successor person'
                    noneOptionText='Add successor person'
                    filterType='all'
                    excludeIds={
                      [
                        beneficiary.primaryBeneficiary?.id,
                        ...beneficiary.successors
                          .map(s => s.person?.id)
                          .filter(Boolean),
                      ].filter(Boolean) as string[]
                    }
                    includeNoneOption={false}
                    disabled={readOnly}
                  />
                </div>
              </div>

              {/* Distribution Stages for under 25 */}
              {needsDistributionStages(beneficiary.id) && (
                <div className='border-t pt-4 space-y-3'>
                  <Label className='text-sm font-medium text-orange-600'>
                    Distribution Stages Required (Beneficiary under 25)
                  </Label>
                  <Alert>
                    <AlertTriangle className='h-4 w-4' />
                    <AlertDescription className='text-sm'>
                      This beneficiary is under 25 years old. You must define
                      distribution stages.
                    </AlertDescription>
                  </Alert>
                  <div className='space-y-3'>
                    <Label className='text-sm'>Distribution Stages</Label>
                    <p className='text-xs text-muted-foreground'>
                      Define at what ages the beneficiary will receive portions
                      of their inheritance.
                    </p>

                    {(beneficiary.distributionStages || []).map(
                      (stage, stageIndex) => (
                        <div
                          key={stageIndex}
                          className='flex items-center gap-2 p-3 border rounded-lg'
                        >
                          <div className='flex-1 grid grid-cols-2 gap-2'>
                            <div className='space-y-1'>
                              <Label className='text-xs'>Age</Label>
                              <Input
                                type='number'
                                min='0'
                                max='150'
                                placeholder='25'
                                value={stage.age || ''}
                                onChange={e => {
                                  const value = parseInt(e.target.value) || 0;
                                  const clampedValue = Math.min(
                                    Math.max(value, 0),
                                    150
                                  );
                                  const updatedStages = [
                                    ...(beneficiary.distributionStages || []),
                                  ];
                                  updatedStages[stageIndex] = {
                                    ...stage,
                                    age: clampedValue,
                                  };
                                  updateBeneficiaryData(beneficiary.id, {
                                    distributionStages: updatedStages,
                                  });
                                }}
                                onInput={e => {
                                  const input = e.target as HTMLInputElement;
                                  if (parseInt(input.value) > 150) {
                                    input.value = '150';
                                  }
                                }}
                                className='h-8'
                                disabled={readOnly}
                              />
                            </div>
                            <div className='space-y-1'>
                              <Label className='text-xs'>Percentage</Label>
                              <Input
                                type='number'
                                min='0'
                                max='100'
                                placeholder='50'
                                value={stage.percentage || ''}
                                onChange={e => {
                                  const value = parseInt(e.target.value) || 0;
                                  const clampedValue = Math.min(
                                    Math.max(value, 0),
                                    100
                                  );
                                  const updatedStages = [
                                    ...(beneficiary.distributionStages || []),
                                  ];
                                  updatedStages[stageIndex] = {
                                    ...stage,
                                    percentage: clampedValue,
                                  };
                                  updateBeneficiaryData(beneficiary.id, {
                                    distributionStages: updatedStages,
                                  });
                                }}
                                onInput={e => {
                                  const input = e.target as HTMLInputElement;
                                  if (parseInt(input.value) > 100) {
                                    input.value = '100';
                                  }
                                }}
                                className='h-8'
                                disabled={readOnly}
                              />
                            </div>
                          </div>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => {
                              const updatedStages = (
                                beneficiary.distributionStages || []
                              ).filter((_, i) => i !== stageIndex);
                              updateBeneficiaryData(beneficiary.id, {
                                distributionStages: updatedStages,
                              });
                            }}
                            className='h-8 w-8 p-0'
                            disabled={readOnly}
                          >
                            <Trash2 className='h-3 w-3' />
                          </Button>
                        </div>
                      )
                    )}

                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => {
                        const newStage = { age: 25, percentage: 50 };
                        const updatedStages = [
                          ...(beneficiary.distributionStages || []),
                          newStage,
                        ];
                        updateBeneficiaryData(beneficiary.id, {
                          distributionStages: updatedStages,
                        });
                      }}
                      className='w-full'
                      disabled={readOnly}
                    >
                      <Plus className='h-3 w-3 mr-1' />
                      Add Distribution Stage
                    </Button>

                    {(beneficiary.distributionStages || []).length > 0 && (
                      <div className='text-xs text-muted-foreground'>
                        Total:{' '}
                        {(beneficiary.distributionStages || []).reduce(
                          (sum, stage) => sum + (stage.percentage || 0),
                          0
                        )}
                        %
                        {(beneficiary.distributionStages || []).reduce(
                          (sum, stage) => sum + (stage.percentage || 0),
                          0
                        ) !== 100 && (
                          <span className='text-red-500 ml-1'>
                            (Must equal 100%)
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* Add New Beneficiary */}
          <div className='border-2 border-dashed rounded-lg p-4 space-y-3'>
            <h4 className='font-medium text-muted-foreground'>
              Add Additional Beneficiaries
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
              <div className='space-y-2'>
                <Label>Primary Beneficiary</Label>
                <PeopleSelectObject
                  value={newBeneficiary.primaryBeneficiary}
                  onValueChange={val =>
                    setNewBeneficiary(prev => ({
                      ...prev,
                      primaryBeneficiary: val,
                    }))
                  }
                  people={people}
                  onPeopleChange={onPeopleChange}
                  placeholder='Select beneficiary'
                  noneOptionText='Select beneficiary'
                  filterType='all'
                  excludeIds={getUsedPrimaryBeneficiaries()}
                  includeNoneOption={false}
                  disabled={readOnly}
                />
              </div>
              <div className='space-y-2'>
                <Label>Percentage Share</Label>
                <Input
                  type='number'
                  min='0'
                  max={100 - getTotalPercentage()}
                  value={newBeneficiary.percentage || ''}
                  onChange={e => {
                    const value = parseInt(e.target.value) || 0;
                    const maxAllowed = 100 - getTotalPercentage();
                    const clampedValue = Math.min(
                      Math.max(value, 0),
                      maxAllowed
                    );
                    setNewBeneficiary(prev => ({
                      ...prev,
                      percentage: clampedValue,
                    }));
                  }}
                  onInput={e => {
                    const input = e.target as HTMLInputElement;
                    const maxAllowed = 100 - getTotalPercentage();
                    if (parseInt(input.value) > maxAllowed) {
                      input.value = maxAllowed.toString();
                    }
                  }}
                  placeholder={`Max: ${100 - getTotalPercentage()}%`}
                  disabled={readOnly}
                />
              </div>
            </div>
            <Button
              onClick={addBeneficiary}
              className='w-full'
              disabled={readOnly}
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Main Beneficiary
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

export default MainBeneficiariesCard;
