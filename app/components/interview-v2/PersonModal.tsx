'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { StreetAutocomplete } from '@/app/components/address-autocomplete/StreetAutocomplete';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Person, PersonType } from '@/app/(protected)/member/interviewV2/page';
import { Loader2 } from 'lucide-react';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';

interface PersonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (person: Person | Omit<Person, 'id'>) => Promise<void>;
  person?: Person | null;
  defaultType?: PersonType;
  filterType?: PersonType | 'all';
}

// Phone number validation utilities
const formatPhoneInput = (value: string): string => {
  // If user tries to delete the +1 prefix, restore it
  if (!value.startsWith('+1 ')) {
    return '+1 ';
  }

  const afterPrefix = value.substring(3);

  // Remove all non-numeric characters including spaces
  const cleaned = afterPrefix.replace(/[^0-9]/g, '');

  if (cleaned.length > 10) {
    return value.substring(0, value.length - 1);
  }

  return '+1 ' + cleaned;
};

const isPhoneValid = (phone: string): boolean =>
  phone.replace(/\D/g, '').replace(/^1/, '').length >= 10;

export function PersonModal({
  isOpen,
  onClose,
  onSave,
  person,
  defaultType = 'individual',
  filterType = 'all',
}: PersonModalProps) {
  const [activeTab, setActiveTab] = useState<PersonType>(defaultType);
  const [formData, setFormData] = useState<Partial<Person>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Get global people library to check for existing spouse
  const { people: globalPeople, updatePerson: updateExistingPerson } =
    usePeopleLibrary();

  useEffect(() => {
    if (person) {
      setFormData(person);
      setActiveTab(person.type);
    } else {
      setFormData({
        type: defaultType,
        phoneNumber: '+1 ', // Set default +1 prefix for new entries
      });
      setActiveTab(defaultType);
    }
  }, [person, defaultType]);

  const handlePhoneChange = (value: string) => {
    const formattedPhone = formatPhoneInput(value);
    updateFormData('phoneNumber', formattedPhone);
  };

  const handleSave = async () => {
    const requiredFields = getRequiredFields(activeTab);

    // Check for missing required fields
    const missingFields = requiredFields.filter(field => {
      if (field === 'name') {
        return activeTab === 'individual'
          ? !(formData.firstName && formData.lastName)
          : !formData.entityName;
      }
      if (field === 'phoneNumber') {
        return !(formData.phoneNumber && isPhoneValid(formData.phoneNumber));
      }
      return !formData[field as keyof Person];
    });

    if (missingFields.length > 0) {
      // Check specifically for phone number validation
      if (missingFields.includes('phoneNumber')) {
        alert(
          'Please enter a valid phone number with at least 10 digits (e.g., *************).'
        );
      } else {
        alert('Please fill in all required fields.');
      }
      return;
    }

    // Handle unique Spouse relationship type
    if (formData.relationshipType === 'Spouse') {
      const existingSpouse = globalPeople.find(
        p => p.relationshipType === 'Spouse' && p.id !== person?.id // Exclude current person if editing
      );

      if (existingSpouse) {
        // Automatically clear the existing spouse relationship
        try {
          await updateExistingPerson({
            ...existingSpouse,
            relationshipType: undefined,
          });
        } catch (error) {
          console.error('Error clearing existing spouse relationship:', error);
        }
      }
    }

    const personData = {
      ...formData,
      type: activeTab,
    };

    setIsSaving(true);
    try {
      if (person) {
        await onSave(personData as Person);
      } else {
        await onSave(personData as Omit<Person, 'id'>);
      }
    } catch (error) {
      console.error('Error saving person:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getRequiredFields = (type: PersonType): string[] => {
    switch (type) {
      case 'individual':
        return ['name', 'dateOfBirth', 'phoneNumber', 'address'];
      case 'charity':
      case 'business':
        return ['entityName'];
      default:
        return [];
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderPhoneInput = (
    required: boolean = false,
    recommended: boolean = false
  ) => (
    <div className='space-y-2'>
      <Label htmlFor='phone'>
        Phone Number {required && '*'} {recommended && '(Recommended)'}
      </Label>
      <Input
        id='phone'
        value={formData.phoneNumber || '+1 '}
        onChange={e => handlePhoneChange(e.target.value)}
        placeholder='*************'
        className={
          recommended &&
          (!formData.phoneNumber || !isPhoneValid(formData.phoneNumber))
            ? 'border-red-300 focus:border-red-500'
            : ''
        }
      />
      {recommended &&
        (!formData.phoneNumber || formData.phoneNumber === '+1 ') && (
          <p className='text-sm text-yellow-600'>
            Phone number is recommended for{' '}
            {activeTab === 'charity' ? 'charities' : 'businesses'}
          </p>
        )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md max-h-[90vh] !top-[16vh] !translate-y-0'>
        <DialogHeader>
          <DialogTitle>{person ? 'Edit' : 'Add New'} Person/Entity</DialogTitle>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={value => {
            setActiveTab(value as PersonType);
            setFormData(prev => ({ ...prev, type: value as PersonType }));
          }}
        >
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='individual'>Individual</TabsTrigger>
            <TabsTrigger value='charity'>Charity</TabsTrigger>
            <TabsTrigger value='business'>Business</TabsTrigger>
          </TabsList>

          <TabsContent value='individual' className='space-y-4 mt-4'>
            <div className='grid grid-cols-3 gap-3'>
              <div className='space-y-2'>
                <Label htmlFor='firstName'>First Name *</Label>
                <Input
                  id='firstName'
                  value={formData.firstName || ''}
                  onChange={e => updateFormData('firstName', e.target.value)}
                  placeholder='John'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='middleName'>Middle Name</Label>
                <Input
                  id='middleName'
                  value={formData.middleName || ''}
                  onChange={e => updateFormData('middleName', e.target.value)}
                  placeholder='Michael'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='lastName'>Last Name *</Label>
                <Input
                  id='lastName'
                  value={formData.lastName || ''}
                  onChange={e => updateFormData('lastName', e.target.value)}
                  placeholder='Doe'
                />
              </div>
            </div>
            <div className='space-y-2'>
              <Label>Date of Birth *</Label>
              <DatePicker
                date={
                  formData.dateOfBirth
                    ? new Date(formData.dateOfBirth)
                    : undefined
                }
                setDate={date =>
                  updateFormData('dateOfBirth', date?.toISOString())
                }
                modal={true}
              />
            </div>
            {renderPhoneInput(true)}
            <StreetAutocomplete
              label='Address *'
              value={formData.address || ''}
              onChange={value => updateFormData('address', value)}
              placeholder='123 Main St, City, State 12345'
            />
            <div className='space-y-2'>
              <Label htmlFor='relationshipType'>Relationship Type</Label>
              <Select
                value={formData.relationshipType || ''}
                onValueChange={value =>
                  updateFormData('relationshipType', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select relationship type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Spouse'>Spouse</SelectItem>
                  <SelectItem value='Partner'>Partner</SelectItem>
                  <SelectItem value='Family'>Family Member</SelectItem>
                  <SelectItem value='Friend'>Friend</SelectItem>
                  <SelectItem value='Business Partner'>
                    Business Partner
                  </SelectItem>
                  <SelectItem value='Professional'>
                    Professional Contact
                  </SelectItem>
                  <SelectItem value='Other'>Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>

          <TabsContent value='charity' className='space-y-4 mt-4'>
            <div className='space-y-2'>
              <Label htmlFor='entityName'>Entity Name *</Label>
              <Input
                id='entityName'
                value={formData.entityName || ''}
                onChange={e => updateFormData('entityName', e.target.value)}
                placeholder='American Red Cross'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='ein'>EIN (Employer Identification Number)</Label>
              <Input
                id='ein'
                value={formData.ein || ''}
                onChange={e => updateFormData('ein', e.target.value)}
                placeholder='12-3456789'
              />
            </div>
            {renderPhoneInput(false, true)}
            <StreetAutocomplete
              label='Address'
              value={formData.address || ''}
              onChange={value => updateFormData('address', value)}
              placeholder='123 Main St, City, State 12345'
            />
          </TabsContent>

          <TabsContent value='business' className='space-y-4 mt-4'>
            <div className='space-y-2'>
              <Label htmlFor='entityName'>Business Name *</Label>
              <Input
                id='entityName'
                value={formData.entityName || ''}
                onChange={e => updateFormData('entityName', e.target.value)}
                placeholder='ABC Corporation'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='ein'>EIN (Employer Identification Number)</Label>
              <Input
                id='ein'
                value={formData.ein || ''}
                onChange={e => updateFormData('ein', e.target.value)}
                placeholder='12-3456789'
              />
            </div>
            {renderPhoneInput(false, true)}
            <StreetAutocomplete
              label='Address'
              value={formData.address || ''}
              onChange={value => updateFormData('address', value)}
              placeholder='123 Main St, City, State 12345'
            />
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant='outline' onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                {person ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              <>{person ? 'Update' : 'Add'} Person</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default PersonModal;
