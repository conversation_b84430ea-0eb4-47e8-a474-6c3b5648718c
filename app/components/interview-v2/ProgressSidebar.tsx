'use client';

import React from 'react';
import { CheckCircle, XCircle } from 'lucide-react';
import { Card } from '@/components/ui/card';

export type ProgressItem = {
  key: string;
  label: string;
  completed?: boolean;
  disabled?: boolean;
};

interface ProgressSidebarProps {
  items: ProgressItem[];
  current: string;
  onSelect: (key: string) => void;
}

export function ProgressSidebar({
  items,
  current,
  onSelect,
}: ProgressSidebarProps) {
  return (
    <Card className='h-fit p-0'>
      <div className='p-6'>
        <h2 className='text-lg font-semibold mb-6'>My Wishes</h2>
        <nav className='space-y-1'>
          {items.map((item, idx) => (
            <button
              key={item.key}
              onClick={() => !item.disabled && onSelect(item.key)}
              disabled={item.disabled}
              className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg cursor-pointer transition-colors ${
                current === item.key
                  ? 'bg-[var(--eggplant)]/10 text-[var(--eggplant)] shadow-[inset_4px_0_0_var(--eggplant)]'
                  : 'hover:bg-[var(--eggplant)]/10 text-[var(--foreground)] hover:text-[var(--eggplant)]'
              } ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span className='flex items-center gap-3 flex-1 min-w-0'>
                <span className='text-[var(--foreground)] font-medium flex-shrink-0'>
                  {idx + 1}.
                </span>
                <span className='truncate' title={item.label}>
                  {item.label}
                </span>
              </span>
              <span className='flex-shrink-0 ml-2'>
                {item.completed ? (
                  <CheckCircle className='h-5 w-5 text-emerald-600' />
                ) : item.disabled ? (
                  <XCircle className='h-5 w-5 text-red-500' />
                ) : (
                  <div className='w-5 h-5 rounded-full border-2 border-muted-foreground/30 flex-shrink-0' />
                )}
              </span>
            </button>
          ))}
        </nav>
      </div>
    </Card>
  );
}

export default ProgressSidebar;
