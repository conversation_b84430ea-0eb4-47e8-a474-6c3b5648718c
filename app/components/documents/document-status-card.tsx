'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  FileText,
  User,
  Eye,
  PenTool,
  Download,
  Truck,
  XCircle,
  Trash2,
} from 'lucide-react';
import { Document } from '@/types/documents';
import {
  DocumentStatusMessage,
  DocumentStatusBadge,
} from '@/components/documents/document-status-badge';
import { TemplateVersionIndicator } from '@/components/documents/template-version-indicator';

interface DocumentStatusCardProps {
  document: Document;
  isDownloadingPdf?: boolean;
  isResubmitting?: boolean; // Loading state for resubmit action
  isRetracting?: boolean; // Loading state for retract action
  isShipping?: boolean; // Loading state for ship action
  isDeleting?: boolean; // Loading state for delete action
  isMarkingReceived?: boolean; // Loading state for mark as received action

  onReview?: () => void;
  onSign?: () => void;
  onDownload?: () => void;

  onPreview?: () => void;
  onResubmit?: () => void; // For resubmitting rejected documents
  onRetract?: () => void; // For retracting sent documents
  onShip?: () => void; // For shipping approved documents
  onDelete?: () => void; // For archiving draft documents
  onPermanentDelete?: () => void; // For permanently deleting archived documents
  onOpenReviewDialog?: () => void; // For opening review dialog (Welon Trust)
  onMarkReceived?: () => void; // For marking document as received (Welon Trust)
  className?: string;
  isWelonTrustView?: boolean; // Whether this is being viewed by Welon Trust user
  // Bulk shipping props
  showBulkSelect?: boolean; // Whether to show the bulk selection checkbox
  isSelected?: boolean; // Whether this document is selected for bulk shipping
  isAddedToShipping?: boolean; // Whether this document is already added to shipping queue
  onBulkSelect?: (documentId: string, selected: boolean) => void; // Callback for bulk selection
}

export function DocumentStatusCard({
  document,
  onReview,
  onSign,
  onDownload,
  onPreview,
  onResubmit,
  onRetract,
  onShip,
  onDelete,
  onPermanentDelete,
  onOpenReviewDialog,
  onMarkReceived,
  isDownloadingPdf = false,
  isResubmitting = false,
  isRetracting = false,
  isShipping = false,
  isDeleting = false,
  isMarkingReceived = false,
  className = '',
  isWelonTrustView = false,
  showBulkSelect = false,
  isSelected = false,
  isAddedToShipping = false,
  onBulkSelect,
}: DocumentStatusCardProps) {
  // State to track if any action is in progress
  const [isActionInProgress, setIsActionInProgress] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Helper function to wrap action handlers with loading state
  const wrapActionHandler = (handler?: () => void) => {
    if (!handler) return undefined;

    return () => {
      setIsActionInProgress(true);
      handler();
      // Reset after longer delay to ensure visual feedback
      setTimeout(() => setIsActionInProgress(false), 3000);
    };
  };

  const getAvailableActions = () => {
    const actions = [];

    // Preview is available for draft documents and others
    if (onPreview) {
      actions.push(
        <Button
          key='preview'
          variant='outline'
          size='sm'
          onClick={wrapActionHandler(onPreview)}
          disabled={isActionInProgress}
          className={`flex items-center gap-2 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Eye className='h-4 w-4' />
          {document.status === 'draft' ? 'Preview' : 'Preview'}
        </Button>
      );
    }

    // Download is available for documents with fileUrl (generated PDF)
    if (onDownload && document.fileUrl) {
      const isSignedDocument = document.status === 'signed';
      actions.push(
        <Button
          key='download'
          disabled={isDownloadingPdf || isActionInProgress}
          variant='outline'
          size='sm'
          onClick={wrapActionHandler(onDownload)}
          className={`flex items-center gap-2 ${
            isSignedDocument
              ? 'border-green-300 text-green-600 hover:bg-green-50'
              : ''
          } ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Download className='h-4 w-4' />
          {isDownloadingPdf
            ? 'Downloading...'
            : isSignedDocument
              ? 'Download Signed'
              : 'Download'}
        </Button>
      );
    }

    // Show message if PDF is not available
    if (onDownload && !document.fileUrl && document.status !== 'draft') {
      actions.push(
        <Button
          key='download-unavailable'
          disabled={true}
          variant='outline'
          size='sm'
          className='flex items-center gap-2 opacity-50'
        >
          <Download className='h-4 w-4' />
          PDF Not Available
        </Button>
      );
    }

    // Review action - only for approved documents (ready for signing) and not Welon Trust view
    if (onReview && document.status === 'approved' && !isWelonTrustView) {
      actions.push(
        <Button
          key='review'
          variant='default'
          size='sm'
          onClick={wrapActionHandler(onReview)}
          disabled={isActionInProgress}
          className={`flex items-center gap-2 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Eye className='h-4 w-4' />
          Review & Sign
        </Button>
      );
    }

    // Sign action for approved documents only and not Welon Trust view
    if (onSign && document.status === 'approved' && !isWelonTrustView) {
      actions.push(
        <Button
          key='sign'
          variant='default'
          size='sm'
          onClick={wrapActionHandler(onSign)}
          disabled={isActionInProgress}
          className={`flex items-center gap-2 bg-green-600 hover:bg-green-700 text-[var(--off-white)] ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <PenTool className='h-4 w-4' />
          Sign
        </Button>
      );
    }

    // Send for electronic review action for draft documents and not Welon Trust view
    if (onShip && document.status === 'draft' && !isWelonTrustView) {
      actions.push(
        <Button
          key='send-review'
          variant='default'
          size='sm'
          onClick={wrapActionHandler(onShip)}
          disabled={isShipping || isActionInProgress}
          className={`flex items-center gap-2 text-[var(--off-white)] bg-blue-600 hover:bg-blue-700 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Eye className='h-4 w-4' />
          {isShipping ? 'Sending...' : 'Send for Review'}
        </Button>
      );
    }

    // Retract action for sent documents and not Welon Trust view
    if (onRetract && document.status === 'sent' && !isWelonTrustView) {
      actions.push(
        <Button
          key='retract'
          variant='outline'
          size='sm'
          onClick={wrapActionHandler(onRetract)}
          disabled={isRetracting || isActionInProgress}
          className={`flex items-center gap-2 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <XCircle className='h-4 w-4' />
          {isRetracting ? 'Retracting...' : 'Retract'}
        </Button>
      );
    }

    if (
      onResubmit &&
      (document.status === 'rejected' || document.status === 'draft')
    ) {
      actions.push(
        <Button
          key='recreate'
          variant='default'
          size='sm'
          onClick={wrapActionHandler(onResubmit)}
          disabled={isResubmitting || isActionInProgress}
          className={`flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-[var(--off-white)] ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <FileText className='h-4 w-4' />
          {isResubmitting ? 'Recreating...' : 'Recreate'}
        </Button>
      );
    }

    // Ship by mail action for signed documents and not Welon Trust view
    if (onShip && document.status === 'signed' && !isWelonTrustView) {
      const isAlreadyAdded = isAddedToShipping || isSelected;
      actions.push(
        <Button
          key='ship-mail'
          variant={isAlreadyAdded ? 'outline' : 'default'}
          size='sm'
          onClick={wrapActionHandler(onShip)}
          disabled={isShipping || isActionInProgress}
          className={`flex items-center gap-2 ${
            isAlreadyAdded
              ? 'border-green-200 text-green-600 bg-green-50 hover:border-red-200 hover:text-red-700 hover:bg-red-50'
              : 'bg-indigo-600 hover:bg-indigo-700 text-[var(--off-white)]'
          } ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Truck className='h-4 w-4' />
          {isShipping
            ? 'Processing...'
            : isAlreadyAdded
              ? 'Remove from Shipping'
              : 'Add to Shipping'}
        </Button>
      );
    }

    // Review dialog action for sent documents (Welon Trust electronic review)
    if (onOpenReviewDialog && document.status === 'sent') {
      actions.push(
        <Button
          key='review-dialog'
          variant='default'
          size='sm'
          onClick={wrapActionHandler(onOpenReviewDialog)}
          disabled={isActionInProgress}
          className={`flex items-center gap-2 text-[var(--off-white)] bg-orange-600 hover:bg-orange-700 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Eye className='h-4 w-4' />
          Review
        </Button>
      );
    }

    // Archive action for draft documents and not Welon Trust view
    if (onDelete && document.status === 'draft' && !isWelonTrustView) {
      actions.push(
        <Button
          key='archive'
          variant='outline'
          size='sm'
          onClick={wrapActionHandler(onDelete)}
          disabled={isDeleting || isActionInProgress}
          className={`flex items-center gap-2 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <XCircle className='h-4 w-4' />
          {isDeleting ? 'Archiving...' : 'Archive'}
        </Button>
      );
    }

    // Permanent delete action for archived documents and not Welon Trust view
    if (
      onPermanentDelete &&
      document.status === 'archived' &&
      !isWelonTrustView
    ) {
      actions.push(
        <Button
          key='permanent-delete'
          variant='outline'
          size='sm'
          onClick={wrapActionHandler(onPermanentDelete)}
          disabled={isDeleting || isActionInProgress}
          className={`flex items-center gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 ${isActionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Trash2 className='h-4 w-4' />
          {isDeleting ? 'Deleting...' : 'Delete Permanently'}
        </Button>
      );
    }

    return actions;
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className='pb-3'>
        <div className='flex items-start justify-between'>
          <div className='flex items-start gap-3'>
            {/* Bulk selection checkbox */}
            {showBulkSelect && document.status === 'approved' && (
              <div className='pt-2'>
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={checked =>
                    onBulkSelect?.(document.id, checked === true)
                  }
                  className='h-5 w-5'
                />
              </div>
            )}
            <div className='p-2 bg-blue-50 rounded-lg'>
              <FileText className='h-5 w-5 text-blue-600' />
            </div>
            <div>
              <CardTitle className='text-lg'>{document.title}</CardTitle>
              <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>{document.type}</span>
                </div>
                <TemplateVersionIndicator
                  documentVersion={document.version}
                  templateVersion={document.templateVersion}
                  templateId={document.templateId}
                  className='text-muted-foreground'
                />
              </div>
            </div>
          </div>
          <DocumentStatusBadge status={document.status} />
        </div>
      </CardHeader>
      <CardContent className='pt-0'>
        <div className='space-y-4'>
          {/* Status Message */}
          <DocumentStatusMessage status={document.status} />

          {/* Additional Status Info */}
          {document.status === 'shipped' && document.trackingNumber && (
            <div className='bg-blue-50 border border-blue-200 rounded-lg p-3'>
              <div className='flex items-center gap-2 text-sm'>
                <Truck className='h-4 w-4 text-blue-600' />
                <span className='font-medium text-[var(--off-black)]'>
                  Tracking Number:
                </span>
                <span className='font-mono text-[var(--off-black)]'>
                  {document.trackingNumber}
                </span>
              </div>
              {/* Mark as received checkbox for Welon Trust - only show for shipped documents */}
              {isWelonTrustView &&
                onMarkReceived &&
                document.status === 'shipped' && (
                  <div className='flex items-center gap-2 mt-3 pt-3 border-t border-blue-200'>
                    <Checkbox
                      id={`received-${document.id}`}
                      checked={false}
                      onCheckedChange={checked => {
                        if (checked) {
                          onMarkReceived();
                        }
                      }}
                      disabled={isMarkingReceived}
                      className='h-4 w-4'
                    />
                    <label
                      htmlFor={`received-${document.id}`}
                      className={`text-sm font-medium cursor-pointer ${
                        isMarkingReceived
                          ? 'text-blue-600'
                          : 'text-blue-800 hover:text-blue-900'
                      }`}
                    >
                      {isMarkingReceived
                        ? 'Marking as received...'
                        : 'Mark as received'}
                    </label>
                  </div>
                )}
            </div>
          )}

          {document.status === 'rejected' && document.rejectionReason && (
            <div className='bg-red-50 border border-red-200 rounded-lg p-3'>
              <div className='flex items-start gap-2 text-sm'>
                <XCircle className='h-4 w-4 text-red-600 mt-0.5' />
                <div>
                  <span className='font-medium text-red-800'>
                    Rejection Reason:
                  </span>
                  <p className='text-red-700 mt-1'>
                    {document.rejectionReason}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Document Metadata */}
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-muted-foreground'>Created:</span>
              <div className='font-medium'>
                {formatDate(document.dateCreated)}
              </div>
            </div>
            <div>
              <span className='text-muted-foreground'>Last Modified:</span>
              <div className='font-medium'>
                {formatDate(document.lastModified)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className='flex items-center gap-2 pt-2 border-t'>
            {getAvailableActions()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
