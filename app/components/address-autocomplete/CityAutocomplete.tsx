'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface CityAutocompleteProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

declare global {
  interface Window {
    google: any;
  }
}

export function CityAutocomplete({
  label,
  value,
  onChange,
  placeholder = 'Start typing a city...',
  disabled = false,
}: CityAutocompleteProps) {
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Search for city suggestions
  const searchCities = async (query: string) => {
    if (!query || query.length < 2 || !window.google) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);

    try {
      const service = new window.google.maps.places.AutocompleteService();

      service.getPlacePredictions(
        {
          input: query,
          types: ['(cities)'],
          componentRestrictions: { country: 'us' },
        },
        (predictions: any[], status: any) => {
          setIsLoading(false);

          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            predictions
          ) {
            setSuggestions(predictions.slice(0, 5));
            setShowSuggestions(true);
          } else {
            setSuggestions([]);
            setShowSuggestions(false);
          }
        }
      );
    } catch (error) {
      Sentry.captureException(error);
      console.error('City search failed:', error);
      setIsLoading(false);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Filter out non-English characters for US addresses
    const englishOnlyValue = newValue.replace(/[^\x00-\x7F]/g, '');
    if (englishOnlyValue !== newValue) {
      console.log('⚠️ City: Non-English characters detected and removed:', {
        original: newValue,
        filtered: englishOnlyValue,
      });
    }

    onChange(englishOnlyValue);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      searchCities(englishOnlyValue);
    }, 300);
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Handle suggestion selection
  const handleSuggestionClick = (suggestion: any) => {
    onChange(suggestion.structured_formatting.main_text);
    setShowSuggestions(false);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className='space-y-2 relative' ref={inputRef}>
      <Label htmlFor='city-input'>{label}</Label>
      <Input
        id='city-input'
        value={value}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        placeholder={placeholder}
        disabled={disabled}
        className='w-full'
      />

      {showSuggestions && suggestions.length > 0 && (
        <div className='absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto'>
          {suggestions.map(suggestion => (
            <div
              key={suggestion.place_id}
              className='px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0'
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className='font-medium text-sm'>
                {suggestion.structured_formatting.main_text}
              </div>
              <div className='text-xs text-gray-500'>
                {suggestion.structured_formatting.secondary_text}
              </div>
            </div>
          ))}
        </div>
      )}

      {isLoading && (
        <div className='absolute right-3 top-8 text-gray-400'>
          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400'></div>
        </div>
      )}
    </div>
  );
}
