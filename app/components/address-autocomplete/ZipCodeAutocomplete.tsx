'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface ZipCodeAutocompleteProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  onZipSelect?: (zipData: { zip: string; city: string; state: string }) => void;
  placeholder?: string;
  disabled?: boolean;
}

declare global {
  interface Window {
    google: any;
  }
}

export function ZipCodeAutocomplete({
  label,
  value,
  onChange,
  onZipSelect,
  placeholder = 'Enter ZIP code...',
  disabled = false,
}: ZipCodeAutocompleteProps) {
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Search for ZIP code suggestions
  const searchZipCodes = async (query: string) => {
    if (!query || query.length < 3 || !window.google) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);

    try {
      const service = new window.google.maps.places.AutocompleteService();

      service.getPlacePredictions(
        {
          input: query,
          types: ['postal_code'],
          componentRestrictions: { country: 'us' },
        },
        (predictions: any[], status: any) => {
          setIsLoading(false);

          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            predictions
          ) {
            setSuggestions(predictions.slice(0, 5));
            setShowSuggestions(true);
          } else {
            setSuggestions([]);
            setShowSuggestions(false);
          }
        }
      );
    } catch (error) {
      Sentry.captureException(error);
      console.error('ZIP code search failed:', error);
      setIsLoading(false);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/[^0-9-]/g, ''); // Only allow numbers and hyphens
    onChange(newValue);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      searchZipCodes(newValue);
    }, 300);
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Handle suggestion selection
  const handleSuggestionClick = async (suggestion: any) => {
    const zipCode = suggestion.structured_formatting.main_text;
    onChange(zipCode);
    setShowSuggestions(false);

    if (onZipSelect && window.google) {
      try {
        const service = new window.google.maps.places.PlacesService(
          document.createElement('div')
        );

        service.getDetails(
          {
            placeId: suggestion.place_id,
            fields: ['address_components'],
          },
          (place: any, status: any) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK) {
              const addressData: any = {};

              place.address_components.forEach((component: any) => {
                const types = component.types;
                if (types.includes('postal_code')) {
                  addressData.zip = component.long_name;
                } else if (types.includes('locality')) {
                  addressData.city = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                  addressData.state = component.short_name;
                }
              });

              if (addressData.zip) {
                onZipSelect({
                  zip: addressData.zip,
                  city: addressData.city || '',
                  state: addressData.state || '',
                });
              }
            }
          }
        );
      } catch (error) {
        Sentry.captureException(error);
        console.error('Failed to get ZIP code details:', error);
      }
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className='space-y-2 relative' ref={inputRef}>
      <Label htmlFor='zip-input'>{label}</Label>
      <Input
        id='zip-input'
        value={value}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        placeholder={placeholder}
        disabled={disabled}
        className='w-full'
        maxLength={10}
      />

      {showSuggestions && suggestions.length > 0 && (
        <div className='absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto'>
          {suggestions.map(suggestion => (
            <div
              key={suggestion.place_id}
              className='px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0'
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className='font-medium text-sm'>
                {suggestion.structured_formatting.main_text}
              </div>
              <div className='text-xs text-gray-500'>
                {suggestion.structured_formatting.secondary_text}
              </div>
            </div>
          ))}
        </div>
      )}

      {isLoading && (
        <div className='absolute right-3 top-8 text-gray-400'>
          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400'></div>
        </div>
      )}
    </div>
  );
}
