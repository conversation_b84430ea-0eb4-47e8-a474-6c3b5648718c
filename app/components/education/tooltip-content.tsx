/**
 * TooltipContent Component
 *
 * A component for displaying tooltip content with a trigger.
 */

'use client';

import React from 'react';
import { TooltipContent as ContentType } from '@/types/education';
import { Button } from '@/components/ui/button';
import { cleanHTML } from '@/utils/cleanHTML';

interface TooltipContentProps {
  tooltip: ContentType;
  onView?: () => void;
  triggerElement?: React.ReactNode;
}

export function TooltipContentComponent({
  tooltip,
  onView,
  triggerElement,
}: TooltipContentProps) {
  // Call onView when tooltip is opened
  const handleOpen = () => {
    if (onView) {
      onView();
    }
  };

  const [isOpen, setIsOpen] = React.useState(false);

  // Simple tooltip implementation
  return (
    <div className='relative inline-block'>
      <div
        onClick={() => {
          setIsOpen(!isOpen);
          if (!isOpen) handleOpen();
        }}
      >
        {triggerElement || (
          <Button variant='link' className='p-0 h-auto font-normal underline'>
            {tooltip.triggerText}
          </Button>
        )}
      </div>
      {isOpen && (
        <div className='absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 bg-background dark:bg-gray-800 shadow-lg rounded-md p-4 max-w-xs'>
          <div>
            <h4 className='font-semibold text-sm mb-1'>{tooltip.title}</h4>
            <div
              className='text-xs'
              dangerouslySetInnerHTML={{ __html: cleanHTML(tooltip.content as string) }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * InlineTooltip Component
 *
 * A component for displaying tooltip content inline within text.
 */
export function InlineTooltip({
  tooltip,
  onView,
}: {
  tooltip: ContentType;
  onView?: () => void;
}) {
  return (
    <TooltipContentComponent
      tooltip={tooltip}
      onView={onView}
      triggerElement={
        <span className='border-b border-dotted border-primary cursor-help'>
          {tooltip.triggerText}
        </span>
      }
    />
  );
}

/**
 * ContextualTooltip Component
 *
 * A component for displaying tooltip content in a contextual manner.
 */
export function ContextualTooltip({
  tooltip,
  onView,
  children,
}: {
  tooltip: ContentType;
  onView?: () => void;
  children: React.ReactNode;
}) {
  return (
    <TooltipContentComponent
      tooltip={tooltip}
      onView={onView}
      triggerElement={
        <span className='border-b border-dotted border-primary cursor-help'>
          {children}
        </span>
      }
    />
  );
}
