'use client';

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { toast } from 'sonner';
import { ContentType, ContentStatus } from '@/types/education';
import {
  getVideoThumbnailUrl,
  VideoValidationResult,
} from '@/utils/video-utils';
import { QUESTIONS_BY_SECTION } from '@/app/constants/interview-questions';
import { VideoUrlInput } from '../ui/video-url-input';

// Validation schema
const editContentSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  type: z.nativeEnum(ContentType, {
    required_error: 'Content type is required',
  }),
  status: z.nativeEnum(ContentStatus),
  description: z.string().optional(),
  contentUrl: z.string().min(1, 'Content URL is required'),
  thumbnailUrl: z.string().optional(),
  duration: z.string().optional(),
  readingTime: z.string().optional(),
  tags: z.array(z.string()),
  // Display location flags for video content
  showInEducationalContent: z.boolean().optional(),
  showInInterviewHints: z.boolean().optional(),
  showInOnboarding: z.boolean().optional(),
  // Interview questions where this video should appear
  interviewQuestions: z.array(z.string()).optional(),
});

type EditContentFormData = z.infer<typeof editContentSchema>;

interface EditContentModalProps {
  contentItem: any;
  isOpen: boolean;
  onClose: () => void;
  onContentUpdated?: () => void;
}

export function EditContentModal({
  contentItem,
  isOpen,
  onClose,
  onContentUpdated,
}: EditContentModalProps) {
  const {
    updateContent,
    hasOnboardingVideo,
    hasVideoForQuestion,
    getVideoTitleForQuestion,
  } = useEducationalContent();
  const [newTag, setNewTag] = useState('');
  const [videoValidation, setVideoValidation] = useState<VideoValidationResult>(
    { isValid: true }
  );

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EditContentFormData>({
    resolver: zodResolver(editContentSchema),
    defaultValues: {
      title: '',
      type: undefined,
      status: ContentStatus.DRAFT,
      description: '',
      contentUrl: '',
      thumbnailUrl: '',
      duration: '',
      readingTime: '',
      tags: [],
      showInEducationalContent: false,
      showInInterviewHints: false,
      showInOnboarding: false,
      interviewQuestions: [],
    },
  });

  const watchedType = watch('type');
  const watchedTags = watch('tags');
  const watchedContentUrl = watch('contentUrl');
  const watchedShowInOnboarding = watch('showInOnboarding');
  const watchedShowInInterviewHints = watch('showInInterviewHints');
  const watchedInterviewQuestions = watch('interviewQuestions');

  // Initialize form data when contentItem changes
  useEffect(() => {
    if (contentItem) {
      reset({
        title: contentItem.title || '',
        type: contentItem.type || undefined,
        status: contentItem.status || ContentStatus.DRAFT,
        description: contentItem.description || '',
        contentUrl: contentItem.contentUrl || '',
        thumbnailUrl: contentItem.thumbnailUrl || '',
        duration: contentItem.duration?.toString() || '',
        readingTime: contentItem.readingTime?.toString() || '',
        tags: contentItem.tags || [],
        showInEducationalContent: contentItem.showInEducationalContent || false,
        showInInterviewHints: contentItem.showInInterviewHints || false,
        showInOnboarding: contentItem.showInOnboarding || false,
        interviewQuestions: contentItem.interviewQuestions || [],
      });
    }
  }, [contentItem, reset]);

  // Auto-fill thumbnail URL for videos when contentUrl changes
  useEffect(() => {
    if (
      watchedType === ContentType.VIDEO &&
      watchedContentUrl &&
      !watch('thumbnailUrl')
    ) {
      const thumbnailUrl = getVideoThumbnailUrl(watchedContentUrl);
      if (thumbnailUrl) {
        setValue('thumbnailUrl', thumbnailUrl);
      }
    }
  }, [watchedType, watchedContentUrl, setValue, watch]);

  const onSubmit = async (data: EditContentFormData) => {
    try {
      // Validate video URL if it's a video content type
      if (data.type === ContentType.VIDEO && !videoValidation.isValid) {
        toast.error('Please fix the video URL before submitting');
        return;
      }

      // Validate onboarding video constraint
      if (
        data.showInOnboarding &&
        !contentItem?.showInOnboarding &&
        hasOnboardingVideo()
      ) {
        toast.error(
          'Cannot enable onboarding for this video. There is already one active. Please disable the existing one first.'
        );
        return;
      }

      const updateData = {
        title: data.title,
        type: data.type,
        status: data.status,
        description: data.description,
        contentUrl: data.contentUrl,
        thumbnailUrl: data.thumbnailUrl,
        tags: data.tags,
        showInEducationalContent: data.showInEducationalContent || false,
        showInInterviewHints: data.showInInterviewHints || false,
        showInOnboarding: data.showInOnboarding || false,
        interviewQuestions: data.interviewQuestions || [],
        ...(data.duration && { duration: parseInt(data.duration) }),
        ...(data.readingTime && {
          readingTime: parseInt(data.readingTime),
        }),
      };

      await updateContent(contentItem.id, updateData);

      onClose();

      if (onContentUpdated) {
        onContentUpdated();
      }

      toast.success('Content updated successfully');
    } catch (error) {
      console.error('Error updating content:', error);
      toast.error('Failed to update educational content');
    }
  };

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      setValue('tags', [...watchedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setValue(
      'tags',
      watchedTags.filter(tag => tag !== tagToRemove)
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-5xl max-h-[90vh] height-auto  overflow-y-auto top-[8vh] !translate-y-0'>
        <DialogHeader>
          <DialogTitle>Edit Educational Content</DialogTitle>
          <DialogDescription>
            Update the educational content information.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Title */}
          <div className='space-y-2'>
            <Label htmlFor='title'>Title *</Label>
            <Controller
              name='title'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='title'
                  placeholder='Enter content title'
                  className={errors.title ? 'border-red-500' : ''}
                />
              )}
            />
            {errors.title && (
              <p className='text-sm text-red-500'>{errors.title.message}</p>
            )}
          </div>

          {/* Type and Status */}
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='type'>Type *</Label>
              <Controller
                name='type'
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger
                      className={errors.type ? 'border-red-500' : ''}
                    >
                      <SelectValue placeholder='Select content type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentType.VIDEO}>Video</SelectItem>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.ARTICLE}
                              disabled
                              className='opacity-50'
                            >
                              Article (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.INFOGRAPHIC}
                              disabled
                              className='opacity-50'
                            >
                              Infographic (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.AVATAR}
                              disabled
                              className='opacity-50'
                            >
                              Interactive Guide (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.TOOLTIP}
                              disabled
                              className='opacity-50'
                            >
                              Quick Tip (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.type && (
                <p className='text-sm text-red-500'>{errors.type.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='status'>Status</Label>
              <Controller
                name='status'
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentStatus.DRAFT}>Draft</SelectItem>
                      <SelectItem value={ContentStatus.PUBLISHED}>
                        Published
                      </SelectItem>
                      <SelectItem value={ContentStatus.ARCHIVED}>
                        Archived
                      </SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>

          {/* Content URL */}
          <div className='space-y-2'>
            {watchedType === ContentType.VIDEO ? (
              <Controller
                name='contentUrl'
                control={control}
                render={({ field }) => (
                  <VideoUrlInput
                    value={field.value}
                    onChange={field.onChange}
                    onValidationChange={setVideoValidation}
                    label='Video URL'
                    placeholder='https://youtube.com/watch?v=... or https://vimeo.com/...'
                    required
                    showPreview={true}
                    autoValidate
                    className={errors.contentUrl ? 'border-red-500' : ''}
                  />
                )}
              />
            ) : (
              <>
                <Label htmlFor='contentUrl'>
                  {watchedType === ContentType.ARTICLE
                    ? 'Article URL'
                    : watchedType === ContentType.INFOGRAPHIC
                      ? 'Image URL'
                      : 'Content URL'}
                </Label>
                <Controller
                  name='contentUrl'
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id='contentUrl'
                      placeholder={
                        watchedType === ContentType.ARTICLE
                          ? 'https://example.com/article'
                          : watchedType === ContentType.INFOGRAPHIC
                            ? 'https://example.com/image.jpg'
                            : 'Enter content URL'
                      }
                      className={errors.contentUrl ? 'border-red-500' : ''}
                    />
                  )}
                />
              </>
            )}
            {errors.contentUrl && (
              <p className='text-sm text-red-500'>
                {errors.contentUrl.message}
              </p>
            )}
          </div>

          {/* Video thumbnail info */}
          {watchedType === ContentType.VIDEO && (
            <div className='space-y-2'>
              <p className='text-sm text-muted-foreground'>
                Thumbnail will be automatically generated from the video URL
              </p>
            </div>
          )}

          {/* Thumbnail URL - Only show for non-video content */}
          {watchedType !== ContentType.VIDEO && (
            <div className='space-y-2'>
              <Label htmlFor='thumbnailUrl'>Thumbnail URL (Optional)</Label>
              <Controller
                name='thumbnailUrl'
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id='thumbnailUrl'
                    placeholder='https://example.com/thumbnail.jpg'
                  />
                )}
              />
            </div>
          )}

          {/* Duration/Reading Time */}
          {(watchedType === ContentType.VIDEO ||
            watchedType === ContentType.ARTICLE) && (
            <div className='space-y-2'>
              <Label
                htmlFor={
                  watchedType === ContentType.VIDEO ? 'duration' : 'readingTime'
                }
              >
                {watchedType === ContentType.VIDEO
                  ? 'Duration (seconds)'
                  : 'Reading Time (minutes)'}
              </Label>
              <Controller
                name={
                  watchedType === ContentType.VIDEO ? 'duration' : 'readingTime'
                }
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id={
                      watchedType === ContentType.VIDEO
                        ? 'duration'
                        : 'readingTime'
                    }
                    type='number'
                    placeholder={
                      watchedType === ContentType.VIDEO ? '300' : '5'
                    }
                  />
                )}
              />
            </div>
          )}

          {/* Description */}
          <div className='space-y-2'>
            <Label htmlFor='description'>Description</Label>
            <Controller
              name='description'
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  id='description'
                  placeholder='Enter content description'
                  rows={3}
                />
              )}
            />
          </div>

          {/* Tags */}
          <div className='space-y-2'>
            <Label>Tags</Label>
            <div className='flex flex-wrap gap-2 mb-2'>
              {watchedTags.map((tag, index) => (
                <Badge
                  key={index}
                  variant='secondary'
                  className='flex items-center gap-1'
                >
                  {tag}
                  <X
                    className='h-3 w-3 cursor-pointer'
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className='flex gap-2'>
              <Input
                value={newTag}
                onChange={e => setNewTag(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder='Add a tag'
              />
              <Button
                type='button'
                variant='outline'
                onClick={addTag}
                disabled={!newTag.trim()}
              >
                Add
              </Button>
            </div>
          </div>

          {/* Display Location Settings - Only show for video content */}
          {watchedType === ContentType.VIDEO && (
            <div className='space-y-4'>
              <div className='border-t pt-4'>
                <Label className='text-base font-medium'>
                  Where should this video appear?
                </Label>
                <p className='text-sm text-muted-foreground mb-4'>
                  Select where you want this video to be displayed in the
                  application.
                </p>

                <div className='space-y-3'>
                  <div className='flex items-center space-x-2'>
                    <Controller
                      name='showInEducationalContent'
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id='showInEducationalContent'
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      )}
                    />
                    <Label
                      htmlFor='showInEducationalContent'
                      className='text-sm'
                    >
                      Educational Content Page
                    </Label>
                  </div>

                  <div className='flex items-center space-x-2'>
                    <Controller
                      name='showInInterviewHints'
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id='showInInterviewHints'
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      )}
                    />
                    <Label htmlFor='showInInterviewHints' className='text-sm'>
                      Interview Questions (as hints)
                    </Label>
                  </div>

                  <div className='flex items-center space-x-2'>
                    <Controller
                      name='showInOnboarding'
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id='showInOnboarding'
                          checked={field.value}
                          onCheckedChange={checked => {
                            field.onChange(checked);
                            // Note: Validation for only one onboarding video will be handled server-side
                          }}
                        />
                      )}
                    />
                    <Label htmlFor='showInOnboarding' className='text-sm'>
                      Onboarding Page
                    </Label>
                  </div>

                  {watchedShowInOnboarding &&
                    hasOnboardingVideo() &&
                    !contentItem?.showInOnboarding && (
                      <div className='bg-red-50 border border-red-200 rounded-md p-3'>
                        <p className='text-sm text-red-800'>
                          <strong>Error:</strong> There is already an onboarding
                          video. Only one onboarding video is allowed. Please
                          disable the existing one first.
                        </p>
                      </div>
                    )}

                  {/* Interview Questions Selector */}
                  {watchedShowInInterviewHints && (
                    <div className='mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md'>
                      <Label className='text-sm font-medium text-blue-900 mb-3 block'>
                        Select Interview Questions/Sections
                      </Label>
                      <p className='text-xs text-blue-700 mb-3'>
                        Choose where this video should appear as a hint in the
                        interview process.
                      </p>

                      <div className='max-h-auto overflow-y-auto space-y-3'>
                        {Object.entries(QUESTIONS_BY_SECTION).map(
                          ([section, questions]) => (
                            <div key={section} className='space-y-2'>
                              <h4 className='text-sm font-medium text-blue-800'>
                                {section}
                              </h4>
                              <div className='space-y-1 ml-2'>
                                {questions.map(question => {
                                  const hasExistingVideo = hasVideoForQuestion(
                                    question.id,
                                    contentItem?.id
                                  );
                                  const videoTitle = getVideoTitleForQuestion(
                                    question.id,
                                    contentItem?.id
                                  );
                                  const isSelected =
                                    watchedInterviewQuestions?.includes(
                                      question.id
                                    ) || false;
                                  const showWarning =
                                    hasExistingVideo && isSelected;

                                  return (
                                    <div
                                      key={question.id}
                                      className='space-y-1'
                                    >
                                      <div className='flex items-start space-x-2'>
                                        <Controller
                                          name='interviewQuestions'
                                          control={control}
                                          render={({ field }) => (
                                            <Checkbox
                                              id={question.id}
                                              checked={isSelected}
                                              onCheckedChange={checked => {
                                                const currentQuestions =
                                                  field.value || [];

                                                if (
                                                  checked &&
                                                  hasExistingVideo
                                                ) {
                                                  toast.error(
                                                    `Cannot select "${question.label}" - this question already has a video assigned`
                                                  );
                                                  return;
                                                }

                                                if (checked) {
                                                  field.onChange([
                                                    ...currentQuestions,
                                                    question.id,
                                                  ]);
                                                } else {
                                                  field.onChange(
                                                    currentQuestions.filter(
                                                      (id: string) =>
                                                        id !== question.id
                                                    )
                                                  );
                                                }
                                              }}
                                            />
                                          )}
                                        />
                                        <div className='flex-1'>
                                          <Label
                                            htmlFor={question.id}
                                            className='text-xs font-normal cursor-pointer'
                                          >
                                            {question.label}
                                            {videoTitle && (
                                              <span className='text-gray-500'>
                                                {' '}
                                                (
                                                {videoTitle.length > 25
                                                  ? videoTitle
                                                      .substring(0, 25)
                                                      .trim() + '...'
                                                  : videoTitle}
                                                )
                                              </span>
                                            )}
                                          </Label>
                                          {showWarning && (
                                            <p className='text-xs text-red-600 mt-1'>
                                              This question already has a video
                                              assigned
                                            </p>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Content'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
