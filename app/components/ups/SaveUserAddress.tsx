'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, Save, MapPin } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UPSAddress } from '@/app/types/ups';
import { StreetAutocomplete } from '@/components/address-autocomplete/StreetAutocomplete';
import { CityAutocomplete } from '@/components/address-autocomplete/CityAutocomplete';
import { ZipCodeAutocomplete } from '@/components/address-autocomplete/ZipCodeAutocomplete';
import { CountyAutocomplete } from '@/components/address-autocomplete/CountyAutocomplete';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/app/context/AuthContext';
import { toast } from 'sonner';

import { useQuery } from '@tanstack/react-query';
import { userShipping<PERSON>pi } from '@/lib/api/user-shipping';
import { validateAddress } from '@/app/utils/addressValidation';

interface SaveUserAddressProps {
  onAddressSaved?: () => void;
}

export default function SaveUserAddress({
  onAddressSaved,
}: SaveUserAddressProps) {
  const { userId } = useAuth();

  const [isSavingShippingAddress, setIsSavingShippingAddress] = useState(false);

  const [error, setError] = useState<string | null>(null);

  const [address, setAddress] = useState<UPSAddress>({
    addressLine1: '',
    addressLine2: '',
    city: '',
    stateProvinceCode: '',
    postalCode: '',
    countryCode: 'US',
  });

  const {
    data: shippingAddress,
    isLoading: isLoadingShippingAddress,
    refetch: refetchShippingAddress,
    error: shippingAddressError,
  } = useQuery({
    queryKey: ['user-shipping-address', userId],
    queryFn: () => userShippingApi.getUserShippingAddress(userId as string),
    enabled: !!userId,
  });

  const handleSaveAddress = async () => {
    setIsSavingShippingAddress(true);
    setError(null);

    try {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Validate address before saving (strict validation for shipping)
      console.log('🔍 Validating shipping address...');
      const addressValidationResult = await validateAddress({
        addressLine1: address.addressLine1,
        city: address.city,
        state: address.stateProvinceCode,
        postalCode: address.postalCode,
      });

      if (!addressValidationResult.isValid) {
        setError(`${addressValidationResult.issues.join(', ')}`);
        return;
      }

      console.log('✅ Address validation passed for shipping');

      await userShippingApi.saveUserShippingAddress(
        userId as string,
        address as any
      );
      await refetchShippingAddress();

      setAddress({
        addressLine1: '',
        addressLine2: '',
        city: '',
        stateProvinceCode: '',
        postalCode: '',
        countryCode: 'US',
      });
      setError(null); // Clear any previous errors
      toast.success('Address saved successfully!');
      if (onAddressSaved) {
        onAddressSaved();
      }
    } catch (error) {
      console.error('Failed to save address:', error);
      setError('Failed to save shipping address. Please try again.');
      toast.error('Failed to save shipping address');
    } finally {
      setIsSavingShippingAddress(false);
    }
  };

  const isFormValid = () => {
    return (
      address.addressLine1 &&
      address.city &&
      address.stateProvinceCode &&
      address.postalCode
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <MapPin className='h-5 w-5' />
          Save Your Shipping Address
        </CardTitle>
        <CardDescription>
          Save your address for future shipping labels
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {error && (
          <Alert variant='destructive'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {shippingAddressError && (
          <Alert variant='destructive'>
            <AlertDescription>
              {(shippingAddressError as any)?.message ||
                'Failed to load shipping address. Please try again.'}
            </AlertDescription>
          </Alert>
        )}

        {/* No Address Yet */}
        {!isLoadingShippingAddress &&
          !shippingAddressError &&
          shippingAddress === null && (
            <Alert>
              <AlertDescription>
                You didn't add a shipping address yet. Use the form below to
                save one.
              </AlertDescription>
            </Alert>
          )}

        {/* Current Address Display */}
        {shippingAddress && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-medium'>
                Current Saved Shipping Address
              </h3>
            </div>
            <div className='p-4 bg-muted rounded-lg'>
              <div className='space-y-2'>
                <p className='font-medium'>{shippingAddress.addressLine1}</p>
                {shippingAddress.addressLine2 && (
                  <p className='text-muted-foreground'>
                    {shippingAddress.addressLine2}
                  </p>
                )}
                <p className='text-muted-foreground'>
                  {shippingAddress.city}, {shippingAddress.stateProvinceCode}{' '}
                  {shippingAddress.postalCode}
                </p>
                <p className='text-muted-foreground'>
                  {shippingAddress.countryCode}
                </p>
              </div>
            </div>
          </div>
        )}

        {isLoadingShippingAddress && (
          <div className='flex items-center justify-center py-4'>
            <Loader2 className='h-6 w-6 animate-spin' />
            <span className='ml-2'>Loading current address...</span>
          </div>
        )}

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Update your shipping Address</h3>

          <StreetAutocomplete
            label='Street Address *'
            value={address.addressLine1 || ''}
            onChange={street => {
              setAddress({ ...address, addressLine1: street });
              setError(null); // Clear error when user changes address
            }}
            onAddressSelect={components => {
              // Parse Google Places address components
              const addressData: any = {};

              components.forEach((component: any) => {
                const types = component.types;
                if (types.includes('street_number')) {
                  addressData.streetNumber = component.long_name;
                } else if (types.includes('route')) {
                  addressData.route = component.long_name;
                } else if (types.includes('locality')) {
                  addressData.city = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                  addressData.state = component.short_name;
                } else if (types.includes('postal_code')) {
                  addressData.zip = component.long_name;
                } else if (types.includes('administrative_area_level_2')) {
                  addressData.county = component.long_name;
                }
              });

              // Update all address fields
              const newAddress = {
                ...address,
                addressLine1:
                  addressData.streetNumber && addressData.route
                    ? `${addressData.streetNumber} ${addressData.route}`
                    : addressData.route ||
                      addressData.streetNumber ||
                      address.addressLine1 ||
                      '',
                city: addressData.city || address.city || '',
                stateProvinceCode:
                  addressData.state || address.stateProvinceCode || '',
                postalCode: addressData.zip || address.postalCode || '',
              };

              setAddress(newAddress);
            }}
            placeholder='Start typing your address...'
          />

          <div className='space-y-2'>
            <Label htmlFor='address2'>Address Line 2</Label>
            <Input
              id='address2'
              value={address.addressLine2 || ''}
              onChange={e =>
                setAddress({ ...address, addressLine2: e.target.value })
              }
              placeholder='Apartment, suite, unit, building, floor, etc.'
            />
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <CityAutocomplete
              label='City *'
              value={address.city || ''}
              onChange={city => {
                setAddress({ ...address, city });
                setError(null);
              }}
              placeholder='Start typing a city...'
            />
            <div className='space-y-2'>
              <Label htmlFor='state'>State *</Label>
              <Input
                id='state'
                value={address.stateProvinceCode || ''}
                onChange={e => {
                  setAddress({
                    ...address,
                    stateProvinceCode: e.target.value.toUpperCase(),
                  });
                  setError(null);
                }}
                placeholder='CA'
                maxLength={2}
              />
            </div>
            <ZipCodeAutocomplete
              label='ZIP Code *'
              value={address.postalCode || ''}
              onChange={zip => {
                setAddress({ ...address, postalCode: zip });
                setError(null);
              }}
              onZipSelect={zipData => {
                // Auto-fill city and state when ZIP is selected
                if (zipData.city && zipData.state) {
                  setAddress({
                    ...address,
                    postalCode: zipData.zip,
                    city: zipData.city,
                    stateProvinceCode: zipData.state,
                  });
                  setError(null);
                }
              }}
              placeholder='Enter ZIP code...'
            />
          </div>
        </div>

        <Button
          onClick={handleSaveAddress}
          disabled={!isFormValid() || isSavingShippingAddress}
          className='w-full'
          size='lg'
        >
          {isSavingShippingAddress ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              {shippingAddress ? 'Updating Address...' : 'Saving Address...'}
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' />
              {shippingAddress ? 'Update Address' : 'Save Address'}
            </>
          )}
        </Button>

        {!isFormValid() && (
          <p className='text-sm text-muted-foreground text-center'>
            Please fill in all required fields to save your shipping address
          </p>
        )}
      </CardContent>
    </Card>
  );
}
