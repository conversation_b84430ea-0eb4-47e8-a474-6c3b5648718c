'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Loader2,
  Search,
  Package,
  Truck,
  CheckCircle,
  AlertTriangle,
  Clock,
  MapPin,
  RefreshCw,
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrackingStatus as TrackingStatusType } from '@/app/types/ups';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

interface TrackingStatusProps {
  initialTrackingNumber?: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
}

export default function TrackingStatus({
  initialTrackingNumber = '',
  autoRefresh = false,
  refreshInterval = 300, // 5 minutes
}: TrackingStatusProps) {
  const [trackingNumber, setTrackingNumber] = useState(initialTrackingNumber);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [trackingData, setTrackingData] = useState<TrackingStatusType | null>(
    null
  );
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && trackingData && refreshInterval > 0) {
      const interval = setInterval(() => {
        handleTrackPackage(true); // Silent refresh
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, trackingData, refreshInterval]);

  // Initial load if tracking number is provided
  useEffect(() => {
    if (initialTrackingNumber) {
      handleTrackPackage();
    }
  }, [initialTrackingNumber]);

  const handleTrackPackage = async (silent = false) => {
    if (!trackingNumber.trim()) {
      setError('Please enter a tracking number');
      return;
    }

    if (!silent) {
      setIsLoading(true);
      setError(null);
    }

    try {
      const { data: trackingResult, errors: trackingErrors } =
        await client.queries.trackUPSPackage({
          trackingNumber: trackingNumber.trim(),
        });

      if (trackingErrors) {
        console.error('❌ UPS tracking errors:', trackingErrors);
        throw new Error('Failed to track package');
      }

      // Parse the result - Lambda returns JSON string
      let parsedResult;
      try {
        const parsed = JSON.parse(String(trackingResult));

        // If it's still a string, parse again (double-encoded JSON)
        if (typeof parsed === 'string') {
          parsedResult = JSON.parse(parsed);
        } else {
          parsedResult = parsed;
        }
      } catch (error) {
        Sentry.captureException(error);
        console.error('Failed to parse tracking result:', error);
        // If it's already an object, use it directly
        parsedResult = trackingResult;
      }

      if (!parsedResult.success) {
        throw new Error(parsedResult.error || 'Failed to track package');
      }

      setTrackingData(parsedResult.data);
      setLastUpdated(new Date());
    } catch (error) {
      Sentry.captureException(error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      setError(errorMessage);
      if (!silent) {
        setTrackingData(null);
      }
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
    }
  };

  const getStatusIcon = (status: TrackingStatusType['status']) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className='h-5 w-5 text-green-600' />;
      case 'in_transit':
        return <Truck className='h-5 w-5 text-blue-600' />;
      case 'exception':
        return <AlertTriangle className='h-5 w-5 text-red-600' />;
      case 'label_created':
        return <Package className='h-5 w-5 text-orange-600' />;
      case 'pending':
        return <Clock className='h-5 w-5 text-yellow-600' />;
      default:
        return <Package className='h-5 w-5 text-gray-600' />;
    }
  };

  const getStatusColor = (status: TrackingStatusType['status']) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'in_transit':
        return 'bg-blue-100 text-blue-800';
      case 'exception':
        return 'bg-red-100 text-red-800';
      case 'label_created':
        return 'bg-orange-100 text-orange-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplayText = (status: TrackingStatusType['status']) => {
    switch (status) {
      case 'delivered':
        return 'DELIVERED';
      case 'in_transit':
        return 'IN TRANSIT';
      case 'exception':
        return 'EXCEPTION';
      case 'label_created':
        return 'LABEL CREATED';
      case 'pending':
        return 'PENDING PICKUP';
      default:
        return 'UNKNOWN';
    }
  };

  const formatDateTime = (dateTime: string) => {
    try {
      return new Date(dateTime).toLocaleString();
    } catch {
      return dateTime;
    }
  };

  return (
    <div className='space-y-6'>
      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Search className='h-5 w-5' />
            Track Your Package
          </CardTitle>
          <CardDescription>
            Enter your UPS tracking number to get the latest status
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex gap-2'>
            <div className='flex-1'>
              <Label htmlFor='tracking-number' className='sr-only'>
                Tracking Number
              </Label>
              <Input
                id='tracking-number'
                value={trackingNumber}
                onChange={e => setTrackingNumber(e.target.value)}
                placeholder='Enter tracking number (e.g., 1Z999AA1234567890)'
                onKeyDown={e => e.key === 'Enter' && handleTrackPackage()}
              />
            </div>
            <Button
              onClick={() => handleTrackPackage()}
              disabled={isLoading || !trackingNumber.trim()}
            >
              {isLoading ? (
                <Loader2 className='h-4 w-4 animate-spin' />
              ) : (
                <Search className='h-4 w-4' />
              )}
            </Button>
            {trackingData && (
              <Button
                variant='outline'
                onClick={() => handleTrackPackage()}
                disabled={isLoading}
                title='Refresh tracking information'
              >
                <RefreshCw className='h-4 w-4' />
              </Button>
            )}
          </div>

          {error && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Tracking Results */}
      {trackingData && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                {getStatusIcon(trackingData.status)}
                Tracking: {trackingData.trackingNumber}
              </div>
              <Badge className={getStatusColor(trackingData.status)}>
                {getStatusDisplayText(trackingData.status)}
              </Badge>
            </CardTitle>
            <CardDescription>
              {trackingData.statusDescription}
              {trackingData.status === 'label_created' && (
                <div className='mt-2 text-sm text-muted-foreground'>
                  📋 Your shipping label has been created, but UPS has not yet
                  received the package. The package will show as "In Transit"
                  once UPS picks it up.
                </div>
              )}
              {trackingData.status === 'pending' && (
                <div className='mt-2 text-sm text-muted-foreground'>
                  ⏳ Package is ready and waiting for UPS pickup.
                </div>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Status Summary */}
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='space-y-1'>
                <Label className='text-sm font-medium text-muted-foreground'>
                  Last Update
                </Label>
                <p className='text-sm'>
                  {formatDateTime(trackingData.lastUpdate)}
                </p>
              </div>

              {trackingData.estimatedDelivery && (
                <div className='space-y-1'>
                  <Label className='text-sm font-medium text-muted-foreground'>
                    Estimated Delivery
                  </Label>
                  <p className='text-sm'>
                    {formatDateTime(trackingData.estimatedDelivery)}
                  </p>
                </div>
              )}

              {trackingData.currentLocation && (
                <div className='space-y-1'>
                  <Label className='text-sm font-medium text-muted-foreground'>
                    Current Location
                  </Label>
                  <p className='text-sm flex items-center gap-1'>
                    <MapPin className='h-3 w-3' />
                    {trackingData.currentLocation}
                  </p>
                </div>
              )}
            </div>

            <Separator />

            {/* Activity Timeline */}
            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Tracking History</h3>

              {trackingData.activities.length > 0 ? (
                <div className='space-y-3'>
                  {trackingData.activities.map((activity, index) => (
                    <div
                      key={index}
                      className='flex gap-3 pb-3 border-b last:border-b-0'
                    >
                      <div className='flex-shrink-0 mt-1'>
                        <div className='w-2 h-2 bg-blue-600 rounded-full'></div>
                      </div>
                      <div className='flex-1 space-y-1'>
                        <div className='flex items-center justify-between'>
                          <p className='font-medium text-sm'>
                            {activity.description || activity.status}
                          </p>
                          <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                            <Clock className='h-3 w-3' />
                            {activity.date} {activity.time}
                          </div>
                        </div>
                        {activity.location && (
                          <p className='text-sm text-muted-foreground flex items-center gap-1'>
                            <MapPin className='h-3 w-3' />
                            {activity.location}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className='text-sm text-muted-foreground'>
                  No tracking activities available yet.
                </p>
              )}
            </div>

            {/* Last Updated Info */}
            {lastUpdated && (
              <div className='text-xs text-muted-foreground text-center pt-4 border-t'>
                Information last updated: {lastUpdated.toLocaleString()}
                {autoRefresh && (
                  <span className='ml-2'>
                    (Auto-refresh every {Math.floor(refreshInterval / 60)}{' '}
                    minutes)
                  </span>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
