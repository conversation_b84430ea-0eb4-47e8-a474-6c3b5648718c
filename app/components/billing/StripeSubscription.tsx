'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import {
  useSubscription,
  createCheckoutSession,
  cancelSubscription,
} from '../../hooks/useSubscription';

interface SubscriptionPlan {
  id: 'BASIC' | 'PRO';
  name: string;
  price: number;
  description: string;
  features: string[];
}

const plans: SubscriptionPlan[] = [
  {
    id: 'BASIC',
    name: 'Basic Plan',
    price: 10,
    description: 'Essential features for getting started',
    features: [
      'Basic document storage',
      'Email support',
      'Standard templates',
      'Basic security features',
    ],
  },
  {
    id: 'PRO',
    name: 'Pro Plan',
    price: 20,
    description: 'Advanced features for power users',
    features: [
      'Unlimited document storage',
      'Priority support',
      'Advanced templates',
      'Enhanced security features',
      'Custom integrations',
      'Advanced analytics',
    ],
  },
];

export default function StripeSubscription() {
  const { user, userId } = useAuth();
  const {
    subscription,
    loading: subscriptionLoading,
    refetch,
  } = useSubscription();
  const [loading, setLoading] = useState<string | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const handleSubscribe = async (planId: 'BASIC' | 'PRO') => {
    if (!user?.userId || !user?.signInDetails?.loginId || !userId) {
      console.error('User not authenticated');
      return;
    }

    setLoading(planId);

    try {
      const data = await createCheckoutSession(
        planId,
        user.signInDetails.loginId,
        userId, // Database user ID
        user.userId // Cognito ID
      );

      if (data.url) {
        window.location.href = data.url;
      } else {
        console.error('Error creating checkout session:', data.error);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription?.stripeSubscriptionId || !user?.userId || !userId) return;

    setLoading('cancel');
    setAlert(null);

    try {
      const data = await cancelSubscription(
        subscription.stripeSubscriptionId,
        userId,
        user.userId
      );

      if (data.success) {
        setAlert({
          type: 'success',
          message:
            'Subscription will be canceled at the end of the current billing period.',
        });
        // Refresh subscription data
        await refetch();
        setShowCancelConfirm(false);
      } else {
        setAlert({
          type: 'error',
          message: 'Failed to cancel subscription. Please try again.',
        });
      }
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'An error occurred while canceling subscription.',
      });
    } finally {
      setLoading(null);
    }
  };

  // Auto-hide alerts after 5 seconds
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => setAlert(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [alert]);

  if (subscriptionLoading) {
    return (
      <div className='space-y-6'>
        <div className='text-center'>
          <div className='animate-pulse'>
            <div className='h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2'></div>
            <div className='h-4 bg-gray-200 rounded w-1/3 mx-auto'></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold'>
          {subscription ? 'Available Plans' : 'Choose Your Plan'}
        </h2>
        <p className='text-muted-foreground mt-2'>
          {subscription
            ? 'You can change or upgrade your plan at any time'
            : 'Select the plan that best fits your needs'}
        </p>
      </div>

      {alert && (
        <Alert
          className={
            alert.type === 'success'
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          }
        >
          {alert.type === 'success' ? (
            <CheckCircle2 className='h-4 w-4 text-green-600' />
          ) : (
            <AlertTriangle className='h-4 w-4 text-red-600' />
          )}
          <AlertDescription
            className={
              alert.type === 'success' ? 'text-green-800' : 'text-red-800'
            }
          >
            {alert.message}
          </AlertDescription>
        </Alert>
      )}

      {subscription && (
        <Card className='mb-6 border-green-200 bg-green-50/50'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CheckCircle2 className='h-5 w-5 text-green-600' />
                <CardTitle className='text-green-800'>
                  Active Subscription
                </CardTitle>
              </div>
              <Badge
                variant={
                  subscription.status === 'ACTIVE' ? 'default' : 'secondary'
                }
                className={
                  subscription.status === 'ACTIVE'
                    ? 'bg-green-600 hover:bg-green-700'
                    : ''
                }
              >
                {subscription.status === 'ACTIVE'
                  ? 'Active'
                  : subscription.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className='grid md:grid-cols-2 gap-6'>
              {/* Subscription Details */}
              <div className='space-y-4'>
                <div>
                  <h4 className='font-semibold text-lg text-green-800'>
                    {subscription.plan === 'BASIC' ? 'Basic' : 'Pro'} Plan
                  </h4>
                  <p className='text-2xl font-bold text-green-700'>
                    ${subscription.amount / 100}
                    <span className='text-sm font-normal text-muted-foreground'>
                      /month
                    </span>
                  </p>
                </div>

                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>Period Start:</span>
                    <span className='font-medium'>
                      {new Date(
                        subscription.currentPeriodStart
                      ).toLocaleDateString('en-US')}
                    </span>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>Next Billing:</span>
                    <span className='font-medium'>
                      {new Date(
                        subscription.currentPeriodEnd
                      ).toLocaleDateString('en-US')}
                    </span>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>Currency:</span>
                    <span className='font-medium uppercase'>
                      {subscription.currency}
                    </span>
                  </div>
                  {subscription.cancelAtPeriodEnd && (
                    <div className='flex justify-between text-sm'>
                      <span className='text-muted-foreground'>
                        Cancellation:
                      </span>
                      <span className='font-medium text-orange-600'>
                        At period end
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Plan Features */}
              <div className='space-y-4'>
                <h4 className='font-semibold text-green-800'>Plan Features</h4>
                <ul className='space-y-2 text-sm'>
                  {subscription.plan === 'BASIC' ? (
                    <>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Basic legacy planning features
                      </li>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Access to educational materials
                      </li>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Basic support
                      </li>
                    </>
                  ) : (
                    <>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        All Basic plan features
                      </li>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Advanced planning tools
                      </li>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Priority support
                      </li>
                      <li className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-green-600' />
                        Personal consultations
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>

            {/* Action Buttons */}
            <div className='mt-6 pt-4 border-t border-green-200'>
              <div className='flex flex-col sm:flex-row gap-3 justify-between'>
                <div className='flex gap-2'>
                  {subscription.plan === 'BASIC' && (
                    <Button
                      onClick={() => handleSubscribe('PRO')}
                      disabled={loading === 'PRO'}
                      className='bg-green-600 hover:bg-green-700'
                    >
                      {loading === 'PRO' ? 'Upgrading...' : 'Upgrade to Pro'}
                    </Button>
                  )}
                </div>

                <div className='flex gap-2'>
                  {!showCancelConfirm ? (
                    <Button
                      variant='outline'
                      onClick={() => setShowCancelConfirm(true)}
                      disabled={loading === 'cancel'}
                      className='border-red-200 text-red-600 hover:bg-red-50'
                    >
                      Cancel Subscription
                    </Button>
                  ) : (
                    <div className='flex gap-2'>
                      <Button
                        variant='destructive'
                        onClick={handleCancelSubscription}
                        disabled={loading === 'cancel'}
                        size='sm'
                      >
                        {loading === 'cancel'
                          ? 'Canceling...'
                          : 'Confirm Cancellation'}
                      </Button>
                      <Button
                        variant='outline'
                        onClick={() => setShowCancelConfirm(false)}
                        disabled={loading === 'cancel'}
                        size='sm'
                      >
                        Keep Subscription
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className='grid md:grid-cols-2 gap-6 items-stretch'>
        {plans.map(plan => (
          <Card key={plan.id} className='relative h-full flex flex-col'>
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className='text-3xl font-bold'>
                ${plan.price}
                <span className='text-sm font-normal text-muted-foreground'>
                  /month
                </span>
              </div>
            </CardHeader>
            <CardContent className='flex flex-col flex-1'>
              <ul className='space-y-2 mb-6'>
                {plan.features.map((feature, index) => (
                  <li key={index} className='flex items-center'>
                    <span className='text-green-500 mr-2'>✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <div className='mt-auto'>
                <Button
                  className='w-full'
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={
                    loading === plan.id ||
                    Boolean(subscription && subscription.plan === plan.id)
                  }
                  variant={
                    subscription && subscription.plan === plan.id
                      ? 'secondary'
                      : 'default'
                  }
                >
                  {subscription && subscription.plan === plan.id
                    ? 'Current Plan'
                    : loading === plan.id
                      ? 'Processing...'
                      : `Subscribe to ${plan.name}`}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
