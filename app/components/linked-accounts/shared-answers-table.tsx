'use client';

import { useQuery } from '@tanstack/react-query';
import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import { Schema } from '@/amplify/data/resource';
import {
  FileText,
  Search,
  Filter,
  Save,
  CheckSquare,
  Plus,
  Trash2,
  User,
  Check,
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAuth } from '@/context/AuthContext';
import { SectionEnum } from '@/hooks/useLinkedAccountPrefill';
import { toast } from 'sonner';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import { useLinkedAccounts } from '@/hooks/useLinkedAccounts';

interface SharedAnswersTableProps {
  onSelectedItemsChange?: (items: any[]) => void;
  showAcceptButton?: boolean;
  compact?: boolean;
  sharedFields?: string;
  sectionsToExclude?: SectionEnum[];
  isEditable?: boolean;
  linkedAccountId?: string;
  onSave?: () => void;
}

const SharedAnswersTable = ({
  onSelectedItemsChange,
  showAcceptButton = true,
  compact = false,
  sharedFields,
  sectionsToExclude = [],
  isEditable = false,
  linkedAccountId,
  onSave,
}: SharedAnswersTableProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'interview' | 'care' | 'people'>(
    'all'
  );
  const [selectedItems, setSelectedItems] = useState<Record<string, any>>({});
  const [selectAll, setSelectAll] = useState(false);
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);
  const [isSelectOpen, setIsSelectOpen] = useState(false);

  const [isSaving, setIsSaving] = useState(false);
  const [editableSharedFields, setEditableSharedFields] = useState<any[]>([]);
  // For add table
  const [selectedItemsToAdd, setSelectedItemsToAdd] = useState<
    Record<string, any>
  >({});
  const [selectAllToAdd, setSelectAllToAdd] = useState(false);
  const [searchQueryToAdd, setSearchQueryToAdd] = useState('');
  const [filterToAdd, setFilterToAdd] = useState<
    'all' | 'interview' | 'care' | 'people'
  >('all');

  const client = generateClient<Schema>();
  const { userExternalId, userPersonalInfo } = useAuth();
  const { people } = usePeopleLibrary();
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const rowRefs = useRef<(HTMLTableRowElement | null)[]>([]);
  const { acceptLinkedAccount } = useLinkedAccounts();

  // Auto-focus on component mount
  useEffect(() => {
    if (tableContainerRef.current) {
      tableContainerRef.current.focus();
    }
  }, []);

  // Parse sharedFields if provided
  const parsedSharedFields = useMemo(() => {
    if (!sharedFields) return [];
    try {
      return typeof sharedFields === 'string'
        ? JSON.parse(sharedFields)
        : sharedFields;
    } catch (error) {
      console.error('Error parsing sharedFields:', error);
      return [];
    }
  }, [sharedFields]);

  // Only fetch data if sharedFields is not provided or isEditable is false
  const shouldFetchData = isEditable ? true : !sharedFields;
  const shouldShowCheckboxes = isEditable || showAcceptButton || !sharedFields;

  // Fetch interview data from interviewProgressV2
  const {
    data: interviewData,
    isLoading: isLoadingInterviewData,
    error: interviewDataError,
  } = useQuery({
    queryKey: ['user-interview-progress-v2'],
    queryFn: async () => {
      if (!shouldFetchData) return null;

      try {
        // Get current user
        // const user = await getCurrentUser();
        if (!userExternalId) {
          console.error('Error fetching user data');
          return null;
        }

        // Fetch user data including interviewProgressV2
        const { data: userList, errors } = await client.models.User.list({
          filter: { cognitoId: { eq: userExternalId } },
          selectionSet: ['id', 'cognitoId', 'interviewProgressV2.*'],
        });

        if (errors || !userList || userList.length === 0) {
          console.error('Error fetching user data:', errors);
          return null;
        }

        const userData = userList[0];
        const interviewData = userData.interviewProgressV2;

        if (!interviewData || !interviewData.stepsData) {
          console.log('No interview data available');
          return null;
        }

        // Parse stepsData if it's a string
        let parsedStepsData;
        try {
          parsedStepsData =
            typeof interviewData.stepsData === 'string'
              ? JSON.parse(interviewData.stepsData)
              : interviewData.stepsData;
        } catch (error) {
          console.error('Error parsing stepsData:', error);
          return null;
        }

        return {
          ...interviewData,
          stepsData: parsedStepsData,
        };
      } catch (error) {
        console.error('Error fetching interview data:', error);
        return null;
      }
    },
    enabled: shouldFetchData, // Only run query if sharedFields is not provided
  });

  // Fetch care documents from CareDocumentNew
  const {
    data: careDocumentsNew,
    isLoading: isLoadingCareDocuments,
    error: careDocumentsError,
  } = useQuery({
    queryKey: ['care-documents-new'],
    queryFn: async () => {
      if (!shouldFetchData) return [];

      try {
        // const user = await getCurrentUser();

        if (!userPersonalInfo) {
          return [];
        }

        // Fetch care documents for the current user
        const { data, errors } = await client.models.CareDocumentNew.list({
          filter: {
            userId: { eq: userPersonalInfo.id },
          },
        });

        if (errors) {
          throw new Error(errors.map(e => e.message).join(', '));
        }

        return data;
      } catch (error) {
        console.error('Error fetching care documents:', error);
        return [];
      }
    },
    enabled: shouldFetchData, // Only run query if sharedFields is not provided
  });

  // Helper function to group person-related fields
  const personFieldsCheck = (answers: any[]) => {
    const formattedAnswers: any[] = [];
    const regularAnswers: any[] = [];

    answers.forEach(answer => {
      // Check if this is a person-related field
      const personMatch = answer.id.match(
        /^(.+?)\.(?:firstName|lastName|middleName|phoneNumber|address|dateOfBirth|relationshipType|type|id)$/
      );

      if (personMatch) {
        console.log('Removing person data...');
      } else {
        regularAnswers.push(answer);
      }
    });

    // Add regular answers first
    formattedAnswers.push(...regularAnswers);

    return formattedAnswers;
  };

  // Process interview data into a unified format with person grouping
  const processInterviewData = (interviewData: any) => {
    if (!interviewData || !interviewData.stepsData) return [];

    const answers: any[] = [];
    const { stepsData } = interviewData;

    // Helper function to recursively extract answers from nested objects
    const extractAnswers = (obj: any, path: string = '') => {
      if (!obj || typeof obj !== 'object') return;

      // Check if current path starts with any excluded section
      const pathSegments = path.split('.');
      const rootSection = pathSegments[0];
      if (
        rootSection &&
        sectionsToExclude.includes(rootSection as SectionEnum)
      ) {
        return; // Skip this section
      }

      Object.entries(obj).forEach(([key, value]) => {
        const currentPath = path ? `${path}.${key}` : key;

        // Skip if this is a root-level excluded section
        if (path === '' && sectionsToExclude.includes(key as SectionEnum)) {
          return;
        }

        if (
          value !== null &&
          typeof value === 'object' &&
          !Array.isArray(value)
        ) {
          // Recursively process nested objects
          extractAnswers(value, currentPath);
        } else if (Array.isArray(value)) {
          // Handle array values - could be arrays of objects or simple values
          if (value.length > 0 && typeof value[0] === 'object') {
            // Array of objects - process each object
            value.forEach((item, index) => {
              extractAnswers(item, `${currentPath}[${index}]`);
            });
          } else {
            // Array of simple values - add as a single answer
            answers.push({
              id: currentPath,
              question: formatQuestionFromPath(currentPath),
              answer: value.join(', '),
              documentType: 'Interview',
              documentTitle: 'Interview Answers',
              answeredAt:
                interviewData.updatedAt ||
                interviewData.createdAt ||
                new Date().toISOString(),
              source: 'interview',
            });
          }
        } else if (value !== undefined && value !== null && value !== '') {
          // Add simple values as answers
          answers.push({
            id: currentPath,
            question: formatQuestionFromPath(currentPath),
            answer: String(value),
            documentType: 'Interview',
            documentTitle: 'Interview Answers',
            answeredAt:
              interviewData.updatedAt ||
              interviewData.createdAt ||
              new Date().toISOString(),
            source: 'interview',
          });
        }
      });
    };

    // Format the path into a readable question
    const formatQuestionFromPath = (path: string) => {
      return path
        .split('.')
        .map(part => {
          // Handle array indices
          const cleanPart = part.replace(/\[\d+\]/, '');
          // Convert camelCase to Title Case with spaces
          return cleanPart
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase());
        })
        .join(' > ');
    };

    // Start extraction from the root object
    extractAnswers(stepsData);

    // Group person-related fields
    return personFieldsCheck(answers);
  };

  // Process care document data into a unified format
  const processCareDocuments = (documents: any[]) => {
    if (!documents || documents.length === 0) return [];

    const answers: any[] = [];

    documents.forEach(doc => {
      if (!doc.data || !Array.isArray(doc.data)) return;

      doc.data.forEach((section: any) => {
        if (!section || !section.content) return;

        // Skip excluded sections
        if (sectionsToExclude.includes(section.sectionType)) {
          return;
        }

        let sectionData;
        try {
          sectionData =
            typeof section.content === 'string'
              ? JSON.parse(section.content)
              : section.content;
        } catch (error) {
          console.error(
            `Error parsing section content for ${section.sectionType}:`,
            error
          );
          return;
        }

        // Process section data based on section type
        const extractSectionAnswers = (data: any, sectionType: string) => {
          if (!data || typeof data !== 'object') return;

          // Helper function to recursively extract answers from nested objects
          const extractFromObject = (obj: any, path: string = '') => {
            if (!obj || typeof obj !== 'object') return;

            Object.entries(obj).forEach(([key, value]) => {
              const currentPath = path ? `${path}.${key}` : key;

              // Skip empty arrays
              if (Array.isArray(value) && value.length === 0) return;

              if (
                value !== null &&
                typeof value === 'object' &&
                !Array.isArray(value)
              ) {
                // Recursively process nested objects
                extractFromObject(value, currentPath);
              } else if (Array.isArray(value)) {
                // Handle array values
                if (value.length > 0 && typeof value[0] === 'object') {
                  // Array of objects
                  value.forEach((item, index) => {
                    extractFromObject(item, `${currentPath}[${index}]`);
                  });
                } else {
                  // Array of simple values
                  answers.push({
                    id: `${sectionType}.${currentPath}`,
                    question: formatSectionPath(sectionType, currentPath),
                    answer: value.join(', '),
                    documentType: 'Care Document',
                    documentTitle: sectionType,
                    answeredAt:
                      doc.updatedAt ||
                      doc.createdAt ||
                      new Date().toISOString(),
                    source: 'care',
                  });
                }
              } else if (
                value !== undefined &&
                value !== null &&
                value !== ''
              ) {
                // Add simple values as answers
                answers.push({
                  id: `${sectionType}.${currentPath}`,
                  question: formatSectionPath(sectionType, currentPath),
                  answer: String(value),
                  documentType: 'Care Document',
                  documentTitle: sectionType,
                  answeredAt:
                    doc.updatedAt || doc.createdAt || new Date().toISOString(),
                  source: 'care',
                });
              }
            });
          };

          extractFromObject(data);
        };

        // Format section path into readable question
        const formatSectionPath = (sectionType: string, path: string) => {
          const formattedSection = sectionType
            .replace(/([A-Z])/g, ' $1')
            .trim();

          const formattedPath = path
            .split('.')
            .map(part => {
              // Handle array indices
              const cleanPart = part.replace(/\[\d+\]/, '');
              // Convert camelCase to Title Case with spaces
              return cleanPart
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, str => str.toUpperCase());
            })
            .join(' > ');

          return `${formattedSection} > ${formattedPath}`;
        };

        extractSectionAnswers(sectionData, section.sectionType);
      });
    });

    return answers;
  };

  const processPeopleData = (people: any[]) => {
    if (!people || people.length === 0) return [];

    const answers: any[] = [];

    people.forEach(person => {
      if (!person || typeof person !== 'object') return;

      answers.push({
        id: person.id,
        question: `People Library > ${person.type.charAt(0).toUpperCase() + person.type.slice(1)}`,
        answer: JSON.stringify(person),
        documentType: 'People Library',
        documentTitle:
          person.type.charAt(0).toUpperCase() + person.type.slice(1),
        answeredAt: new Date().toISOString(),
        source: 'people',
        isPerson: true,
        personData: person,
        personDataForTable: `${person.firstName} ${person.lastName}`,
      });
    });

    return answers;
  };

  // Process all answers into a unified format
  const allAnswers = useMemo(() => {
    // If in edit mode, use editable shared fields
    if (isEditable) {
      return personFieldsCheck(editableSharedFields);
    }

    // If sharedFields is provided and not in edit mode, use that
    if (parsedSharedFields.length > 0) {
      return personFieldsCheck(parsedSharedFields);
    }

    // Otherwise use fetched data
    const interviewAnswers = interviewData
      ? processInterviewData(interviewData)
      : [];
    const careAnswers = careDocumentsNew
      ? processCareDocuments(careDocumentsNew)
      : [];
    const peopleAnswers = people ? processPeopleData(people) : [];

    return [...interviewAnswers, ...careAnswers, ...peopleAnswers];
  }, [
    interviewData,
    careDocumentsNew,
    parsedSharedFields,
    isEditable,
    editableSharedFields,
    people,
  ]);

  // Filter and search answers
  const filteredAnswers = useMemo(() => {
    return allAnswers.filter((answer: any) => {
      // Apply source filter
      if (filter !== 'all' && answer.source !== filter) return false;

      // Apply search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          answer.question.toLowerCase().includes(query) ||
          answer.answer.toLowerCase().includes(query) ||
          answer.documentTitle.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [allAnswers, filter, searchQuery]);

  // Initialize editable shared fields
  useEffect(() => {
    if (isEditable && parsedSharedFields.length > 0) {
      setEditableSharedFields([...parsedSharedFields]);
    }
  }, [isEditable, parsedSharedFields]);

  // Get available fields that can be added (not already in shared fields)
  const availableFieldsToAdd = useMemo(() => {
    if (!isEditable) return [];

    const interviewAnswers = interviewData
      ? processInterviewData(interviewData)
      : [];
    const careAnswers = careDocumentsNew
      ? processCareDocuments(careDocumentsNew)
      : [];
    const peopleAnswers = people ? processPeopleData(people) : [];
    const allAvailableAnswers = [
      ...interviewAnswers,
      ...careAnswers,
      ...peopleAnswers,
    ];

    const currentFieldIds = editableSharedFields.map(field => field.id);
    return allAvailableAnswers.filter(
      answer => !currentFieldIds.includes(answer.id)
    );
  }, [isEditable, editableSharedFields, interviewData, careDocumentsNew]);

  // Filter available fields to add
  const filteredAvailableFields = useMemo(() => {
    return availableFieldsToAdd.filter((answer: any) => {
      // Apply source filter
      if (filterToAdd !== 'all' && answer.source !== filterToAdd) return false;

      // Apply search query
      if (searchQueryToAdd) {
        const query = searchQueryToAdd.toLowerCase();
        return (
          answer.question.toLowerCase().includes(query) ||
          answer.answer.toLowerCase().includes(query) ||
          answer.documentTitle.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [availableFieldsToAdd, filterToAdd, searchQueryToAdd]);

  // Handle item selection for add table
  const toggleItemSelectionToAdd = (item: any) => {
    const itemKey = `${item.id}-${item.documentTitle}`;
    setSelectedItemsToAdd(prev => {
      const newSelected = { ...prev };
      if (newSelected[itemKey]) {
        delete newSelected[itemKey];
      } else {
        newSelected[itemKey] = item;
      }
      return newSelected;
    });
  };

  // Handle select all for add table
  const toggleSelectAllToAdd = () => {
    if (selectAllToAdd) {
      // Deselect all
      setSelectedItemsToAdd({});
    } else {
      // Select all filtered items
      const newSelected: Record<string, any> = {};
      filteredAvailableFields.forEach((item: any) => {
        const itemKey = `${item.id}-${item.documentTitle}`;
        newSelected[itemKey] = item;
      });
      setSelectedItemsToAdd(newSelected);
    }
    setSelectAllToAdd(!selectAllToAdd);
  };

  // Add selected fields to main table
  const addSelectedFields = () => {
    const fieldsToAdd = Object.values(selectedItemsToAdd);
    setEditableSharedFields(prev => [...prev, ...fieldsToAdd]);
    setSelectedItemsToAdd({});
    setSelectAllToAdd(false);
  };

  // Remove selected fields from main table
  const removeSelectedFields = () => {
    const fieldIdsToRemove = Object.values(selectedItems).map(
      (item: any) => item.id
    );
    setEditableSharedFields(prev =>
      prev.filter(field => !fieldIdsToRemove.includes(field.id))
    );
    setSelectedItems({});
    setSelectAll(false);
  };

  // Save updated shared fields
  const saveSharedFields = async () => {
    if (!linkedAccountId || !isEditable) return;

    setIsSaving(true);
    try {
      const sharedFieldsJson = JSON.stringify(editableSharedFields);

      await client.models.LinkedAccountWithSharedFields.update({
        id: linkedAccountId,
        sharedFields: sharedFieldsJson,
      });

      if (onSave) {
        onSave();
      }

      toast.success('Shared fields saved successfully');
    } catch (error) {
      console.error('Error saving shared fields:', error);
      toast.error('Failed to save shared fields. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const acceptSelectedFields = async () => {
    if (!linkedAccountId) return;

    setIsSaving(true);
    try {
      const selectedData = Object.values(selectedItems);
      const sharedFieldsJson = JSON.stringify(selectedData);
      await acceptLinkedAccount(linkedAccountId, sharedFieldsJson);

      if (onSave) {
        onSave();
      }

      toast.success('Linked account accepted successfully');
    } catch (error) {
      console.error('Error accepting linked account:', error);
      toast.error('Failed to accept linked account');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle item selection
  const toggleItemSelection = (item: any) => {
    const itemKey = `${item.id}-${item.documentTitle}`;
    setSelectedItems(prev => {
      const newSelected = { ...prev };
      if (newSelected[itemKey]) {
        delete newSelected[itemKey];
      } else {
        newSelected[itemKey] = item;
      }
      return newSelected;
    });
  };

  // Handle select all
  const toggleSelectAll = () => {
    if (selectAll) {
      // Deselect all
      setSelectedItems({});
    } else {
      // Select all filtered items
      const newSelected: Record<string, any> = {};
      filteredAnswers.forEach((item: any) => {
        const itemKey = `${item.id}-${item.documentTitle}`;
        newSelected[itemKey] = item;
      });
      setSelectedItems(newSelected);
    }
    setSelectAll(!selectAll);
  };

  // Export selected items as JSON
  const exportSelectedItems = () => {
    const selectedData = Object.values(selectedItems);
    console.log(JSON.stringify(selectedData, null, 2));
    alert(
      `Exported ${selectedData.length} items to console. Check browser console.`
    );
  };

  // Notify parent component when selected items change
  useEffect(() => {
    if (onSelectedItemsChange) {
      const selectedValues = Object.values(selectedItems);
      onSelectedItemsChange(selectedValues);
    }
  }, [selectedItems, onSelectedItemsChange]);

  // Loading state - only show if we're fetching data and no sharedFields provided
  if (
    (isLoadingInterviewData || isLoadingCareDocuments) &&
    !parsedSharedFields.length
  ) {
    return (
      <div className='flex justify-center items-center py-8'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
      </div>
    );
  }

  // Error state - only show if we're fetching data and no sharedFields provided
  if (
    (interviewDataError || careDocumentsError) &&
    !parsedSharedFields.length
  ) {
    return (
      <div className='bg-destructive/10 p-4 rounded-md text-destructive'>
        <p>Error loading answers. Please try again later.</p>
      </div>
    );
  }

  const selectedCount = Object.keys(selectedItems).length;
  const selectedCountToAdd = Object.keys(selectedItemsToAdd).length;

  return (
    <div className={`space-y-4 ${compact ? 'text-xs' : 'text-sm'}`}>
      {/* Edit mode header */}
      {isEditable && (
        <div className='bg-blue-50 p-4 rounded-md border border-blue-200'>
          <div className='flex items-center justify-between mb-2'>
            <h4 className='font-medium text-blue-900'>Edit Shared Fields</h4>
            <Button
              onClick={saveSharedFields}
              disabled={isSaving}
              size='sm'
              className='bg-blue-600 hover:bg-blue-700'
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
          <p className='text-sm text-blue-700'>
            Use the tables below to add or remove fields that will be shared
            with this linked account.
          </p>
        </div>
      )}

      {/* Add fields section for edit mode */}
      {isEditable && (
        <div className='space-y-2'>
          <h4 className='font-medium text-green-900'>
            Available Fields to Add
          </h4>

          {/* Search and filter controls for add table */}
          <div className='flex flex-col sm:flex-row gap-2 justify-between'>
            <div className='flex flex-col sm:flex-row gap-2'>
              <div className='relative w-full sm:w-48'>
                <Search className='absolute left-2 top-2 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search fields...'
                  className='pl-7 h-8 text-xs'
                  value={searchQueryToAdd}
                  onChange={e => setSearchQueryToAdd(e.target.value)}
                />
              </div>

              <div className='flex items-center gap-1'>
                <Filter className='h-4 w-4 text-muted-foreground' />
                <Select
                  value={filterToAdd}
                  onValueChange={value => setFilterToAdd(value as any)}
                >
                  <SelectTrigger className='w-[120px] h-8 text-xs'>
                    <SelectValue placeholder='Filter by source' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All Sources</SelectItem>
                    <SelectItem value='interview'>Interview Only</SelectItem>
                    <SelectItem value='care'>Care Documents Only</SelectItem>
                    <SelectItem value='people'>People Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              variant='default'
              size='sm'
              onClick={addSelectedFields}
              disabled={selectedCountToAdd === 0}
              className='flex items-center gap-1 h-8 text-xs bg-green-600 hover:bg-green-700'
            >
              <Plus className='h-3 w-3' />
              Add Selected ({selectedCountToAdd})
            </Button>
          </div>

          {/* Available fields table */}
          {filteredAvailableFields.length > 0 ? (
            <div className='border rounded-md overflow-hidden bg-green-50'>
              <div
                className={`${isEditable ? 'max-h-[150px]' : 'max-h-[200px]'} overflow-y-auto`}
              >
                <Table>
                  <TableHeader className='sticky top-0 bg-green-100 z-10'>
                    <TableRow>
                      <TableHead className='w-[30px] p-2'>
                        <Checkbox
                          checked={selectAllToAdd}
                          onCheckedChange={toggleSelectAllToAdd}
                          aria-label='Select all available'
                        />
                      </TableHead>
                      <TableHead className='p-2'>Question</TableHead>
                      <TableHead className='p-2'>Answer</TableHead>
                      <TableHead className='p-2 hidden sm:table-cell'>
                        Type
                      </TableHead>
                      <TableHead className='p-2 hidden sm:table-cell'>
                        Document
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAvailableFields.map((answer: any) => {
                      const itemKey = `${answer.id}-${answer.documentTitle}`;
                      const isSelected = !!selectedItemsToAdd[itemKey];

                      return (
                        <TableRow
                          key={itemKey}
                          className={
                            isSelected ? 'bg-green-100' : 'bg-green-25'
                          }
                        >
                          <TableCell className='p-2'>
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() =>
                                toggleItemSelectionToAdd(answer)
                              }
                              aria-label={`Select ${answer.question}`}
                            />
                          </TableCell>
                          <TableCell className='font-medium p-2 truncate max-w-[150px]'>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className='truncate block'>
                                    {answer.isPerson && (
                                      <span className='inline-flex items-center gap-1'>
                                        <User className='h-3 w-3 text-blue-600' />
                                        {answer.question}
                                      </span>
                                    )}
                                    {!answer.isPerson && answer.question}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                                  {answer.question}
                                  {answer.isPerson && (
                                    <div className='mt-1 text-xs text-muted-foreground'>
                                      👤 Person data (grouped)
                                    </div>
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                          <TableCell className='p-2 truncate max-w-[150px]'>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className='truncate block'>
                                    {answer.isPerson &&
                                    answer.personDataForTable
                                      ? answer.personDataForTable
                                      : answer.answer}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                                  {answer.isPerson && answer.personData ? (
                                    <div className='space-y-1'>
                                      <div>
                                        <strong>Name:</strong>{' '}
                                        {answer.personData.firstName}{' '}
                                        {answer.personData.lastName}
                                      </div>
                                      {answer.personData.relationshipType && (
                                        <div>
                                          <strong>Relationship:</strong>{' '}
                                          {answer.personData.relationshipType}
                                        </div>
                                      )}
                                      {answer.personData.phoneNumber && (
                                        <div>
                                          <strong>Phone:</strong>{' '}
                                          {answer.personData.phoneNumber}
                                        </div>
                                      )}
                                      {answer.personData.address && (
                                        <div>
                                          <strong>Address:</strong>{' '}
                                          {answer.personData.address}
                                        </div>
                                      )}
                                      {answer.personData.dateOfBirth && (
                                        <div>
                                          <strong>DOB:</strong>{' '}
                                          {new Date(
                                            answer.personData.dateOfBirth
                                          ).toLocaleDateString()}
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    answer.answer
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                          {/* <TableCell className='font-medium p-2 truncate max-w-[150px]'>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className='truncate block'>
                                    {answer.question}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                                  {answer.question}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                          <TableCell className='p-2 truncate max-w-[150px]'>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className='truncate block'>
                                    {answer.answer}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                                  {answer.answer}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell> */}
                          <TableCell className='p-2 hidden sm:table-cell'>
                            {answer.documentType}
                          </TableCell>
                          <TableCell className='p-2 hidden sm:table-cell'>
                            {answer.documentTitle}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            <div className='text-center py-4 text-muted-foreground border rounded-md bg-green-50'>
              <FileText className='h-8 w-8 mx-auto mb-1 opacity-20' />
              <p className='text-xs'>No available fields to add</p>
            </div>
          )}
        </div>
      )}

      {/* Current shared fields section */}
      {isEditable && (
        <h4 className='font-medium text-gray-900'>Current Shared Fields</h4>
      )}

      {/* Search, filter, and action controls for main table */}
      <div className='flex flex-col sm:flex-row gap-2 justify-between'>
        <div className='flex flex-col sm:flex-row gap-2'>
          <div className='relative w-full sm:w-48'>
            <Search className='absolute left-2 top-2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search fields...'
              className='pl-7 h-8 text-xs'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
          </div>

          <div className='flex items-center gap-1'>
            <Filter className='h-4 w-4 text-muted-foreground' />
            <Select
              value={filter}
              onValueChange={value => setFilter(value as any)}
              onOpenChange={setIsSelectOpen}
            >
              <SelectTrigger className='w-[120px] h-8 text-xs'>
                <SelectValue placeholder='Filter by source' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Sources</SelectItem>
                <SelectItem value='interview'>Interview Only</SelectItem>
                <SelectItem value='care'>Care Documents Only</SelectItem>
                <SelectItem value='people'>People Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isEditable ? (
          <Button
            variant='destructive'
            size='sm'
            onClick={removeSelectedFields}
            disabled={selectedCount === 0}
            className='flex items-center gap-1 h-8 text-xs'
          >
            <Trash2 className='h-3 w-3' />
            Remove Selected ({selectedCount})
          </Button>
        ) : showAcceptButton ? (
          <Button
            variant='default'
            size='sm'
            onClick={acceptSelectedFields}
            disabled={selectedCount === 0 || isSaving}
            className='flex items-center gap-1 h-8 text-xs'
          >
            <Check className='h-3 w-3' />
            {isSaving
              ? 'Accepting...'
              : `Accept ${selectedCount > 0 ? `(${selectedCount})` : 'Selected Fields'}`}
          </Button>
        ) : null}
      </div>

      {/* Main table of answers */}
      {filteredAnswers.length > 0 ? (
        <div className='border rounded-md overflow-hidden'>
          <div
            className={`${isEditable ? 'max-h-[150px]' : 'max-h-[200px]'} overflow-y-auto`}
          >
            <Table>
              <TableHeader className='sticky top-0 bg-background z-10'>
                <TableRow>
                  {shouldShowCheckboxes && (
                    <TableHead className='w-[30px] p-2'>
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={toggleSelectAll}
                        aria-label='Select all'
                      />
                    </TableHead>
                  )}
                  <TableHead className='p-2'>Question</TableHead>
                  <TableHead className='p-2'>Answer</TableHead>
                  <TableHead className='p-2 hidden sm:table-cell'>
                    Type
                  </TableHead>
                  <TableHead className='p-2 hidden sm:table-cell'>
                    Document
                  </TableHead>
                  <TableHead className='p-2 text-right hidden sm:table-cell'>
                    Date
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAnswers.map((answer: any, index: number) => {
                  const itemKey = `${answer.id}-${answer.documentTitle}`;
                  const isSelected = !!selectedItems[itemKey];
                  const isFocused = focusedRowIndex === index;

                  return (
                    <TableRow
                      key={itemKey}
                      ref={el => {
                        rowRefs.current[index] = el;
                      }}
                      className={`
                        ${isSelected ? 'bg-[var(--custom-gray-light)]/10' : ''}
                        ${isFocused ? 'bg-[var(--custom-gray-light)]/20' : ''}
                        cursor-pointer transition-colors hover:bg-[var(--custom-gray-light)]/10
                      `}
                      onClick={() => toggleItemSelection(answer)}
                      tabIndex={-1}
                    >
                      {shouldShowCheckboxes && (
                        <TableCell className='p-2'>
                          <div onClick={e => e.stopPropagation()}>
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() =>
                                toggleItemSelection(answer)
                              }
                              aria-label={`Select ${answer.question}`}
                              tabIndex={-1}
                            />
                          </div>
                        </TableCell>
                      )}
                      <TableCell className='font-medium p-2 truncate max-w-[150px]'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className='truncate block'>
                                {answer.isPerson && (
                                  <span className='inline-flex items-center gap-1'>
                                    <User className='h-3 w-3 text-blue-600' />
                                    {answer.question}
                                  </span>
                                )}
                                {!answer.isPerson && answer.question}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                              {answer.question}
                              {answer.isPerson && (
                                <div className='mt-1 text-xs text-muted-foreground'>
                                  👤 Person data (grouped)
                                </div>
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell className='p-2 truncate max-w-[150px]'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className='truncate block'>
                                {answer.isPerson && answer.personDataForTable
                                  ? answer.personDataForTable
                                  : answer.answer}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                              {answer.isPerson && answer.personData ? (
                                <div className='space-y-1'>
                                  <div>
                                    <strong>Name:</strong>{' '}
                                    {answer.personData.firstName}{' '}
                                    {answer.personData.lastName}
                                  </div>
                                  {answer.personData.relationshipType && (
                                    <div>
                                      <strong>Relationship:</strong>{' '}
                                      {answer.personData.relationshipType}
                                    </div>
                                  )}
                                  {answer.personData.phoneNumber && (
                                    <div>
                                      <strong>Phone:</strong>{' '}
                                      {answer.personData.phoneNumber}
                                    </div>
                                  )}
                                  {answer.personData.address && (
                                    <div>
                                      <strong>Address:</strong>{' '}
                                      {answer.personData.address}
                                    </div>
                                  )}
                                  {answer.personData.dateOfBirth && (
                                    <div>
                                      <strong>DOB:</strong>{' '}
                                      {new Date(
                                        answer.personData.dateOfBirth
                                      ).toLocaleDateString()}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                answer.answer
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      {/* <TableCell className='font-medium p-2 truncate max-w-[150px]'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className='truncate block'>
                                {answer.question}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                              {answer.question}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell className='p-2 truncate max-w-[150px]'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className='truncate block'>
                                {answer.answer}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                              {answer.answer}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell> */}
                      <TableCell className='p-2 hidden sm:table-cell'>
                        {answer.documentType}
                      </TableCell>
                      <TableCell className='p-2 hidden sm:table-cell'>
                        {answer.documentTitle}
                      </TableCell>
                      <TableCell className='p-2 text-right hidden sm:table-cell'>
                        {new Date(answer.answeredAt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      ) : (
        <div className='text-center py-4 text-muted-foreground border rounded-md'>
          <FileText className='h-8 w-8 mx-auto mb-1 opacity-20' />
          <p className='text-xs'>No answers found</p>
          {searchQuery && (
            <p className='text-xs mt-1'>Try adjusting your search or filter</p>
          )}
        </div>
      )}
    </div>
  );
};

export default SharedAnswersTable;
