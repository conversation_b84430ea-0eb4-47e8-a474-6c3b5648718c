'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { formatRole, masterRoles, roleSubroles } from '@/lib/validations/user';
import type { MasterRole } from '@/app/types/account';
import { useAuth } from '@/context/AuthContext';
import { fetchUserByCognitoId } from '@/lib/data/users';
import type { User as UserType } from '@/types/account';
import SharedAnswersTable from '@/app/components/linked-accounts/shared-answers-table';
import { useLinkedAccounts } from '@/hooks/useLinkedAccounts';
import { SectionEnum } from '@/hooks/useLinkedAccountPrefill';

const client = generateClient<Schema>();

// Create a dynamic schema that accepts any of the master roles
const inviteSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(masterRoles, {
    required_error: 'Please select a role',
  }),
  subrole: z.string().min(1, 'Please select a subrole'),
});

type InviteFormData = z.infer<typeof inviteSchema>;

interface InviteUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInviteSent?: () => void;
  preSignedRole?: 'Member' | 'Administrator' | 'WelonTrust' | 'Professional';
  preSignedSubrole?: string;
  showSharedAnswers?: boolean;
}

export function InviteUserModal({
  open,
  onOpenChange,
  onInviteSent,
  preSignedRole,
  preSignedSubrole,
  showSharedAnswers = false,
}: InviteUserModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [availableSubroles, setAvailableSubroles] = useState<string[]>([]);
  const [fullUserData, setFullUserData] = useState<UserType | null>(null);
  const [selectedSharedFields, setSelectedSharedFields] = useState<any[]>([]);
  const [emailCheckError, setEmailCheckError] = useState<string | null>(null);

  const { userExternalId } = useAuth();
  const { checkExistingLinkedAccountByEmail } = useLinkedAccounts();

  useEffect(() => {
    const loadUserData = async () => {
      if (userExternalId) {
        try {
          const userData = await fetchUserByCognitoId(userExternalId);
          setFullUserData(userData);
        } catch (error) {
          console.error('Error loading user data:', error);
          // Keep fullUserData as null, but don't retry automatically
        }
      }
    };
    loadUserData();
  }, [userExternalId]);

  // Determine available roles based on user's role and subrole
  const availableRoles = useMemo(() => {
    // If user is an Administrator with Advanced subrole, show all roles
    if (
      fullUserData?.role === 'Administrator' &&
      fullUserData?.subrole === 'Advanced'
    ) {
      return masterRoles;
    }
    // Otherwise, only show WelonTrust role
    return ['WelonTrust'] as const;
  }, [fullUserData]);

  const form = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      email: '',
      role: 'WelonTrust',
      subrole: '',
    },
  });

  // Watch for role changes to update available subroles
  const selectedRole = form.watch('role') as MasterRole;
  const email = form.watch('email');

  // Check for existing linked account when email changes
  // useEffect(() => {
  //   const checkExistingAccount = async () => {
  //     if (email && showSharedAnswers) {
  //       try {
  //         setEmailCheckError(null);
  //         const exists = await checkExistingLinkedAccountByEmail(email);
  //         if (exists) {
  //           setEmailCheckError(
  //             'There is already a linked account with this email'
  //           );
  //         }
  //       } catch (error) {
  //         console.error('Error checking existing linked account:', error);
  //       }
  //     }
  //   };

  //   checkExistingAccount();
  // }, [email, showSharedAnswers]);

  // Update available subroles when role changes
  useEffect(() => {
    const subroles = roleSubroles[selectedRole] || [];
    setAvailableSubroles(subroles);

    // Reset subrole when role changes
    if (subroles.length > 0) {
      form.setValue('subrole', subroles[0]);
    } else {
      form.setValue('subrole', '');
    }
  }, [selectedRole, form]);

  // Reset role to WelonTrust if user changes and they're not an Advanced Administrator
  useEffect(() => {
    if (
      fullUserData &&
      !(
        fullUserData.role === 'Administrator' &&
        fullUserData.subrole === 'Advanced'
      )
    ) {
      form.setValue('role', 'WelonTrust');
    }
  }, [fullUserData, form]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (open) {
      // Reset form state when modal opens
      form.reset({
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        role: 'WelonTrust',
        subrole: availableSubroles[0] || '',
      });
      // Clear any error states
      setEmailCheckError(null);
      setSelectedSharedFields([]);
      setIsLoading(false);
    }
  }, [open, form]);

  // Handle selected shared fields change
  const handleSelectedItemsChange = React.useCallback((items: any[]) => {
    setSelectedSharedFields(items);
  }, []);

  const onSubmit = async (data: InviteFormData) => {
    // Prevent double submissions
    if (isLoading) {
      return;
    }

    // Check for existing linked account before submitting
    // if (showSharedAnswers) {
    //   try {
    //     const exists = await checkExistingLinkedAccountByEmail(data.email);
    //     if (exists) {
    //       setEmailCheckError(
    //         'There is already a linked account with this email'
    //       );
    //       return;
    //     }
    //   } catch (error) {
    //     console.error('Error checking existing linked account:', error);
    //     toast.error('Error checking existing linked account', {
    //       description:
    //         error instanceof Error
    //           ? error.message
    //           : 'An unexpected error occurred',
    //     });
    //     return;
    //   }
    // }

    setIsLoading(true);

    try {
      // Prepare shared fields data if any are selected
      const sharedFields =
        selectedSharedFields.length > 0
          ? JSON.stringify(selectedSharedFields)
          : '[]';

      const { data: inviteResultData, errors } =
        await client.mutations.inviteUser({
          firstName: data.firstName,
          middleName: data.middleName || '',
          lastName: data.lastName,
          email: data.email,
          role: preSignedRole ? preSignedRole : data.role,
          subrole: preSignedSubrole ? preSignedSubrole : data.subrole,
          sharedFields: sharedFields,
          userId: fullUserData?.cognitoId,
          baseUrl: window.location.origin,
          inviterEmail: fullUserData?.email,
        });

      if (errors) {
        throw new Error(errors[0].message);
      }

      const result = JSON.parse(inviteResultData as string);

      if (!result.success) {
        throw new Error(result.error || 'Failed to send invitation');
      }

      toast.success(`Invitation sent to ${data.email}`, {
        description:
          'The user will receive an email with instructions to join. The invitation will expire in 7 days.',
      });

      // Reset form and clear states
      form.reset();
      setSelectedSharedFields([]);
      setEmailCheckError(null);

      // Close modal and notify parent
      onOpenChange(false);
      onInviteSent?.();
    } catch (error) {
      console.error('Failed to send invite:', error);
      toast.error('Failed to send invitation', {
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={showSharedAnswers ? 'sm:max-w-[800px]' : 'sm:max-w-[500px]'}
      >
        <DialogHeader>
          <DialogTitle>Invite User</DialogTitle>
          <DialogDescription>
            Send an invitation to join. We'll check if the email is already
            registered or has a pending invitation. They will receive an email
            with instructions to complete their registration.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='grid grid-cols-3 gap-4'>
              <FormField
                control={form.control}
                name='firstName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter first name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='middleName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Middle Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Enter middle name (optional)'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='lastName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter last name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter email address'
                      type='email'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  {emailCheckError && (
                    <p className='text-sm font-medium text-destructive mt-1'>
                      {emailCheckError}
                    </p>
                  )}
                </FormItem>
              )}
            />
            <div className='grid grid-cols-2 gap-4'>
              {!preSignedRole && (
                <FormField
                  control={form.control}
                  name='role'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select a role' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableRoles.map(role => (
                            <SelectItem key={role} value={role}>
                              {formatRole(role)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {!preSignedSubrole && (
                <FormField
                  control={form.control}
                  name='subrole'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subrole</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select a subrole' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableSubroles.map(subrole => (
                            <SelectItem key={subrole} value={subrole}>
                              {subrole}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Shared Answers Table */}
            {showSharedAnswers && (
              <div className='mt-6'>
                <h3 className='text-sm font-medium mb-2'>
                  Select shared answers to include with invitation:
                </h3>
                <div className='border p-4 rounded-md bg-muted/30'>
                  <SharedAnswersTable
                    onSelectedItemsChange={handleSelectedItemsChange}
                    showAcceptButton={false}
                    compact={true}
                    sectionsToExclude={[
                      // Care Documents
                      SectionEnum.CARE_DOCUMENTS_PERSONAL_IDENTIFICATION,
                      SectionEnum.CARE_DOCUMENTS_MEDICAL,
                      SectionEnum.CARE_DOCUMENTS_LAST_WISHES,
                      SectionEnum.CARE_DOCUMENTS_PROPERTY,
                      SectionEnum.CARE_DOCUMENTS_EMPLOYMENT,
                      SectionEnum.CARE_DOCUMENTS_EMERGENCY_INCIDENTS,
                      SectionEnum.CARE_DOCUMENTS_IMPORTANT_TO_ME,
                      SectionEnum.CARE_DOCUMENTS_LEGACY,
                      SectionEnum.CARE_DOCUMENTS_LONG_TERM_CARE,
                      SectionEnum.CARE_DOCUMENTS_UPLOADS,

                      // Interview
                      SectionEnum.INTERVIEW_PROFILE,
                      SectionEnum.INTERVIEW_AFTER_YOU_DIE,
                      SectionEnum.INTERVIEW_MEDICAL,
                      SectionEnum.INTERVIEW_ADDITIONAL,
                      SectionEnum.INTERVIEW_PEOPLE,
                      SectionEnum.INTERVIEW_COMPLETED_SUBSECTIONS,
                      SectionEnum.INTERVIEW_REVIEW,
                    ]}
                  />
                </div>
                <p className='text-xs text-muted-foreground mt-2'>
                  Selected answers: {selectedSharedFields.length}
                </p>
              </div>
            )}

            <div className='flex justify-end space-x-2 pt-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading || !!emailCheckError}>
                {isLoading ? 'Sending...' : 'Send Invitation'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
