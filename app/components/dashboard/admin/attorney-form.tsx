'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { StreetAutocomplete } from '@/components/address-autocomplete/StreetAutocomplete';
import { CityAutocomplete } from '@/components/address-autocomplete/CityAutocomplete';
import { ZipCodeAutocomplete } from '@/components/address-autocomplete/ZipCodeAutocomplete';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { useModal } from '@/hooks/use-modal';
import { AlertCircle, Plus, X, Scale, Trash2 } from 'lucide-react';
import { useAttorneyForm } from '@/hooks/use-attorney-form';
import { type AttorneyFormProps } from '@/lib/validations/attorney';

// US States for the select dropdown
const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
];

export function AttorneyForm({
  attorney,
  mode,
  onSave,
  onDelete,
  isLoading = false,
}: AttorneyFormProps) {
  const [customSpecialty, setCustomSpecialty] = useState('');
  const deleteModal = useModal();

  const {
    form,
    isSubmitting,
    submitError,
    watchedSpecialties,
    availableSpecialties,
    onSubmit,
    handleCancel,
    handleSpecialtyChange,
    handleAddCustomSpecialty,
    handleRemoveSpecialty,
    isValidatingAddress,
  } = useAttorneyForm({
    attorney,
    mode,
    onSave,
    isLoading,
  });

  const handleAddCustomSpecialtyClick = () => {
    if (customSpecialty.trim()) {
      handleAddCustomSpecialty(customSpecialty);
      setCustomSpecialty('');
    }
  };

  const handleDeleteClick = () => {
    deleteModal.open(attorney);
  };

  const handleDeleteConfirm = async () => {
    if (!onDelete) return;

    await deleteModal.confirm(async () => {
      await onDelete();
    });
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center space-x-4'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleCancel}
          className='flex items-center gap-2'
        >
          ← Back to Attorneys
        </Button>
      </div>

      <div className='flex items-center gap-3'>
        <Scale className='h-8 w-8 text-blue-600' />
        <div>
          <h1 className='text-3xl font-geologica font-semibold'>
            {mode === 'create' ? 'Create New Attorney' : 'Edit Attorney'}
          </h1>
          <p className='text-muted-foreground mt-2'>
            {mode === 'create'
              ? 'Add a new state-licensed attorney to the system'
              : 'Update attorney information and contact details'}
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {submitError && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Form {...form}>
        <form onSubmit={onSubmit} className='space-y-6'>
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the attorney's basic contact information
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-2 gap-4'>
                {/* Name Field */}
                <FormField
                  control={form.control as any}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter attorney full name'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Firm Field */}
                <FormField
                  control={form.control as any}
                  name='firm'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Law Firm</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter law firm name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                {/* Phone Field */}
                <FormField
                  control={form.control as any}
                  name='phone'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number *</FormLabel>
                      <FormControl>
                        <Input placeholder='(*************' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field */}
                <FormField
                  control={form.control as any}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input
                          type='email'
                          placeholder='<EMAIL>'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle>Location Information</CardTitle>
              <CardDescription>
                Enter the attorney's practice location details
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Address Field with Google Places */}
              <FormField
                control={form.control as any}
                name='address'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <StreetAutocomplete
                        label='Street Address'
                        value={field.value || ''}
                        onChange={field.onChange}
                        onAddressSelect={components => {
                          // Parse Google Places address components
                          const addressData: any = {};

                          components.forEach((component: any) => {
                            const types = component.types;
                            if (types.includes('street_number')) {
                              addressData.streetNumber = component.long_name;
                            } else if (types.includes('route')) {
                              addressData.route = component.long_name;
                            } else if (types.includes('locality')) {
                              addressData.city = component.long_name;
                            } else if (
                              types.includes('administrative_area_level_1')
                            ) {
                              addressData.state = component.short_name;
                            } else if (types.includes('postal_code')) {
                              addressData.zip = component.long_name;
                            }
                          });

                          // Update form fields
                          const street =
                            addressData.streetNumber && addressData.route
                              ? `${addressData.streetNumber} ${addressData.route}`
                              : addressData.route ||
                                addressData.streetNumber ||
                                field.value;

                          form.setValue('address', street);
                          if (addressData.city)
                            form.setValue('city', addressData.city);
                          if (addressData.state)
                            form.setValue('state', addressData.state);
                          if (addressData.zip)
                            form.setValue('zipCode', addressData.zip);
                        }}
                        placeholder='Start typing an address...'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='grid grid-cols-3 gap-4'>
                {/* City Field with Google Places */}
                <FormField
                  control={form.control as any}
                  name='city'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <CityAutocomplete
                          label='City'
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder='Start typing a city...'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* State Field */}
                <FormField
                  control={form.control as any}
                  name='state'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select state' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {US_STATES.map(state => (
                            <SelectItem key={state.code} value={state.code}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* ZIP Code Field with Autocomplete */}
                <FormField
                  control={form.control as any}
                  name='zipCode'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <ZipCodeAutocomplete
                          label='ZIP Code'
                          value={field.value || ''}
                          onChange={field.onChange}
                          onZipSelect={zipData => {
                            form.setValue('zipCode', zipData.zip);
                            if (zipData.city && !form.getValues('city')) {
                              form.setValue('city', zipData.city);
                            }
                            if (zipData.state && !form.getValues('state')) {
                              // Check if the state code is valid
                              const validState = US_STATES.find(
                                state => state.code === zipData.state
                              );
                              if (validState) {
                                form.setValue('state', zipData.state as any);
                              }
                            }
                          }}
                          placeholder='12345'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle>Professional Information</CardTitle>
              <CardDescription>
                Enter the attorney's professional credentials and experience
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-2 gap-4'>
                {/* Bar Number Field */}
                <FormField
                  control={form.control as any}
                  name='barNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bar Number</FormLabel>
                      <FormControl>
                        <Input placeholder='State bar number' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Years of Experience Field */}
                <FormField
                  control={form.control as any}
                  name='yearsExperience'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Years of Experience</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          min='0'
                          max='70'
                          placeholder='0'
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Preferred Attorney Checkbox */}
              <FormField
                control={form.control as any}
                name='isPreferred'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className='space-y-1 leading-none'>
                      <FormLabel>Preferred Attorney</FormLabel>
                      <p className='text-sm text-muted-foreground'>
                        Mark this attorney as preferred for member
                        recommendations
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              {/* Active Status Checkbox */}
              <FormField
                control={form.control as any}
                name='isActive'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className='space-y-1 leading-none'>
                      <FormLabel>Active Status</FormLabel>
                      <p className='text-sm text-muted-foreground'>
                        Attorney is available for new client assignments
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Legal Specialties */}
          <Card>
            <CardHeader>
              <CardTitle>Legal Specialties</CardTitle>
              <CardDescription>
                Select the attorney's areas of legal expertise
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Current Specialties */}
              {watchedSpecialties.length > 0 && (
                <div className='space-y-2'>
                  <FormLabel>Selected Specialties</FormLabel>
                  <div className='flex flex-wrap gap-2'>
                    {watchedSpecialties.map(specialty => (
                      <Badge
                        key={specialty}
                        variant='secondary'
                        className='flex items-center gap-1'
                      >
                        {specialty}
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          className='h-4 w-4 p-0 hover:bg-transparent'
                          onClick={() => handleRemoveSpecialty(specialty)}
                        >
                          <X className='h-3 w-3' />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Common Specialties */}
              <div className='space-y-2'>
                <FormLabel>Common Specialties</FormLabel>
                <div className='grid grid-cols-2 gap-2'>
                  {availableSpecialties.map(specialty => (
                    <div
                      key={specialty}
                      className='flex items-center space-x-2'
                    >
                      <Checkbox
                        id={`specialty-${specialty}`}
                        checked={watchedSpecialties.includes(specialty)}
                        onCheckedChange={checked =>
                          handleSpecialtyChange(specialty, checked as boolean)
                        }
                      />
                      <label
                        htmlFor={`specialty-${specialty}`}
                        className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                      >
                        {specialty}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Add Custom Specialty */}
              <div className='space-y-2'>
                <FormLabel>Add Custom Specialty</FormLabel>
                <div className='flex gap-2'>
                  <Input
                    placeholder='Enter custom specialty'
                    value={customSpecialty}
                    onChange={e => setCustomSpecialty(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddCustomSpecialtyClick();
                      }
                    }}
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleAddCustomSpecialtyClick}
                    disabled={!customSpecialty.trim()}
                  >
                    <Plus className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className='flex justify-between'>
            {/* Delete Button - Only show in edit mode */}
            {mode === 'edit' && onDelete && (
              <Button
                type='button'
                variant='destructive'
                onClick={handleDeleteClick}
                disabled={isSubmitting || deleteModal.loading}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                {deleteModal.loading ? 'Deleting...' : 'Delete Attorney'}
              </Button>
            )}

            {/* Right side actions */}
            <div className='flex space-x-4 ml-auto'>
              <Button variant='outline' onClick={handleCancel}>
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isValidatingAddress
                  ? 'Validating address...'
                  : isSubmitting
                    ? mode === 'create'
                      ? 'Creating...'
                      : 'Saving...'
                    : mode === 'create'
                      ? 'Create Attorney'
                      : 'Save Changes'}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        {...deleteModal.modalProps}
        title='Delete Attorney'
        description={
          deleteModal.data
            ? `Are you sure you want to delete attorney "${deleteModal.data.name}"? This action cannot be undone.`
            : 'Are you sure you want to delete this attorney?'
        }
        confirmLabel={deleteModal.loading ? 'Deleting...' : 'Delete'}
        cancelLabel='Cancel'
        confirmVariant='destructive'
        onConfirm={handleDeleteConfirm}
        icon={<Trash2 className='h-6 w-6 text-red-600' />}
      />
    </div>
  );
}
