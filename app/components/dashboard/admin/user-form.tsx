'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2, CreditCard, Check, X } from 'lucide-react';
import { useUserForm } from '@/hooks/use-user-form';
import { type UserFormProps } from '@/lib/validations/user';

export function UserForm({
  user,
  mode,
  onSave,
  isLoading = false,
}: UserFormProps) {
  const {
    form,
    isSubmitting,
    submitError,
    welonTrustUsers,
    loadingWelonTrust,
    userSubscription,
    loadingSubscription,
    watchedRole,
    watchedAssignedWelonTrust,
    availableSubroles,
    availablePermissions,
    onSubmit,
    handleCancel,
    handleRoleChange,
    handleSubroleChange,
    handlePermissionChange,
    handleWelonTrustChange,
    handleSubscriptionPlanChange,
    handleSubscriptionActivation,
  } = useUserForm({
    user,
    mode,
    onSave,
    isLoading,
  });

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center space-x-4'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleCancel}
          icon='arrow-left'
          iconSize='sm'
        >
          Back to Users
        </Button>
      </div>

      <div>
        <h1 className='text-3xl font-geologica font-semibold'>
          {mode === 'create' ? 'Create New User' : 'Edit User'}
        </h1>
        <p className='text-muted-foreground mt-2'>
          {mode === 'create'
            ? 'Add a new user to the system'
            : 'Update user details and permissions'}
        </p>
      </div>

      {/* Error Alert */}
      {submitError && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Form {...form}>
        <form onSubmit={onSubmit} className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>
                Enter the user's basic information and role assignments
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-3 gap-4'>
                {/* First Name Field */}
                <FormField
                  control={form.control as any}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter first name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Middle Name Field */}
                <FormField
                  control={form.control as any}
                  name='middleName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Middle Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter middle name (optional)'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Last Name Field */}
                <FormField
                  control={form.control as any}
                  name='lastName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter last name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-1 gap-4'>
                {/* Email Field */}
                <FormField
                  control={form.control as any}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type='email'
                          placeholder='Enter email address'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                {/* Role Field */}
                <FormField
                  control={form.control as any}
                  name='role'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={value => handleRoleChange(value as any)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select role' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='Member'>Member</SelectItem>
                          <SelectItem value='Administrator'>
                            Administrator
                          </SelectItem>
                          <SelectItem value='WelonTrust'>
                            Welon Trust
                          </SelectItem>
                          <SelectItem value='Professional'>
                            Professional
                          </SelectItem>
                          <SelectItem value='CallCenter'>
                            Call Center
                          </SelectItem>
                          <SelectItem value='MedicalReview'>Medical</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subrole Field */}
                <FormField
                  control={form.control as any}
                  name='subrole'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subrole</FormLabel>
                      <Select
                        onValueChange={handleSubroleChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select subrole' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableSubroles.map(subrole => (
                            <SelectItem key={subrole} value={subrole}>
                              {subrole}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Permissions */}
          {/* <Card>
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
              <CardDescription>
                Select the permissions for this user based on their role
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                {availablePermissions.map(permission => (
                  <div key={permission} className='flex items-center space-x-2'>
                    <Checkbox
                      id={`permission-${permission}`}
                      checked={form.watch('permissions').includes(permission)}
                      onCheckedChange={checked =>
                        handlePermissionChange(permission, checked as boolean)
                      }
                    />
                    <label
                      htmlFor={`permission-${permission}`}
                      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                    >
                      {permission}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card> */}

          {/* Welon Trust Assignment - Only show for Members */}
          {watchedRole === 'Member' && (
            <Card>
              <CardHeader>
                <CardTitle>Welon Trust Assignment</CardTitle>
                <CardDescription>
                  Assign a Welon Trust to this member for estate management
                  (required)
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Welon Trust Selection Field */}
                <FormField
                  control={form.control as any}
                  name='assignedWelonTrust'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select Welon Trust *</FormLabel>
                      <Select
                        onValueChange={handleWelonTrustChange}
                        value={
                          loadingWelonTrust ? 'loading' : field.value || ''
                        }
                        disabled={loadingWelonTrust}
                      >
                        <FormControl>
                          <SelectTrigger
                            className={
                              loadingWelonTrust
                                ? 'cursor-not-allowed opacity-60'
                                : ''
                            }
                          >
                            {loadingWelonTrust ? (
                              <div className='flex items-center gap-2'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span>Loading Welon Trust users...</span>
                              </div>
                            ) : (
                              <SelectValue placeholder='Select existing Welon Trust or invite new (required)' />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingWelonTrust ? (
                            <SelectItem value='loading' disabled>
                              <div className='flex items-center gap-2'>
                                <Loader2 className='h-4 w-4 animate-spin' />
                                <span>Loading Welon Trust users...</span>
                              </div>
                            </SelectItem>
                          ) : (
                            <>
                              {welonTrustUsers.map(wt => (
                                <SelectItem key={wt.id} value={wt.id}>
                                  {wt.name} ({wt.email})
                                </SelectItem>
                              ))}
                              <SelectItem value='invite_new'>
                                Invite new Welon Trust via email
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* New Welon Trust Email Field - Show when "invite_new" is selected */}
                {watchedAssignedWelonTrust === 'invite_new' && (
                  <FormField
                    control={form.control as any}
                    name='newWelonTrustEmail'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Welon Trust Email</FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            placeholder='Enter email to invite new Welon Trust'
                            {...field}
                          />
                        </FormControl>
                        <p className='text-sm text-muted-foreground'>
                          An invitation will be sent to this email to join as
                          Welon Trust for this member.
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Show current assignment info for edit mode */}
                {mode === 'edit' && user?.assignedWelonTrust && (
                  <div className='bg-blue-50 p-3 rounded-md'>
                    <p className='text-sm font-medium text-blue-800'>
                      Current Assignment:
                    </p>
                    <p className='text-sm text-blue-700'>
                      {user.assignedWelonTrust.welonTrustName} (
                      {user.assignedWelonTrust.welonTrustEmail})
                    </p>
                    <p className='text-xs text-blue-600'>
                      Status: {user.assignedWelonTrust.status} • Assigned:{' '}
                      {new Date(
                        user.assignedWelonTrust.assignedAt
                      ).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Subscription Management - Only show for edit mode */}
          {mode === 'edit' && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <CreditCard className='h-5 w-5' />
                  Subscription Management
                </CardTitle>
                <CardDescription>
                  Manage user subscription and activate plans without payment
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Current Subscription Status */}
                {loadingSubscription ? (
                  <div className='flex items-center gap-2 p-4 bg-gray-50 rounded-md'>
                    <Loader2 className='h-4 w-4 animate-spin' />
                    <span>Loading subscription information...</span>
                  </div>
                ) : userSubscription ? (
                  <div className='p-4 bg-green-50 border border-green-200 rounded-md'>
                    <div className='flex items-center gap-2 mb-2'>
                      <Check className='h-4 w-4 text-green-600' />
                      <span className='font-medium text-green-800'>
                        Active Subscription
                      </span>
                    </div>
                    <div className='text-sm text-green-700 space-y-1'>
                      <p>
                        <strong>Plan:</strong> {userSubscription.plan}
                      </p>
                      <p>
                        <strong>Amount:</strong> $
                        {(userSubscription.amount / 100).toFixed(2)}/month
                      </p>
                      <p>
                        <strong>Status:</strong> {userSubscription.status}
                      </p>
                      <p>
                        <strong>Current Period:</strong>{' '}
                        {new Date(
                          userSubscription.currentPeriodStart
                        ).toLocaleDateString()}{' '}
                        -{' '}
                        {new Date(
                          userSubscription.currentPeriodEnd
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className='p-4 bg-gray-50 border border-gray-200 rounded-md'>
                    <div className='flex items-center gap-2 mb-2'>
                      <X className='h-4 w-4 text-gray-600' />
                      <span className='font-medium text-gray-800'>
                        No Active Subscription
                      </span>
                    </div>
                    <p className='text-sm text-gray-600'>
                      User does not have an active subscription
                    </p>
                  </div>
                )}

                {/* Subscription Plan Selection */}
                <div className='grid grid-cols-2 gap-4'>
                  <FormField
                    control={form.control as any}
                    name='subscriptionPlan'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Select Plan</FormLabel>
                        <Select
                          onValueChange={handleSubscriptionPlanChange}
                          value={field.value || ''}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Choose subscription plan' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='BASIC'>
                              Basic Plan - $10/month
                            </SelectItem>
                            <SelectItem value='PRO'>
                              Pro Plan - $20/month
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Trial Duration Field */}
                  <FormField
                    control={form.control as any}
                    name='subscriptionTrialDays'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Trial Duration (days)</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            placeholder='31'
                            {...field}
                            value={field.value || 31}
                            onChange={e =>
                              field.onChange(
                                e.target.value ? Number(e.target.value) : 31
                              )
                            }
                            min={1}
                            max={3650}
                          />
                        </FormControl>
                        <p className='text-xs text-muted-foreground'>
                          Number of days for trial period (1-3650 days, default:
                          31)
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Subscription Type Selection */}
                <FormField
                  control={form.control as any}
                  name='subscriptionType'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subscription Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || 'trial'}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Choose subscription type' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='trial'>
                            Trial Only (Free for trial period)
                          </SelectItem>
                          <SelectItem value='paid'>
                            Paid Subscription (Charges after trial)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <p className='text-xs text-muted-foreground'>
                        Trial only: Free during trial, no charges. Paid: Regular
                        subscription after trial ends.
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className='flex justify-end space-x-4'>
            <Button variant='outline' onClick={handleCancel} icon='close'>
              Cancel
            </Button>
            <Button
              type='submit'
              icon={mode === 'create' ? 'plus-circle' : 'save'}
              disabled={isSubmitting}
            >
              {isSubmitting
                ? mode === 'create'
                  ? 'Creating...'
                  : 'Saving...'
                : mode === 'create'
                  ? 'Create User'
                  : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
