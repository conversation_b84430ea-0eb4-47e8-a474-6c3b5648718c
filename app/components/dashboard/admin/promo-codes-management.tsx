'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';

const client = generateClient<Schema>();

export function PromoCodesManagement() {
  const { user, userExternalId } = useAuth();
  const [promoCodes, setPromoCodes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [newPromoCode, setNewPromoCode] = useState({
    code: '',
    discountPercentage: 10,
    description: '',
    expiresAt: null as Date | null,
  });

  // Load promo codes
  const loadPromoCodes = async () => {
    setLoading(true);
    try {
      const { data, errors } = await client.models.PromoCode.list();
      if (errors) {
        console.error('Error loading promo codes:', errors);
        toast.error('Failed to load promo codes');
      } else {
        setPromoCodes(data || []);
      }
    } catch (error) {
      console.error('Error loading promo codes:', error);
      toast.error('Failed to load promo codes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPromoCodes();
  }, []);

  // Create new promo code
  const handleCreatePromoCode = async () => {
    if (!newPromoCode.code.trim()) {
      toast.error('Promo code is required');
      return;
    }

    if (!user?.userId) {
      toast.error('User information not available');
      return;
    }

    setCreating(true);
    try {
      const { data, errors } = await client.models.PromoCode.create({
        code: newPromoCode.code.trim().toUpperCase(),
        discountPercentage: newPromoCode.discountPercentage,
        status: 'active',
        description: newPromoCode.description.trim(),
        expiresAt: newPromoCode.expiresAt
          ? newPromoCode.expiresAt.toISOString()
          : null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: userExternalId || '',
        createdByEmail: user.userId,
      });

      if (errors) {
        console.error('Error creating promo code:', errors);
        toast.error('Failed to create promo code');
      } else {
        toast.success('Promo code created successfully');
        setNewPromoCode({
          code: '',
          discountPercentage: 10,
          description: '',
          expiresAt: null,
        });
        loadPromoCodes(); // Reload the list
      }
    } catch (error) {
      console.error('Error creating promo code:', error);
      toast.error('Failed to create promo code');
    } finally {
      setCreating(false);
    }
  };

  // Delete promo code
  const handleDeletePromoCode = async (id: string) => {
    if (!confirm('Are you sure you want to delete this promo code?')) {
      return;
    }

    try {
      const { errors } = await client.models.PromoCode.delete({ id });
      if (errors) {
        console.error('Error deleting promo code:', errors);
        toast.error('Failed to delete promo code');
      } else {
        toast.success('Promo code deleted successfully');
        loadPromoCodes(); // Reload the list
      }
    } catch (error) {
      console.error('Error deleting promo code:', error);
      toast.error('Failed to delete promo code');
    }
  };

  // Table columns
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'code',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Code' />
      ),
      cell: ({ row }) => (
        <span className='font-mono font-medium'>{row.getValue('code')}</span>
      ),
    },
    {
      accessorKey: 'discountPercentage',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Discount' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>
          {row.getValue('discountPercentage')}%
        </span>
      ),
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Description' />
      ),
      cell: ({ row }) => (
        <span className='max-w-xs truncate' title={row.getValue('description')}>
          {row.getValue('description') || '-'}
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const isExpired =
          row.original.expiresAt &&
          new Date(row.original.expiresAt) < new Date();
        const displayStatus = isExpired ? 'expired' : status;

        return (
          <span
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              displayStatus === 'active'
                ? 'bg-green-100 text-green-800'
                : displayStatus === 'inactive'
                  ? 'bg-gray-100 text-gray-800'
                  : 'bg-red-100 text-red-800'
            }`}
          >
            {displayStatus}
          </span>
        );
      },
    },
    {
      accessorKey: 'expiresAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Expires' />
      ),
      cell: ({ row }) => {
        const expiresAt = row.getValue('expiresAt') as string;
        return expiresAt ? new Date(expiresAt).toLocaleDateString() : 'Never';
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Created' />
      ),
      cell: ({ row }) => (
        <span>{new Date(row.getValue('createdAt')).toLocaleDateString()}</span>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant='destructive'
          size='sm'
          onClick={() => handleDeletePromoCode(row.original.id)}
        >
          <Trash2 className='h-4 w-4' />
        </Button>
      ),
    },
  ];

  // Table configuration
  const tableConfig: DataTableConfig = {
    searchColumn: 'code',
    searchPlaceholder: 'Search promo codes...',
    filters: [
      {
        id: 'status',
        title: 'Status',
        options: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' },
          { value: 'expired', label: 'Expired' },
        ],
      },
    ],
    enableColumnVisibility: true,
    enablePagination: true,
    defaultPageSize: 10,
  };

  return (
    <div className='space-y-6'>
      {/* Create new promo code */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Promo Code</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
              <Label className='pb-1' htmlFor='code'>
                Code
              </Label>
              <Input
                id='code'
                value={newPromoCode.code}
                onChange={e =>
                  setNewPromoCode(prev => ({
                    ...prev,
                    code: e.target.value.toUpperCase(),
                  }))
                }
                placeholder='SAVE20'
                maxLength={20}
              />
            </div>
            <div>
              <Label className='pb-1' htmlFor='discount'>
                Discount %
              </Label>
              <Input
                id='discount'
                type='number'
                value={newPromoCode.discountPercentage}
                onChange={e =>
                  setNewPromoCode(prev => ({
                    ...prev,
                    discountPercentage: parseInt(e.target.value) || 0,
                  }))
                }
                min={1}
                max={100}
              />
            </div>
            <div>
              <Label className='pb-1' htmlFor='expires'>
                Expires At (optional)
              </Label>
              <DatePicker
                date={newPromoCode.expiresAt || undefined}
                setDate={date =>
                  setNewPromoCode(prev => ({
                    ...prev,
                    expiresAt: date || null,
                  }))
                }
                showYearPicker={true}
                showMonthPicker={true}
                yearRange={{
                  from: new Date().getFullYear(),
                  to: new Date().getFullYear() + 10,
                }}
              />
            </div>
            <div className='flex items-end'>
              <Button
                onClick={handleCreatePromoCode}
                disabled={creating || !newPromoCode.code.trim()}
                className='w-full'
              >
                <Plus className='mr-2 h-4 w-4' />
                {creating ? 'Creating...' : 'Create'}
              </Button>
            </div>
          </div>
          <div>
            <Label className='pb-1' htmlFor='description'>
              Description (optional)
            </Label>
            <Input
              id='description'
              value={newPromoCode.description}
              onChange={e =>
                setNewPromoCode(prev => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder='Holiday discount'
            />
          </div>
        </CardContent>
      </Card>

      {/* Existing promo codes */}
      <Card>
        <CardHeader>
          <CardTitle>Existing Promo Codes</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={promoCodes}
            config={tableConfig}
            loading={loading}
          />
        </CardContent>
      </Card>
    </div>
  );
}
