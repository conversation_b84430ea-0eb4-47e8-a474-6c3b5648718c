'use client';

import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import type { ColumnDef } from '@tanstack/react-table';
import { useUsers } from '@/hooks/useUsers';
import { toast } from 'sonner';
import * as ExcelJS from 'exceljs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PromoCodesManagement } from './promo-codes-management';

interface ReferralSourcesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ReferralData {
  id: string;
  name: string;
  email: string;
  howDidYouHearAboutUs: string;
  createdAt: string;
}

export function ReferralSourcesModal({
  open,
  onOpenChange,
}: ReferralSourcesModalProps) {
  const { users, loading } = useUsers();
  const [isExporting, setIsExporting] = useState(false);
  const [activeTab, setActiveTab] = useState('referrals');

  // Filter users who have provided referral source information
  const referralData: ReferralData[] = useMemo(() => {
    console.log(
      'All users:',
      users.map(u => ({
        id: u.id,
        name: u.name,
        howDidYouHearAboutUs: u.howDidYouHearAboutUs,
      }))
    );

    const filtered = users
      .filter(
        user =>
          user.howDidYouHearAboutUs &&
          user.howDidYouHearAboutUs.trim() !== '' &&
          user.howDidYouHearAboutUs !== 'No referral'
      )
      .map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        howDidYouHearAboutUs: user.howDidYouHearAboutUs!,
        createdAt: user.createdAt,
      }));

    console.log('Filtered referral data:', filtered);
    return filtered;
  }, [users]);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      console.log('Creating Excel workbook');

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Referral Sources');

      // Define columns with headers and widths
      worksheet.columns = [
        { header: 'Name', key: 'name', width: 25 },
        { header: 'Email', key: 'email', width: 30 },
        {
          header: 'How did you hear about us?',
          key: 'howDidYouHearAboutUs',
          width: 40,
        },
        { header: 'Registration Date', key: 'registrationDate', width: 20 },
      ];

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      // Add data rows
      referralData.forEach(item => {
        worksheet.addRow({
          name: item.name,
          email: item.email,
          howDidYouHearAboutUs: item.howDidYouHearAboutUs,
          registrationDate: new Date(item.createdAt).toLocaleDateString(),
        });
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        if (column.key) {
          const headerLength = column.header?.length || 0;
          const maxDataLength = Math.max(
            ...referralData.map(item => {
              const value = item[column.key as keyof ReferralData];
              return String(value || '').length;
            })
          );
          const maxLength = Math.max(headerLength, maxDataLength);
          column.width = Math.min(Math.max(maxLength + 2, 10), 50);
        }
      });

      console.log('Generating Excel file');

      // Generate Excel file buffer
      const excelBuffer = await workbook.xlsx.writeBuffer();

      // Create blob and download
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `referral-sources-${timestamp}.xlsx`;
      link.setAttribute('download', filename);

      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Referral sources exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export referral sources');
    } finally {
      setIsExporting(false);
    }
  };

  const columns: ColumnDef<ReferralData>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Name' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>{row.getValue('name')}</span>
      ),
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Email' />
      ),
      cell: ({ row }) => <span>{row.getValue('email')}</span>,
    },
    {
      accessorKey: 'howDidYouHearAboutUs',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Referral Source' />
      ),
      cell: ({ row }) => (
        <span
          className='max-w-xs truncate'
          title={row.getValue('howDidYouHearAboutUs')}
        >
          {row.getValue('howDidYouHearAboutUs') || '-'}
        </span>
      ),
    },

    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Registered' />
      ),
      cell: ({ row }) => (
        <span>{new Date(row.getValue('createdAt')).toLocaleDateString()}</span>
      ),
    },
  ];

  const tableConfig: DataTableConfig = {
    searchColumn: 'howDidYouHearAboutUs',
    searchPlaceholder: 'Search referral sources...',
    filters: [],
    enableColumnVisibility: true,
    enablePagination: true,
    defaultPageSize: 10,
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='!max-w-[95vw] !w-[80vw] max-h-[90vh] overflow-hidden flex flex-col !top-[8vh] !translate-y-0'>
        <DialogHeader className='flex-shrink-0'>
          <div className='flex items-center justify-between'>
            <DialogTitle>Referral Sources & Promo Codes</DialogTitle>
          </div>
        </DialogHeader>

        <div className='flex-1 flex flex-col'>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className='flex-1 flex flex-col'
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='referrals'>Referral Sources</TabsTrigger>
              <TabsTrigger value='promocodes'>Promo Codes</TabsTrigger>
            </TabsList>

            <TabsContent
              value='referrals'
              className='flex-1 flex flex-col space-y-4 mt-4'
            >
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
                  <CardTitle>Referral Sources</CardTitle>
                  <Button
                    onClick={handleExport}
                    disabled={isExporting || referralData.length === 0}
                    variant='outline'
                  >
                    <Download className='mr-2 h-4 w-4' />
                    {isExporting ? 'Exporting...' : 'Export Excel'}
                  </Button>
                </CardHeader>
                <CardContent>
                  <DataTable
                    columns={columns}
                    data={referralData}
                    config={tableConfig}
                    loading={loading}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value='promocodes'
              className='flex-1 flex flex-col mt-4'
            >
              <div className='flex-1 min-h-0 overflow-auto'>
                <PromoCodesManagement />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
