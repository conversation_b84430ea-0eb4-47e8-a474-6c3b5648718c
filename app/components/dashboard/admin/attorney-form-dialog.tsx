'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Save, AlertTriangle, Scale, X } from 'lucide-react';
import {
  Attorney,
  CreateAttorneyRequest,
  UpdateAttorneyRequest,
} from '@/types/attorney-reviews';
import { US_STATES, validateAttorneyData } from '@/lib/api/attorneys';
import { StreetAutocomplete } from '@/components/address-autocomplete/StreetAutocomplete';
import { CityAutocomplete } from '@/components/address-autocomplete/CityAutocomplete';
import { ZipCodeAutocomplete } from '@/components/address-autocomplete/ZipCodeAutocomplete';
import { validateAddress } from '@/app/utils/addressValidation';

interface AttorneyFormDialogProps {
  attorney: Attorney | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    data: CreateAttorneyRequest | UpdateAttorneyRequest
  ) => Promise<void>;
  loading?: boolean;
}

const COMMON_SPECIALTIES = [
  'Estate Planning',
  'Wills & Trusts',
  'Probate',
  'Elder Law',
  'Tax Law',
  'Business Law',
  'Trust Administration',
  'Asset Protection',
  'Guardianship',
  'Special Needs Planning',
];

export function AttorneyFormDialog({
  attorney,
  isOpen,
  onClose,
  onSave,
  loading = false,
}: AttorneyFormDialogProps) {
  const [formData, setFormData] = useState<CreateAttorneyRequest>({
    name: '',
    phone: '',
    email: '',
    state: '',
    firm: '',
    address: '',
    city: '',
    zipCode: '',
    specialties: [],
    barNumber: '',
    yearsExperience: 0,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [newSpecialty, setNewSpecialty] = useState('');
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);

  // Initialize form data when attorney prop changes
  useEffect(() => {
    if (attorney) {
      setFormData({
        name: attorney.name,
        phone: attorney.phone,
        email: attorney.email,
        state: attorney.state,
        firm: attorney.firm || '',
        address: attorney.address || '',
        city: attorney.city || '',
        zipCode: attorney.zipCode || '',
        specialties: attorney.specialties || [],
        barNumber: attorney.barNumber || '',
        yearsExperience: attorney.yearsExperience,
      });
    } else {
      setFormData({
        name: '',
        phone: '',
        email: '',
        state: '',
        firm: '',
        address: '',
        city: '',
        zipCode: '',
        specialties: [],
        barNumber: '',
        yearsExperience: 0,
      });
    }
    setErrors({});
    setNewSpecialty('');
  }, [attorney, isOpen]);

  const handleFieldChange = (
    field: keyof CreateAttorneyRequest,
    value: any
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddSpecialty = (specialty: string) => {
    if (specialty && !formData.specialties?.includes(specialty)) {
      setFormData(prev => ({
        ...prev,
        specialties: [...(prev.specialties || []), specialty],
      }));
    }
    setNewSpecialty('');
  };

  const handleRemoveSpecialty = (specialty: string) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties?.filter(s => s !== specialty) || [],
    }));
  };

  const handleSubmit = async () => {
    // Validate form data
    const validationErrors = validateAttorneyData(formData);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    // Validate address if provided
    if (
      formData.address &&
      formData.city &&
      formData.state &&
      formData.zipCode
    ) {
      console.log('🔍 Validating attorney address...');
      setIsValidatingAddress(true);

      try {
        const addressValidationResult = await validateAddress({
          addressLine1: formData.address,
          city: formData.city,
          state: formData.state,
          postalCode: formData.zipCode,
        });

        if (!addressValidationResult.isValid) {
          setErrors({
            submit: `Address validation failed: ${addressValidationResult.issues.join(', ')}`,
          });
          setIsValidatingAddress(false);
          return;
        }

        console.log('✅ Attorney address validation passed');
      } catch (error) {
        console.error('❌ Attorney address validation error:', error);
        setErrors({
          submit:
            'Address validation failed. Please check the address and try again.',
        });
        setIsValidatingAddress(false);
        return;
      }
      setIsValidatingAddress(false);
    }

    try {
      if (attorney) {
        // Update existing attorney
        await onSave({ id: attorney.id, ...formData } as UpdateAttorneyRequest);
      } else {
        // Create new attorney
        await onSave(formData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving attorney:', error);
      setErrors({ submit: 'Failed to save attorney. Please try again.' });
    }
  };

  const isEditing = !!attorney;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <Scale className='h-5 w-5' />
            <span>{isEditing ? 'Edit Attorney' : 'Add New Attorney'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update attorney information and contact details.'
              : 'Add a new state-licensed attorney to the system.'}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {errors.submit && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Basic Information</CardTitle>
              <CardDescription>Required attorney details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='name'>
                    Attorney Name <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                    id='name'
                    value={formData.name}
                    onChange={e => handleFieldChange('name', e.target.value)}
                    placeholder='Full name'
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className='text-sm text-red-500'>{errors.name}</p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='firm'>Law Firm</Label>
                  <Input
                    id='firm'
                    value={formData.firm}
                    onChange={e => handleFieldChange('firm', e.target.value)}
                    placeholder='Law firm name'
                  />
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='email'>
                    Email Address <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                    id='email'
                    type='email'
                    value={formData.email}
                    onChange={e => handleFieldChange('email', e.target.value)}
                    placeholder='<EMAIL>'
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className='text-sm text-red-500'>{errors.email}</p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='phone'>
                    Phone Number <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                    id='phone'
                    value={formData.phone}
                    onChange={e => handleFieldChange('phone', e.target.value)}
                    placeholder='(*************'
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className='text-sm text-red-500'>{errors.phone}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Location</CardTitle>
              <CardDescription>Attorney's practice location</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='state'>
                    State <span className='text-red-500'>*</span>
                  </Label>
                  <Select
                    value={formData.state}
                    onValueChange={value => handleFieldChange('state', value)}
                  >
                    <SelectTrigger
                      className={errors.state ? 'border-red-500' : ''}
                    >
                      <SelectValue placeholder='Select state' />
                    </SelectTrigger>
                    <SelectContent>
                      {US_STATES.map(state => (
                        <SelectItem key={state.code} value={state.code}>
                          {state.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.state && (
                    <p className='text-sm text-red-500'>{errors.state}</p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='city'>City</Label>
                  <Input
                    id='city'
                    value={formData.city}
                    onChange={e => handleFieldChange('city', e.target.value)}
                    placeholder='City'
                  />
                </div>
              </div>

              <div className='space-y-4'>
                <StreetAutocomplete
                  label='Address'
                  value={formData.address}
                  onChange={address => handleFieldChange('address', address)}
                  onAddressSelect={components => {
                    // Parse Google Places address components
                    const addressData: any = {};

                    components.forEach((component: any) => {
                      const types = component.types;
                      if (types.includes('street_number')) {
                        addressData.streetNumber = component.long_name;
                      } else if (types.includes('route')) {
                        addressData.route = component.long_name;
                      } else if (types.includes('locality')) {
                        addressData.city = component.long_name;
                      } else if (
                        types.includes('administrative_area_level_1')
                      ) {
                        addressData.state = component.short_name;
                      } else if (types.includes('postal_code')) {
                        addressData.zip = component.long_name;
                      }
                    });

                    // Update address fields
                    const street =
                      addressData.streetNumber && addressData.route
                        ? `${addressData.streetNumber} ${addressData.route}`
                        : addressData.route ||
                          addressData.streetNumber ||
                          formData.address;

                    handleFieldChange('address', street);
                    if (addressData.city)
                      handleFieldChange('city', addressData.city);
                    if (addressData.zip)
                      handleFieldChange('zipCode', addressData.zip);
                  }}
                  placeholder='Start typing an address...'
                />

                <div className='grid grid-cols-2 gap-4'>
                  <CityAutocomplete
                    label='City'
                    value={formData.city || ''}
                    onChange={city => handleFieldChange('city', city)}
                    placeholder='Start typing a city...'
                  />
                  <ZipCodeAutocomplete
                    label='ZIP Code'
                    value={formData.zipCode || ''}
                    onChange={zipCode => handleFieldChange('zipCode', zipCode)}
                    onZipSelect={zipData => {
                      handleFieldChange('zipCode', zipData.zip);
                      if (zipData.city) handleFieldChange('city', zipData.city);
                      if (zipData.state)
                        handleFieldChange('state', zipData.state);
                    }}
                    placeholder='12345'
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>
                Professional Information
              </CardTitle>
              <CardDescription>
                Bar admission and practice details
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='barNumber'>Bar Number</Label>
                  <Input
                    id='barNumber'
                    value={formData.barNumber}
                    onChange={e =>
                      handleFieldChange('barNumber', e.target.value)
                    }
                    placeholder='State bar number'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='yearsExperience'>Years of Experience</Label>
                  <Input
                    id='yearsExperience'
                    type='number'
                    min='0'
                    value={formData.yearsExperience || ''}
                    onChange={e =>
                      handleFieldChange(
                        'yearsExperience',
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    placeholder='Years practicing law'
                    className={errors.yearsExperience ? 'border-red-500' : ''}
                  />
                  {errors.yearsExperience && (
                    <p className='text-sm text-red-500'>
                      {errors.yearsExperience}
                    </p>
                  )}
                </div>
              </div>

              {/* Specialties */}
              <div className='space-y-2'>
                <Label>Practice Areas / Specialties</Label>
                <div className='flex flex-wrap gap-2 mb-2'>
                  {formData.specialties?.map((specialty, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {specialty}
                      <X
                        className='h-3 w-3 cursor-pointer hover:text-red-500'
                        onClick={() => handleRemoveSpecialty(specialty)}
                      />
                    </Badge>
                  ))}
                </div>

                <div className='flex gap-2'>
                  <Select value={newSpecialty} onValueChange={setNewSpecialty}>
                    <SelectTrigger className='flex-1'>
                      <SelectValue placeholder='Select a specialty' />
                    </SelectTrigger>
                    <SelectContent>
                      {COMMON_SPECIALTIES.filter(
                        specialty => !formData.specialties?.includes(specialty)
                      ).map(specialty => (
                        <SelectItem key={specialty} value={specialty}>
                          {specialty}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => handleAddSpecialty(newSpecialty)}
                    disabled={
                      !newSpecialty ||
                      formData.specialties?.includes(newSpecialty)
                    }
                  >
                    Add
                  </Button>
                </div>

                <div className='flex gap-2 mt-2'>
                  <Input
                    placeholder='Or type custom specialty'
                    value={newSpecialty}
                    onChange={e => setNewSpecialty(e.target.value)}
                    onKeyPress={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddSpecialty(newSpecialty);
                      }
                    }}
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => handleAddSpecialty(newSpecialty)}
                    disabled={
                      !newSpecialty ||
                      formData.specialties?.includes(newSpecialty)
                    }
                  >
                    Add Custom
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={onClose}
            disabled={loading || isValidatingAddress}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || isValidatingAddress}
          >
            {isValidatingAddress ? (
              'Validating address...'
            ) : loading ? (
              'Saving...'
            ) : (
              <>
                <Save className='h-4 w-4 mr-2' />
                {isEditing ? 'Update Attorney' : 'Add Attorney'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
