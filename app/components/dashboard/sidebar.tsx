'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  Home,
  FileText,
  Shield,
  AlertTriangle,
  BookOpen,
  Settings,
  Users,
  GraduationCap,
  Share,
  Upload,
  Bell,
  MessageSquare,
  CheckSquare,
  FileCheck,
  Package,
  Scale,
  ClipboardList,
} from 'lucide-react';
import { useRole } from '@/lib/roles/role-context';
import routes from '@/utils/routes';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/utils/interviewV2Progress';

interface SidebarProps {
  userRole?:
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional'
    | 'Call Center'
    | 'Medical Review';
}

export function Sidebar({ userRole = 'Member' }: SidebarProps) {
  const pathname = usePathname();
  const { userContext } = useRole();

  const { data: interviewData } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
  });

  const { user, loading, userRoles, onboardingStatus, userPersonalInfo } =
    useAuth();


  // Define navigation items based on user role and context
  const getNavItems = () => {
    // For linked accounts, show limited navigation
    if (userContext?.role === 'linked_account') {
      return [
        {
          title: 'Dashboard',
          href: '/linked',
          icon: <Home className='h-5 w-5' />,
          description: 'Linked account overview',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Notifications',
          href: '/member/notifications',
          icon: <Bell className='h-5 w-5' />,
          description: 'View notifications',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Shared Documents',
          href: '/dashboard/member/shared-documents',
          icon: <Share className='h-5 w-5' />,
          description: 'View shared documents',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Emergency Access',
          href: '/emergency/documents',
          icon: <Shield className='h-5 w-5' />,
          description: 'Emergency document access',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Emergency Contacts',
          href: '/member/emergency-contacts',
          icon: <Users className='h-5 w-5' />,
          description: 'View emergency contacts',
          disabled: false,
          disabledTooltipContent: '',
        },
      ];
    }

    if (userRole === 'Member') {
      return [
        {
          title: 'My Dashboard',
          href: routes.member.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Overview & Actions',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'My Info',
          href: routes.member.interviewV2,
          icon: <ClipboardList className='h-5 w-5' />,
          description: 'Personal & Legal Details',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'My Legacy',
          href: routes.member.documents,
          icon: <FileText className='h-5 w-5' />,
          description: 'Manage Your Will, Trust & POA',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'My Care',
          href: routes.member.careDocuments,
          icon: <FileCheck className='h-5 w-5' />,
          description: 'Keep Us Up To Date',
          disabled: interviewData?.status !== 'completed',
          disabledTooltipContent: 'Please complete My Info first',
        },
        // {
        //   title: 'My Emergency Contacts',
        //   href: routes.member.emergencyContacts,
        //   icon: <Shield className='h-5 w-5' />,
        //   description: 'Trusted People',
        // },
        {
          title: 'My Wellness Checks',
          href: routes.member.wellnessChecks,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Automated Check-in Status',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Educational Content',
          href: routes.member.educationalContent,
          icon: <GraduationCap className='h-5 w-5' />,
          description: 'Videos, Articles & Guides',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Shipping & Tracking',
          href: routes.member.shipping,
          icon: <Package className='h-5 w-5' />,
          description: 'Save Address & Track Packages',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Settings',
          href: routes.member.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'Account Settings',
          disabled: false,
          disabledTooltipContent: '',
        },
      ];
    }

    if (userRole === 'Administrator') {
      return [
        {
          title: 'Dashboard',
          href: routes.admin.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Admin overview',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Users',
          href: routes.admin.users,
          icon: <Users className='h-5 w-5' />,
          description: 'User management',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Content Management',
          href: routes.admin.content,
          icon: <BookOpen className='h-5 w-5' />,
          description: 'Educational content',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Emergency Management',
          href: routes.admin.emergency,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'DMS & emergency access',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Templates',
          href: routes.admin.templates,
          icon: <FileText className='h-5 w-5' />,
          description: 'Document templates',
          disabled: false,
          disabledTooltipContent: '',
        },
        // {
        //   title: 'Interview Builder',
        //   href: routes.admin.interviewBuilder,
        //   icon: <MessageSquare className='h-5 w-5' />,
        //   description: 'Create & manage interviews',
        // },
        // {
        //   title: 'Care Documents Builder',
        //   href: routes.admin.careDocumentsBuilder,
        //   icon: <CheckSquare className='h-5 w-5' />,
        //   description: 'Care Documents Builder',
        // },
        {
          title: 'Attorneys',
          href: routes.admin.attorneys,
          icon: <Scale className='h-5 w-5' />,
          description: 'Manage attorneys',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Settings',
          href: routes.admin.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'System settings',
          disabled: false,
          disabledTooltipContent: '',
        },
      ];
    }

    if (userRole === 'Welon Trust') {
      return [
        {
          title: 'Dashboard',
          href: routes.welon.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Welon Trust overview',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Document Management',
          href: routes.welon.documents,
          icon: <FileText className='h-5 w-5' />,
          description: 'View & manage all documents',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Emergency',
          href: routes.welon.emergency,
          icon: <Users className='h-5 w-5' />,
          description: 'Emergency, Wellness Checks, etc',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Document Upload',
          href: routes.welon.uploadDocuments,
          icon: <Upload className='h-5 w-5' />,
          description: 'Upload signed documents',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Submit Evidence',
          href: routes.welon.submitEvidence,
          icon: <Shield className='h-5 w-5' />,
          description: 'Evidence submission',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Shipping & Tracking',
          href: routes.welon.shipping,
          icon: <Package className='h-5 w-5' />,
          description: 'Save address & track packages',
          disabled: false,
          disabledTooltipContent: '',
        },
        {
          title: 'Settings',
          href: routes.welon.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'Account settings',
          disabled: false,
          disabledTooltipContent: '',
        },
      ];
    }

    if (userRole === 'Call Center') {
      return [
        // {
        //   title: 'Dashboard',
        //   href: routes.callCenter.dashboard,
        //   icon: <Home className='h-5 w-5' />,
        //   description: 'Overview',
        //   disabled: false,
        //   disabledTooltipContent: '',
        // },
         {
          title: 'Medical Incident',
          href: routes.callCenter.medicalIncident,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Medical incident reporting',
          disabled: false,
          disabledTooltipContent: '',
        },
        // {
        //   title: 'Settings',
        //   href: routes.callCenter.settings,
        //   icon: <Settings className='h-5 w-5' />,
        //   description: 'Account settings',
        //   disabled: false,
        //   disabledTooltipContent: '',
        // },
      ];
    }

    if (userRole === 'Medical Review') {
      return [
        // {
        //   title: 'Dashboard',
        //   href: routes.medical.dashboard,
        //   icon: <Home className='h-5 w-5' />,
        //   description: 'Overview',
        //   disabled: false,
        //   disabledTooltipContent: '',
        // },
        {
          title: 'Medical Review',
          href: routes.medical.medicalReview,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Medical review',
          disabled: false,
          disabledTooltipContent: '',
        },
      ];
    }

    return [];
  };

  const navItems = getNavItems();

  const getActiveItemHref = () => {
    if (pathname.includes('/dashboard/member/documents')) {
      const documentsItem = navItems.find(item => item.title === 'Documents');
      if (documentsItem) return documentsItem.href;
    }

    const matchingItems = navItems.filter(
      item => pathname === item.href || pathname.startsWith(`${item.href}/`)
    );

    if (matchingItems.length === 0) return null;
    if (matchingItems.length === 1) return matchingItems[0].href;

    const bestMatch = matchingItems.reduce((longest, current) =>
      current.href.length > longest.href.length ? current : longest
    );

    return bestMatch.href;
  };

  const activeItemHref = getActiveItemHref();

  // Get display title based on role context
  const getDisplayTitle = () => {
    if (userContext?.role === 'linked_account') {
      return `${userContext.displayName}`;
    }
    if (userRole === 'Administrator') {
      return '';
    }
    if (userRole === 'Call Center') {
      return '';
    }
    if (userRole === 'Medical Review') {
      return '';
    }
    return `${userRole} Dashboard`;
  };

  return (
    <aside
      className={`sticky top-16 w-64 h-[calc(100vh-4rem)] border-r border-gray-200 shadow-sm overflow-y-auto ${isWelonTrust(userRoles) ? 'bg-[var(--background-welon-trust)]' : ''} ${isAdmin(userRoles) ? 'bg-[var(--background-admin)]' : ''} ${!isAdmin(userRoles) && !isWelonTrust(userRoles) ? 'bg-background' : ''}`}
    >
      <div className='p-6'>
        {getDisplayTitle() && (
          <h2 className='text-lg font-semibold mb-6'>{getDisplayTitle()}</h2>
        )}

        <nav className='space-y-2'>
          {navItems.map(item => {
            const isActive = activeItemHref === item.href;

            return (
              <Link
                key={item.href}
                href={item.disabled ? '#' : item.href}
                aria-disabled={item.disabled}
                title={item.disabled ? item.disabledTooltipContent : undefined}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors group',
                  isActive
                    ? 'bg-[var(--eggplant)]/10 text-[var(--eggplant)] shadow-[inset_4px_0_0_var(--eggplant)]'
                    : 'hover:bg-gray-100 hover:text-[var(--eggplant)]',
                  item.disabled && 'opacity-50 cursor-not-allowed'
                )}
              >
                <span
                  className={cn(
                    'mr-3',
                    isActive
                      ? 'text-[var(--eggplant)]'
                      : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]'
                  )}
                >
                  {item.icon}
                </span>
                <div className='flex-1'>
                  <div className='font-medium'>{item.title}</div>
                  <div
                    className={cn(
                      'text-xs mt-0.5',
                      isActive
                        ? 'text-[var(--eggplant)]/70'
                        : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]/70'
                    )}
                  >
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
