'use client'

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

const FormSection: React.FC<{
  title: string;
  description?: string;
  children: React.ReactNode;
  id?: string;
  showAlertBanner?: boolean;
  alertBannerContent?: string;
  disabled?: boolean;
}> = ({ title, description, children, id, showAlertBanner, alertBannerContent, disabled }) => (
  <Card
    id={id}
    className={`border-2 border-gray-200 rounded-2xl bg-white/80 backdrop-blur-md shadow-xl overflow-hidden mb-8 ${disabled ? 'opacity-70 pointer-events-none' : ''}`}
  >
    <CardHeader className="bg-gray-50 p-6 border-b">
      <CardTitle className="text-2xl font-bold text-gray-900">{title}</CardTitle>
      {description && (
        <CardDescription className="text-md text-gray-600 mt-2">
          {description}
        </CardDescription>
      )}
      {showAlertBanner && alertBannerContent && <Alert variant='destructive'>
        <AlertTriangle className='h-4 w-4' />
        <AlertDescription>
          {alertBannerContent}
        </AlertDescription>
      </Alert>}
    </CardHeader>
    <CardContent className="p-6 space-y-8">{children}</CardContent>
  </Card>
);

export default FormSection;