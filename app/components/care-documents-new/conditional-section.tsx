'use client'
import { Label } from '@/components/ui/label';
import RadioGroup from './radio-group';

const ConditionalSection: React.FC<{
  label: string;
  value: boolean | null;
  onChange: (val: boolean) => void;
  children: React.ReactNode;
  required?: boolean;
  description?: string;
}> = ({ label, value, onChange, children, required, description }) => (
  <div className="col-span-full p-6 bg-gray-50/80 rounded-xl border border-gray-200 space-y-4">
    <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
      <Label className="text-base font-semibold text-gray-800 flex items-center">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <RadioGroup value={value} onChange={onChange} />
    </div>
    {description && <p className="text-sm text-gray-500 pt-2">{description}</p>}
    {value === true && (
      <div className="pt-6 border-t border-gray-200 space-y-6">{children}</div>
    )}
  </div>
);

export default ConditionalSection;