'use client'

import { Button } from '@/components/ui/button';
import {
  Upload,
  FileText,
} from 'lucide-react';

const FileUploadSection: React.FC<{
  title?: string;
  description?: string;
}> = ({
  title = "Upload Files",
  description = "Attach relevant documents here (e.g., PDFs, images).",
}) => (
  <div className="col-span-full p-6 border-2 border-dashed border-gray-300 rounded-2xl text-center space-y-4 bg-gray-50">
    <div className="mx-auto w-16 h-16 flex items-center justify-center bg-white rounded-full shadow-md">
      <Upload className="h-8 w-8" />
    </div>
    <h3 className="font-semibold text-lg text-gray-800">{title}</h3>
    <p className="text-sm text-gray-500">{description}</p>
    <Button
      variant="outline"
      className="rounded-xl"
      onClick={() => console.log('File upload clicked')}
    >
      <FileText className="mr-2 h-4 w-4" />
      Select Files
    </Button>
  </div>
);

export default FileUploadSection;