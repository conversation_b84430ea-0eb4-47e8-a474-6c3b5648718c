'use client'

import { useState, useEffect } from "react";
import FormSection from "../form-section";
import SubSection from "../sub-section";
import <PERSON><PERSON>ield from "../form-field";
import Text<PERSON>reaField from "../text-area-field";
import FileUploadSection from "../file-upload-section";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from "lucide-react";
import { useCareDocumentsContext } from "@/app/context/CareDocumentsContext";
import { useLinkedAccountPrefill } from "@/hooks/useLinkedAccountPrefill";

interface AssetInformationSectionProps {
  disabled?: boolean;
}

const AssetInformationSection: React.FC<AssetInformationSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    properties: [] as Array<{
      address1: string;
      address2: string;
      city: string;
      state: string;
      zip: string;
      type: string;
      ownership: string;
      notes: string;
    }>,
    utilities: [] as Array<{
      name: string;
      type: string;
      account: string;
      contact: string;
      notes: string;
    }>,
    hoa: {} as {
      name: string;
      contact: string;
      website: string;
      fees: string;
      notes: string;
    },
    otherProperties: [] as Array<{
      type: string;
      description: string;
      location: string;
      value: string;
      notes: string;
    }>,
    safeDeposit: {} as {
      institution: string;
      location: string;
      boxNumber: string;
      keyLocation: string;
      authorizedPersons: string;
      contents: string;
    },
    additionalInstructions: ""
  });

  useLinkedAccountPrefill('AssetInformation', sectionData.AssetInformation, setData)

  // Load data from context when available
  useEffect(() => {
    if (sectionData.AssetInformation) {
      setData(sectionData.AssetInformation);
    }
  }, [sectionData.AssetInformation]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('AssetInformation', newData);
    console.log(`Updated ${field}:`, value);
  };

  const addProperty = () => {
    update("properties", [...data.properties, {
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      type: "",
      ownership: "",
      notes: ""
    }]);
  };

  const removeProperty = (index: number) => {
    update(
      "properties",
      data.properties.filter((_, i) => i !== index)
    );
  };

  const updateProperty = (index: number, field: string, value: any) => {
    const newProperties = [...data.properties];
    newProperties[index] = { ...newProperties[index], [field]: value };
    update("properties", newProperties);
  };

  const addUtility = () => {
    update("utilities", [...data.utilities, {
      name: "",
      type: "",
      account: "",
      contact: "",
      notes: ""
    }]);
  };

  const removeUtility = (index: number) => {
    update(
      "utilities",
      data.utilities.filter((_, i) => i !== index)
    );
  };

  const updateUtility = (index: number, field: string, value: any) => {
    const newUtilities = [...data.utilities];
    newUtilities[index] = { ...newUtilities[index], [field]: value };
    update("utilities", newUtilities);
  };

  const updateHOA = (field: string, value: any) => {
    update("hoa", { ...data.hoa, [field]: value });
  };

  const addOtherProperty = () => {
    update("otherProperties", [...data.otherProperties, {
      type: "",
      description: "",
      location: "",
      value: "",
      notes: ""
    }]);
  };

  const removeOtherProperty = (index: number) => {
    update(
      "otherProperties",
      data.otherProperties.filter((_, i) => i !== index)
    );
  };

  const updateOtherProperty = (index: number, field: string, value: any) => {
    const newOther = [...data.otherProperties];
    newOther[index] = { ...newOther[index], [field]: value };
    update("otherProperties", newOther);
  };

  const updateSafeDeposit = (field: string, value: any) => {
    update("safeDeposit", { ...data.safeDeposit, [field]: value });
  };

  const propertyTypes = [
    "Personal Residence",
    "Additional Property",
    "Long Term Rental",
    "Short Term Rental",
    "Commercial Property - For My Business",
    "Commercial Property - That I Rent to a Business",
  ];

  const utilityTypes = [
    "Electric",
    "Gas",
    "Water",
    "Sewer",
    "Trash",
    "Internet",
    "Cable/Satellite",
    "Phone",
    "Other"
  ];

  const otherPropertyTypes = [
    "Vehicle",
    "Boat",
    "RV",
    "Artwork",
    "Collectibles",
    "Jewelry",
    "Other Valuables"
  ];

  return (
    <FormSection
      title="Asset Information - Real Estate & Others"
      description="This section will cover all physical real estate property information whether you own or rent."
      disabled={disabled}
    >
      <SubSection title="Property Information">
        {data.properties.length === 0 && (
          <div className="flex justify-center my-4">
            <Button
              type="button"
              onClick={addProperty}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Button>
          </div>
        )}

        {data.properties.map((prop, index) => (
          <div key={index} className="space-y-4 border p-4 rounded-xl mb-4">
            <div className="flex justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeProperty(index)}
                className="h-8 w-8 text-red-500"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <FormField
                  label="Address Line 1"
                  value={prop.address1}
                  onChange={(e) =>
                    updateProperty(index, "address1", e.target.value)
                  }
                />
                <FormField
                  label="Address Line 2"
                  value={prop.address2}
                  onChange={(e) =>
                    updateProperty(index, "address2", e.target.value)
                  }
                />
                <FormField
                  label="City"
                  value={prop.city}
                  onChange={(e) => updateProperty(index, "city", e.target.value)}
                />
                <FormField
                  label="State"
                  value={prop.state}
                  onChange={(e) => updateProperty(index, "state", e.target.value)}
                />
                <FormField
                  label="Zip Code"
                  value={prop.zip}
                  onChange={(e) => updateProperty(index, "zip", e.target.value)}
                />
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Property Type</Label>
                  <Select
                    value={prop.type}
                    onValueChange={(value) => updateProperty(index, "type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <FormField
                  label="Ownership Information"
                  value={prop.ownership}
                  onChange={(e) =>
                    updateProperty(index, "ownership", e.target.value)
                  }
                  placeholder="Sole owner, joint tenancy, etc."
                />

                <TextAreaField
                  label="Additional Notes"
                  value={prop.notes}
                  onChange={(e) =>
                    updateProperty(index, "notes", e.target.value)
                  }
                  placeholder="Any additional information about this property"
                />
              </div>
            </div>

            {index === data.properties.length - 1 && (
              <div className="flex justify-center mt-4">
                <Button
                  type="button"
                  onClick={addProperty}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Property
                </Button>
              </div>
            )}
          </div>
        ))}
      </SubSection>

      <SubSection title="Utilities">
        {data.utilities.length === 0 && (
          <div className="flex justify-center my-4">
            <Button
              type="button"
              onClick={addUtility}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Utility
            </Button>
          </div>
        )}

        {data.utilities.map((utility, index) => (
          <div key={index} className="space-y-4 border p-4 rounded-xl mb-4">
            <div className="flex justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeUtility(index)}
                className="h-8 w-8 text-red-500"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <FormField
                  label="Utility Name"
                  value={utility.name}
                  onChange={(e) =>
                    updateUtility(index, "name", e.target.value)
                  }
                />

                <div className="space-y-2">
                  <Label>Utility Type</Label>
                  <Select
                    value={utility.type}
                    onValueChange={(value) => updateUtility(index, "type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select utility type" />
                    </SelectTrigger>
                    <SelectContent>
                      {utilityTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <FormField
                  label="Account Number"
                  value={utility.account}
                  onChange={(e) =>
                    updateUtility(index, "account", e.target.value)
                  }
                />

                <FormField
                  label="Contact Information"
                  value={utility.contact}
                  onChange={(e) =>
                    updateUtility(index, "contact", e.target.value)
                  }
                  placeholder="Phone number, website, etc."
                />

                <TextAreaField
                  label="Additional Notes"
                  value={utility.notes}
                  onChange={(e) =>
                    updateUtility(index, "notes", e.target.value)
                  }
                />
              </div>
            </div>

            {index === data.utilities.length - 1 && (
              <div className="flex justify-center mt-4">
                <Button
                  type="button"
                  onClick={addUtility}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Utility
                </Button>
              </div>
            )}
          </div>
        ))}
      </SubSection>

      <SubSection title="Homeowners Association (HOA)">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="HOA Name"
            value={data.hoa?.name || ""}
            onChange={(e) => updateHOA("name", e.target.value)}
          />

          <FormField
            label="HOA Contact Information"
            value={data.hoa?.contact || ""}
            onChange={(e) => updateHOA("contact", e.target.value)}
            placeholder="Phone number, email, etc."
          />

          <FormField
            label="HOA Website"
            value={data.hoa?.website || ""}
            onChange={(e) => updateHOA("website", e.target.value)}
          />

          <FormField
            label="HOA Fees"
            value={data.hoa?.fees || ""}
            onChange={(e) => updateHOA("fees", e.target.value)}
            placeholder="Amount and frequency"
          />

          <TextAreaField
            label="Additional HOA Notes"
            value={data.hoa?.notes || ""}
            onChange={(e) => updateHOA("notes", e.target.value)}
            placeholder="Rules, restrictions, etc."
          />
        </div>
      </SubSection>

      <SubSection title="Other Valuable Property">
        {data.otherProperties.length === 0 && (
          <div className="flex justify-center my-4">
            <Button
              type="button"
              onClick={addOtherProperty}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Valuable Property
            </Button>
          </div>
        )}

        {data.otherProperties.map((item, index) => (
          <div key={index} className="space-y-4 border p-4 rounded-xl mb-4">
            <div className="flex justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeOtherProperty(index)}
                className="h-8 w-8 text-red-500"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Property Type</Label>
                <Select
                  value={item.type}
                  onValueChange={(value) => updateOtherProperty(index, "type", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select property type" />
                  </SelectTrigger>
                  <SelectContent>
                    {otherPropertyTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <FormField
                label="Description"
                value={item.description}
                onChange={(e) =>
                  updateOtherProperty(index, "description", e.target.value)
                }
                placeholder="Make, model, year, etc."
              />

              <FormField
                label="Location"
                value={item.location}
                onChange={(e) =>
                  updateOtherProperty(index, "location", e.target.value)
                }
                placeholder="Where this property is stored or located"
              />

              <FormField
                label="Estimated Value"
                value={item.value}
                onChange={(e) =>
                  updateOtherProperty(index, "value", e.target.value)
                }
              />

              <TextAreaField
                label="Additional Notes"
                value={item.notes}
                onChange={(e) =>
                  updateOtherProperty(index, "notes", e.target.value)
                }
                placeholder="Insurance information, maintenance details, etc."
              />
            </div>

            {index === data.otherProperties.length - 1 && (
              <div className="flex justify-center mt-4">
                <Button
                  type="button"
                  onClick={addOtherProperty}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Valuable Property
                </Button>
              </div>
            )}
          </div>
        ))}
      </SubSection>

      <SubSection title="Safe Deposit Box">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Financial Institution"
            value={data.safeDeposit?.institution || ""}
            onChange={(e) => updateSafeDeposit("institution", e.target.value)}
          />

          <FormField
            label="Branch Location"
            value={data.safeDeposit?.location || ""}
            onChange={(e) => updateSafeDeposit("location", e.target.value)}
          />

          <FormField
            label="Box Number"
            value={data.safeDeposit?.boxNumber || ""}
            onChange={(e) => updateSafeDeposit("boxNumber", e.target.value)}
          />

          <FormField
            label="Key Location"
            value={data.safeDeposit?.keyLocation || ""}
            onChange={(e) => updateSafeDeposit("keyLocation", e.target.value)}
            placeholder="Where the key is stored"
          />

          <TextAreaField
            label="Authorized Persons"
            value={data.safeDeposit?.authorizedPersons || ""}
            onChange={(e) => updateSafeDeposit("authorizedPersons", e.target.value)}
            placeholder="Names of people who have access"
          />

          <TextAreaField
            label="Contents"
            value={data.safeDeposit?.contents || ""}
            onChange={(e) => updateSafeDeposit("contents", e.target.value)}
            placeholder="List of items stored in the safe deposit box"
          />
        </div>
      </SubSection>

      <TextAreaField
        label="Additional Asset Instructions"
        value={data.additionalInstructions}
        onChange={(e) => update("additionalInstructions", e.target.value)}
        placeholder="Any additional information about your assets"
      />

      {/* <FileUploadSection title="Upload Asset Documents" description="Upload deeds, titles, or other asset-related documents." /> */}
    </FormSection>
  );
};

export default AssetInformationSection;