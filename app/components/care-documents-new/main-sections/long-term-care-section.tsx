'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import Text<PERSON>rea<PERSON>ield from '../text-area-field';
import CheckboxGroup from '../checkbox-group';
import ConditionalSection from '../conditional-section';
import <PERSON><PERSON>ield from '../form-field';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

// Define blank LTC contact template
const BLANK_LTC_CONTACT = {
  name: '',
  phone: '',
  email: '',
  role: '',
};

interface LongTermCareSectionProps {
  disabled?: boolean;
}

const LongTermCareSection: React.FC<LongTermCareSectionProps> = ({ disabled}) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    preferredLivingArrangement: '',
    carePreferences: [] as string[],
    facilityPreferences: '',
    homeCarePreferences: '',
    financialConsiderations: '',
    additionalInstructions: '',
    hasWorkedWithLTC: null as boolean | null,
    ltcContacts: [] as (typeof BLANK_LTC_CONTACT)[],
    funding: [] as string[],
    fundingExplanation: '',
    careLocation: '',
    facilityImportance: '',
    carePhilosophy: '',
    careImportant: '',
    otherLTC: '',
  });

  useLinkedAccountPrefill('LongTermCare', sectionData.LongTermCare, setData);

  useEffect(() => {
    if (sectionData.LongTermCare) {
      setData(sectionData.LongTermCare);
    }
  }, [sectionData.LongTermCare]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('LongTermCare', newData);
    console.log(`Updated ${field}:`, value);
  };

  // Functions for managing LTC contacts
  const addContact = () => {
    update('ltcContacts', [...data.ltcContacts, BLANK_LTC_CONTACT]);
  };

  const removeContact = (index: number) => {
    update(
      'ltcContacts',
      data.ltcContacts.filter((_, i) => i !== index)
    );
  };

  const updateContact = (index: number, field: string, value: string) => {
    const newContacts = [...data.ltcContacts];
    newContacts[index] = { ...newContacts[index], [field]: value };
    update('ltcContacts', newContacts);
  };

  // Toggle functions for checkboxes
  const toggleCare = (pref: string) => {
    update(
      'carePreferences',
      data.carePreferences.includes(pref)
        ? data.carePreferences.filter(p => p !== pref)
        : [...data.carePreferences, pref]
    );
  };

  const toggleFunding = (fund: string) => {
    update(
      'funding',
      data.funding.includes(fund)
        ? data.funding.filter(f => f !== fund)
        : [...data.funding, fund]
    );
  };

  const careOptions = [
    'Continuing Care Retirement Community (CCRC)',
    'Adult Day Care',
    'In-home Care',
    'Independent Living',
    'Assisted Living',
    'Skilled Care Nursing',
  ];

  const fundingOptions = [
    'I have money set aside for long-term care. (Please explain where below.)',
    'I plan to go on Medicaid (Remember this requires you to spend your assets down.)',
    'I have long-term care insurance. (Please share in the insurance section.)',
    'I have already paid for care in a facility. (Please upload a contract at the bottom of this section.)',
    "I haven't given much thought to how this will be paid for.",
  ];

  return (
    <FormSection
      title='Long-Term Care Preferences'
      description='Please provide your preferences for long-term care if needed.'
      disabled={disabled}
    >
      <SubSection title='Care Preferences'>
        <div className='space-y-2'>
          <Label className='text-sm font-medium text-gray-700'>
            Preferred Living Arrangement
          </Label>
          <Select
            value={data.preferredLivingArrangement}
            onValueChange={value => update('preferredLivingArrangement', value)}
          >
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Select your preference' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='home'>
                Remain at home with assistance
              </SelectItem>
              <SelectItem value='family'>Live with family member</SelectItem>
              <SelectItem value='assisted'>Assisted living facility</SelectItem>
              <SelectItem value='nursing'>Nursing home</SelectItem>
              <SelectItem value='ccrc'>
                Continuing care retirement community
              </SelectItem>
              <SelectItem value='other'>Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <ConditionalSection
          label='Have you already worked with someone on your long-term care wishes such as an Aging Care Manager?'
          value={data.hasWorkedWithLTC}
          onChange={val => {
            update('hasWorkedWithLTC', val);
            if (val && data.ltcContacts.length === 0) {
              addContact();
            }
          }}
        >
          {data.ltcContacts.map((contact, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Name'
                value={contact.name}
                onChange={e => updateContact(index, 'name', e.target.value)}
              />
              <FormField
                label='Phone Number'
                value={contact.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateContact(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={contact.email}
                onChange={e => updateContact(index, 'email', e.target.value)}
                type='email'
              />
              <FormField
                label='Role'
                value={contact.role}
                onChange={e => updateContact(index, 'role', e.target.value)}
              />
              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.ltcContacts.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeContact(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.ltcContacts.length - 1 && (
                  <Button
                    type='button'
                    onClick={addContact}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Another Contact
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <CheckboxGroup
          label='What is your preference for aging care? Please select all that apply.'
          options={careOptions}
          selected={data.carePreferences}
          onChange={toggleCare}
        />

        <CheckboxGroup
          label='Do you have money set aside for long-term care or long-term care insurance? Please select all that apply.'
          options={fundingOptions}
          selected={data.funding}
          onChange={toggleFunding}
        />

        <TextAreaField
          label='If you have money set aside, please explain where below.'
          value={data.fundingExplanation}
          onChange={e => update('fundingExplanation', e.target.value)}
        />

        <TextAreaField
          label='Where would you like to go into care? (City, State, or Regional Preference)'
          value={data.careLocation}
          onChange={e => update('careLocation', e.target.value)}
        />

        <TextAreaField
          label='What is important to you when it comes to aging care facilities?'
          value={data.facilityImportance}
          onChange={e => update('facilityImportance', e.target.value)}
          placeholder="Please explain if they're important to you & what your preference is regarding them."
        />

        <TextAreaField
          label='Is there a philosophy of care that you want the facility to ascribe to?'
          value={data.carePhilosophy}
          onChange={e => update('carePhilosophy', e.target.value)}
        />

        <TextAreaField
          label="Is there anything that's important to you that you want considered in your care?"
          value={data.careImportant}
          onChange={e => update('careImportant', e.target.value)}
          placeholder='For example, is there a favorite food you want brought in for you?'
        />

        <TextAreaField
          label='Please share anything else that may be helpful.'
          value={data.otherLTC}
          onChange={e => update('otherLTC', e.target.value)}
        />
      </SubSection>
    </FormSection>
  );
};

export default LongTermCareSection;
