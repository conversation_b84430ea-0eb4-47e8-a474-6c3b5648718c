'use client'

import { useState, useEffect } from "react";
import FormSection from "../form-section";
import SubSection from "../sub-section";
import <PERSON><PERSON>ield from "../form-field";
import Text<PERSON>reaField from "../text-area-field";
import FileUploadSection from "../file-upload-section";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from "lucide-react";
import { useCareDocumentsContext } from "@/app/context/CareDocumentsContext";
import { useInterviewPrefill } from "@/hooks/useInterviewPrefill";
import { useLinkedAccountPrefill } from "@/hooks/useLinkedAccountPrefill";
import { useQuery } from "@tanstack/react-query";
import { loadInterviewProgressV2 } from "@/utils/interviewV2Progress";

type Recipient = {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  id: string;
  type: string;
};

type Property = {
  address: string;
  ownership: number;
  recipient: Recipient;
  id: string;
  type: string;
  title: string;
};

const propertyTypes = [
    "Primary Residence",
    "Secondary Residence",
    "Rental Property",
    "Commercial Property",
    "Land",
    "Other"
  ];

// PropertySelect component for selecting properties from interview data
const PropertySelect = ({ onSelect }: { onSelect: (property: any) => void }) => {

  const { data: interviewData } = useQuery({
      queryKey: ['interviewV2Progress'],
      queryFn: loadInterviewProgressV2,
    });
  
  // Get properties from interview data if available
  const interviewProperties = interviewData?.stepsData?.afterYouDie?.properties || [];
  
  if (interviewProperties.length === 0) {
    return null; // Don't render the select if no properties available
  }
  
  return (
    <div className="space-y-2 mb-6">
      <Label className="text-sm font-medium text-gray-700">
        Select from existing properties
      </Label>
      <Select onValueChange={(value) => {
        const selectedProperty = interviewProperties.find((p: Property) => p.id === value);
        if (selectedProperty) {
          onSelect(selectedProperty);
        }
      }}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Choose a property from your interview" />
        </SelectTrigger>
        <SelectContent>
          {interviewProperties.map((property: Property) => (
            <SelectItem key={property.id} value={property.id}>
              {property.type} - {property.address}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

interface PropertySectionProps {
  disabled?: boolean;
}

const PropertySection: React.FC<PropertySectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    properties: [] as Array<{
      type: string;
      address: string;
      ownership: string;
      mortgage: string;
      lender: string;
      insuranceInfo: string;
      notes: string;
    }>,
    additionalInstructions: ""
  });

  useLinkedAccountPrefill('Property', sectionData.Medical, setData);
  // useInterviewPrefill('Property', sectionData.Property, setData);

  useEffect(() => {
    if (sectionData.Property) { // Replace SectionType with the actual section type
      setData(sectionData.Property);
    }
  }, [sectionData.Property]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Property', newData);
    console.log(`Updated ${field}:`, value);
  };

  const updateProperty = (index: number, field: string, value: string) => {
    const newProperties = [...data.properties];
    newProperties[index] = { ...newProperties[index], [field]: value };
    update("properties", newProperties);
  };

  const updatePropertyWithValues = (index: number, updates: Record<string, any>) => {
    const newContacts = [...data.properties];
    newContacts[index] = { ...newContacts[index], ...updates };
    update("properties", newContacts);
  };

  const addProperty = () => {
    update("properties", [
      ...data.properties,
      {
        type: "",
        address: "",
        ownership: "",
        mortgage: "",
        lender: "",
        insuranceInfo: "",
        notes: ""
      },
    ]);
  };

  const removeProperty = (index: number) => {
    update(
      "properties",
      data.properties.filter((_, i) => i !== index)
    );
  };

  // Handle property selection from interview data
  const handlePropertySelect = (index: number, selectedProperty: any) => {
    // Map the interview property data to the format expected by the form
    const newProperty = {
      type: selectedProperty.type && propertyTypes.includes(selectedProperty.type) ? selectedProperty.type : "Other",
      address: selectedProperty.address || "",
      ownership: selectedProperty.ownership ? `${selectedProperty.ownership}%` : "",
      mortgage: "", // Not available in interview data
      lender: "", // Not available in interview data
      insuranceInfo: "", // Not available in interview data
      notes: selectedProperty.title ? `Title: ${selectedProperty.title}` : ""
    };
    updatePropertyWithValues(index, newProperty);
  };

  return (
    <FormSection
      title="Property Information"
      description="Please provide information about your real estate and other significant property."
      disabled={disabled}
    >
      <SubSection title="Real Estate Properties">
        <Button
          type="button"
          onClick={() => {
            if (data.properties.length === 0) {
              addProperty();
            }
          }}
          className="flex items-center gap-2 mb-6"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Property
        </Button>

        {data.properties.map((property, index) => (
          <div key={index} className="space-y-6 mb-6">
            {index > 0 && <div className="border-t border-gray-200 pt-6"></div>}
             <PropertySelect onSelect={(selectedProperty) => handlePropertySelect(index, selectedProperty)} />
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Property Type
              </Label>
              <Select
                value={property.type}
                onValueChange={(value) => updateProperty(index, "type", value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select property type" />
                </SelectTrigger>
                <SelectContent>
                  {propertyTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <FormField
              label="Property Address"
              value={property.address}
              onChange={(e) =>
                updateProperty(index, "address", e.target.value)
              }
            />
            
            <FormField
              label="Ownership Information"
              value={property.ownership}
              onChange={(e) =>
                updateProperty(index, "ownership", e.target.value)
              }
              placeholder="Sole owner, joint tenancy, etc."
            />
            
            <TextAreaField
              label="Mortgage Information"
              value={property.mortgage}
              onChange={(e) =>
                updateProperty(index, "mortgage", e.target.value)
              }
              placeholder="Mortgage amount, account number, etc."
            />
            
            <FormField
              label="Mortgage Lender"
              value={property.lender}
              onChange={(e) =>
                updateProperty(index, "lender", e.target.value)
              }
            />
            
            <FormField
              label="Property Insurance Information"
              value={property.insuranceInfo}
              onChange={(e) =>
                updateProperty(index, "insuranceInfo", e.target.value)
              }
              placeholder="Insurance company, policy number, etc."
            />
            
            <TextAreaField
              label="Additional Notes"
              value={property.notes}
              onChange={(e) =>
                updateProperty(index, "notes", e.target.value)
              }
            />
            
            <div className="flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                className="text-red-600 hover:text-red-700"
                onClick={() => removeProperty(index)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove
              </Button>
              {index === data.properties.length - 1 && (
                <Button
                  type="button"
                  onClick={addProperty}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Property
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      <TextAreaField
        label="Additional Property Instructions"
        value={data.additionalInstructions}
        onChange={(e) => update("additionalInstructions", e.target.value)}
        placeholder="Any additional property information or instructions"
      />

      {/* <FileUploadSection title="Upload Property Documents" description="Upload deeds, titles, or other property-related documents." /> */}
    </FormSection>
  );
};

export default PropertySection;