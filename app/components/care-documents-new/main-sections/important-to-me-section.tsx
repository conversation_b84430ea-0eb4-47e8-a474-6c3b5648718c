'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import <PERSON><PERSON><PERSON><PERSON>ield from '../text-area-field';
import <PERSON>Field from '../form-field';
import ConditionalSection from '../conditional-section';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

// Define blank contact template
const BLANK_IMPORTANT_CONTACT = {
  name: '',
  phone: '',
  email: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
  shouldContact: null as boolean | 'no_pref' | null,
  contactTiming: '',
  notes: '',
};

// Define blank organization template
const BLANK_ORGANIZATION = {
  name: '',
  phone: '',
  email: '',
  website: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
  shouldContact: null as boolean | 'no_pref' | null,
  contactTiming: '',
  notes: '',
};

interface ImportantToMeSectionProps {
  disabled?: boolean;
}

const ImportantToMeSection: React.FC<ImportantToMeSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    importantValues: '',
    importantPeople: '',
    importantPlaces: '',
    importantThings: '',
    importantExperiences: '',
    additionalInstructions: '',
    importantContacts: [] as (typeof BLANK_IMPORTANT_CONTACT)[],
    organizations: [] as (typeof BLANK_ORGANIZATION)[],
  });

  useLinkedAccountPrefill('ImportantToMe', sectionData.ImportantToMe, setData);

  useEffect(() => {
    if (sectionData.ImportantToMe) {
      setData(sectionData.ImportantToMe);
    }
  }, [sectionData.ImportantToMe]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('ImportantToMe', newData);
    console.log(`Updated ${field}:`, value);
  };

  // Contact management functions
  const addContact = () => {
    update('importantContacts', [
      ...data.importantContacts,
      BLANK_IMPORTANT_CONTACT,
    ]);
  };

  const removeContact = (index: number) => {
    update(
      'importantContacts',
      data.importantContacts.filter((_, i) => i !== index)
    );
  };

  const updateContact = (index: number, field: string, value: any) => {
    const newContacts = [...data.importantContacts];
    newContacts[index] = { ...newContacts[index], [field]: value };
    update('importantContacts', newContacts);
  };

  // Organization management functions
  const addOrganization = () => {
    update('organizations', [...data.organizations, BLANK_ORGANIZATION]);
  };

  const removeOrganization = (index: number) => {
    update(
      'organizations',
      data.organizations.filter((_, i) => i !== index)
    );
  };

  const updateOrganization = (index: number, field: string, value: any) => {
    const newOrganizations = [...data.organizations];
    newOrganizations[index] = { ...newOrganizations[index], [field]: value };
    update('organizations', newOrganizations);
  };

  const contactTimingOptions = [
    { label: 'While Living', value: 'living' },
    { label: 'After my passing', value: 'passing' },
    { label: "I don't have a preference", value: 'no_pref' },
  ];

  const shouldContactOptions = [
    { label: 'Yes', value: true },
    { label: 'No', value: false },
    { label: "I don't have a preference", value: 'no_pref' },
  ];

  return (
    <FormSection
      title='Important To Me'
      description='Please share what matters most to you in your life.'
      disabled={disabled}
    >
      <SubSection title='Values & Priorities'>
        <TextAreaField
          label='What values and principles are most important to you?'
          value={data.importantValues}
          onChange={e => update('importantValues', e.target.value)}
          placeholder='Examples: independence, creativity, honesty, etc.'
        />

        <TextAreaField
          label='Who are the most important people in your life?'
          value={data.importantPeople}
          onChange={e => update('importantPeople', e.target.value)}
          placeholder='Friends, mentors, or other important relationships'
        />

        <TextAreaField
          label='What places are most meaningful to you?'
          value={data.importantPlaces}
          onChange={e => update('importantPlaces', e.target.value)}
          placeholder='Homes, travel destinations, or other significant locations'
        />

        <TextAreaField
          label='What possessions or things are most important to you?'
          value={data.importantThings}
          onChange={e => update('importantThings', e.target.value)}
          placeholder='Heirlooms, collections, or other meaningful items'
        />

        <TextAreaField
          label='What experiences or activities bring you the most joy?'
          value={data.importantExperiences}
          onChange={e => update('importantExperiences', e.target.value)}
          placeholder='Hobbies, travel, learning, etc.'
        />

        <TextAreaField
          label="Additional notes about what's important to you"
          value={data.additionalInstructions}
          onChange={e => update('additionalInstructions', e.target.value)}
        />
      </SubSection>

      <SubSection title='Important Contacts'>
        <p className='text-sm text-gray-600'>
          Who are the people you care about that have not been listed already?
        </p>
        {data.importantContacts && data.importantContacts.length > 0 ? (
          data.importantContacts.map((contact, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
              <FormField
                label='Name'
                value={contact.name}
                onChange={e => updateContact(index, 'name', e.target.value)}
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={contact.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateContact(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email Address'
                type='email'
                value={contact.email}
                onChange={e => updateContact(index, 'email', e.target.value)}
              />

              <FormField
                label='Address Line 1'
                value={contact.address1}
                onChange={e => updateContact(index, 'address1', e.target.value)}
              />

              <FormField
                label='Address Line 2'
                value={contact.address2}
                onChange={e => updateContact(index, 'address2', e.target.value)}
              />

              <FormField
                label='City'
                value={contact.city}
                onChange={e => updateContact(index, 'city', e.target.value)}
              />

              <FormField
                label='State'
                value={contact.state}
                onChange={e => updateContact(index, 'state', e.target.value)}
              />

              <FormField
                label='Zip Code'
                value={contact.zip}
                onChange={e => updateContact(index, 'zip', e.target.value)}
              />

              <FormField
                label='Country'
                value={contact.country}
                onChange={e => updateContact(index, 'country', e.target.value)}
              />

              <div className='space-y-3'>
                <Label>Should we try contacting this individual?</Label>
                <RadioGroup
                  value={contact.shouldContact as string}
                  onValueChange={val =>
                    updateContact(
                      index,
                      'shouldContact',
                      val === 'true' ? true : val === 'false' ? false : val
                    )
                  }
                  className='space-y-2'
                >
                  {shouldContactOptions.map(option => (
                    <div
                      key={String(option.value)}
                      className='flex items-center space-x-2'
                    >
                      <RadioGroupItem
                        value={String(option.value)}
                        id={`contact-should-${index}-${String(option.value)}`}
                      />
                      <Label
                        htmlFor={`contact-should-${index}-${String(option.value)}`}
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {contact.shouldContact === true && (
                <div className='space-y-3'>
                  <Label>
                    If you selected 'Yes,' when should we try to contact them?
                  </Label>
                  <RadioGroup
                    value={contact.contactTiming}
                    onValueChange={val =>
                      updateContact(index, 'contactTiming', val)
                    }
                    className='space-y-2'
                  >
                    {contactTimingOptions.map(option => (
                      <div
                        key={option.value}
                        className='flex items-center space-x-2'
                      >
                        <RadioGroupItem
                          value={option.value}
                          id={`contact-timing-${index}-${option.value}`}
                        />
                        <Label
                          htmlFor={`contact-timing-${index}-${option.value}`}
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              )}

              <TextAreaField
                label='Is there anything else we should know?'
                value={contact.notes}
                onChange={e => updateContact(index, 'notes', e.target.value)}
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.importantContacts.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeContact(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.importantContacts.length - 1 && (
                  <Button type='button' variant='outline' onClick={addContact}>
                    <Plus className='h-4 w-4 mr-2' />
                    Add Person
                  </Button>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className='flex justify-center my-4'>
            <Button type='button' variant='outline' onClick={addContact}>
              <Plus className='h-4 w-4 mr-2' />
              Add Person
            </Button>
          </div>
        )}
      </SubSection>

      <SubSection title='Organizations'>
        <p className='text-sm text-gray-600'>
          What are the memberships or organizations you are involved in?
        </p>
        {data.organizations && data.organizations.length > 0 ? (
          data.organizations.map((org, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
              <FormField
                label='Name'
                value={org.name}
                onChange={e =>
                  updateOrganization(index, 'name', e.target.value)
                }
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={org.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateOrganization(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email Address'
                type='email'
                value={org.email}
                onChange={e =>
                  updateOrganization(index, 'email', e.target.value)
                }
              />

              <FormField
                label='Website'
                value={org.website}
                onChange={e =>
                  updateOrganization(index, 'website', e.target.value)
                }
              />

              <FormField
                label='Address Line 1'
                value={org.address1}
                onChange={e =>
                  updateOrganization(index, 'address1', e.target.value)
                }
              />

              <FormField
                label='Address Line 2'
                value={org.address2}
                onChange={e =>
                  updateOrganization(index, 'address2', e.target.value)
                }
              />

              <FormField
                label='City'
                value={org.city}
                onChange={e =>
                  updateOrganization(index, 'city', e.target.value)
                }
              />

              <FormField
                label='State'
                value={org.state}
                onChange={e =>
                  updateOrganization(index, 'state', e.target.value)
                }
              />

              <FormField
                label='Zip Code'
                value={org.zip}
                onChange={e => updateOrganization(index, 'zip', e.target.value)}
              />

              <FormField
                label='Country'
                value={org.country}
                onChange={e =>
                  updateOrganization(index, 'country', e.target.value)
                }
              />

              <div className='space-y-3'>
                <Label>Should we try contacting this organization?</Label>
                <RadioGroup
                  value={org.shouldContact as string}
                  onValueChange={val =>
                    updateOrganization(
                      index,
                      'shouldContact',
                      val === 'true' ? true : val === 'false' ? false : val
                    )
                  }
                  className='space-y-2'
                >
                  {shouldContactOptions.map(option => (
                    <div
                      key={String(option.value)}
                      className='flex items-center space-x-2'
                    >
                      <RadioGroupItem
                        value={String(option.value)}
                        id={`org-should-${index}-${String(option.value)}`}
                      />
                      <Label
                        htmlFor={`org-should-${index}-${String(option.value)}`}
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {org.shouldContact === true && (
                <div className='space-y-3'>
                  <Label>
                    If you selected 'Yes,' when should we try to contact them?
                  </Label>
                  <RadioGroup
                    value={org.contactTiming}
                    onValueChange={val =>
                      updateOrganization(index, 'contactTiming', val)
                    }
                    className='space-y-2'
                  >
                    {contactTimingOptions.map(option => (
                      <div
                        key={option.value}
                        className='flex items-center space-x-2'
                      >
                        <RadioGroupItem
                          value={option.value}
                          id={`org-timing-${index}-${option.value}`}
                        />
                        <Label htmlFor={`org-timing-${index}-${option.value}`}>
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              )}

              <TextAreaField
                label='Is there anything else we should know?'
                value={org.notes}
                onChange={e =>
                  updateOrganization(index, 'notes', e.target.value)
                }
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.organizations.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeOrganization(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.organizations.length - 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    onClick={addOrganization}
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Organization
                  </Button>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className='flex justify-center my-4'>
            <Button type='button' variant='outline' onClick={addOrganization}>
              <Plus className='h-4 w-4 mr-2' />
              Add Organization
            </Button>
          </div>
        )}
      </SubSection>
    </FormSection>
  );
};

export default ImportantToMeSection;
