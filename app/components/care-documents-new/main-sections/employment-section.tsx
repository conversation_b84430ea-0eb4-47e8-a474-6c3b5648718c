'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import CheckboxGroup from '../checkbox-group';
import SubSection from '../sub-section';
import <PERSON>Field from '../form-field';
import <PERSON><PERSON>reaField from '../text-area-field';
import ConditionalSection from '../conditional-section';
import { Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

// Define blank license template
const BLANK_LICENSE = {
  name: '',
  number: '',
  expiration: '',
  state: '',
  website: '',
  renew: null,
  notes: '',
};

// Define blank business template
const BLANK_BUSINESS = {
  name: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  website: '',
  type: '',
  accessInfo: '',
  careInfo: '',
  hasManager: null,
  managerName: '',
  managerPhone: '',
  managerEmail: '',
  hasContinuityPlan: null,
  ownershipStructure: '',
  partners: '',
  clientNotification: '',
  notes: '',
};

interface EmploymentSectionProps {
  disabled?: boolean;
}

const EmploymentSection: React.FC<EmploymentSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    workingStatus: [] as string[],
    employers: [
      {
        companyName: '',
        jobTitle: '',
        supervisor: '',
        supervisorPhone: '',
        supervisorEmail: '',
        notes: '',
      },
    ],
    businesses: [] as Array<{
      name: string;
      address1: string;
      address2: string;
      city: string;
      state: string;
      zip: string;
      website: string;
      type: string;
      accessInfo: string;
      careInfo: string;
      hasManager: boolean | null;
      managerName: string;
      managerPhone: string;
      managerEmail: string;
      hasContinuityPlan: boolean | null;
      ownershipStructure: string;
      partners: string;
      clientNotification: string;
      notes: string;
    }>,
    hasLicenses: null as boolean | null,
    licenses: [] as Array<{
      name: string;
      number: string;
      expiration: string;
      state: string;
      website: string;
      renew: boolean | null;
      notes: string;
      dateIssued: string;
      issuingEntity: string;
    }>,
  });

  useLinkedAccountPrefill('Employment', sectionData.Medical, setData);
  // useInterviewPrefill('Employment', sectionData.Employment, setData);

  // Load data from context when available
  useEffect(() => {
    if (sectionData.Employment) {
      setData(sectionData.Employment);
    }
  }, [sectionData.Employment]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Employment', newData);
    console.log(`Updated ${field}:`, value);
  };

  const updateEmployer = (index: number, field: string, value: string) => {
    const newEmployers = [...data.employers];
    newEmployers[index] = { ...newEmployers[index], [field]: value };
    update('employers', newEmployers);
  };

  const addEmployer = () => {
    update('employers', [
      ...data.employers,
      {
        companyName: '',
        jobTitle: '',
        supervisor: '',
        supervisorPhone: '',
        supervisorEmail: '',
        notes: '',
      },
    ]);
  };

  const removeEmployer = (index: number) => {
    update(
      'employers',
      data.employers.filter((_, i) => i !== index)
    );
  };

  // Business functions
  const addBusiness = () => {
    update('businesses', [...data.businesses, BLANK_BUSINESS]);
  };

  const removeBusiness = (index: number) => {
    update(
      'businesses',
      data.businesses.filter((_, i) => i !== index)
    );
  };

  const updateBusiness = (index: number, field: string, value: any) => {
    const newBusinesses = [...data.businesses];
    newBusinesses[index] = { ...newBusinesses[index], [field]: value };
    update('businesses', newBusinesses);
  };

  // License functions
  const addLicense = () => {
    update('licenses', [...data.licenses, BLANK_LICENSE]);
  };

  const removeLicense = (index: number) => {
    update(
      'licenses',
      data.licenses.filter((_, i) => i !== index)
    );
  };

  const updateLicense = (index: number, field: string, value: any) => {
    const newLicenses = [...data.licenses];
    newLicenses[index] = { ...newLicenses[index], [field]: value };
    update('licenses', newLicenses);
  };

  const toggleWorkingStatus = (status: string) => {
    const exclusives = ['Retired', 'Not currently working'];

    if (exclusives.includes(status)) {
      // Selecting an exclusive option clears all others and sets only this one
      if (data.workingStatus.includes(status)) {
        // Uncheck it
        update(
          'workingStatus',
          data.workingStatus.filter(s => s !== status)
        );
      } else {
        update('workingStatus', [status]);
      }
      return;
    }

    // For non-exclusive options: if any exclusive is selected, remove them first
    const withoutExclusives = data.workingStatus.filter(
      s => !exclusives.includes(s)
    );
    const exists = withoutExclusives.includes(status);
    const newStatus = exists
      ? withoutExclusives.filter(s => s !== status)
      : [...withoutExclusives, status];
    update('workingStatus', newStatus);
  };

  const workingStatusOptions = [
    'Full-time employee',
    'Part-time employee',
    'Self-employed',
    'Business owner',
    'Retired',
    'Not currently working',
  ];

  return (
    <FormSection
      title='Employment Information'
      description='Providing employment information allows us to inform your employer or business of your absence or passing.'
      disabled={disabled}
    >
      <CheckboxGroup
        label='What is your current working status? (Select all that apply)'
        options={workingStatusOptions}
        selected={data.workingStatus}
        onChange={toggleWorkingStatus}
        required
        isOptionDisabled={(value, selected) => {
          const exclusives = ['Retired', 'Not currently working'];
          const selectedHasExclusive = selected.some(s =>
            exclusives.includes(s)
          );
          // If an exclusive is selected, disable all non-exclusive; otherwise if user is toggling a non-exclusive, disable exclusives
          return selectedHasExclusive
            ? !exclusives.includes(value) && selected.length > 0
            : false;
        }}
      />

      {(data.workingStatus.includes('Full-time employee') ||
        data.workingStatus.includes('Part-time employee')) && (
        <SubSection title='Employer Information'>
          {data.employers.map((employer, index) => (
            <div key={index} className='space-y-6 mb-6'>
              {index > 0 && (
                <div className='border-t border-gray-200 pt-6'></div>
              )}
              <FormField
                label='Company Name'
                value={employer.companyName}
                onChange={e =>
                  updateEmployer(index, 'companyName', e.target.value)
                }
                required
              />
              <FormField
                label='Job Title'
                value={employer.jobTitle}
                onChange={e =>
                  updateEmployer(index, 'jobTitle', e.target.value)
                }
              />
              <FormField
                label='Supervisor Name'
                value={employer.supervisor}
                onChange={e =>
                  updateEmployer(index, 'supervisor', e.target.value)
                }
              />
              <FormField
                label='Supervisor Phone'
                type='tel'
                value={employer.supervisorPhone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateEmployer(index, 'supervisorPhone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Supervisor Email'
                type='email'
                value={employer.supervisorEmail}
                onChange={e =>
                  updateEmployer(index, 'supervisorEmail', e.target.value)
                }
              />
              <TextAreaField
                label='Additional Notes'
                value={employer.notes}
                onChange={e => updateEmployer(index, 'notes', e.target.value)}
              />
              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.employers.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeEmployer(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.employers.length - 1 && (
                  <Button
                    type='button'
                    onClick={addEmployer}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Another Employer
                  </Button>
                )}
              </div>
            </div>
          ))}
        </SubSection>
      )}

      {(data.workingStatus.includes('Self-employed') ||
        data.workingStatus.includes('Business owner')) && (
        <SubSection title='Business Ownership'>
          {data.businesses.length === 0 ? (
            <Button
              type='button'
              onClick={addBusiness}
              className='flex items-center gap-2'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Business
            </Button>
          ) : (
            data.businesses.map((business, index) => (
              <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
                <FormField
                  label='Business Name'
                  value={business.name}
                  onChange={e => updateBusiness(index, 'name', e.target.value)}
                  required
                />
                <FormField
                  label='Address Line 1'
                  value={business.address1}
                  onChange={e =>
                    updateBusiness(index, 'address1', e.target.value)
                  }
                />
                <FormField
                  label='Address Line 2'
                  value={business.address2}
                  onChange={e =>
                    updateBusiness(index, 'address2', e.target.value)
                  }
                />
                <FormField
                  label='City'
                  value={business.city}
                  onChange={e => updateBusiness(index, 'city', e.target.value)}
                />
                <FormField
                  label='State'
                  value={business.state}
                  onChange={e => updateBusiness(index, 'state', e.target.value)}
                />
                <FormField
                  label='Zip Code'
                  value={business.zip}
                  onChange={e => updateBusiness(index, 'zip', e.target.value)}
                />
                <FormField
                  label='Website'
                  value={business.website}
                  onChange={e =>
                    updateBusiness(index, 'website', e.target.value)
                  }
                />
                <TextAreaField
                  label='Type of Business'
                  value={business.type}
                  onChange={e => updateBusiness(index, 'type', e.target.value)}
                />
                <TextAreaField
                  label='How can this place be accessed? (Example: Key Location, Door Code, Etc.)'
                  value={business.accessInfo}
                  onChange={e =>
                    updateBusiness(index, 'accessInfo', e.target.value)
                  }
                />
                <TextAreaField
                  label='Please describe how this place will need cared for'
                  value={business.careInfo}
                  onChange={e =>
                    updateBusiness(index, 'careInfo', e.target.value)
                  }
                />
                <ConditionalSection
                  label='Is there a business manager we can contact to help care for this?'
                  value={business.hasManager}
                  onChange={val => updateBusiness(index, 'hasManager', val)}
                >
                  <FormField
                    label='Name'
                    value={business.managerName}
                    onChange={e =>
                      updateBusiness(index, 'managerName', e.target.value)
                    }
                  />
                  <FormField
                    label='Phone'
                    type='tel'
                    value={business.managerPhone || '+1'}
                    onChange={e => {
                      const formattedValue = formatPhoneNumber(e.target.value);
                      updateBusiness(index, 'managerPhone', formattedValue);
                    }}
                    placeholder='+1XXXXXXXXXX'
                  />
                  <FormField
                    label='Email Address'
                    type='email'
                    value={business.managerEmail}
                    onChange={e =>
                      updateBusiness(index, 'managerEmail', e.target.value)
                    }
                  />
                </ConditionalSection>
                <TextAreaField
                  label='Ownership Structure'
                  value={business.ownershipStructure}
                  onChange={e =>
                    updateBusiness(index, 'ownershipStructure', e.target.value)
                  }
                />
                <TextAreaField
                  label='Partners, If any'
                  value={business.partners}
                  onChange={e =>
                    updateBusiness(index, 'partners', e.target.value)
                  }
                />
                <TextAreaField
                  label='How do you notify your clients'
                  value={business.clientNotification}
                  onChange={e =>
                    updateBusiness(index, 'clientNotification', e.target.value)
                  }
                />
                <TextAreaField
                  label='Is there anything we should know when communicating with your business contacts?'
                  value={business.notes}
                  onChange={e => updateBusiness(index, 'notes', e.target.value)}
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeBusiness(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === data.businesses.length - 1 && (
                    <Button
                      type='button'
                      onClick={addBusiness}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Business
                    </Button>
                  )}
                </div>
              </div>
            ))
          )}
        </SubSection>
      )}

      <ConditionalSection
        label='Do you have any professional licenses or certifications?'
        value={data.hasLicenses}
        onChange={val => update('hasLicenses', val)}
      >
        <p className='text-sm text-gray-600 mb-4'>
          Please provide information about your professional licenses or
          certifications.
        </p>

        {data.licenses.map((license, index) => (
          <div key={index} className='space-y-4 border p-4 rounded-xl mb-4'>
            <FormField
              label='License Name'
              value={license.name}
              onChange={e => updateLicense(index, 'name', e.target.value)}
              required
            />
            <FormField
              label='License Number'
              value={license.number}
              onChange={e => updateLicense(index, 'number', e.target.value)}
            />
            <FormField
              label='Expiration Date'
              value={license.expiration}
              onChange={e => updateLicense(index, 'expiration', e.target.value)}
              type='date'
            />
            <FormField
              label='State'
              value={license.state}
              onChange={e => updateLicense(index, 'state', e.target.value)}
            />
            <FormField
              label='Website'
              value={license.website}
              onChange={e => updateLicense(index, 'website', e.target.value)}
            />
            <FormField
              label='Date Issued'
              value={license.dateIssued}
              onChange={e => updateLicense(index, 'dateIssued', e.target.value)}
              type='date'
            />
            <FormField
              label='Issuing Entity'
              value={license.issuingEntity}
              onChange={e =>
                updateLicense(index, 'issuingEntity', e.target.value)
              }
            />
            <ConditionalSection
              label='If possible, would you like this to be renewed?'
              value={license.renew}
              onChange={val => updateLicense(index, 'renew', val)}
            >
              <p className='text-sm text-gray-500'>
                Please note we cannot renew licenses that require continuing
                education credits to be completed by you.
              </p>
            </ConditionalSection>
            <TextAreaField
              label='Is there anything else we should know about?'
              value={license.notes}
              onChange={e => updateLicense(index, 'notes', e.target.value)}
            />

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              {data.licenses.length > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  className='text-red-600 hover:text-red-700'
                  onClick={() => removeLicense(index)}
                >
                  <Trash2 className='h-4 w-4 mr-2' />
                  Remove
                </Button>
              )}
              {index === data.licenses.length - 1 && (
                <Button
                  type='button'
                  onClick={addLicense}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Professional License
                </Button>
              )}
            </div>
          </div>
        ))}

        {data.licenses.length === 0 && (
          <Button
            type='button'
            onClick={addLicense}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4 mr-2' />
            Add License/Certification
          </Button>
        )}
      </ConditionalSection>
    </FormSection>
  );
};

export default EmploymentSection;
