'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import { Plus, Trash2 } from 'lucide-react';
import <PERSON><PERSON>ield from '../form-field';
import <PERSON><PERSON>reaField from '../text-area-field';
import ConditionalSection from '../conditional-section';
import RadioGroup from '../radio-group';
import StringRadioGroup from '../string-radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

// Blank constants for new entities
const BLANK_FINANCIAL_PROFESSIONAL = {
  name: '',
  phone: '',
  email: '',
  notes: '',
};

const BLANK_ASSET_ACCOUNT = {
  name: '',
  owner: '',
  institution: '',
  number: '',
  type: '',
};

const BLANK_LIABILITY_ACCOUNT = {
  name: '',
  owner: '',
  institution: '',
  number: '',
  type: '',
};

interface FinancialSectionProps {
  disabled?: boolean;
}

const FinancialSection: React.FC<FinancialSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    accounts: [] as Array<{
      type: string;
      institution: string;
      accountNumber: string;
      contactInfo: string;
      onlineAccess: string;
      beneficiaries: string;
      notes: string;
    }>,
    hasProfessionals: null as boolean | null,
    professionals: [] as Array<{
      name: string;
      type: string;
      company: string;
      phone: string;
      email: string;
      notes: string;
    }>,
    // New fields from mockup
    hasAdvisor: null as boolean | null,
    financialAdvisors: [] as Array<typeof BLANK_FINANCIAL_PROFESSIONAL>,
    hasTaxProfessional: null as boolean | null,
    taxProfessionals: [] as Array<typeof BLANK_FINANCIAL_PROFESSIONAL>,
    otherFinancial: [] as Array<typeof BLANK_FINANCIAL_PROFESSIONAL>,
    assetAccounts: [] as Array<typeof BLANK_ASSET_ACCOUNT>,
    liabilityAccounts: [] as Array<typeof BLANK_LIABILITY_ACCOUNT>,
    taxState: '',
    taxFilingStatus: '',
    additionalInstructions: '',
  });

  useLinkedAccountPrefill('Financial', sectionData.Medical, setData);
  // useInterviewPrefill('Financial', sectionData.Financial, setData);

  useEffect(() => {
    if (sectionData.Financial) {
      setData(sectionData.Financial);
    }
  }, [sectionData.Financial]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Financial', newData);
    console.log(`Updated ${field}:`, value);
  };

  // Existing account functions
  const updateAccount = (index: number, field: string, value: string) => {
    const newAccounts = [...data.accounts];
    newAccounts[index] = { ...newAccounts[index], [field]: value };
    update('accounts', newAccounts);
  };

  const addAccount = () => {
    update('accounts', [
      ...data.accounts,
      {
        type: '',
        institution: '',
        accountNumber: '',
        contactInfo: '',
        onlineAccess: '',
        beneficiaries: '',
        notes: '',
      },
    ]);
  };

  const removeAccount = (index: number) => {
    update(
      'accounts',
      data.accounts.filter((_, i) => i !== index)
    );
  };

  // Existing professional functions
  const updateProfessional = (index: number, field: string, value: string) => {
    const newProfessionals = [...data.professionals];
    newProfessionals[index] = { ...newProfessionals[index], [field]: value };
    update('professionals', newProfessionals);
  };

  const addProfessional = () => {
    update('professionals', [
      ...data.professionals,
      {
        name: '',
        type: '',
        company: '',
        phone: '',
        email: '',
        notes: '',
      },
    ]);
  };

  const removeProfessional = (index: number) => {
    update(
      'professionals',
      data.professionals.filter((_, i) => i !== index)
    );
  };

  // New financial advisor functions
  const addAdvisor = () => {
    update('financialAdvisors', [
      ...data.financialAdvisors,
      BLANK_FINANCIAL_PROFESSIONAL,
    ]);
  };

  const removeAdvisor = (index: number) => {
    update(
      'financialAdvisors',
      data.financialAdvisors.filter((_, i) => i !== index)
    );
  };

  const updateAdvisor = (index: number, field: string, value: string) => {
    const newAdvisors = [...data.financialAdvisors];
    newAdvisors[index] = { ...newAdvisors[index], [field]: value };
    update('financialAdvisors', newAdvisors);
  };

  // New tax professional functions
  const addTaxProfessional = () => {
    update('taxProfessionals', [
      ...data.taxProfessionals,
      BLANK_FINANCIAL_PROFESSIONAL,
    ]);
  };

  const removeTaxProfessional = (index: number) => {
    update(
      'taxProfessionals',
      data.taxProfessionals.filter((_, i) => i !== index)
    );
  };

  const updateTaxProfessional = (
    index: number,
    field: string,
    value: string
  ) => {
    const newTax = [...data.taxProfessionals];
    newTax[index] = { ...newTax[index], [field]: value };
    update('taxProfessionals', newTax);
  };

  // New other financial professional functions
  const addOtherFinancial = () => {
    update('otherFinancial', [
      ...data.otherFinancial,
      BLANK_FINANCIAL_PROFESSIONAL,
    ]);
  };

  const removeOtherFinancial = (index: number) => {
    update(
      'otherFinancial',
      data.otherFinancial.filter((_, i) => i !== index)
    );
  };

  const updateOtherFinancial = (
    index: number,
    field: string,
    value: string
  ) => {
    const newOther = [...data.otherFinancial];
    newOther[index] = { ...newOther[index], [field]: value };
    update('otherFinancial', newOther);
  };

  // New asset account functions
  const addAssetAccount = () => {
    update('assetAccounts', [...data.assetAccounts, BLANK_ASSET_ACCOUNT]);
  };

  const removeAssetAccount = (index: number) => {
    update(
      'assetAccounts',
      data.assetAccounts.filter((_, i) => i !== index)
    );
  };

  const updateAssetAccount = (index: number, field: string, value: string) => {
    const newAssets = [...data.assetAccounts];
    newAssets[index] = { ...newAssets[index], [field]: value };
    update('assetAccounts', newAssets);
  };

  // New liability account functions
  const addLiabilityAccount = () => {
    update('liabilityAccounts', [
      ...data.liabilityAccounts,
      BLANK_LIABILITY_ACCOUNT,
    ]);
  };

  const removeLiabilityAccount = (index: number) => {
    update(
      'liabilityAccounts',
      data.liabilityAccounts.filter((_, i) => i !== index)
    );
  };

  const updateLiabilityAccount = (
    index: number,
    field: string,
    value: string
  ) => {
    const newLiabilities = [...data.liabilityAccounts];
    newLiabilities[index] = { ...newLiabilities[index], [field]: value };
    update('liabilityAccounts', newLiabilities);
  };

  // Existing account types
  const accountTypes = [
    'Checking Account',
    'Savings Account',
    'Retirement Account (401k, IRA)',
    'Investment Account',
    'Credit Card',
    'Loan',
    'Other',
  ];

  // Existing professional types
  const professionalTypes = [
    'Financial Advisor',
    'Accountant',
    'Tax Preparer',
    'Attorney',
    'Insurance Agent',
    'Other',
  ];

  // New asset types from mockup
  const assetTypes = [
    'Bank - Checking',
    'Bank - Savings',
    'Bank - CD',
    'Bank - Money Market',
    'Bank - Cash',
    'Bank - Other',
    'Investment - Taxable Brokerage',
    '401ks - Traditional/Pre-tax 401(k), 403(b), or Other Retirement Plan',
    '401ks - Roth 401(k), 403(b), or Other Retirement Plan',
    '401ks - Pre-tax and Roth 401(k), 403(b), or Other Retirement Plan',
    'IRAs - Traditional IRA',
    'IRAs - Rollover IRA',
    'IRAs - Inherited Traditional IRA',
    'IRAs - Inherited Roth IRA',
    'IRAs - SEP IRA',
    'IRAs - Roth SEP IRA',
    'IRAs - SIMPLE IRA',
    'IRAs - Roth SIMPLE IRA',
    'HSA - Health Savings Account',
    'Other Accounts - Deferred Compensation Plan',
    'Other Accounts - Non-Qualified Pension Plan',
    'Other Accounts - Qualified Pension Plan',
    'Other Accounts - Non-Qualified Annuity',
    'Other Accounts - Qualified Annuity',
    'Other Accounts - Roth Annuity',
    'Other Accounts - Trust Account',
    'Other Accounts - Donor Advised Account',
    'Other Accounts - Profit-Sharing',
    'Other Accounts - Employee Stock Purchase Plan',
    'Other Accounts - Employee Stock Ownership Plan',
    'Other Accounts - 529 College Savings Plan',
    'Other Accounts - UTMA or UGMA',
    'Other Accounts - Coverdell ESA',
  ];

  // New liability types from mockup
  const liabilityTypes = [
    'Credit Card',
    'Mortgage',
    'Adjustable Rate Mortgage (ARM)',
    'Interest Only Mortgage',
    'Home Equity Loan',
    'Home Equity Line of Credit (HELOC)',
    'Reverse Mortgage',
    'Car Loan',
    'Student Loan',
    'Other Loan',
  ];

  return (
    <FormSection
      title='Financial Information'
      description='Please be advised that to act as your Financial Power of Attorney (POA), we will need information regarding your finances.'
      disabled={disabled}
    >
      <SubSection title='Financial Professionals'>
        <ConditionalSection
          label='Do you currently work with a financial advisor or financial planner?'
          value={data.hasAdvisor}
          onChange={val => {
            update('hasAdvisor', val);
            if (val && data.financialAdvisors.length === 0) {
              addAdvisor();
            }
          }}
        >
          {data.financialAdvisors.map((adv, index) => (
            <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
              <FormField
                label='Name'
                value={adv.name}
                onChange={e => updateAdvisor(index, 'name', e.target.value)}
              />
              <FormField
                label='Phone Number'
                value={adv.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateAdvisor(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={adv.email}
                onChange={e => updateAdvisor(index, 'email', e.target.value)}
                type='email'
              />
              <TextAreaField
                label="Is there anything you'd like us to know?"
                value={adv.notes}
                onChange={e => updateAdvisor(index, 'notes', e.target.value)}
              />
              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.financialAdvisors.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeAdvisor(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.financialAdvisors.length - 1 && (
                  <Button
                    type='button'
                    onClick={addAdvisor}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Financial Planner
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <ConditionalSection
          label='Do you currently work with a professional for your taxes such as a Certified Public Account (CPA) or Enrolled Agent (EA)?'
          value={data.hasTaxProfessional}
          onChange={val => {
            update('hasTaxProfessional', val);
            if (val && data.taxProfessionals.length === 0) {
              addTaxProfessional();
            }
          }}
        >
          {data.taxProfessionals.map((tax, index) => (
            <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
              <FormField
                label='Name'
                value={tax.name}
                onChange={e =>
                  updateTaxProfessional(index, 'name', e.target.value)
                }
              />
              <FormField
                label='Phone Number'
                value={tax.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateTaxProfessional(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={tax.email}
                onChange={e =>
                  updateTaxProfessional(index, 'email', e.target.value)
                }
                type='email'
              />
              <TextAreaField
                label="Is there anything you'd like us to know?"
                value={tax.notes}
                onChange={e =>
                  updateTaxProfessional(index, 'notes', e.target.value)
                }
              />
              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.taxProfessionals.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeTaxProfessional(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.taxProfessionals.length - 1 && (
                  <Button
                    type='button'
                    onClick={addTaxProfessional}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add CPA or EA
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        {/* Other Financial Professionals */}
        <Button
          type='button'
          onClick={() => {
            if (data.otherFinancial.length === 0) {
              addOtherFinancial();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Other Financial Professional
        </Button>

        {data.otherFinancial.map((other, index) => (
          <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
            <FormField
              label='Name'
              value={other.name}
              onChange={e =>
                updateOtherFinancial(index, 'name', e.target.value)
              }
            />
            <FormField
              label='Phone Number'
              value={other.phone || '+1'}
              onChange={e => {
                const formattedValue = formatPhoneNumber(e.target.value);
                updateOtherFinancial(index, 'phone', formattedValue);
              }}
              type='tel'
              placeholder='+1XXXXXXXXXX'
            />
            <FormField
              label='Email Address'
              value={other.email}
              onChange={e =>
                updateOtherFinancial(index, 'email', e.target.value)
              }
              type='email'
            />
            <TextAreaField
              label="Is there anything you'd like us to know?"
              value={other.notes}
              onChange={e =>
                updateOtherFinancial(index, 'notes', e.target.value)
              }
            />
            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              <Button
                type='button'
                variant='outline'
                className='text-red-600 hover:text-red-700'
                onClick={() => removeOtherFinancial(index)}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                Remove
              </Button>
              {index === data.otherFinancial.length - 1 && (
                <Button
                  type='button'
                  onClick={addOtherFinancial}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Financial Professional
                </Button>
              )}
            </div>
          </div>
        ))}

        {/* Keep the existing professionals section for backward compatibility */}
        <ConditionalSection
          label='Do you work with any other financial professionals?'
          value={data.hasProfessionals}
          onChange={val => {
            update('hasProfessionals', val);
            if (val && data.professionals.length === 0) {
              addProfessional();
            }
          }}
        >
          {/* ... existing professionals code ... */}
          {data.professionals.map((professional, index) => (
            <div key={index} className='space-y-6 mb-6'>
              {index > 0 && (
                <div className='border-t border-gray-200 pt-6'></div>
              )}

              <FormField
                label="Professional's Name"
                value={professional.name}
                onChange={e =>
                  updateProfessional(index, 'name', e.target.value)
                }
                required
              />

              <div className='space-y-2'>
                <Label className='text-sm font-medium text-gray-700'>
                  Professional Type
                </Label>
                <Select
                  value={professional.type}
                  onValueChange={value =>
                    updateProfessional(index, 'type', value)
                  }
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select professional type' />
                  </SelectTrigger>
                  <SelectContent>
                    {professionalTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <FormField
                label='Company/Firm'
                value={professional.company}
                onChange={e =>
                  updateProfessional(index, 'company', e.target.value)
                }
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={professional.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateProfessional(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email'
                type='email'
                value={professional.email}
                onChange={e =>
                  updateProfessional(index, 'email', e.target.value)
                }
              />

              <TextAreaField
                label='Additional Notes'
                value={professional.notes}
                onChange={e =>
                  updateProfessional(index, 'notes', e.target.value)
                }
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.professionals.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeProfessional(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.professionals.length - 1 && (
                  <Button
                    type='button'
                    onClick={addProfessional}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Another Professional
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>
      </SubSection>

      {/* Asset Accounts Section */}
      <SubSection title='Accounts - Assets'>
        <p className='text-sm text-gray-600'>
          Please be sure to list all the accounts you have here regardless of
          whether or not you are working with a financial professional.
        </p>
        <Button
          type='button'
          onClick={() => {
            if (data.assetAccounts.length === 0) {
              addAssetAccount();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Asset Account
        </Button>

        {data.assetAccounts.map((account, index) => (
          <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
            <FormField
              label='Account Name'
              value={account.name}
              onChange={e => updateAssetAccount(index, 'name', e.target.value)}
            />
            <FormField
              label='Account Owner'
              value={account.owner}
              onChange={e => updateAssetAccount(index, 'owner', e.target.value)}
            />
            <FormField
              label='Financial Institution'
              value={account.institution}
              onChange={e =>
                updateAssetAccount(index, 'institution', e.target.value)
              }
            />
            <FormField
              label='Account Number'
              value={account.number}
              onChange={e =>
                updateAssetAccount(index, 'number', e.target.value)
              }
            />
            <StringRadioGroup
              label='What type of account is this?'
              value={account.type}
              onChange={val => updateAssetAccount(index, 'type', val)}
              options={assetTypes}
            />
            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              <Button
                type='button'
                variant='outline'
                className='text-red-600 hover:text-red-700'
                onClick={() => removeAssetAccount(index)}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                Remove
              </Button>
              {index === data.assetAccounts.length - 1 && (
                <Button
                  type='button'
                  onClick={addAssetAccount}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Asset
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      {/* Liability Accounts Section */}
      <SubSection title='Accounts - Liabilities'>
        <p className='text-sm text-gray-600'>
          Please list out any credit cards or loans you owe here.
        </p>
        <Button
          type='button'
          onClick={() => {
            if (data.liabilityAccounts.length === 0) {
              addLiabilityAccount();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Liability Account
        </Button>

        {data.liabilityAccounts.map((account, index) => (
          <div key={index} className='space-y-6 mb-6 border p-4 rounded-xl'>
            <FormField
              label='Account Name'
              value={account.name}
              onChange={e =>
                updateLiabilityAccount(index, 'name', e.target.value)
              }
            />
            <FormField
              label='Account Owner'
              value={account.owner}
              onChange={e =>
                updateLiabilityAccount(index, 'owner', e.target.value)
              }
            />
            <FormField
              label='Financial Institution'
              value={account.institution}
              onChange={e =>
                updateLiabilityAccount(index, 'institution', e.target.value)
              }
            />
            <FormField
              label='Account Number'
              value={account.number}
              onChange={e =>
                updateLiabilityAccount(index, 'number', e.target.value)
              }
            />
            <StringRadioGroup
              label='What type of account is this?'
              value={account.type}
              onChange={val => updateLiabilityAccount(index, 'type', val)}
              options={liabilityTypes}
            />
            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              <Button
                type='button'
                variant='outline'
                className='text-red-600 hover:text-red-700'
                onClick={() => removeLiabilityAccount(index)}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                Remove
              </Button>
              {index === data.liabilityAccounts.length - 1 && (
                <Button
                  type='button'
                  onClick={addLiabilityAccount}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Liability
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      {/* Taxes Section */}
      <SubSection title='Taxes'>
        <p className='text-sm text-gray-600'>
          By providing information about your taxes, you can help ensure we have
          all necessary documentation regarding filing taxes on your behalf.
        </p>
        <FormField
          label='State of Filing'
          value={data.taxState}
          onChange={e => update('taxState', e.target.value)}
        />
        <FormField
          label='Filing Status'
          value={data.taxFilingStatus}
          onChange={e => update('taxFilingStatus', e.target.value)}
        />
      </SubSection>

      {/* Keep the existing Financial Accounts section for backward compatibility */}
      <SubSection title='Legacy Financial Accounts'>
        <Button
          type='button'
          onClick={() => {
            if (data.accounts.length === 0) {
              addAccount();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Financial Account
        </Button>

        {data.accounts.map((account, index) => (
          <div key={index} className='space-y-6 mb-6'>
            {index > 0 && <div className='border-t border-gray-200 pt-6'></div>}

            <div className='space-y-2'>
              <Label className='text-sm font-medium text-gray-700'>
                Account Type
              </Label>
              <Select
                value={account.type}
                onValueChange={value => updateAccount(index, 'type', value)}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Select account type' />
                </SelectTrigger>
                <SelectContent>
                  {accountTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <FormField
              label='Financial Institution'
              value={account.institution}
              onChange={e =>
                updateAccount(index, 'institution', e.target.value)
              }
            />

            <FormField
              label='Account Number (last 4 digits only)'
              value={account.accountNumber}
              onChange={e =>
                updateAccount(index, 'accountNumber', e.target.value)
              }
              placeholder='xxxx'
            />

            <FormField
              label='Institution Contact Information'
              value={account.contactInfo}
              onChange={e =>
                updateAccount(index, 'contactInfo', e.target.value)
              }
              placeholder='Phone number, website, etc.'
            />

            <TextAreaField
              label='Online Access Information'
              value={account.onlineAccess}
              onChange={e =>
                updateAccount(index, 'onlineAccess', e.target.value)
              }
              placeholder='Username (do not include passwords)'
            />

            <TextAreaField
              label='Beneficiaries'
              value={account.beneficiaries}
              onChange={e =>
                updateAccount(index, 'beneficiaries', e.target.value)
              }
              placeholder='List any designated beneficiaries for this account'
            />

            <TextAreaField
              label='Additional Notes'
              value={account.notes}
              onChange={e => updateAccount(index, 'notes', e.target.value)}
            />

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              <Button
                type='button'
                variant='outline'
                className='text-red-600 hover:text-red-700'
                onClick={() => removeAccount(index)}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                Remove
              </Button>
              {index === data.accounts.length - 1 && (
                <Button
                  type='button'
                  onClick={addAccount}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Another Account
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      <TextAreaField
        label='Additional Financial Instructions'
        value={data.additionalInstructions}
        onChange={e => update('additionalInstructions', e.target.value)}
        placeholder='Any additional financial information or instructions'
      />
    </FormSection>
  );
};

export default FinancialSection;
