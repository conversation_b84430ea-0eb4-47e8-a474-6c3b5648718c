'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import CheckboxGroup from '../checkbox-group';
import FileUploadSection from '../file-upload-section';
import ConditionalSection from '../conditional-section';
import <PERSON><PERSON>ield from '../form-field';
import <PERSON><PERSON>rea<PERSON>ield from '../text-area-field';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Save } from 'lucide-react';
import { toast } from 'sonner';

interface PersonalIdentificationSectionProps {
  disabled?: boolean;
}

const PersonalIdentificationSection: React.FC<PersonalIdentificationSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData, saveSection, loading } =
    useCareDocumentsContext();

  // Initialize state from context or with defaults
  const [data, setData] = useState({
    identifyingDocuments: [] as string[],
    hasPasswordManager: null as boolean | null,
    passwordManagerName: '',
    passwordManagerUsername: '',
    passwordManagerPassword: '',
    passwordManagerEmail: '',
    noPasswordManagerInfo: '',
    socialMedia: [] as string[],
    socialAccounts: {} as Record<
      string,
      Array<{
        platform: string;
        username: string;
        password: string;
        action: string;
        notes: string;
      }>
    >,
    cloudStorage: [] as string[],
    cloudAccounts: {} as Record<
      string,
      Array<{
        platform: string;
        username: string;
        password: string;
        action: string;
        notes: string;
      }>
    >,
    deviceWishes: '',
    additionalNotes: '',
  });

  useLinkedAccountPrefill(
    'PersonalIdentification',
    sectionData.PersonalIdentification,
    setData
  );
  // useInterviewPrefill('PersonalIdentification', sectionData.PersonalIdentification, setData);

  // Load data from context when available
  useEffect(() => {
    if (sectionData.PersonalIdentification) {
      setData(sectionData.PersonalIdentification);
    }
  }, [sectionData.PersonalIdentification]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('PersonalIdentification', newData);
  };

  const updateWithValues = (values: Record<string, any>) => {
    const newData = { ...data, ...values };
    setData(newData);
    // Update the context with the new data
    updateSectionData('PersonalIdentification', newData);
  };

  const toggleDocument = (doc: string) => {
    const newDocs = data.identifyingDocuments.includes(doc)
      ? data.identifyingDocuments.filter(d => d !== doc)
      : [...data.identifyingDocuments, doc];
    update('identifyingDocuments', newDocs);
  };

  const toggleSocial = (social: string) => {
    const newSocial = data.socialMedia.includes(social)
      ? data.socialMedia.filter(s => s !== social)
      : [...data.socialMedia, social];

    const newSocialAccounts = { ...data.socialAccounts };

    if (social === "I don't use any of these.") {
      if (newSocial.includes(social)) {
        // Clear all social accounts when "I don't use any" is selected
        updateWithValues({ socialMedia: [social], socialAccounts: {} });

        return;
      }
    } else {
      // Remove "I don't use any" if another option is selected
      const filteredSocial = newSocial.filter(
        s => s !== "I don't use any of these."
      );

      if (filteredSocial.includes(social)) {
        // Add initial form for this platform
        newSocialAccounts[social] = [
          {
            platform: social === 'Other' ? '' : social,
            username: '',
            password: '',
            action: '',
            notes: '',
          },
        ];
      } else {
        // Remove all forms for this platform
        delete newSocialAccounts[social];
      }

      updateWithValues({
        socialMedia: filteredSocial,
        socialAccounts: newSocialAccounts,
      });
      return;
    }

    update('socialMedia', newSocial);
  };

  const addSocialAccount = (platform: string) => {
    const newSocialAccounts = { ...data.socialAccounts };
    if (!newSocialAccounts[platform]) {
      newSocialAccounts[platform] = [];
    }
    newSocialAccounts[platform].push({
      platform: platform === 'Other' ? '' : platform,
      username: '',
      password: '',
      action: '',
      notes: '',
    });
    update('socialAccounts', newSocialAccounts);
  };

  const updateSocialAccount = (
    platform: string,
    index: number,
    field: string,
    value: string
  ) => {
    const newSocialAccounts = { ...data.socialAccounts };
    newSocialAccounts[platform][index] = {
      ...newSocialAccounts[platform][index],
      [field]: value,
    };
    update('socialAccounts', newSocialAccounts);
  };

  const removeSocialAccount = (platform: string, index: number) => {
    const newSocialAccounts = { ...data.socialAccounts };
    newSocialAccounts[platform] = newSocialAccounts[platform].filter(
      (_, i) => i !== index
    );

    if (newSocialAccounts[platform].length === 0) {
      delete newSocialAccounts[platform];
      // Also remove from selected social media
      const newSocialMedia = data.socialMedia.filter(s => s !== platform);
      update('socialMedia', newSocialMedia);
    }

    update('socialAccounts', newSocialAccounts);
  };

  const saveSocialAccounts = async () => {
    try {
      await saveSection('PersonalIdentification');
      toast.success('Social media accounts saved');
    } catch (err) {
      console.error('Failed to save social media accounts:', err);
      toast.error('Failed to save social media accounts');
    }
  };

  const toggleCloud = (cloud: string) => {
    const newCloud = data.cloudStorage.includes(cloud)
      ? data.cloudStorage.filter(c => c !== cloud)
      : [...data.cloudStorage, cloud];

    const newCloudAccounts = { ...data.cloudAccounts };

    if (cloud === "I don't use any of these.") {
      if (newCloud.includes(cloud)) {
        // Clear all cloud accounts when "I don't use any" is selected
        updateWithValues({ cloudStorage: [cloud], cloudAccounts: {} });
        return;
      }
    } else {
      // Remove "I don't use any" if another option is selected
      const filteredCloud = newCloud.filter(
        c => c !== "I don't use any of these."
      );

      if (filteredCloud.includes(cloud)) {
        // Add initial form for this platform
        newCloudAccounts[cloud] = [
          {
            platform: cloud === 'Other' ? '' : cloud,
            username: '',
            password: '',
            action: '',
            notes: '',
          },
        ];
      } else {
        // Remove all forms for this platform
        delete newCloudAccounts[cloud];
      }

      updateWithValues({
        cloudStorage: filteredCloud,
        cloudAccounts: newCloudAccounts,
      });
      return;
    }

    update('cloudStorage', newCloud);
  };

  const addCloudAccount = (platform: string) => {
    const newCloudAccounts = { ...data.cloudAccounts };
    if (!newCloudAccounts[platform]) {
      newCloudAccounts[platform] = [];
    }
    newCloudAccounts[platform].push({
      platform: platform === 'Other' ? '' : platform,
      username: '',
      password: '',
      action: '',
      notes: '',
    });
    update('cloudAccounts', newCloudAccounts);
  };

  const updateCloudAccount = (
    platform: string,
    index: number,
    field: string,
    value: string
  ) => {
    const newCloudAccounts = { ...data.cloudAccounts };
    newCloudAccounts[platform][index] = {
      ...newCloudAccounts[platform][index],
      [field]: value,
    };
    update('cloudAccounts', newCloudAccounts);
  };

  const removeCloudAccount = (platform: string, index: number) => {
    const newCloudAccounts = { ...data.cloudAccounts };
    newCloudAccounts[platform] = newCloudAccounts[platform].filter(
      (_, i) => i !== index
    );

    if (newCloudAccounts[platform].length === 0) {
      delete newCloudAccounts[platform];
      // Also remove from selected cloud storage
      const newCloudStorage = data.cloudStorage.filter(c => c !== platform);
      update('cloudStorage', newCloudStorage);
    }

    update('cloudAccounts', newCloudAccounts);
  };

  const documentOptions = [
    'Birth Certificate (long form)',
    "Driver's License/ State ID",
    'Passport',
    'Social Security Card',
    'Permanent Resident Card',
    'Veteran ID Card',
    'Tribal ID Card',
    'Global Entry/TSA Precheck',
    'Other Identification',
  ];

  const socialOptions = [
    'Facebook',
    'Reddit',
    'BlueSky',
    'LinkedIn',
    'X (Formerly Twitter)',
    'Other',
    'Instagram',
    'Pinterest',
    "I don't use any of these.",
  ];

  const cloudOptions = [
    'iCloud',
    'Dropbox',
    'One Drive',
    'Google Drive',
    'Other',
    "I don't use any of these.",
  ];

  const socialActionOptions = [
    'Change to Memorial Account (If available)',
    'Delete',
    'Nothing',
    'Something Else (Explain below)',
  ];

  const cloudActionOptions = [
    'Access',
    'Delete',
    'Nothing',
    'Something Else (Explain below)',
  ];

  return (
    <FormSection
      title='Personal Identification'
      description='Please provide your personal identification details.'
      disabled={disabled}
    >
      <SubSection title='Identifying Documents (IDs)*'>
        <CheckboxGroup
          label='Please select the type of identifying documents you have. Please note that we do require at least 1 form of photo ID to be supplied. This information is only used should you become incapacitated to help facilitate making decisions on your behalf.*'
          options={documentOptions}
          selected={data.identifyingDocuments}
          onChange={toggleDocument}
          columns={3}
        />
        {/* <FileUploadSection /> */}
      </SubSection>

      <SubSection title='Digital Assets'>
        <p className='text-sm text-gray-600 mb-6'>
          This section will allow us to manage your digital assets (email
          accounts, social media, etc.) in the event you can no longer do so.
          Please note that we will not post on your behalf. We suggest using a
          digital password manager to hold your passwords for digital assets.
        </p>
        <ConditionalSection
          label='Do you use a password manager?'
          value={data.hasPasswordManager}
          onChange={val => update('hasPasswordManager', val)}
        >
          <FormField
            label='Password Manager Name'
            value={data.passwordManagerName}
            onChange={e => update('passwordManagerName', e.target.value)}
          />
          <FormField
            label='Username'
            value={data.passwordManagerUsername}
            onChange={e => update('passwordManagerUsername', e.target.value)}
          />
          <FormField
            label='Associated Email'
            type='email'
            value={data.passwordManagerEmail}
            onChange={e => update('passwordManagerEmail', e.target.value)}
          />
          <FormField
            label='Master Password'
            type='password'
            value={data.passwordManagerPassword}
            onChange={e => update('passwordManagerPassword', e.target.value)}
          />
        </ConditionalSection>

        {data.hasPasswordManager === false && (
          <TextAreaField
            label="If you don't use a password manager please share how we can find information regarding your accounts & your wishes so we can properly manage them. At a minimum we need email addresses and passwords to be listed."
            value={data.noPasswordManagerInfo}
            onChange={e => update('noPasswordManagerInfo', e.target.value)}
          />
        )}
      </SubSection>

      <SubSection title='Social Media'>
        <CheckboxGroup
          label='Which social media platforms do you use?'
          options={socialOptions}
          selected={data.socialMedia}
          onChange={toggleSocial}
          columns={3}
        />

        {Object.entries(data.socialAccounts).map(([platform, accounts]) => (
          <div key={platform} className='mt-6'>
            <h4 className='text-lg font-medium text-gray-900 mb-4'>
              {platform}
            </h4>
            {accounts.map((account, index) => (
              <div key={index} className='space-y-4 border p-4 rounded-xl mt-4'>
                <FormField
                  label='Social Platform Name'
                  value={account.platform}
                  onChange={e =>
                    updateSocialAccount(
                      platform,
                      index,
                      'platform',
                      e.target.value
                    )
                  }
                  disabled={platform !== 'Other'}
                />
                <FormField
                  label='Username'
                  value={account.username}
                  onChange={e =>
                    updateSocialAccount(
                      platform,
                      index,
                      'username',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Password'
                  type='password'
                  value={account.password}
                  onChange={e =>
                    updateSocialAccount(
                      platform,
                      index,
                      'password',
                      e.target.value
                    )
                  }
                />

                <div className='space-y-2'>
                  <Label>What would you like us to do with this account?</Label>
                  <RadioGroup
                    value={account.action}
                    onValueChange={val =>
                      updateSocialAccount(platform, index, 'action', val)
                    }
                    className='space-y-2'
                  >
                    {socialActionOptions.map(option => (
                      <div key={option} className='flex items-center space-x-2'>
                        <RadioGroupItem
                          value={option}
                          id={`social-${platform}-${index}-${option}`}
                        />
                        <Label
                          htmlFor={`social-${platform}-${index}-${option}`}
                        >
                          {option}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <TextAreaField
                  label='Please let us know if there is anything specific regarding this account we should be aware of. For example, if this account is for a business or you’ve built a personal brand using this account.'
                  value={account.notes}
                  onChange={e =>
                    updateSocialAccount(
                      platform,
                      index,
                      'notes',
                      e.target.value
                    )
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeSocialAccount(platform, index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === accounts.length - 1 && (
                    <Button
                      type='button'
                      onClick={() => addSocialAccount(platform)}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Another {platform}
                    </Button>
                  )}
                  {/* <Button
                    type='button'
                    onClick={saveSocialAccounts}
                    disabled={loading}
                    className='flex items-center gap-2'
                  >
                    <Save className='h-4 w-4 mr-2' />
                    {loading ? 'Saving...' : 'Save'}
                  </Button> */}
                </div>
              </div>
            ))}
          </div>
        ))}
      </SubSection>

      <SubSection title='File/Cloud Storage'>
        <CheckboxGroup
          label='Which cloud storage platforms do you use?'
          options={cloudOptions}
          selected={data.cloudStorage}
          onChange={toggleCloud}
          columns={3}
        />

        {Object.entries(data.cloudAccounts).map(([platform, accounts]) => (
          <div key={platform} className='mt-6'>
            <h4 className='text-lg font-medium text-gray-900 mb-4'>
              {platform}
            </h4>
            {accounts.map((account, index) => (
              <div key={index} className='space-y-4 border p-4 rounded-xl mt-4'>
                <FormField
                  label='Platform'
                  value={account.platform}
                  onChange={e =>
                    updateCloudAccount(
                      platform,
                      index,
                      'platform',
                      e.target.value
                    )
                  }
                  disabled={platform !== 'Other'}
                />
                <FormField
                  label='Platform Username'
                  value={account.username}
                  onChange={e =>
                    updateCloudAccount(
                      platform,
                      index,
                      'username',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Platform Password'
                  type='password'
                  value={account.password}
                  onChange={e =>
                    updateCloudAccount(
                      platform,
                      index,
                      'password',
                      e.target.value
                    )
                  }
                />

                <div className='space-y-2'>
                  <Label>What would you like us to do with this account?</Label>
                  <RadioGroup
                    value={account.action}
                    onValueChange={val =>
                      updateCloudAccount(platform, index, 'action', val)
                    }
                    className='space-y-2'
                  >
                    {cloudActionOptions.map(option => (
                      <div key={option} className='flex items-center space-x-2'>
                        <RadioGroupItem
                          value={option}
                          id={`cloud-${platform}-${index}-${option}`}
                        />
                        <Label htmlFor={`cloud-${platform}-${index}-${option}`}>
                          {option}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <TextAreaField
                  label='Please let us know if there is anything specific regarding this account we should be aware of. For example, if this account is for a business or you’ve built a personal brand using this account.'
                  value={account.notes}
                  onChange={e =>
                    updateCloudAccount(platform, index, 'notes', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeCloudAccount(platform, index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === accounts.length - 1 && (
                    <Button
                      type='button'
                      onClick={() => addCloudAccount(platform)}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Another {platform}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ))}
      </SubSection>
      <SubSection title='Device Wishes'>
        <TextAreaField
          label='What would you like us to do with your phone/tablet/computer, file storage, photos, creative works of art/writing?'
          value={data.deviceWishes}
          onChange={e => update('deviceWishes', e.target.value)}
        />
      </SubSection>
      <SubSection title='Additional Notes'>
        <TextAreaField
          label='Is there anything else you would like to share?'
          value={data.additionalNotes}
          onChange={e => update('additionalNotes', e.target.value)}
        />
      </SubSection>
    </FormSection>
  );
};

export default PersonalIdentificationSection;
