'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import FormField from '../form-field';
import ConditionalSection from '../conditional-section';
import Text<PERSON>reaField from '../text-area-field';
import ContactSelect from '../contact-select';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from 'lucide-react';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';
import PeopleSelectObject from '@/components/interview-v2/PeopleSelectObject';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/utils/interviewV2Progress';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

interface EmergencyContactsSectionProps {
  disabled?: boolean;
}

const EmergencyContactsSection: React.FC<EmergencyContactsSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();
  const { people } = usePeopleLibrary();
  const { data: interviewData } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
  });

  const [data, setData] = useState({
    importantEmergency: '',
    contacts: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        isPrimary: null as boolean | null,
        isAuthorized: null as boolean | null,
        contactTiming: '',
        notes: '',
      },
    ],
    hasContacts: null as boolean | null,
    hasNonContacts: null as boolean | null,
    nonContacts: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        contactWhen: null as boolean | null,
        contactWhenDetails: '',
        allowPresence: null as boolean | null,
        allowPresenceWhen: '',
        notes: '',
      },
    ],
    hasCareFor: null as boolean | null,
    careForPersons: [
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        careType: '',
        fillIn: '',
        contactTiming: '',
        notes: '',
      },
    ],
    additionalInstructions: '',
  });

  useLinkedAccountPrefill('EmergencyContacts', sectionData.Medical, setData);
  // useInterviewPrefill('EmergencyContacts', sectionData.EmergencyContacts, setData);

  // Load data from context when available
  useEffect(() => {
    if (sectionData.EmergencyContacts) {
      setData(sectionData.EmergencyContacts);
    }
  }, [sectionData.EmergencyContacts]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('EmergencyContacts', newData);
  };

  const updateContact = (index: number, field: string, value: any) => {
    const newContacts = [...data.contacts];
    newContacts[index] = { ...newContacts[index], [field]: value };
    update('contacts', newContacts);
  };

  const updateContactWithValues = (
    index: number,
    updates: Record<string, any>
  ) => {
    const newContacts = [...data.contacts];
    newContacts[index] = { ...newContacts[index], ...updates };
    update('contacts', newContacts);
  };

  const addContact = () => {
    update('contacts', [
      ...data.contacts,
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        isPrimary: null,
        isAuthorized: null,
        contactTiming: '',
        notes: '',
      },
    ]);
  };

  const removeContact = (index: number) => {
    update(
      'contacts',
      data.contacts.filter((_, i) => i !== index)
    );
  };

  const updateNonContact = (index: number, field: string, value: any) => {
    const newNonContacts = [...data.nonContacts];
    newNonContacts[index] = { ...newNonContacts[index], [field]: value };
    update('nonContacts', newNonContacts);
  };

  const updateNonContactWithValues = (
    index: number,
    updates: Record<string, any>
  ) => {
    const newContacts = [...data.contacts];
    newContacts[index] = { ...newContacts[index], ...updates };
    update('nonContacts', newContacts);
  };

  const addNonContact = () => {
    update('nonContacts', [
      ...data.nonContacts,
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        contactWhen: null,
        contactWhenDetails: '',
        allowPresence: null,
        allowPresenceWhen: '',
        notes: '',
      },
    ]);
  };

  const removeNonContact = (index: number) => {
    update(
      'nonContacts',
      data.nonContacts.filter((_, i) => i !== index)
    );
  };

  const updateCareFor = (index: number, field: string, value: any) => {
    const newCareFor = [...data.careForPersons];
    newCareFor[index] = { ...newCareFor[index], [field]: value };
    update('careForPersons', newCareFor);
  };

  const updateCareForWithValues = (
    index: number,
    updates: Record<string, any>
  ) => {
    const newContacts = [...data.contacts];
    newContacts[index] = { ...newContacts[index], ...updates };
    update('careForPersons', newContacts);
  };

  const addCareFor = () => {
    update('careForPersons', [
      ...data.careForPersons,
      {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        careType: '',
        fillIn: '',
        contactTiming: '',
        notes: '',
      },
    ]);
  };

  const removeCareFor = (index: number) => {
    update(
      'careForPersons',
      data.careForPersons.filter((_, i) => i !== index)
    );
  };

  const contactTimingOptions = [
    { label: 'As Soon As Possible', value: 'asap' },
    { label: 'Within 24 hours', value: '24h' },
    { label: "After I'm better", value: 'after' },
    { label: "I don't have a preference", value: 'no_pref' },
  ];

  // Function to prefill contact data from interview data
  const prefillContactData = (index: number, contact: any) => {
    updateContactWithValues(index, {
      name: `${contact.firstName} ${contact.lastName}`,
      phone: contact.phoneNumber || '',
      // "email": contact.email || "",
      address1: contact.address || '',
      relationship: contact.relationshipType || '',
    });
  };

  // Function to prefill non-contact data from interview data
  const prefillNonContactData = (index: number, contact: any) => {
    updateNonContactWithValues(index, {
      name: `${contact.firstName} ${contact.lastName}`,
      phone: contact.phoneNumber || '',
      // "email": contact.email || "",
      address1: contact.address || '',
      relationship: contact.relationshipType || '',
    });
  };

  // Function to prefill care-for data from interview data
  const prefillCareForData = (index: number, contact: any) => {
    updateCareForWithValues(index, {
      name: `${contact.firstName} ${contact.lastName}`,
      phone: contact.phoneNumber || '',
      // "email": contact.email || "",
      address1: contact.address || '',
      relationship: contact.relationshipType || '',
    });
  };

  // Get contacts from interview data
  const getContactsFromInterviewData = () => {
    if (
      !interviewData ||
      !interviewData.stepsData ||
      !interviewData.stepsData.people
    ) {
      return [];
    }
    return interviewData.stepsData.people;
  };

  const contacts = getContactsFromInterviewData();

  return (
    <FormSection
      title='Emergency Contacts'
      description='Please provide information about your emergency contacts.'
      disabled={disabled}
    >
      <TextAreaField
        label='What is the most important thing for us to keep in mind during an emergency? (Example: Having medical care as quickly as possible, having family or friends contacted, etc.)'
        value={data.importantEmergency}
        onChange={e => update('importantEmergency', e.target.value)}
      />

      <SubSection title='Emergency Contacts'>
        <ConditionalSection
          label='Is there anyone you do want us to contact in the event of an emergency?'
          value={data.hasContacts}
          onChange={val => update('hasContacts', val)}
        >
          {data.contacts.map((contact, index) => (
            <div key={index} className='space-y-6 mb-6'>
              {index > 0 && (
                <div className='border-t border-gray-200 pt-6'></div>
              )}

              {/* <ContactSelect
                label="Select a contact from your interview data"
                contacts={contacts}
                onSelect={(selectedContact) => prefillContactData(index, selectedContact)}
              /> */}

              <PeopleSelectObject
                // value={people.find(it => it.id = contact.prefilledFromPeopleLibraryId)}
                onValueChange={selectedContact =>
                  prefillContactData(index, selectedContact)
                }
                people={people}
                onPeopleChange={val => {
                  console.log('People changed:', val);
                }}
                placeholder='Select a contact from your interview data to prefill values'
                noneOptionText='Select a contact from your interview data to prefill values'
                filterType='individual'
              />

              <FormField
                label='Contact Name'
                value={contact.name}
                onChange={e => updateContact(index, 'name', e.target.value)}
                required
              />

              <FormField
                label='Relationship'
                value={contact.relationship}
                onChange={e =>
                  updateContact(index, 'relationship', e.target.value)
                }
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={contact.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateContact(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email'
                type='email'
                value={contact.email}
                onChange={e => updateContact(index, 'email', e.target.value)}
              />

              <FormField
                label='Address Line 1'
                value={contact.address1}
                onChange={e => updateContact(index, 'address1', e.target.value)}
              />

              <FormField
                label='Address Line 2'
                value={contact.address2}
                onChange={e => updateContact(index, 'address2', e.target.value)}
              />

              <FormField
                label='City'
                value={contact.city}
                onChange={e => updateContact(index, 'city', e.target.value)}
              />

              <FormField
                label='State'
                value={contact.state}
                onChange={e => updateContact(index, 'state', e.target.value)}
              />

              <FormField
                label='Zip Code'
                value={contact.zip}
                onChange={e => updateContact(index, 'zip', e.target.value)}
              />

              <FormField
                label='Country'
                value={contact.country}
                onChange={e => updateContact(index, 'country', e.target.value)}
              />

              <ConditionalSection
                label='Is this your primary emergency contact?'
                value={contact.isPrimary}
                onChange={val => updateContact(index, 'isPrimary', val)}
              >
                <div></div>
              </ConditionalSection>

              <ConditionalSection
                label='Is this contact authorized to make decisions on your behalf?'
                value={contact.isAuthorized}
                onChange={val => updateContact(index, 'isAuthorized', val)}
              >
                <div></div>
              </ConditionalSection>

              <div className='space-y-3'>
                <Label>When should we try to contact them?</Label>
                <RadioGroup
                  value={contact.contactTiming}
                  onValueChange={val =>
                    updateContact(index, 'contactTiming', val)
                  }
                  className='space-y-2'
                >
                  {contactTimingOptions.map(option => (
                    <div
                      key={option.value}
                      className='flex items-center space-x-2'
                    >
                      <RadioGroupItem
                        value={option.value}
                        id={`contact-${index}-${option.value}`}
                      />
                      <Label htmlFor={`contact-${index}-${option.value}`}>
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <TextAreaField
                label='Is there anything else we should know when contacting them? Ex: Do you want them to be with you? Simply notify them?'
                value={contact.notes}
                onChange={e => updateContact(index, 'notes', e.target.value)}
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.contacts.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeContact(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.contacts.length - 1 && (
                  <Button
                    type='button'
                    onClick={addContact}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Another Contact
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <ConditionalSection
          label='Is there anyone you specifically DO NOT want to be with you in the event of an emergency or hospitalization?'
          value={data.hasNonContacts}
          onChange={val => update('hasNonContacts', val)}
        >
          {data.nonContacts.map((nonContact, index) => (
            <div key={index} className='space-y-6 mb-6'>
              {index > 0 && (
                <div className='border-t border-gray-200 pt-6'></div>
              )}

              {/* <ContactSelect
                label="Select a contact from your interview data"
                contacts={contacts}
                onSelect={(selectedContact) => prefillNonContactData(index, selectedContact)}
              /> */}

              <PeopleSelectObject
                // value={people.find(it => it.id = contact.prefilledFromPeopleLibraryId)}
                onValueChange={selectedContact =>
                  prefillNonContactData(index, selectedContact)
                }
                people={people}
                onPeopleChange={val => {
                  console.log('People changed:', val);
                }}
                placeholder='Select a contact from your interview data to prefill values'
                noneOptionText='Select a contact from your interview data to prefill values'
                filterType='individual'
              />

              <FormField
                label='Name'
                value={nonContact.name}
                onChange={e => updateNonContact(index, 'name', e.target.value)}
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={nonContact.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateNonContact(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email Address'
                type='email'
                value={nonContact.email}
                onChange={e => updateNonContact(index, 'email', e.target.value)}
              />

              <FormField
                label='Address Line 1'
                value={nonContact.address1}
                onChange={e =>
                  updateNonContact(index, 'address1', e.target.value)
                }
              />

              <FormField
                label='Address Line 2'
                value={nonContact.address2}
                onChange={e =>
                  updateNonContact(index, 'address2', e.target.value)
                }
              />

              <FormField
                label='City'
                value={nonContact.city}
                onChange={e => updateNonContact(index, 'city', e.target.value)}
              />

              <FormField
                label='State'
                value={nonContact.state}
                onChange={e => updateNonContact(index, 'state', e.target.value)}
              />

              <FormField
                label='Zip Code'
                value={nonContact.zip}
                onChange={e => updateNonContact(index, 'zip', e.target.value)}
              />

              <FormField
                label='Country'
                value={nonContact.country}
                onChange={e =>
                  updateNonContact(index, 'country', e.target.value)
                }
              />

              <FormField
                label='Relationship to Client'
                value={nonContact.relationship}
                onChange={e =>
                  updateNonContact(index, 'relationship', e.target.value)
                }
              />

              <ConditionalSection
                label='Should they ever be contacted?'
                value={nonContact.contactWhen}
                onChange={val => updateNonContact(index, 'contactWhen', val)}
              >
                <TextAreaField
                  label="If you selected 'Yes,' please tell us when:"
                  value={nonContact.contactWhenDetails}
                  onChange={e =>
                    updateNonContact(
                      index,
                      'contactWhenDetails',
                      e.target.value
                    )
                  }
                />
              </ConditionalSection>

              <ConditionalSection
                label='Should they ever be allowed in your presence while hospitalized, incapacitated, or during passing in the event they find out about your condition from another individual?'
                value={nonContact.allowPresence}
                onChange={val => updateNonContact(index, 'allowPresence', val)}
              >
                <TextAreaField
                  label="If you selected 'Yes,' please tell us when:"
                  value={nonContact.allowPresenceWhen}
                  onChange={e =>
                    updateNonContact(index, 'allowPresenceWhen', e.target.value)
                  }
                />
              </ConditionalSection>

              <TextAreaField
                label='Is there anything else we should know in relation to this person? Please provide details as you are comfortable.'
                value={nonContact.notes}
                onChange={e => updateNonContact(index, 'notes', e.target.value)}
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.nonContacts.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeNonContact(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.nonContacts.length - 1 && (
                  <Button
                    type='button'
                    onClick={addNonContact}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Person
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>
      </SubSection>

      <SubSection title='People You Care For'>
        <ConditionalSection
          label='Is there anyone you currently care for that we need to contact in the event of an emergency?'
          value={data.hasCareFor}
          onChange={val => update('hasCareFor', val)}
        >
          {data.careForPersons.map((person, index) => (
            <div key={index} className='space-y-6 mb-6'>
              {index > 0 && (
                <div className='border-t border-gray-200 pt-6'></div>
              )}

              {/* <ContactSelect
                label="Select a contact from your interview data"
                contacts={contacts}
                onSelect={(selectedContact) => prefillCareForData(index, selectedContact)}
              /> */}

              <PeopleSelectObject
                // value={people.find(it => it.id = contact.prefilledFromPeopleLibraryId)}
                onValueChange={selectedContact =>
                  prefillCareForData(index, selectedContact)
                }
                people={people}
                onPeopleChange={val => {
                  console.log('People changed:', val);
                }}
                placeholder='Select a contact from your interview data to prefill values'
                noneOptionText='Select a contact from your interview data to prefill values'
                filterType='individual'
              />

              <FormField
                label='Name'
                value={person.name}
                onChange={e => updateCareFor(index, 'name', e.target.value)}
              />

              <FormField
                label='Phone Number'
                type='tel'
                value={person.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateCareFor(index, 'phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />

              <FormField
                label='Email Address'
                type='email'
                value={person.email}
                onChange={e => updateCareFor(index, 'email', e.target.value)}
              />

              <FormField
                label='Address Line 1'
                value={person.address1}
                onChange={e => updateCareFor(index, 'address1', e.target.value)}
              />

              <FormField
                label='Address Line 2'
                value={person.address2}
                onChange={e => updateCareFor(index, 'address2', e.target.value)}
              />

              <FormField
                label='City'
                value={person.city}
                onChange={e => updateCareFor(index, 'city', e.target.value)}
              />

              <FormField
                label='State'
                value={person.state}
                onChange={e => updateCareFor(index, 'state', e.target.value)}
              />

              <FormField
                label='Zip Code'
                value={person.zip}
                onChange={e => updateCareFor(index, 'zip', e.target.value)}
              />

              <FormField
                label='Country'
                value={person.country}
                onChange={e => updateCareFor(index, 'country', e.target.value)}
              />

              <FormField
                label='Relationship to Client'
                value={person.relationship}
                onChange={e =>
                  updateCareFor(index, 'relationship', e.target.value)
                }
              />

              <TextAreaField
                label='Type of Care I provide & Frequency:'
                value={person.careType}
                onChange={e => updateCareFor(index, 'careType', e.target.value)}
              />

              <FormField
                label='Who Can Fill In for me:'
                value={person.fillIn}
                onChange={e => updateCareFor(index, 'fillIn', e.target.value)}
              />

              <div className='space-y-3'>
                <Label>When should we try to contact them?</Label>
                <RadioGroup
                  value={person.contactTiming}
                  onValueChange={val =>
                    updateCareFor(index, 'contactTiming', val)
                  }
                  className='space-y-2'
                >
                  {contactTimingOptions.map(option => (
                    <div
                      key={option.value}
                      className='flex items-center space-x-2'
                    >
                      <RadioGroupItem
                        value={option.value}
                        id={`carefor-${index}-${option.value}`}
                      />
                      <Label htmlFor={`carefor-${index}-${option.value}`}>
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <TextAreaField
                label='Is there anything else we should know when contacting them? Ex: Do you want them to be with you? Simply notify them?'
                value={person.notes}
                onChange={e => updateCareFor(index, 'notes', e.target.value)}
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.careForPersons.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeCareFor(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.careForPersons.length - 1 && (
                  <Button
                    type='button'
                    onClick={addCareFor}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Person You Care For
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>
      </SubSection>

      <TextAreaField
        label='Additional Emergency Instructions'
        value={data.additionalInstructions}
        onChange={e => update('additionalInstructions', e.target.value)}
        placeholder='Please provide any additional instructions for emergency situations.'
      />
    </FormSection>
  );
};

export default EmergencyContactsSection;
