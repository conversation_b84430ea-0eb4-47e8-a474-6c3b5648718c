'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import ConditionalSection from '../conditional-section';
import <PERSON><PERSON>ield from '../form-field';
import <PERSON><PERSON>reaField from '../text-area-field';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import PeopleSelectObject from '@/components/interview-v2/PeopleSelectObject';
import { usePeopleLibrary } from '@/hooks/usePeopleLibrary';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

interface PetsSectionProps {
  disabled?: boolean;
}

const PetsSection: React.FC<PetsSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    hasPets: null as boolean | null,
    pets: [] as Array<{
      name: string;
      type: string;
      breed: string;
      dob: string; // Changed from age to date of birth
      veterinarian: string;
      vetPhone: string;
      // New fields from mockup
      isMicrochipped: boolean | null;
      microchipNumber: string;
      hasPreferredBoarding: boolean | null;
      boardingName: string;
      boardingPhone: string;
      boardingAddress: string;
      hasPreferredGuardian: boolean | null;
      guardianName: string;
      guardianPhone: string;
      guardianEmail: string;
      hasPreferredSitter: boolean | null;
      sitterName: string;
      sitterPhone: string;
      sitterEmail: string;
      specificInfo: string;
      shortTermEmergencyInfo: string;
    }>,
    additionalInstructions: '',
  });

  // Use our custom hook for pre-filling data from interview
  useInterviewPrefill('Pets', sectionData.Pets, setData);
  useLinkedAccountPrefill('Pets', sectionData.Pets, setData);
  const { people } = usePeopleLibrary();

  useEffect(() => {
    if (sectionData.Pets) {
      setData(sectionData.Pets);
    }
  }, [sectionData.Pets]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Pets', newData);
    console.log(`Updated ${field}:`, value);
  };

  const updatePet = (index: number, field: string, value: any) => {
    const newPets = [...data.pets];
    newPets[index] = { ...newPets[index], [field]: value };
    update('pets', newPets);
  };

  const updatePetWithValues = (index: number, updates: Record<string, any>) => {
    const newPets = [...data.pets];
    newPets[index] = { ...newPets[index], ...updates };
    update('pets', newPets);
  };

  const prefillPetsGuardianData = (index: number, contact: any) => {
    updatePetWithValues(index, {
      guardianName: `${contact.firstName} ${contact.lastName}`,
      guardianPhone: contact.phoneNumber || '',
      guardianEmail: contact.email || '',
    });
  };

  const prefillPetsSitterData = (index: number, contact: any) => {
    updatePetWithValues(index, {
      sitterName: `${contact.firstName} ${contact.lastName}`,
      sitterPhone: contact.phoneNumber || '',
      sitterEmail: contact.email || '',
    });
  };

  const addPet = () => {
    update('pets', [
      ...data.pets,
      {
        name: '',
        type: '',
        breed: '',
        dob: '', // Changed from age to date of birth
        veterinarian: '',
        vetPhone: '',
        // New fields from mockup
        isMicrochipped: null,
        microchipNumber: '',
        hasPreferredBoarding: null,
        boardingName: '',
        boardingPhone: '',
        boardingAddress: '',
        hasPreferredGuardian: null,
        guardianName: '',
        guardianPhone: '',
        guardianEmail: '',
        hasPreferredSitter: null,
        sitterName: '',
        sitterPhone: '',
        sitterEmail: '',
        specificInfo: '',
        shortTermEmergencyInfo: '',
      },
    ]);
  };

  const removePet = (index: number) => {
    update(
      'pets',
      data.pets.filter((_, i) => i !== index)
    );
  };

  return (
    <FormSection
      title='Pets'
      description='Please provide information about your pets and their care.'
      disabled={disabled}
    >
      <ConditionalSection
        label='Do you have pets?'
        value={data.hasPets}
        onChange={val => {
          update('hasPets', val);
          if (val && data.pets.length === 0) {
            addPet();
          }
        }}
      >
        {data.pets.map((pet, index) => (
          <div key={index} className='space-y-6 mb-6'>
            {index > 0 && <div className='border-t border-gray-200 pt-6'></div>}
            <FormField
              label='Pet Name'
              value={pet.name}
              onChange={e => updatePet(index, 'name', e.target.value)}
              required
            />

            <FormField
              label='Type of Pet'
              value={pet.type}
              onChange={e => updatePet(index, 'type', e.target.value)}
              placeholder='Dog, Cat, Bird, etc.'
            />

            <FormField
              label='Breed/Species'
              value={pet.breed}
              onChange={e => updatePet(index, 'breed', e.target.value)}
            />

            <FormField
              label='Date of Birth'
              type='date'
              value={pet.dob}
              onChange={e => updatePet(index, 'dob', e.target.value)}
            />

            <FormField
              label='Veterinarian Name'
              value={pet.veterinarian}
              onChange={e => updatePet(index, 'veterinarian', e.target.value)}
            />

            <FormField
              label='Veterinarian Phone'
              type='tel'
              value={pet.vetPhone || '+1'}
              onChange={e => {
                const formattedValue = formatPhoneNumber(e.target.value);
                updatePet(index, 'vetPhone', formattedValue);
              }}
              placeholder='+1XXXXXXXXXX'
            />

            {/* New fields from mockup */}
            <ConditionalSection
              label='Is your pet micro chipped?'
              value={pet.isMicrochipped}
              onChange={val => updatePet(index, 'isMicrochipped', val)}
            >
              <FormField
                label='Microchip #'
                value={pet.microchipNumber}
                onChange={e =>
                  updatePet(index, 'microchipNumber', e.target.value)
                }
              />
            </ConditionalSection>

            <ConditionalSection
              label='Do you have a preferred boarding facility or daycare for your pet?'
              value={pet.hasPreferredBoarding}
              onChange={val => updatePet(index, 'hasPreferredBoarding', val)}
            >
              <FormField
                label='Boarding and/or Daycare Facility Name'
                value={pet.boardingName}
                onChange={e => updatePet(index, 'boardingName', e.target.value)}
              />
              <FormField
                label='Boarding and/or Daycare Phone Number'
                type='tel'
                value={pet.boardingPhone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePet(index, 'boardingPhone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Boarding and/or Daycare Address'
                type='tel'
                value={pet.boardingAddress}
                onChange={e =>
                  updatePet(index, 'boardingAddress', e.target.value)
                }
              />
            </ConditionalSection>

            <ConditionalSection
              label='Do you have a preferred guardian for your pet?'
              value={pet.hasPreferredGuardian}
              onChange={val => updatePet(index, 'hasPreferredGuardian', val)}
            >
              <PeopleSelectObject
                onValueChange={selectedContact =>
                  prefillPetsGuardianData(index, selectedContact)
                }
                people={people}
                onPeopleChange={val => {
                  console.log('People changed:', val);
                }}
                placeholder='Select a contact from your interview data to prefill values'
                noneOptionText='Select a contact from your interview data to prefill values'
                filterType='individual'
              />
              <FormField
                label='Name'
                value={pet.guardianName}
                onChange={e => updatePet(index, 'guardianName', e.target.value)}
              />
              <FormField
                label='Phone Number'
                type='tel'
                value={pet.guardianPhone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePet(index, 'guardianPhone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                type='email'
                value={pet.guardianEmail}
                onChange={e =>
                  updatePet(index, 'guardianEmail', e.target.value)
                }
              />
            </ConditionalSection>

            <ConditionalSection
              label='Do you have a preferred pet sitter/walker for your pet?'
              value={pet.hasPreferredSitter}
              onChange={val => updatePet(index, 'hasPreferredSitter', val)}
            >
              <PeopleSelectObject
                onValueChange={selectedContact =>
                  prefillPetsSitterData(index, selectedContact)
                }
                people={people}
                onPeopleChange={val => {
                  console.log('People changed:', val);
                }}
                placeholder='Select a contact from your interview data to prefill values'
                noneOptionText='Select a contact from your interview data to prefill values'
                filterType='individual'
              />
              <FormField
                label='Name'
                value={pet.sitterName}
                onChange={e => updatePet(index, 'sitterName', e.target.value)}
              />
              <FormField
                label='Phone Number'
                type='tel'
                value={pet.sitterPhone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePet(index, 'sitterPhone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                type='email'
                value={pet.sitterEmail}
                onChange={e => updatePet(index, 'sitterEmail', e.target.value)}
              />
            </ConditionalSection>

            <TextAreaField
              label='Is there anything specific we should know about this pet? [Allergies, behavioral issues, medications, treats, regular routine, etc.]'
              value={pet.specificInfo}
              onChange={e => updatePet(index, 'specificInfo', e.target.value)}
            />

            <TextAreaField
              label='Short Term Emergency: Is there anything specific we should do differently than what is listed above (contact, guardian, etc)'
              value={pet.shortTermEmergencyInfo}
              onChange={e =>
                updatePet(index, 'shortTermEmergencyInfo', e.target.value)
              }
            />

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              {data.pets.length > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  className='text-red-600 hover:text-red-700'
                  onClick={() => removePet(index)}
                >
                  <Trash2 className='h-4 w-4 mr-2' />
                  Remove
                </Button>
              )}
              {index === data.pets.length - 1 && (
                <Button
                  type='button'
                  onClick={addPet}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Another Pet
                </Button>
              )}
            </div>
          </div>
        ))}

        <TextAreaField
          label='Additional Pet Care Instructions'
          value={data.additionalInstructions}
          onChange={e => update('additionalInstructions', e.target.value)}
          placeholder='Any additional instructions for the care of your pets'
        />
      </ConditionalSection>
    </FormSection>
  );
};

export default PetsSection;
