'use client';

import { useState, useEffect, useMemo } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import Text<PERSON>reaField from '../text-area-field';
import FormField from '../form-field';
import ConditionalSection from '../conditional-section';
import RadioGroup from '../radio-group';
import CheckboxGroup from '../checkbox-group';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/utils/interviewV2Progress';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

// Define blank objects for new entities
const BLANK_FUNERAL_HOME = {
  organization: '',
  directorName: '',
  phone: '',
  email: '',
  website: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
  isPrepaid: null,
};

const BLANK_SERVICE_PLACE = {
  place: '',
  phone: '',
  email: '',
  website: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
};

const BLANK_VISITATION_PLACE = {
  place: '',
  phone: '',
  email: '',
  website: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
};

const BLANK_OFFICIANT = {
  name: '',
  phone: '',
  email: '',
  website: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  country: '',
};

const BLANK_PALLBEARER = {
  name: '',
  phone: '',
  email: '',
};

const BLANK_PUBLICATION = {
  name: '',
  website: '',
  type: '',
  phone: '',
};

const BLANK_MILITARY = {
  serviceNumber: '',
  branch: '',
  enteredDate: '',
  enteredPlace: '',
  separationDate: '',
  separationPlace: '',
  highestRank: '',
  dischargePapers: '',
  serviceLocation: '',
};

const BLANK_FIRST_RESPONDER = {
  department: '',
  enteredDate: '',
  enteredPlace: '',
  separationDate: '',
  separationPlace: '',
  highestRank: '',
};

const BLANK_FLAG_RECIPIENT = {
  name: '',
  phone: '',
  email: '',
};

interface LastWishesSectionProps {
  disabled?: boolean;
}

const LastWishesSection: React.FC<LastWishesSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const { data: interviewData } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
  });

  const [data, setData] = useState({
    // Existing fields
    funeralPreferences: '',
    memorialPreferences: '',
    burialPreferences: '',
    cremationPreferences: '',
    obituaryPreferences: '',
    otherRemainsPreferences: '',
    memorialDonations: '',
    digitalLegacy: '',
    additionalInstructions: '',

    // New fields from mockup
    hasServicePlan: null as boolean | null,
    funeralHomes: [] as (typeof BLANK_FUNERAL_HOME)[],
    hasPreferredFuneral: null as boolean | null,
    preferredFuneralHomes: [] as (typeof BLANK_FUNERAL_HOME)[],
    wantsService: null as boolean | null,
    servicePlaces: [] as (typeof BLANK_SERVICE_PLACE)[],
    wantsVisitation: null as boolean | null,
    visitationPlaces: [] as (typeof BLANK_VISITATION_PLACE)[],
    officiants: [] as (typeof BLANK_OFFICIANT)[],
    hasPallbearers: null as boolean | null,
    pallbearers: [] as (typeof BLANK_PALLBEARER)[],
    wantsFlowers: null as boolean | null,
    flowerPreferences: '',
    readingsSongs: '',
    burialInfo: '',
    wantsObituary: null as boolean | null,
    obituaryInfo: '',
    publications: [] as (typeof BLANK_PUBLICATION)[],
    obituaryNotes: '',
    affiliations: [] as string[],
    military: [] as (typeof BLANK_MILITARY)[],
    firstResponders: [] as (typeof BLANK_FIRST_RESPONDER)[],
    wantsHonors: null as boolean | null,
    wantsPresence: null as boolean | null,
    presenceDetails: '',
    flagRecipients: [] as (typeof BLANK_FLAG_RECIPIENT)[],
  });

  useLinkedAccountPrefill('LastWishes', sectionData.Medical, setData);
  // useInterviewPrefill('LastWishes', sectionData.LastWishes, setData);

  useEffect(() => {
    if (sectionData.LastWishes) {
      setData(sectionData.LastWishes);
    }
  }, [sectionData.LastWishes]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('LastWishes', newData);
    console.log(`Updated ${field}:`, value);
  };

  // Funeral Home management
  const addFuneralHome = () => {
    update('funeralHomes', [...data.funeralHomes, { ...BLANK_FUNERAL_HOME }]);
  };

  const removeFuneralHome = (index: number) => {
    update(
      'funeralHomes',
      data.funeralHomes.filter((_, i) => i !== index)
    );
  };

  const updateFuneralHome = (index: number, field: string, value: any) => {
    const newHomes = [...data.funeralHomes];
    newHomes[index] = { ...newHomes[index], [field]: value };
    update('funeralHomes', newHomes);
  };

  // Preferred Funeral Home management
  const addPreferredFuneralHome = () => {
    update('preferredFuneralHomes', [
      ...data.preferredFuneralHomes,
      { ...BLANK_FUNERAL_HOME },
    ]);
  };

  const removePreferredFuneralHome = (index: number) => {
    update(
      'preferredFuneralHomes',
      data.preferredFuneralHomes.filter((_, i) => i !== index)
    );
  };

  const updatePreferredFuneralHome = (
    index: number,
    field: string,
    value: any
  ) => {
    const newHomes = [...data.preferredFuneralHomes];
    newHomes[index] = { ...newHomes[index], [field]: value };
    update('preferredFuneralHomes', newHomes);
  };

  // Service Place management
  const addServicePlace = () => {
    update('servicePlaces', [
      ...data.servicePlaces,
      { ...BLANK_SERVICE_PLACE },
    ]);
  };

  const removeServicePlace = (index: number) => {
    update(
      'servicePlaces',
      data.servicePlaces.filter((_, i) => i !== index)
    );
  };

  const updateServicePlace = (index: number, field: string, value: any) => {
    const newPlaces = [...data.servicePlaces];
    newPlaces[index] = { ...newPlaces[index], [field]: value };
    update('servicePlaces', newPlaces);
  };

  // Visitation Place management
  const addVisitationPlace = () => {
    update('visitationPlaces', [
      ...data.visitationPlaces,
      { ...BLANK_VISITATION_PLACE },
    ]);
  };

  const removeVisitationPlace = (index: number) => {
    update(
      'visitationPlaces',
      data.visitationPlaces.filter((_, i) => i !== index)
    );
  };

  const updateVisitationPlace = (index: number, field: string, value: any) => {
    const newPlaces = [...data.visitationPlaces];
    newPlaces[index] = { ...newPlaces[index], [field]: value };
    update('visitationPlaces', newPlaces);
  };

  // Officiant management
  const addOfficiant = () => {
    update('officiants', [...data.officiants, { ...BLANK_OFFICIANT }]);
  };

  const removeOfficiant = (index: number) => {
    update(
      'officiants',
      data.officiants.filter((_, i) => i !== index)
    );
  };

  const updateOfficiant = (index: number, field: string, value: any) => {
    const newOfficiants = [...data.officiants];
    newOfficiants[index] = { ...newOfficiants[index], [field]: value };
    update('officiants', newOfficiants);
  };

  // Pallbearer management
  const addPallbearer = () => {
    update('pallbearers', [...data.pallbearers, { ...BLANK_PALLBEARER }]);
  };

  const removePallbearer = (index: number) => {
    update(
      'pallbearers',
      data.pallbearers.filter((_, i) => i !== index)
    );
  };

  const updatePallbearer = (index: number, field: string, value: any) => {
    const newPallbearers = [...data.pallbearers];
    newPallbearers[index] = { ...newPallbearers[index], [field]: value };
    update('pallbearers', newPallbearers);
  };

  // Publication management
  const addPublication = () => {
    update('publications', [...data.publications, { ...BLANK_PUBLICATION }]);
  };

  const removePublication = (index: number) => {
    update(
      'publications',
      data.publications.filter((_, i) => i !== index)
    );
  };

  const updatePublication = (index: number, field: string, value: any) => {
    const newPublications = [...data.publications];
    newPublications[index] = { ...newPublications[index], [field]: value };
    update('publications', newPublications);
  };

  // Affiliation management
  const toggleAffiliation = (aff: string) => {
    update(
      'affiliations',
      data.affiliations.includes(aff)
        ? data.affiliations.filter(a => a !== aff)
        : [...data.affiliations, aff]
    );
  };

  // Military management
  const addMilitary = () => {
    update('military', [...data.military, { ...BLANK_MILITARY }]);
  };

  const removeMilitary = (index: number) => {
    update(
      'military',
      data.military.filter((_, i) => i !== index)
    );
  };

  const updateMilitary = (index: number, field: string, value: any) => {
    const newMilitary = [...data.military];
    newMilitary[index] = { ...newMilitary[index], [field]: value };
    update('military', newMilitary);
  };

  // First Responder management
  const addFirstResponder = () => {
    update('firstResponders', [
      ...data.firstResponders,
      { ...BLANK_FIRST_RESPONDER },
    ]);
  };

  const removeFirstResponder = (index: number) => {
    update(
      'firstResponders',
      data.firstResponders.filter((_, i) => i !== index)
    );
  };

  const updateFirstResponder = (index: number, field: string, value: any) => {
    const newFirstResponders = [...data.firstResponders];
    newFirstResponders[index] = {
      ...newFirstResponders[index],
      [field]: value,
    };
    update('firstResponders', newFirstResponders);
  };

  // Flag Recipient management
  const addFlagRecipient = () => {
    update('flagRecipients', [
      ...data.flagRecipients,
      { ...BLANK_FLAG_RECIPIENT },
    ]);
  };

  const removeFlagRecipient = (index: number) => {
    update(
      'flagRecipients',
      data.flagRecipients.filter((_, i) => i !== index)
    );
  };

  const updateFlagRecipient = (index: number, field: string, value: any) => {
    const newRecipients = [...data.flagRecipients];
    newRecipients[index] = { ...newRecipients[index], [field]: value };
    update('flagRecipients', newRecipients);
  };

  const affiliationOptions = ['Military', 'First Responder', 'No'];
  const publicationTypeOptions = [
    { label: 'Print', value: 'print' },
    { label: 'Electronic', value: 'electronic' },
  ];
  const disposition = useMemo(() => {
    return interviewData?.stepsData?.additional?.burial ?? null;
  }, [interviewData]);

  return (
    <FormSection
      title='Last Wishes'
      description='Please provide your preferences for end-of-life arrangements.'
      disabled={disabled}
    >
      <SubSection title='End-of-Life Preferences'>
        <TextAreaField
          label='Funeral or memorial service preferences'
          value={data.funeralPreferences}
          onChange={e => update('funeralPreferences', e.target.value)}
          placeholder='Tell us more about your wishes (Location, type of service, music, readings, etc.)'
        />

        <TextAreaField
          label='Memorial preferences'
          value={data.memorialPreferences}
          onChange={e => update('memorialPreferences', e.target.value)}
          placeholder='Tell us more about your wishes. How you would like to be remembered'
        />

        {disposition === 'buried' && (
          <TextAreaField
            label='Burial preferences (according to the interview)'
            value={data.burialPreferences}
            onChange={e => update('burialPreferences', e.target.value)}
            placeholder='Location, type of casket, headstone preferences, etc.'
          />
        )}

        {disposition === 'cremated' && (
          <TextAreaField
            label='Cremation preferences (according to the interview)'
            value={data.cremationPreferences}
            onChange={e => update('cremationPreferences', e.target.value)}
            placeholder='Disposition of ashes, type of urn, etc.'
          />
        )}

        {disposition === 'other' && (
          <TextAreaField
            label='Other preferences for my remains (according to the interview)'
            value={data.otherRemainsPreferences}
            onChange={e => update('otherRemainsPreferences', e.target.value)}
            placeholder='Describe your preferences...'
          />
        )}

        <TextAreaField
          label='Obituary preferences'
          value={data.obituaryPreferences}
          onChange={e => update('obituaryPreferences', e.target.value)}
          placeholder='Information to include or exclude in obituary'
        />

        <TextAreaField
          label='Memorial donations'
          value={data.memorialDonations}
          onChange={e => update('memorialDonations', e.target.value)}
          placeholder='Preferred charities or causes for memorial donations'
        />

        <TextAreaField
          label='Digital legacy wishes'
          value={data.digitalLegacy}
          onChange={e => update('digitalLegacy', e.target.value)}
          placeholder='Preferences for handling digital accounts and assets'
        />

        <TextAreaField
          label='Additional instructions or preferences'
          value={data.additionalInstructions}
          onChange={e => update('additionalInstructions', e.target.value)}
        />
      </SubSection>

      <SubSection title='Instructions & Information'>
        <ConditionalSection
          label='Do you already have a service plan in place at an existing funeral home or membership in something such as the Neptune Society?'
          value={data.hasServicePlan}
          onChange={val => {
            update('hasServicePlan', val);
            if (val && data.funeralHomes.length === 0) {
              addFuneralHome();
            }
          }}
        >
          {data.funeralHomes.map((home, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Funeral or Membership Organization'
                value={home.organization}
                onChange={e =>
                  updateFuneralHome(index, 'organization', e.target.value)
                }
              />
              <FormField
                label='Director Name'
                value={home.directorName}
                onChange={e =>
                  updateFuneralHome(index, 'directorName', e.target.value)
                }
              />
              <FormField
                label='Phone Number'
                value={home.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateFuneralHome(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={home.email}
                onChange={e =>
                  updateFuneralHome(index, 'email', e.target.value)
                }
                type='email'
              />
              <FormField
                label='Website'
                value={home.website}
                onChange={e =>
                  updateFuneralHome(index, 'website', e.target.value)
                }
              />
              <FormField
                label='Address Line 1'
                value={home.address1}
                onChange={e =>
                  updateFuneralHome(index, 'address1', e.target.value)
                }
              />
              <FormField
                label='Address Line 2'
                value={home.address2}
                onChange={e =>
                  updateFuneralHome(index, 'address2', e.target.value)
                }
              />
              <FormField
                label='City'
                value={home.city}
                onChange={e => updateFuneralHome(index, 'city', e.target.value)}
              />
              <FormField
                label='State'
                value={home.state}
                onChange={e =>
                  updateFuneralHome(index, 'state', e.target.value)
                }
              />
              <FormField
                label='Zip Code'
                value={home.zip}
                onChange={e => updateFuneralHome(index, 'zip', e.target.value)}
              />
              <FormField
                label='Country'
                value={home.country}
                onChange={e =>
                  updateFuneralHome(index, 'country', e.target.value)
                }
              />
              <ConditionalSection
                label='Has this already been prepaid?'
                value={home.isPrepaid}
                onChange={val => updateFuneralHome(index, 'isPrepaid', val)}
              >
                <div></div>
              </ConditionalSection>

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.funeralHomes.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeFuneralHome(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.funeralHomes.length - 1 && (
                  <Button
                    type='button'
                    onClick={addFuneralHome}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Funeral Home or Membership Organization
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        {!data.hasServicePlan && (
          <ConditionalSection
            label='Do you have a preferred funeral home or membership organization?'
            value={data.hasPreferredFuneral}
            onChange={val => {
              update('hasPreferredFuneral', val);
              if (val && data.preferredFuneralHomes.length === 0) {
                addPreferredFuneralHome();
              }
            }}
          >
            {data.preferredFuneralHomes.map((home, index) => (
              <div key={index} className='space-y-4 border p-4 rounded-xl'>
                <FormField
                  label='Funeral or Membership Organization'
                  value={home.organization}
                  onChange={e =>
                    updatePreferredFuneralHome(
                      index,
                      'organization',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Director Name'
                  value={home.directorName}
                  onChange={e =>
                    updatePreferredFuneralHome(
                      index,
                      'directorName',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Phone Number'
                  value={home.phone || '+1'}
                  onChange={e => {
                    const formattedValue = formatPhoneNumber(e.target.value);
                    updatePreferredFuneralHome(index, 'phone', formattedValue);
                  }}
                  type='tel'
                  placeholder='+1XXXXXXXXXX'
                />
                <FormField
                  label='Email Address'
                  value={home.email}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'email', e.target.value)
                  }
                  type='email'
                />
                <FormField
                  label='Website'
                  value={home.website}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'website', e.target.value)
                  }
                />
                <FormField
                  label='Address Line 1'
                  value={home.address1}
                  onChange={e =>
                    updatePreferredFuneralHome(
                      index,
                      'address1',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Address Line 2'
                  value={home.address2}
                  onChange={e =>
                    updatePreferredFuneralHome(
                      index,
                      'address2',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='City'
                  value={home.city}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'city', e.target.value)
                  }
                />
                <FormField
                  label='State'
                  value={home.state}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'state', e.target.value)
                  }
                />
                <FormField
                  label='Zip Code'
                  value={home.zip}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'zip', e.target.value)
                  }
                />
                <FormField
                  label='Country'
                  value={home.country}
                  onChange={e =>
                    updatePreferredFuneralHome(index, 'country', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  {data.preferredFuneralHomes.length > 1 && (
                    <Button
                      type='button'
                      variant='outline'
                      className='text-red-600 hover:text-red-700'
                      onClick={() => removePreferredFuneralHome(index)}
                    >
                      <Trash2 className='h-4 w-4 mr-2' />
                      Remove
                    </Button>
                  )}
                  {index === data.preferredFuneralHomes.length - 1 && (
                    <Button
                      type='button'
                      onClick={addPreferredFuneralHome}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Funeral Home or Membership Organization
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </ConditionalSection>
        )}

        <ConditionalSection
          label='Do you want to have a final service?'
          value={data.wantsService}
          onChange={val => {
            update('wantsService', val);
            if (val && data.servicePlaces.length === 0) {
              addServicePlace();
            }
          }}
        >
          <p className='text-sm text-gray-600'>
            Where should the service take place? If not at the funeral home,
            please provide as much detail as possible to ensure we can follow
            through with your wishes
          </p>
          {data.servicePlaces.map((place, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Place'
                value={place.place}
                onChange={e =>
                  updateServicePlace(index, 'place', e.target.value)
                }
              />
              <FormField
                label='Phone Number'
                value={place.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateServicePlace(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={place.email}
                onChange={e =>
                  updateServicePlace(index, 'email', e.target.value)
                }
                type='email'
              />
              <FormField
                label='Website'
                value={place.website}
                onChange={e =>
                  updateServicePlace(index, 'website', e.target.value)
                }
              />
              <FormField
                label='Address Line 1'
                value={place.address1}
                onChange={e =>
                  updateServicePlace(index, 'address1', e.target.value)
                }
              />
              <FormField
                label='Address Line 2'
                value={place.address2}
                onChange={e =>
                  updateServicePlace(index, 'address2', e.target.value)
                }
              />
              <FormField
                label='City'
                value={place.city}
                onChange={e =>
                  updateServicePlace(index, 'city', e.target.value)
                }
              />
              <FormField
                label='State'
                value={place.state}
                onChange={e =>
                  updateServicePlace(index, 'state', e.target.value)
                }
              />
              <FormField
                label='Zip Code'
                value={place.zip}
                onChange={e => updateServicePlace(index, 'zip', e.target.value)}
              />
              <FormField
                label='Country'
                value={place.country}
                onChange={e =>
                  updateServicePlace(index, 'country', e.target.value)
                }
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.servicePlaces.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeServicePlace(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.servicePlaces.length - 1 && (
                  <Button
                    type='button'
                    onClick={addServicePlace}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Service Place
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <ConditionalSection
          label='Do you want a visitation?'
          value={data.wantsVisitation}
          onChange={val => {
            update('wantsVisitation', val);
            if (val && data.visitationPlaces.length === 0) {
              addVisitationPlace();
            }
          }}
        >
          <p className='text-sm text-gray-600'>
            Where should the visitation take place? If not at the funeral home,
            please provide as much detail as possible to ensure we can follow
            through with your wishes
          </p>
          {data.visitationPlaces.map((place, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Place'
                value={place.place}
                onChange={e =>
                  updateVisitationPlace(index, 'place', e.target.value)
                }
              />
              <FormField
                label='Phone Number'
                value={place.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateVisitationPlace(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={place.email}
                onChange={e =>
                  updateVisitationPlace(index, 'email', e.target.value)
                }
                type='email'
              />
              <FormField
                label='Website'
                value={place.website}
                onChange={e =>
                  updateVisitationPlace(index, 'website', e.target.value)
                }
              />
              <FormField
                label='Address Line 1'
                value={place.address1}
                onChange={e =>
                  updateVisitationPlace(index, 'address1', e.target.value)
                }
              />
              <FormField
                label='Address Line 2'
                value={place.address2}
                onChange={e =>
                  updateVisitationPlace(index, 'address2', e.target.value)
                }
              />
              <FormField
                label='City'
                value={place.city}
                onChange={e =>
                  updateVisitationPlace(index, 'city', e.target.value)
                }
              />
              <FormField
                label='State'
                value={place.state}
                onChange={e =>
                  updateVisitationPlace(index, 'state', e.target.value)
                }
              />
              <FormField
                label='Zip Code'
                value={place.zip}
                onChange={e =>
                  updateVisitationPlace(index, 'zip', e.target.value)
                }
              />
              <FormField
                label='Country'
                value={place.country}
                onChange={e =>
                  updateVisitationPlace(index, 'country', e.target.value)
                }
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.visitationPlaces.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeVisitationPlace(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.visitationPlaces.length - 1 && (
                  <Button
                    type='button'
                    onClick={addVisitationPlace}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Visitation Place
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <Button
          type='button'
          onClick={() => {
            if (data.officiants.length === 0) {
              addOfficiant();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Officiant
        </Button>

        {data.officiants.map((officiant, index) => (
          <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
            <FormField
              label='Officiant Name'
              value={officiant.name}
              onChange={e => updateOfficiant(index, 'name', e.target.value)}
            />
            <FormField
              label='Phone Number'
              value={officiant.phone || '+1'}
              onChange={e => {
                const formattedValue = formatPhoneNumber(e.target.value);
                updateOfficiant(index, 'phone', formattedValue);
              }}
              type='tel'
              placeholder='+1XXXXXXXXXX'
            />
            <FormField
              label='Email Address'
              value={officiant.email}
              onChange={e => updateOfficiant(index, 'email', e.target.value)}
              type='email'
            />
            <FormField
              label='Website'
              value={officiant.website}
              onChange={e => updateOfficiant(index, 'website', e.target.value)}
            />
            <FormField
              label='Address Line 1'
              value={officiant.address1}
              onChange={e => updateOfficiant(index, 'address1', e.target.value)}
            />
            <FormField
              label='Address Line 2'
              value={officiant.address2}
              onChange={e => updateOfficiant(index, 'address2', e.target.value)}
            />
            <FormField
              label='City'
              value={officiant.city}
              onChange={e => updateOfficiant(index, 'city', e.target.value)}
            />
            <FormField
              label='State'
              value={officiant.state}
              onChange={e => updateOfficiant(index, 'state', e.target.value)}
            />
            <FormField
              label='Zip Code'
              value={officiant.zip}
              onChange={e => updateOfficiant(index, 'zip', e.target.value)}
            />
            <FormField
              label='Country'
              value={officiant.country}
              onChange={e => updateOfficiant(index, 'country', e.target.value)}
            />

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              {data.officiants.length > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  className='text-red-600 hover:text-red-700'
                  onClick={() => removeOfficiant(index)}
                >
                  <Trash2 className='h-4 w-4 mr-2' />
                  Remove
                </Button>
              )}
              {index === data.officiants.length - 1 && (
                <Button
                  type='button'
                  onClick={addOfficiant}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Officiant
                </Button>
              )}
            </div>
          </div>
        ))}

        <ConditionalSection
          label='Do you have preferred pallbearers?'
          value={data.hasPallbearers}
          onChange={val => {
            update('hasPallbearers', val);
            if (val && data.pallbearers.length === 0) {
              addPallbearer();
            }
          }}
        >
          {data.pallbearers.map((pallbearer, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Name'
                value={pallbearer.name}
                onChange={e => updatePallbearer(index, 'name', e.target.value)}
              />
              <FormField
                label='Phone Number'
                value={pallbearer.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePallbearer(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={pallbearer.email}
                onChange={e => updatePallbearer(index, 'email', e.target.value)}
                type='email'
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.pallbearers.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removePallbearer(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.pallbearers.length - 1 && (
                  <Button
                    type='button'
                    onClick={addPallbearer}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Pallbearer
                  </Button>
                )}
              </div>
            </div>
          ))}
        </ConditionalSection>

        <ConditionalSection
          label='Would you like flowers?'
          value={data.wantsFlowers}
          onChange={val => update('wantsFlowers', val)}
        >
          <TextAreaField
            label='Preferences on flowers or decorations'
            value={data.flowerPreferences}
            onChange={e => update('flowerPreferences', e.target.value)}
          />
        </ConditionalSection>

        <TextAreaField
          label='Please list any preferred readings or songs:'
          value={data.readingsSongs}
          onChange={e => update('readingsSongs', e.target.value)}
        />
      </SubSection>

      <SubSection title='Obituary Information'>
        <ConditionalSection
          label='Would you like an obituary to be published?'
          value={data.wantsObituary}
          onChange={val => {
            update('wantsObituary', val);
            if (val && data.publications.length === 0) {
              addPublication();
            }
          }}
        >
          <TextAreaField
            label="Please provide any information you'd like included in an obituary below."
            value={data.obituaryInfo}
            onChange={e => update('obituaryInfo', e.target.value)}
          />

          {data.publications.map((pub, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl'>
              <FormField
                label='Publication Name'
                value={pub.name}
                onChange={e => updatePublication(index, 'name', e.target.value)}
              />
              <FormField
                label='Website'
                value={pub.website}
                onChange={e =>
                  updatePublication(index, 'website', e.target.value)
                }
              />
              <div className='space-y-2'>
                <RadioGroup
                  // label="Print or electronic publication"
                  value={
                    pub.type === 'print'
                      ? true
                      : pub.type === 'electronic'
                        ? false
                        : null
                  }
                  onChange={val =>
                    updatePublication(
                      index,
                      'type',
                      val ? 'print' : 'electronic'
                    )
                  }
                  options={[
                    { label: 'Print', value: true },
                    { label: 'Electronic', value: false },
                  ]}
                />
              </div>
              <FormField
                label='Phone Number'
                value={pub.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePublication(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.publications.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removePublication(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.publications.length - 1 && (
                  <Button
                    type='button'
                    onClick={addPublication}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Publication
                  </Button>
                )}
              </div>
            </div>
          ))}

          <TextAreaField
            label='Is there anything else we should know about?'
            value={data.obituaryNotes}
            onChange={e => update('obituaryNotes', e.target.value)}
          />
        </ConditionalSection>
      </SubSection>

      <SubSection title='Military or First Responder Affiliation'>
        <CheckboxGroup
          label='Do you have any military or first responder affiliation? (Select all that apply.)'
          options={affiliationOptions}
          selected={data.affiliations}
          onChange={toggleAffiliation}
        />

        {data.affiliations.includes('Military') && (
          <SubSection title='Military Affiliation'>
            <Button
              type='button'
              onClick={() => {
                if (data.military.length === 0) {
                  addMilitary();
                }
              }}
              className='flex items-center gap-2 mb-6'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Military Service
            </Button>

            {data.military.map((mil, index) => (
              <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
                <FormField
                  label='Service Number'
                  value={mil.serviceNumber}
                  onChange={e =>
                    updateMilitary(index, 'serviceNumber', e.target.value)
                  }
                />
                <FormField
                  label='Branch of Service'
                  value={mil.branch}
                  onChange={e =>
                    updateMilitary(index, 'branch', e.target.value)
                  }
                />
                <FormField
                  label='Entered Date'
                  value={mil.enteredDate}
                  onChange={e =>
                    updateMilitary(index, 'enteredDate', e.target.value)
                  }
                  type='date'
                />
                <FormField
                  label='Place'
                  value={mil.enteredPlace}
                  onChange={e =>
                    updateMilitary(index, 'enteredPlace', e.target.value)
                  }
                />
                <FormField
                  label='Separation Date'
                  value={mil.separationDate}
                  onChange={e =>
                    updateMilitary(index, 'separationDate', e.target.value)
                  }
                  type='date'
                />
                <FormField
                  label='Place'
                  value={mil.separationPlace}
                  onChange={e =>
                    updateMilitary(index, 'separationPlace', e.target.value)
                  }
                />
                <FormField
                  label='Highest Rank'
                  value={mil.highestRank}
                  onChange={e =>
                    updateMilitary(index, 'highestRank', e.target.value)
                  }
                />
                <FormField
                  label='Location of Discharge Papers'
                  value={mil.dischargePapers}
                  onChange={e =>
                    updateMilitary(index, 'dischargePapers', e.target.value)
                  }
                />
                <TextAreaField
                  label='Location of Service (War Time):'
                  value={mil.serviceLocation}
                  onChange={e =>
                    updateMilitary(index, 'serviceLocation', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  {data.military.length > 1 && (
                    <Button
                      type='button'
                      variant='outline'
                      className='text-red-600 hover:text-red-700'
                      onClick={() => removeMilitary(index)}
                    >
                      <Trash2 className='h-4 w-4 mr-2' />
                      Remove
                    </Button>
                  )}
                  {index === data.military.length - 1 && (
                    <Button
                      type='button'
                      onClick={addMilitary}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Military Service
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </SubSection>
        )}

        {data.affiliations.includes('First Responder') && (
          <SubSection title='First Responder Affiliation'>
            <Button
              type='button'
              onClick={() => {
                if (data.firstResponders.length === 0) {
                  addFirstResponder();
                }
              }}
              className='flex items-center gap-2 mb-6'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add First Responder Service
            </Button>

            {data.firstResponders.map((fr, index) => (
              <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
                <FormField
                  label='Department'
                  value={fr.department}
                  onChange={e =>
                    updateFirstResponder(index, 'department', e.target.value)
                  }
                />
                <FormField
                  label='Entered Date'
                  value={fr.enteredDate}
                  onChange={e =>
                    updateFirstResponder(index, 'enteredDate', e.target.value)
                  }
                  type='date'
                />
                <FormField
                  label='Place'
                  value={fr.enteredPlace}
                  onChange={e =>
                    updateFirstResponder(index, 'enteredPlace', e.target.value)
                  }
                />
                <FormField
                  label='Separation Date'
                  value={fr.separationDate}
                  onChange={e =>
                    updateFirstResponder(
                      index,
                      'separationDate',
                      e.target.value
                    )
                  }
                  type='date'
                />
                <FormField
                  label='Place'
                  value={fr.separationPlace}
                  onChange={e =>
                    updateFirstResponder(
                      index,
                      'separationPlace',
                      e.target.value
                    )
                  }
                />
                <FormField
                  label='Highest Rank'
                  value={fr.highestRank}
                  onChange={e =>
                    updateFirstResponder(index, 'highestRank', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  {data.firstResponders.length > 1 && (
                    <Button
                      type='button'
                      variant='outline'
                      className='text-red-600 hover:text-red-700'
                      onClick={() => removeFirstResponder(index)}
                    >
                      <Trash2 className='h-4 w-4 mr-2' />
                      Remove
                    </Button>
                  )}
                  {index === data.firstResponders.length - 1 && (
                    <Button
                      type='button'
                      onClick={addFirstResponder}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add First Responder Service
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </SubSection>
        )}

        <ConditionalSection
          label='Would you like a Military Honors or First Responders funeral?'
          value={data.wantsHonors}
          onChange={val => update('wantsHonors', val)}
        >
          <div></div>
        </ConditionalSection>

        <ConditionalSection
          label='Is there any military branch or first responder department that should be present?'
          value={data.wantsPresence}
          onChange={val => update('wantsPresence', val)}
        >
          <TextAreaField
            label='Please explain your wishes further & include contact information.'
            value={data.presenceDetails}
            onChange={e => update('presenceDetails', e.target.value)}
          />
        </ConditionalSection>

        <SubSection title='Who would you like to receive the U.S. flag in your memory?'>
          <Button
            type='button'
            onClick={() => {
              if (data.flagRecipients.length === 0) {
                addFlagRecipient();
              }
            }}
            className='flex items-center gap-2 mb-6'
          >
            <Plus className='h-4 w-4 mr-2' />
            Add Selected Individual
          </Button>

          {data.flagRecipients.map((recipient, index) => (
            <div key={index} className='space-y-4 border p-4 rounded-xl mb-6'>
              <FormField
                label='Name'
                value={recipient.name}
                onChange={e =>
                  updateFlagRecipient(index, 'name', e.target.value)
                }
              />
              <FormField
                label='Phone'
                value={recipient.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updateFlagRecipient(index, 'phone', formattedValue);
                }}
                type='tel'
                placeholder='+1XXXXXXXXXX'
              />
              <FormField
                label='Email Address'
                value={recipient.email}
                onChange={e =>
                  updateFlagRecipient(index, 'email', e.target.value)
                }
                type='email'
              />

              <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                {data.flagRecipients.length > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeFlagRecipient(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                )}
                {index === data.flagRecipients.length - 1 && (
                  <Button
                    type='button'
                    onClick={addFlagRecipient}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Selected Individual
                  </Button>
                )}
              </div>
            </div>
          ))}
        </SubSection>
      </SubSection>

      {/* <FileUploadSection title="Upload Related Documents" description="Upload any pre-arranged funeral contracts or other relevant documents." /> */}
    </FormSection>
  );
};

export default LastWishesSection;
