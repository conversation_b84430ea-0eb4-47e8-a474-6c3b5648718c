'use client'

import { useState, useEffect } from "react";
import FormSection from "../form-section";
import SubSection from "../sub-section";
import <PERSON><PERSON><PERSON><PERSON>ield from "../text-area-field";
import FileUploadSection from "../file-upload-section";
import { useCareDocumentsContext } from "@/app/context/CareDocumentsContext";
import { useLinkedAccountPrefill } from "@/hooks/useLinkedAccountPrefill";
import ConditionalSection from "../conditional-section";

interface LegacySectionProps {
  disabled?: boolean;
}

const LegacySection: React.FC<LegacySectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    isLegacyImportant: null as boolean | null,
    howLegacy: "",
    rememberedHow: "",
    additionalLegacy: "",
    personalMessages: "",
    ethicalWill: "",
    lifeStory: "",
    familyHistory: "",
    legacyProjects: "",
    additionalInstructions: ""
  });

  useLinkedAccountPrefill('Legacy', sectionData.Legacy, setData);

  useEffect(() => {
    if (sectionData.Legacy) {
      setData(sectionData.Legacy);
    }
  }, [sectionData.Legacy]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Legacy', newData);
    console.log(`Updated ${field}:`, value);
  };

  return (
    <FormSection
      title="Legacy"
      description="Share your personal legacy and messages for loved ones."
      disabled={disabled}
    >
      {/* New fields from mockup */}
      <ConditionalSection
        label="Is 'legacy' important to you?"
        value={data.isLegacyImportant}
        onChange={(val) => update("isLegacyImportant", val)}
      >
        <TextAreaField
          label="If so, how?"
          value={data.howLegacy}
          onChange={(e) => update("howLegacy", e.target.value)}
          rows={6}
        />
      </ConditionalSection>
      
      <TextAreaField
        label="How do you want to be remembered after you pass?"
        value={data.rememberedHow}
        onChange={(e) => update("rememberedHow", e.target.value)}
        rows={6}
      />
      
      <TextAreaField
        label="Is there anything that is not in your obituary information that you want to make sure we know?"
        value={data.additionalLegacy}
        onChange={(e) => update("additionalLegacy", e.target.value)}
        rows={6}
      />
      
      {/* Existing fields */}
      <SubSection title="Personal Legacy">
        <TextAreaField
          label="Personal messages for loved ones"
          value={data.personalMessages}
          onChange={(e) => update("personalMessages", e.target.value)}
          placeholder="Messages you'd like to leave for specific people"
          rows={6}
        />
        
        <TextAreaField
          label="Ethical will or statement of values"
          value={data.ethicalWill}
          onChange={(e) => update("ethicalWill", e.target.value)}
          placeholder="Share your values, beliefs, and life lessons"
          rows={6}
        />
        
        <TextAreaField
          label="Life story or memoir"
          value={data.lifeStory}
          onChange={(e) => update("lifeStory", e.target.value)}
          placeholder="Share important moments and memories from your life"
          rows={6}
        />
        
        <TextAreaField
          label="Family history or genealogy information"
          value={data.familyHistory}
          onChange={(e) => update("familyHistory", e.target.value)}
          placeholder="Share family history or genealogical information"
          rows={6}
        />
        
        <TextAreaField
          label="Legacy projects or initiatives"
          value={data.legacyProjects}
          onChange={(e) => update("legacyProjects", e.target.value)}
          placeholder="Describe any projects, foundations, or initiatives you've established"
          rows={6}
        />
        
        <TextAreaField
          label="Additional legacy information"
          value={data.additionalInstructions}
          onChange={(e) => update("additionalInstructions", e.target.value)}
          rows={6}
        />
      </SubSection>
      
      {/* <FileUploadSection title="Upload Legacy Documents" description="Upload letters, photos, videos, or other legacy materials." /> */}
    </FormSection>
  );
};

export default LegacySection;