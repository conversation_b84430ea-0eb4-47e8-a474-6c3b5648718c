'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import <PERSON>Field from '../form-field';
import ConditionalSection from '../conditional-section';
import <PERSON><PERSON>reaField from '../text-area-field';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Trash2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useInterviewPrefill } from '@/hooks/useInterviewPrefill';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { Checkbox } from '@/components/ui/checkbox';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/utils/interviewV2Progress';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

const BLANK_MEDICATION = {
  name: '',
  diagnosisDate: '',
  providers: [] as string[],
  dosage: '',
  notes: '',
};

const BLANK_CONDITION = {
  name: '',
  diagnosisDate: '',
  providers: [] as string[],
  notes: '',
};

const BLANK_SURGERY = {
  type: '',
  date: '',
  providers: [] as string[],
  notes: '',
};

const BLANK_ALLERGY = {
  name: '',
  diagnosisDate: '',
  providers: [] as string[],
  notes: '',
};

const BLANK_PHARMACY = {
  name: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zip: '',
  website: '',
  phone: '',
};

interface MedicalSectionProps {
  disabled?: boolean;
}

const MedicalSection: React.FC<MedicalSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const { data: interviewData } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
  });

  const [data, setData] = useState({
    professionals: [
      {
        name: '',
        isPrimary: null as boolean | null,
        isSpecialist: null as boolean | null,
        specialty: '',
        hospital: '',
        phone: '',
        notes: '',
      },
    ],
    medications: [] as (typeof BLANK_MEDICATION)[],
    conditions: [] as (typeof BLANK_CONDITION)[],
    surgeries: [] as (typeof BLANK_SURGERY)[],
    allergies: [] as (typeof BLANK_ALLERGY)[],
    pharmacy: { ...BLANK_PHARMACY },
    bloodType: '',
    bloodTypeOther: '',
    organDonation: null as boolean | null,
    organDonationDetails: '',
    medicalDirective: '',
    coreWishes: '',
    dignityMeaning: '',
    noMedical: '',
    foodPreferences: '',
    additionalMedical: '',
  });

  useLinkedAccountPrefill('Medical', sectionData.Medical, setData);
  // useInterviewPrefill('Medical', sectionData.Medical, setData);

  // Load data from context when available
  useEffect(() => {
    if (sectionData.Medical) {
      setData(sectionData.Medical);
    }
  }, [sectionData.Medical]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Medical', newData);
  };

  // Medical Professionals functions
  const updateProfessional = (index: number, field: string, value: any) => {
    const newProfessionals = [...data.professionals];
    newProfessionals[index] = { ...newProfessionals[index], [field]: value };
    update('professionals', newProfessionals);
  };

  const addProfessional = () => {
    update('professionals', [
      ...data.professionals,
      {
        name: '',
        isPrimary: null,
        isSpecialist: null,
        specialty: '',
        hospital: '',
        phone: '',
        notes: '',
      },
    ]);
  };

  const removeProfessional = (index: number) => {
    update(
      'professionals',
      data.professionals.filter((_, i) => i !== index)
    );
  };

  // Medications functions
  const addMedication = () => {
    update('medications', [...data.medications, { ...BLANK_MEDICATION }]);
  };

  const removeMedication = (index: number) => {
    update(
      'medications',
      data.medications.filter((_, i) => i !== index)
    );
  };

  const updateMedication = (index: number, field: string, value: any) => {
    const newMedications = [...data.medications];
    newMedications[index] = { ...newMedications[index], [field]: value };
    update('medications', newMedications);
  };

  const toggleMedicationProvider = (index: number, provider: string) => {
    const newMedications = [...data.medications];
    const currentProviders = newMedications[index].providers;
    newMedications[index].providers = currentProviders.includes(provider)
      ? currentProviders.filter(p => p !== provider)
      : [...currentProviders, provider];
    update('medications', newMedications);
  };

  // Conditions functions
  const addCondition = () => {
    update('conditions', [...data.conditions, { ...BLANK_CONDITION }]);
  };

  const removeCondition = (index: number) => {
    update(
      'conditions',
      data.conditions.filter((_, i) => i !== index)
    );
  };

  const updateCondition = (index: number, field: string, value: any) => {
    const newConditions = [...data.conditions];
    newConditions[index] = { ...newConditions[index], [field]: value };
    update('conditions', newConditions);
  };

  const toggleConditionProvider = (index: number, provider: string) => {
    const newConditions = [...data.conditions];
    const currentProviders = newConditions[index].providers;
    newConditions[index].providers = currentProviders.includes(provider)
      ? currentProviders.filter(p => p !== provider)
      : [...currentProviders, provider];
    update('conditions', newConditions);
  };

  // Surgeries functions
  const addSurgery = () => {
    update('surgeries', [...data.surgeries, { ...BLANK_SURGERY }]);
  };

  const removeSurgery = (index: number) => {
    update(
      'surgeries',
      data.surgeries.filter((_, i) => i !== index)
    );
  };

  const updateSurgery = (index: number, field: string, value: any) => {
    const newSurgeries = [...data.surgeries];
    newSurgeries[index] = { ...newSurgeries[index], [field]: value };
    update('surgeries', newSurgeries);
  };

  const toggleSurgeryProvider = (index: number, provider: string) => {
    const newSurgeries = [...data.surgeries];
    const currentProviders = newSurgeries[index].providers;
    newSurgeries[index].providers = currentProviders.includes(provider)
      ? currentProviders.filter(p => p !== provider)
      : [...currentProviders, provider];
    update('surgeries', newSurgeries);
  };

  // Allergies functions
  const addAllergy = () => {
    update('allergies', [...data.allergies, { ...BLANK_ALLERGY }]);
  };

  const removeAllergy = (index: number) => {
    update(
      'allergies',
      data.allergies.filter((_, i) => i !== index)
    );
  };

  const updateAllergy = (index: number, field: string, value: any) => {
    const newAllergies = [...data.allergies];
    newAllergies[index] = { ...newAllergies[index], [field]: value };
    update('allergies', newAllergies);
  };

  const toggleAllergyProvider = (index: number, provider: string) => {
    const newAllergies = [...data.allergies];
    const currentProviders = newAllergies[index].providers;
    newAllergies[index].providers = currentProviders.includes(provider)
      ? currentProviders.filter(p => p !== provider)
      : [...currentProviders, provider];
    update('allergies', newAllergies);
  };

  // Pharmacy functions
  const updatePharmacy = (field: string, value: string) => {
    update('pharmacy', { ...data.pharmacy, [field]: value });
  };

  const bloodTypeOptions = [
    { label: 'A+', value: 'A+' },
    { label: 'A-', value: 'A-' },
    { label: 'B+', value: 'B+' },
    { label: 'B-', value: 'B-' },
    { label: 'AB+', value: 'AB+' },
    { label: 'AB-', value: 'AB-' },
    { label: 'O+', value: 'O+' },
    { label: 'O-', value: 'O-' },
    { label: 'Other', value: 'Other' },
    { label: "I don't know", value: 'Unknown' },
  ];

  // Create professional options for checkboxes
  const professionalOptions = data.professionals
    .filter(p => p.name.trim() !== '')
    .map(p => ({
      label: p.name,
      value: p.name,
    }));

  return (
    <FormSection
      title='Medical Information'
      description='Please provide your medical information and preferences.'
      showAlertBanner={interviewData?.stepsData?.medical?.proxy?.useCompany}
      alertBannerContent={
        'You haven’t appointed a Medical POA in your Interview. We’ll store your preferences here, but a medical proxy may still be required to act on them.'
      }
      disabled={disabled}
    >
      <SubSection title='Medical Professionals'>
        {data.professionals.map((professional, index) => (
          <div key={index} className='space-y-6 mb-6'>
            {index > 0 && <div className='border-t border-gray-200 pt-6'></div>}
            <FormField
              label='Medical Professional Name'
              value={professional.name}
              onChange={e => updateProfessional(index, 'name', e.target.value)}
              required
            />

            <ConditionalSection
              label='Is this your primary care physician?'
              value={professional.isPrimary}
              onChange={val => updateProfessional(index, 'isPrimary', val)}
            >
              <div></div>
            </ConditionalSection>

            <ConditionalSection
              label='Is this a specialist?'
              value={professional.isSpecialist}
              onChange={val => updateProfessional(index, 'isSpecialist', val)}
            >
              <FormField
                label='Specialty'
                value={professional.specialty}
                onChange={e =>
                  updateProfessional(index, 'specialty', e.target.value)
                }
              />
            </ConditionalSection>

            <FormField
              label='Hospital/Practice'
              value={professional.hospital}
              onChange={e =>
                updateProfessional(index, 'hospital', e.target.value)
              }
            />

            <FormField
              label='Phone Number'
              type='tel'
              value={professional.phone || '+1'}
              onChange={e => {
                const formattedValue = formatPhoneNumber(e.target.value);
                updateProfessional(index, 'phone', formattedValue);
              }}
              placeholder='+1XXXXXXXXXX'
            />

            <TextAreaField
              label='Additional Notes'
              value={professional.notes}
              onChange={e => updateProfessional(index, 'notes', e.target.value)}
            />

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              {data.professionals.length > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  className='text-red-600 hover:text-red-700'
                  onClick={() => removeProfessional(index)}
                >
                  <Trash2 className='h-4 w-4 mr-2' />
                  Remove
                </Button>
              )}
              {index === data.professionals.length - 1 && (
                <Button
                  type='button'
                  onClick={addProfessional}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Another Medical Professional
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      <SubSection title='Medical Information'>
        <div className='space-y-6'>
          <div className='space-y-2'>
            <Label className='text-sm font-medium text-gray-700'>
              Blood Type
            </Label>
            <Select
              value={data.bloodType}
              onValueChange={value => update('bloodType', value)}
            >
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select your blood type' />
              </SelectTrigger>
              <SelectContent>
                {bloodTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {data.bloodType === 'Other' && (
            <FormField
              label='Please specify your blood type'
              value={data.bloodTypeOther}
              onChange={e => update('bloodTypeOther', e.target.value)}
            />
          )}

          <ConditionalSection
            label='Are you an organ donor?'
            value={data.organDonation}
            onChange={val => update('organDonation', val)}
          >
            <TextAreaField
              label='Please provide any specific details about your organ donation preferences'
              value={data.organDonationDetails}
              onChange={e => update('organDonationDetails', e.target.value)}
            />
          </ConditionalSection>

          {/* Medications Section */}
          <div className='space-y-6'>
            <div className='flex justify-between items-center'>
              <Label className='text-base font-semibold'>Medications</Label>
              {data.medications.length === 0 && (
                <Button
                  type='button'
                  onClick={addMedication}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Medication
                </Button>
              )}
            </div>

            {data.medications.map((medication, index) => (
              <div
                key={index}
                className='space-y-4 border border-gray-200 rounded-lg p-4'
              >
                <FormField
                  label='Medication Name'
                  value={medication.name}
                  onChange={e =>
                    updateMedication(index, 'name', e.target.value)
                  }
                />

                <FormField
                  label='Date of Diagnosis'
                  type='date'
                  value={medication.diagnosisDate}
                  onChange={e =>
                    updateMedication(index, 'diagnosisDate', e.target.value)
                  }
                />

                {professionalOptions.length > 0 && (
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-gray-700'>
                      Treating Medical Provider
                    </Label>
                    <div className='space-y-2'>
                      {professionalOptions.map(option => (
                        <div
                          key={option.value}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`med-${index}-${option.value}`}
                            checked={medication.providers.includes(
                              option.value
                            )}
                            onCheckedChange={() =>
                              toggleMedicationProvider(index, option.value)
                            }
                          />
                          <Label
                            htmlFor={`med-${index}-${option.value}`}
                            className='font-normal'
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <TextAreaField
                  label='Please provide dosing information regarding this medication including amount & timing:'
                  value={medication.dosage}
                  onChange={e =>
                    updateMedication(index, 'dosage', e.target.value)
                  }
                />

                <TextAreaField
                  label='Additional information regarding this medication:'
                  value={medication.notes}
                  onChange={e =>
                    updateMedication(index, 'notes', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeMedication(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === data.medications.length - 1 && (
                    <Button
                      type='button'
                      onClick={addMedication}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Medication
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Conditions Section */}
          <div className='space-y-6'>
            <div className='flex justify-between items-center'>
              <Label className='text-base font-semibold'>
                Existing Conditions
              </Label>
              {data.conditions.length === 0 && (
                <Button
                  type='button'
                  onClick={addCondition}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Condition
                </Button>
              )}
            </div>

            {data.conditions.map((condition, index) => (
              <div
                key={index}
                className='space-y-4 border border-gray-200 rounded-lg p-4'
              >
                <FormField
                  label='Condition/Disease Name'
                  value={condition.name}
                  onChange={e => updateCondition(index, 'name', e.target.value)}
                />

                <FormField
                  label='Date of Diagnosis'
                  type='date'
                  value={condition.diagnosisDate}
                  onChange={e =>
                    updateCondition(index, 'diagnosisDate', e.target.value)
                  }
                />

                {professionalOptions.length > 0 && (
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-gray-700'>
                      Treating Medical Provider
                    </Label>
                    <div className='space-y-2'>
                      {professionalOptions.map(option => (
                        <div
                          key={option.value}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`cond-${index}-${option.value}`}
                            checked={condition.providers.includes(option.value)}
                            onCheckedChange={() =>
                              toggleConditionProvider(index, option.value)
                            }
                          />
                          <Label
                            htmlFor={`cond-${index}-${option.value}`}
                            className='font-normal'
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <TextAreaField
                  label='Additional information regarding this condition or disease:'
                  value={condition.notes}
                  onChange={e =>
                    updateCondition(index, 'notes', e.target.value)
                  }
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeCondition(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === data.conditions.length - 1 && (
                    <Button
                      type='button'
                      onClick={addCondition}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Condition
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Surgeries Section */}
          <div className='space-y-6'>
            <div className='flex justify-between items-center'>
              <Label className='text-base font-semibold'>Surgeries</Label>
              {data.surgeries.length === 0 && (
                <Button
                  type='button'
                  onClick={addSurgery}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Surgery
                </Button>
              )}
            </div>

            {data.surgeries.map((surgery, index) => (
              <div
                key={index}
                className='space-y-4 border border-gray-200 rounded-lg p-4'
              >
                <FormField
                  label='Surgery Type'
                  value={surgery.type}
                  onChange={e => updateSurgery(index, 'type', e.target.value)}
                />

                <FormField
                  label='Date of Surgery'
                  type='date'
                  value={surgery.date}
                  onChange={e => updateSurgery(index, 'date', e.target.value)}
                />

                {professionalOptions.length > 0 && (
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-gray-700'>
                      Treating Medical Provider
                    </Label>
                    <div className='space-y-2'>
                      {professionalOptions.map(option => (
                        <div
                          key={option.value}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`surg-${index}-${option.value}`}
                            checked={surgery.providers.includes(option.value)}
                            onCheckedChange={() =>
                              toggleSurgeryProvider(index, option.value)
                            }
                          />
                          <Label
                            htmlFor={`surg-${index}-${option.value}`}
                            className='font-normal'
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <TextAreaField
                  label='Additional information regarding this surgery:'
                  value={surgery.notes}
                  onChange={e => updateSurgery(index, 'notes', e.target.value)}
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeSurgery(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === data.surgeries.length - 1 && (
                    <Button
                      type='button'
                      onClick={addSurgery}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Surgery
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Allergies Section */}
          <div className='space-y-6'>
            <div className='flex justify-between items-center'>
              <Label className='text-base font-semibold'>Allergies</Label>
              {data.allergies.length === 0 && (
                <Button
                  type='button'
                  onClick={addAllergy}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Allergy
                </Button>
              )}
            </div>

            {data.allergies.map((allergy, index) => (
              <div
                key={index}
                className='space-y-4 border border-gray-200 rounded-lg p-4'
              >
                <FormField
                  label='Allergy Name'
                  value={allergy.name}
                  onChange={e => updateAllergy(index, 'name', e.target.value)}
                />

                <FormField
                  label='Date of Diagnosis'
                  type='date'
                  value={allergy.diagnosisDate}
                  onChange={e =>
                    updateAllergy(index, 'diagnosisDate', e.target.value)
                  }
                />

                {professionalOptions.length > 0 && (
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-gray-700'>
                      Treating Medical Provider
                    </Label>
                    <div className='space-y-2'>
                      {professionalOptions.map(option => (
                        <div
                          key={option.value}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`allergy-${index}-${option.value}`}
                            checked={allergy.providers.includes(option.value)}
                            onCheckedChange={() =>
                              toggleAllergyProvider(index, option.value)
                            }
                          />
                          <Label
                            htmlFor={`allergy-${index}-${option.value}`}
                            className='font-normal'
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <TextAreaField
                  label='Additional information regarding this allergy (What happens with this allergy?):'
                  value={allergy.notes}
                  onChange={e => updateAllergy(index, 'notes', e.target.value)}
                />

                <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
                  <Button
                    type='button'
                    variant='outline'
                    className='text-red-600 hover:text-red-700'
                    onClick={() => removeAllergy(index)}
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Remove
                  </Button>
                  {index === data.allergies.length - 1 && (
                    <Button
                      type='button'
                      onClick={addAllergy}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4 mr-2' />
                      Add Allergy
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Preferred Pharmacy Section */}
          <SubSection title='Preferred Pharmacy'>
            <div className='space-y-4'>
              <FormField
                label='Pharmacy Name'
                value={data.pharmacy.name}
                onChange={e => updatePharmacy('name', e.target.value)}
              />
              <FormField
                label='Address Line 1'
                value={data.pharmacy.address1}
                onChange={e => updatePharmacy('address1', e.target.value)}
              />
              <FormField
                label='Address Line 2'
                value={data.pharmacy.address2}
                onChange={e => updatePharmacy('address2', e.target.value)}
              />
              <FormField
                label='City'
                value={data.pharmacy.city}
                onChange={e => updatePharmacy('city', e.target.value)}
              />
              <FormField
                label='State'
                value={data.pharmacy.state}
                onChange={e => updatePharmacy('state', e.target.value)}
              />
              <FormField
                label='Zip Code'
                value={data.pharmacy.zip}
                onChange={e => updatePharmacy('zip', e.target.value)}
              />
              <FormField
                label='Website'
                value={data.pharmacy.website}
                onChange={e => updatePharmacy('website', e.target.value)}
              />
              <FormField
                label='Phone Number'
                type='tel'
                value={data.pharmacy.phone || '+1'}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  updatePharmacy('phone', formattedValue);
                }}
                placeholder='+1XXXXXXXXXX'
              />
            </div>
          </SubSection>

          <TextAreaField
            label='What are your core wishes for medical care if you are unable to communicate?'
            value={data.coreWishes}
            onChange={e => update('coreWishes', e.target.value)}
          />

          <TextAreaField
            label='What does dignity mean to you in a medical context?'
            value={data.dignityMeaning}
            onChange={e => update('dignityMeaning', e.target.value)}
          />

          <TextAreaField
            label='Is there anything you specifically do NOT want in terms of medical care?'
            value={data.noMedical}
            onChange={e => update('noMedical', e.target.value)}
          />

          <TextAreaField
            label='Do you have any food preferences or restrictions that should be considered during medical care?'
            value={data.foodPreferences}
            onChange={e => update('foodPreferences', e.target.value)}
          />

          <TextAreaField
            label='Is there any additional medical information you would like to share?'
            value={data.additionalMedical}
            onChange={e => update('additionalMedical', e.target.value)}
          />
        </div>
      </SubSection>
    </FormSection>
  );
};

export default MedicalSection;
