'use client'

import { useState, useEffect } from "react";
import FormSection from "../form-section";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../text-area-field";
import FileUploadSection from "../file-upload-section";
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from "lucide-react";
import { useCareDocumentsContext } from "@/app/context/CareDocumentsContext";
import { useLinkedAccountPrefill } from "@/hooks/useLinkedAccountPrefill";

interface ClientEmergencyIncidentsSectionProps {
  disabled?: boolean;
}

const ClientEmergencyIncidentsSection: React.FC<ClientEmergencyIncidentsSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    logs: [] as string[],
    additionalInstructions: ""
  });

  useLinkedAccountPrefill('EmergencyIncidents', sectionData.EmergencyIncidents, setData)

  // Load data from context when available
  useEffect(() => {
    if (sectionData.EmergencyIncidents) {
      setData(sectionData.EmergencyIncidents);
    }
  }, [sectionData.EmergencyIncidents]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('EmergencyIncidents', newData);
    console.log(`Updated ${field}:`, value);
  };

  const addLog = () => update("logs", [...data.logs, ""]);
  
  const removeLog = (index: number) =>
    update(
      "logs",
      data.logs.filter((_, i) => i !== index)
    );
  
  const updateLog = (index: number, value: string) => {
    const newLogs = [...data.logs];
    newLogs[index] = value;
    update("logs", newLogs);
  };

  return (
    <FormSection
      title="Client Emergency Incidents"
      description="Log of client emergency incidents and protocols"
      disabled={disabled}
    >
      {data.logs.length === 0 && (
        <div className="flex justify-center my-4">
          <Button
            type="button"
            onClick={addLog}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Emergency Incident Log
          </Button>
        </div>
      )}

      {data.logs.map((log, index) => (
        <div key={index} className="space-y-4 border p-4 rounded-xl mb-4">
          <div className="flex justify-end">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => removeLog(index)}
              className="h-8 w-8 text-red-500"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          <TextAreaField
            label={`Emergency Incident Log ${index + 1}`}
            value={log}
            onChange={(e) => updateLog(index, e.target.value)}
            placeholder="Describe the emergency incident, date, actions taken, and outcome"
          />

          {index === data.logs.length - 1 && (
            <div className="flex justify-center mt-4">
              <Button
                type="button"
                onClick={addLog}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another Log
              </Button>
            </div>
          )}
        </div>
      ))}

      <TextAreaField
        label="Additional Emergency Instructions"
        value={data.additionalInstructions}
        onChange={(e) => update("additionalInstructions", e.target.value)}
        placeholder="Any additional emergency protocols or instructions"
      />

      {/* <FileUploadSection title="Upload Emergency Documents" description="Upload emergency protocols, incident reports, or other related documents." /> */}
    </FormSection>
  );
};

export default ClientEmergencyIncidentsSection;