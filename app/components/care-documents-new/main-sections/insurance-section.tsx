'use client';

import { useState, useEffect } from 'react';
import FormSection from '../form-section';
import SubSection from '../sub-section';
import <PERSON>Field from '../form-field';
import Text<PERSON>reaField from '../text-area-field';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from 'lucide-react';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { useLinkedAccountPrefill } from '@/hooks/useLinkedAccountPrefill';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  if (value.length < 2 || !value.startsWith('+1')) {
    return '+1';
  }
  const digits = value.slice(1).replace(/\D/g, '');
  if (digits.length === 0) return '+1';
  if (digits.startsWith('1')) {
    const limitedDigits = digits.slice(0, 11);
    return `+${limitedDigits}`;
  } else {
    const limitedDigits = digits.slice(0, 10);
    return `+1${limitedDigits}`;
  }
};

interface InsuranceSectionProps {
  disabled?: boolean;
}

const InsuranceSection: React.FC<InsuranceSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    policies: [] as Array<{
      type: string;
      company: string;
      policyNumber: string;
      contactInfo: string;
      coverage: string;
      beneficiaries: string;
      notes: string;
      // Health insurance specific fields
      subscriber?: string;
      groupNumber?: string;
      memberNumber?: string;
      isEmployer?: boolean;
      employerType?: string;
      employerName?: string;
      // Agent-based insurance fields
      agentName?: string;
      agentPhone?: string;
      agentEmail?: string;
      // Other insurance specific field
      purpose?: string;
    }>,
    additionalInstructions: '',
  });

  useLinkedAccountPrefill('Insurance', sectionData.Insurance, setData);

  useEffect(() => {
    if (sectionData.Insurance) {
      setData(sectionData.Insurance);
    }
  }, [sectionData.Insurance]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Insurance', newData);
    console.log(`Updated ${field}:`, value);
  };

  const updatePolicy = (index: number, field: string, value: any) => {
    const newPolicies = [...data.policies];
    newPolicies[index] = { ...newPolicies[index], [field]: value };
    update('policies', newPolicies);
  };

  const addPolicy = () => {
    update('policies', [
      ...data.policies,
      {
        type: '',
        company: '',
        policyNumber: '',
        contactInfo: '',
        coverage: '',
        beneficiaries: '',
        notes: '',
      },
    ]);
  };

  const removePolicy = (index: number) => {
    update(
      'policies',
      data.policies.filter((_, i) => i !== index)
    );
  };

  const policyTypes = [
    'Health Insurance',
    'Life Insurance',
    'Auto Insurance',
    'Homeowners/Renters Insurance',
    'Disability Insurance',
    'Long-Term Care Insurance',
    'Umbrella Insurance',
    'E&O/Malpractice or Liability Insurance',
    'Pet Insurance',
    'Other',
  ];

  const employerTypeOptions = [
    { label: 'Self', value: 'self' },
    { label: 'Spouse/Partner', value: 'spouse' },
  ];

  return (
    <FormSection
      title='Insurance Information'
      description='Sharing information regarding your insurance will allow us to ensure everything continues to be paid on time while needed.'
      disabled={disabled}
    >
      <SubSection title='Insurance Policies'>
        <Button
          type='button'
          onClick={() => {
            if (data.policies.length === 0) {
              addPolicy();
            }
          }}
          className='flex items-center gap-2 mb-6'
        >
          <Plus className='h-4 w-4 mr-2' />
          Add Insurance Policy
        </Button>

        {data.policies.map((policy, index) => (
          <div key={index} className='space-y-6 mb-6'>
            {index > 0 && <div className='border-t border-gray-200 pt-6'></div>}

            <div className='space-y-2'>
              <Label className='text-sm font-medium text-gray-700'>
                Insurance Type
              </Label>
              <Select
                value={policy.type}
                onValueChange={value => updatePolicy(index, 'type', value)}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Select insurance type' />
                </SelectTrigger>
                <SelectContent>
                  {policyTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Common fields for all insurance types */}
            {policy.type && (
              <>
                {/* Health Insurance specific fields */}
                {policy.type === 'Health Insurance' && (
                  <>
                    <FormField
                      label='Insurance Company'
                      value={policy.company}
                      onChange={e =>
                        updatePolicy(index, 'company', e.target.value)
                      }
                    />
                    <FormField
                      label='Subscriber Name'
                      value={policy.subscriber || ''}
                      onChange={e =>
                        updatePolicy(index, 'subscriber', e.target.value)
                      }
                    />
                    <FormField
                      label='Group Number'
                      value={policy.groupNumber || ''}
                      onChange={e =>
                        updatePolicy(index, 'groupNumber', e.target.value)
                      }
                    />
                    <FormField
                      label='Member Number'
                      value={policy.memberNumber || ''}
                      onChange={e =>
                        updatePolicy(index, 'memberNumber', e.target.value)
                      }
                    />
                    <div className='space-y-2'>
                      <Label className='text-sm font-medium text-gray-700'>
                        Is this health insurance associated with an employer?
                      </Label>
                      <RadioGroup
                        value={
                          policy.isEmployer === undefined
                            ? undefined
                            : String(policy.isEmployer)
                        }
                        onValueChange={val =>
                          updatePolicy(index, 'isEmployer', val === 'true')
                        }
                        className='flex space-x-4'
                      >
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem
                            id={`employer-yes-${index}`}
                            value='true'
                          />
                          <Label htmlFor={`employer-yes-${index}`}>Yes</Label>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <RadioGroupItem
                            id={`employer-no-${index}`}
                            value='false'
                          />
                          <Label htmlFor={`employer-no-${index}`}>No</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {policy.isEmployer && (
                      <>
                        <div className='space-y-2'>
                          <Label className='text-sm font-medium text-gray-700'>
                            If yes, is it through your employer or a
                            spouse/partner's employer?
                          </Label>
                          <RadioGroup
                            value={policy.employerType || undefined}
                            onValueChange={val =>
                              updatePolicy(index, 'employerType', val)
                            }
                            className='flex space-x-4'
                          >
                            {employerTypeOptions.map(option => (
                              <div
                                key={option.value}
                                className='flex items-center space-x-2'
                              >
                                <RadioGroupItem
                                  id={`employer-type-${option.value}-${index}`}
                                  value={option.value}
                                />
                                <Label
                                  htmlFor={`employer-type-${option.value}-${index}`}
                                >
                                  {option.label}
                                </Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </div>
                        <FormField
                          label='Employer Name'
                          value={policy.employerName || ''}
                          onChange={e =>
                            updatePolicy(index, 'employerName', e.target.value)
                          }
                        />
                      </>
                    )}
                  </>
                )}

                {/* Agent-based insurance fields (Auto, Homeowners, Umbrella, etc.) */}
                {[
                  'Auto Insurance',
                  'Homeowners/Renters Insurance',
                  'Umbrella Insurance',
                  'Disability Insurance',
                  'E&O/Malpractice or Liability Insurance',
                  'Life Insurance',
                  'Long-Term Care Insurance',
                  'Pet Insurance',
                ].includes(policy.type) && (
                  <>
                    <FormField
                      label='Insurance Agent Name'
                      value={policy.agentName || ''}
                      onChange={e =>
                        updatePolicy(index, 'agentName', e.target.value)
                      }
                    />
                    <FormField
                      label='Agent Phone Number'
                      value={policy.agentPhone || '+1'}
                      onChange={e => {
                        const formattedValue = formatPhoneNumber(
                          e.target.value
                        );
                        updatePolicy(index, 'agentPhone', formattedValue);
                      }}
                      type='tel'
                      placeholder='+1XXXXXXXXXX'
                    />
                    <FormField
                      label='Agent Email Address'
                      value={policy.agentEmail || ''}
                      onChange={e =>
                        updatePolicy(index, 'agentEmail', e.target.value)
                      }
                      type='email'
                    />
                  </>
                )}

                {/* Auto Insurance specific fields */}
                {policy.type === 'Auto Insurance' && (
                  <>
                    <FormField
                      label='Policy Number'
                      value={policy.policyNumber}
                      onChange={e =>
                        updatePolicy(index, 'policyNumber', e.target.value)
                      }
                    />
                  </>
                )}

                {/* Other insurance specific fields */}
                {policy.type === 'Other' && (
                  <>
                    <FormField
                      label='Insurance Company'
                      value={policy.company}
                      onChange={e =>
                        updatePolicy(index, 'company', e.target.value)
                      }
                    />
                    <FormField
                      label='Policy Number'
                      value={policy.policyNumber}
                      onChange={e =>
                        updatePolicy(index, 'policyNumber', e.target.value)
                      }
                    />
                    <FormField
                      label='What is this insurance for?'
                      value={policy.purpose || ''}
                      onChange={e =>
                        updatePolicy(index, 'purpose', e.target.value)
                      }
                    />
                  </>
                )}

                {/* Common fields for all types except those handled above */}
                {!['Health Insurance', 'Auto Insurance', 'Other'].includes(
                  policy.type
                ) && (
                  <>
                    <FormField
                      label='Policy Number'
                      value={policy.policyNumber}
                      onChange={e =>
                        updatePolicy(index, 'policyNumber', e.target.value)
                      }
                    />
                  </>
                )}

                {/* Common fields for all insurance types */}
                <TextAreaField
                  label='Coverage Details'
                  value={policy.coverage}
                  onChange={e =>
                    updatePolicy(index, 'coverage', e.target.value)
                  }
                  placeholder='Coverage amount, deductible, etc.'
                />

                <TextAreaField
                  label='Beneficiaries'
                  value={policy.beneficiaries}
                  onChange={e =>
                    updatePolicy(index, 'beneficiaries', e.target.value)
                  }
                  placeholder='List any designated beneficiaries for this policy'
                />

                <TextAreaField
                  label='Additional Notes'
                  value={policy.notes}
                  onChange={e => updatePolicy(index, 'notes', e.target.value)}
                />
              </>
            )}

            <div className='flex justify-end items-center space-x-3 mt-4 pt-4 border-t border-gray-200'>
              <Button
                type='button'
                variant='outline'
                className='text-red-600 hover:text-red-700'
                onClick={() => removePolicy(index)}
              >
                <Trash2 className='h-4 w-4 mr-2' />
                Remove
              </Button>
              {index === data.policies.length - 1 && (
                <Button
                  type='button'
                  onClick={addPolicy}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add Another Policy
                </Button>
              )}
            </div>
          </div>
        ))}
      </SubSection>

      <TextAreaField
        label='Additional Insurance Instructions'
        value={data.additionalInstructions}
        onChange={e => update('additionalInstructions', e.target.value)}
        placeholder='Any additional insurance information or instructions'
      />
    </FormSection>
  );
};

export default InsuranceSection;
