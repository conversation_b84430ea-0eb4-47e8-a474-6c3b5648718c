'use client'

import { useState, useEffect } from "react";
import FormSection from "../form-section";
import SubSection from "../sub-section";
import <PERSON><PERSON><PERSON><PERSON>ield from "../text-area-field";
import EnhancedFileUpload from "../enhanced-file-upload";
import FilePreview from "../file-preview";
import CheckboxGroup from "../checkbox-group";
import { useCareDocumentsContext } from "@/app/context/CareDocumentsContext";
import { UploadedFile } from "@/hooks/useCareDocumentsNew";
import { useLinkedAccountPrefill } from "@/hooks/useLinkedAccountPrefill";

const CategoryEnum = {
  "Identification Documents": "identification-documents",
  "Medical Records": "medical-records",
  "Financial Documents": "financial-documents",
  "Legal Documents": "legal-documents",
  "Insurance Policies": "insurance-policies",
  "Property Records": "property-records",
  "Personal Messages": "personal-messages",
  "Photos and Videos": "photos-and-videos",
  "Other": "other"
}

interface UploadsSectionProps {
  disabled?: boolean;
}

const UploadsSection: React.FC<UploadsSectionProps> = ({ disabled }) => {
  const { sectionData, updateSectionData } = useCareDocumentsContext();

  const [data, setData] = useState({
    documentCategories: [] as string[],
    additionalNotes: "",
    uploadedFiles: [] as UploadedFile[],
  });

  useLinkedAccountPrefill('Uploads', sectionData.Uploads, setData);

  useEffect(() => {
    if (sectionData.Uploads) {
      setData(sectionData.Uploads);
    }
  }, [sectionData.Uploads]);

  const update = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    setData(newData);
    // Update the context with the new data
    updateSectionData('Uploads', newData);
    console.log(`Updated ${field}:`, value);
  };

  const toggleCategory = (category: string) => {
    const newCategories = data.documentCategories.includes(category)
      ? data.documentCategories.filter((c) => c !== category)
      : [...data.documentCategories, category];
    update("documentCategories", newCategories);
  };

  const handleFileUploaded = (category: string, file: UploadedFile) => {
    // Ensure the file has the correct reference
    const fileWithReference = {
      ...file,
      reference: CategoryEnum[category as keyof typeof CategoryEnum] || category.replace(/\s+/g, '-').toLowerCase()
    };
    
    // Add to the array of uploaded files
    const newUploadedFiles = [...data.uploadedFiles, fileWithReference];
    update("uploadedFiles", newUploadedFiles);
  };

  const handleFileDeleted = (fileKey: string) => {
    // Filter out the deleted file by fileKey
    const updatedFiles = data.uploadedFiles.filter(file => file.fileKey !== fileKey);
    update("uploadedFiles", updatedFiles);
  };

  // Helper function to get files for a specific category
  const getFilesForCategory = (category: string): UploadedFile[] => {
    const categoryId = CategoryEnum[category as keyof typeof CategoryEnum] || category.replace(/\s+/g, '-').toLowerCase();
    return data.uploadedFiles.filter(file => file.reference === categoryId);
  };

  const categoryOptions = [
    "Identification Documents",
    "Medical Records",
    "Financial Documents",
    "Legal Documents",
    "Insurance Policies",
    "Property Records",
    "Personal Messages",
    "Photos and Videos",
    "Other"
  ];

  return (
    <FormSection
      title="Document Uploads"
      description="Upload and organize important documents."
      disabled={disabled}
    >
      <SubSection title="Document Categories">
        <CheckboxGroup
          label="Select document categories to upload"
          options={categoryOptions}
          selected={data.documentCategories}
          onChange={toggleCategory}
          columns={3}
        />
        
        <TextAreaField
          label="Additional notes about your uploads"
          value={data.additionalNotes}
          onChange={(e) => update("additionalNotes", e.target.value)}
        />
      </SubSection>
      
      {data.documentCategories.map((category) => {
        const categoryFiles = getFilesForCategory(category);
        const categoryId = CategoryEnum[category as keyof typeof CategoryEnum] || category.replace(/\s+/g, '-').toLowerCase();
        
        return (
          <SubSection key={category} title={category}>
            <EnhancedFileUpload 
              title={`Upload ${category}`} 
              description={`Upload your ${category.toLowerCase()} here.`}
              sectionType="Uploads"
              questionId={categoryId}
              onFileUploaded={(file) => handleFileUploaded(category, file)}
              existingFiles={categoryFiles}
            />
            
            {/* Display uploaded files */}
            {categoryFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Uploaded Files</h4>
                <div className="space-y-2">
                  {categoryFiles.map((file) => (
                    <FilePreview 
                      key={file.fileKey} 
                      file={file} 
                      onDelete={(fileKey) => handleFileDeleted(fileKey)}
                    />
                  ))}
                </div>
              </div>
            )}
          </SubSection>
        );
      })}
    </FormSection>
  );
};

export default UploadsSection;