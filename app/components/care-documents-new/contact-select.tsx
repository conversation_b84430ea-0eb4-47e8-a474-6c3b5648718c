'use client'

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type Contact = {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  address?: string;
  email?: string;
  type: string;
  dateOfBirth?: string;
  entityName?: string;
};

type ContactSelectProps = {
  label: string;
  contacts: Contact[];
  onSelect: (contact: Contact) => void;
};

const ContactSelect: React.FC<ContactSelectProps> = ({ label, contacts, onSelect }) => {
  if (!contacts || contacts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2 mb-4">
      <Label className="text-sm font-medium text-gray-700">
        {label}
      </Label>
      <Select onValueChange={(value) => {
        const selectedContact = contacts.find(contact => contact.id === value);
        if (selectedContact) {
          onSelect(selectedContact);
        }
      }}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select a contact" />
        </SelectTrigger>
        <SelectContent>
          {contacts.filter(contact => contact.type === "individual").map((contact) => (
            <SelectItem key={contact.id} value={contact.id}>
              {contact.type === 'charity' 
                ? contact.entityName 
                : `${contact.firstName} ${contact.lastName}`}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ContactSelect;