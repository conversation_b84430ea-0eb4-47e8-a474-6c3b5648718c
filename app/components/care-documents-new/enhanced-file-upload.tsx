'use client'

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, FileText, X, Loader2, File } from 'lucide-react';
import { useCareDocumentFiles, FileInfo } from '@/hooks/useCareDocumentFiles';
import { useCareDocumentsContext } from '@/app/context/CareDocumentsContext';
import { toast } from 'sonner';
import { UploadedFile } from '@/hooks/useCareDocumentsNew';

interface EnhancedFileUploadProps {
  title?: string;
  description?: string;
  sectionType: string;
  questionId: string;
  onFileUploaded?: (fileInfo: UploadedFile) => void;
  existingFiles?: UploadedFile[];
}

const EnhancedFileUpload: React.FC<EnhancedFileUploadProps> = ({
  title = "Upload Files",
  description = "Attach relevant documents here (e.g., PDFs, images).",
  sectionType,
  questionId,
  onFileUploaded,
  existingFiles = [],
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { uploadFile, getFileUrl, deleteFile } = useCareDocumentFiles();
  const { documentId, saveSection } = useCareDocumentsContext();

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0 || !documentId) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Upload each file
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        
        // Simulate progress
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 5, 90));
        }, 200);
        
        // Upload the file
        const fileInfo = await uploadFile(file, documentId, `${sectionType}-${questionId}`);
        
        clearInterval(progressInterval);
        setUploadProgress(100);
        
        // Convert FileInfo to UploadedFile format
        const uploadedFile: UploadedFile = {
          fileKey: fileInfo.key,
          fileName: fileInfo.fileName,
          fileType: fileInfo.fileType,
          fileSize: fileInfo.fileSize,
          reference: questionId
        };
        
        // Notify parent component
        if (onFileUploaded) {
          onFileUploaded(uploadedFile);
        }
        
        toast.success(`File ${fileInfo.fileName} uploaded successfully`);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="col-span-full p-6 border-2 border-dashed border-gray-300 rounded-2xl text-center space-y-4 bg-gray-50">
      <div className="mx-auto w-16 h-16 flex items-center justify-center bg-white rounded-full shadow-md">
        <Upload className="h-8 w-8" />
      </div>
      <h3 className="font-semibold text-lg text-gray-800">{title}</h3>
      <p className="text-sm text-gray-500">{description}</p>
      
      {isUploading ? (
        <div className="space-y-2">
          <Loader2 className="h-5 w-5 animate-spin mx-auto" />
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-500">Uploading... {uploadProgress}%</p>
        </div>
      ) : (
        <Button
          variant="outline"
          className="rounded-xl"
          onClick={handleFileSelect}
        >
          <FileText className="mr-2 h-4 w-4" />
          Select Files
        </Button>
      )}
      
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        multiple
      />
    </div>
  );
};

export default EnhancedFileUpload;