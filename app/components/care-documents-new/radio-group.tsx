'use client'

import { Button } from '@/components/ui/button';

const RadioGroup: React.FC<{
  value: boolean | null;
  onChange: (value: boolean) => void;
  options?: Array<{ label: string; value: boolean }>;
}> = ({
  value,
  onChange,
  options = [
    { label: "Yes", value: true },
    { label: "No", value: false },
  ],
}) => (
  <div className="flex flex-wrap gap-2">
    {options.map((opt) => (
      <Button
        key={opt.label}
        type="button"
        variant={value === opt.value ? "default" : "outline"}
        onClick={() => onChange(opt.value)}
        className={`rounded-xl px-4 py-2 ${
          value === opt.value
            ? "text-white shadow-md"
            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
        }`}
      >
        {opt.label}
      </Button>
    ))}
  </div>
);

export default RadioGroup;