'use client'

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

const StringRadioGroup: React.FC<{
  label?: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<string>;
}> = ({
  label,
  value,
  onChange,
  options,
}) => (
  <div className="space-y-2">
    {label && (
      <Label className="text-sm font-medium text-gray-700">
        {label}
      </Label>
    )}
    <div className="flex flex-wrap gap-2">
      {options.map((opt) => (
        <Button
          key={opt}
          type="button"
          variant={value === opt ? "default" : "outline"}
          onClick={() => onChange(opt)}
          className={`rounded-xl px-4 py-2 ${
            value === opt
              ? "text-white shadow-md"
              : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
          }`}
        >
          {opt}
        </Button>
      ))}
    </div>
  </div>
);

export default StringRadioGroup;