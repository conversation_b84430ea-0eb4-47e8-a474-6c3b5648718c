'use client'

import { useState, useEffect, useRef } from 'react';
import { File, X, Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCareDocumentFiles } from '@/hooks/useCareDocumentFiles';
import { UploadedFile } from '@/hooks/useCareDocumentsNew';
import { toast } from 'sonner';

interface FilePreviewProps {
  file: UploadedFile;
  onDelete?: (fileKey: string) => void;
  showDelete?: boolean;
}

const FilePreview: React.FC<FilePreviewProps> = ({ 
  file, 
  onDelete,
  showDelete = true 
}) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const { getFileUrl, deleteFile } = useCareDocumentFiles();
  const retryCount = useRef(0);
  const retryTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchFileUrl = async () => {
      try {
        setLoading(true);
        const url = await getFileUrl(file.fileKey);
        setFileUrl(url);
        retryCount.current = 0; // reset on success
      } catch (error) {
        if (retryCount.current < 3) {
          retryCount.current += 1;
          console.log(`Retrying in 1s... attempt ${retryCount.current}`);
          retryTimeout.current = setTimeout(fetchFileUrl, 1000); // retry after 1s
        } else {
          console.error("Max retries reached. Could not fetch file URL:", error);
          // toast.error("Failed to load file after 3 attempts.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchFileUrl();

    return () => {
      if (retryTimeout.current) {
        clearTimeout(retryTimeout.current);
      }
    };
  }, [file.fileKey]);

  const handleDelete = async () => {
    try {
      setLoading(true);
    //   await deleteFile(file.fileKey);
      if (onDelete) {
        onDelete(file.fileKey);
      }
      toast.success(`File ${file.fileName} deleted successfully`);
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = () => {
    // You can add more file type icons based on file.fileType
    return <File className="h-6 w-6" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="flex items-center justify-between p-3 bg-white border rounded-lg shadow-sm">
      {loading ? (
        <div className="flex items-center space-x-3 w-full">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span className="text-sm text-gray-500">Loading...</span>
        </div>
      ) : (
        <>
          <div className="flex items-center space-x-3">
            {getFileIcon()}
            <div>
              <p className="text-sm font-medium truncate max-w-[200px]">{file.fileName}</p>
              <p className="text-xs text-gray-500">{formatFileSize(file.fileSize)}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            {fileUrl && (
              <a 
                href={fileUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800"
              >
                <Button size="sm" variant="ghost">
                  <Download className="h-4 w-4" />
                </Button>
              </a>
            )}
            {showDelete && (
              <Button size="sm" variant="ghost" onClick={handleDelete}>
                <X className="h-4 w-4 text-red-500" />
              </Button>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default FilePreview;