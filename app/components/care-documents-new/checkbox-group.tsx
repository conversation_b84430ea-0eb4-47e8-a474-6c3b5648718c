'use client';

import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

const CheckboxGroup: React.FC<{
  label: string;
  options: Array<{ label: string; value: string } | string>;
  selected: string[];
  onChange: (value: string) => void;
  required?: boolean;
  columns?: number;
  isOptionDisabled?: (value: string, selected: string[]) => boolean;
}> = ({
  label,
  options,
  selected = [],
  onChange,
  required,
  columns = 2,
  isOptionDisabled,
}) => (
  <div className='space-y-3 col-span-full'>
    <Label className='text-sm font-medium text-gray-700 flex items-center'>
      {label}
      {required && <span className='text-red-500 ml-1'>*</span>}
    </Label>
    <div className={cn('grid grid-cols-1 gap-3', `md:grid-cols-${columns}`)}>
      {options.map(option => {
        const optionValue = typeof option === 'string' ? option : option.value;
        const optionLabel = typeof option === 'string' ? option : option.label;
        const disabled = isOptionDisabled?.(optionValue, selected) ?? false;

        return (
          <label
            key={optionValue}
            className={cn(
              'flex items-center space-x-3 bg-white p-3 rounded-lg border border-gray-200 transition',
              disabled
                ? 'opacity-60 cursor-not-allowed'
                : 'cursor-pointer hover:bg-gray-50'
            )}
          >
            <Checkbox
              checked={selected.includes(optionValue)}
              onCheckedChange={() => !disabled && onChange(optionValue)}
              className='h-5 w-5 rounded text-blue-600 focus:ring-blue-500'
              disabled={disabled}
            />
            <span className='text-sm text-gray-800'>{optionLabel}</span>
          </label>
        );
      })}
    </div>
  </div>
);

export default CheckboxGroup;
