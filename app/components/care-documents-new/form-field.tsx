'use client'

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

const FormField: React.FC<{
  label: string;
  id?: string;
  required?: boolean;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  placeholder?: string;
  disabled?: boolean;
}> = ({ label, id, required, value, onChange, type = "text", placeholder, disabled }) => (
  <div className="space-y-2">
    <Label
      htmlFor={id}
      className="text-sm font-medium text-gray-700 flex items-center"
    >
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
    <Input
      id={id}
      disabled={disabled}
      type={type}
      className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-30 disabled:cursor-not-allowed"
      value={value || ""}
      onChange={onChange}
      placeholder={placeholder}
    />
  </div>
);

export default FormField;