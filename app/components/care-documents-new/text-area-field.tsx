'use client'

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const TextAreaField: React.FC<{
  label: string;
  id?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  required?: boolean;
  rows?: number;
  placeholder?: string;
  disabled?: boolean;
}> = ({ label, id, value, onChange, required, rows = 4, placeholder, disabled }) => (
  <div className="space-y-2 col-span-full">
    <Label
      htmlFor={id}
      className="text-sm font-medium text-gray-700 flex items-center"
    >
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
    <Textarea
      id={id}
      rows={rows}
      disabled={disabled}
      className={`w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${disabled ? 'opacity-30 pointer-events-none' : ''}`}
      value={value || ""}
      onChange={onChange}
      placeholder={placeholder}
    />
  </div>
);

export default TextAreaField;