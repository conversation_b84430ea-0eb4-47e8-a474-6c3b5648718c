'use client';

import * as Sentry from '@sentry/nextjs';
import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  Phone,
  MapPin,
  Loader2,
} from 'lucide-react';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usaStates } from '@/app/utils/states';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import PasswordRequirements from './PasswordRequirements';
import { checkEmailExists } from '@/app/utils/emailValidation';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { useModal } from '@/hooks/use-modal';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { customSignUp } from '@/utils/customSignUp';
import { signIn } from 'aws-amplify/auth';
import routes from '@/utils/routes';
import { StreetAutocomplete } from '@/components/address-autocomplete/StreetAutocomplete';
import { CityAutocomplete } from '@/components/address-autocomplete/CityAutocomplete';
import { ZipCodeAutocomplete } from '@/components/address-autocomplete/ZipCodeAutocomplete';
import { validateAddress } from '@/app/utils/addressValidation';
import { UPSAddress } from '@/app/types/ups';
import { Checkbox } from '@/components/ui/checkbox';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { verifyPromoCode } from '@/lib/utils/promoCodeUtils';

// Map our component steps to the onboarding steps enum
type SignUpStep = 'credentials' | 'personalInfo' | 'confirmation';

interface SignUpFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  confirmPassword: string;
  setConfirmPassword: (confirmPassword: string) => void;
  code: string;
  setCode: (code: string) => void;
  showPassword: boolean;
  togglePasswordVisibility: () => void;
  error: string;
  setError: (error: string) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  onComplete: (step: 'confirmSignUp') => void;
  onStepChange?: (step: SignUpStep) => void;
  // Invite-related props
  inviteData?: {
    firstName?: string;
    middleName?: string;
    lastName?: string;
    phoneNumber?: string;
    birthdate?: string;
    state?: string;
    inviteToken?: string;
    role?: string;
  };
}

export default function SignUpForm({
  email,
  setEmail,
  password,
  setPassword,
  confirmPassword,
  setConfirmPassword,
  code,
  setCode,
  showPassword,
  togglePasswordVisibility,
  error,
  setError,
  loading,
  setLoading,
  onComplete,
  onStepChange,
  inviteData,
}: SignUpFormProps) {
  const { refreshUser } = useAuth();
  const router = useRouter();

  const [currentStep, setCurrentStep] = useState<SignUpStep>('credentials');
  const [isSuccess, setIsSuccess] = useState(false);
  const inviteDataInitialized = useRef(false);

  console.log('===> CURRENT STEP ', currentStep);

  const [firstName, setFirstName] = useState('');
  const [middleName, setMiddleName] = useState('');
  const [lastName, setLastName] = useState('');
  const [state, setState] = useState('');
  const [birthdayDate, setBirthdayDate] = useState<Date | undefined>(undefined);
  const [phone, setPhone] = useState('+1');
  const [howDidYouHearAboutUs, setHowDidYouHearAboutUs] = useState('');
  const [gender, setGender] = useState('not-specified');
  const [emailChecking, setEmailChecking] = useState(false);
  const [inviteToken, setInviteToken] = useState<string | null>(null);
  const [inviteRole, setInviteRole] = useState<string>('');

  // Promo code states
  const [isPromoCode, setIsPromoCode] = useState(false);
  const [promoCodeValidating, setPromoCodeValidating] = useState(false);
  const [promoCodeError, setPromoCodeError] = useState<string>('');
  const [promoCodeValid, setPromoCodeValid] = useState(false);

  // Address validation states
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);

  // Add address state
  const [address, setAddress] = useState<
    UPSAddress & { name?: string; phone?: string }
  >({
    addressLine1: '',
    addressLine2: '',
    city: '',
    stateProvinceCode: '',
    postalCode: '',
    countryCode: 'US',
  });

  // Modal for age restriction
  const ageRestrictionModal = useModal();

  const validatePromoCode = async (code: string): Promise<boolean> => {
    try {
      setPromoCodeValidating(true);
      return await verifyPromoCode(code);
    } catch (error) {
      console.error('Error validating promo code:', error);
      Sentry.captureException(error);
      setPromoCodeError('Error validating promo code');
      setPromoCodeValid(false);
      return false;
    } finally {
      setPromoCodeValidating(false);
    }
  };

  // Pre-fill form with invite data if available
  useEffect(() => {
    if (inviteData && !inviteDataInitialized.current) {
      setFirstName(inviteData?.firstName || '');
      setMiddleName(inviteData?.middleName || '');
      setLastName(inviteData.lastName || '');
      setPhone(
        inviteData.phoneNumber
          ? formatPhoneNumber(inviteData.phoneNumber)
          : '+1'
      );
      setState(inviteData.state || '');
      setBirthdayDate(
        inviteData.birthdate ? new Date(inviteData.birthdate) : undefined
      );
      setInviteToken(inviteData.inviteToken || null);
      setInviteRole(inviteData.role || 'Member');

      // Only set to credentials step on initial load
      setCurrentStep('credentials');
      onStepChange?.('credentials');
      inviteDataInitialized.current = true;
    }
  }, [inviteData]);

  // Validation functions for each step
  const isCredentialsStepValid = () => {
    if (!email || !password || !confirmPassword) return false;

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return false;

    // Password strength validation
    const hasMinLength = password.length >= 12;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[^A-Za-z0-9]/.test(password);
    const isPasswordStrong =
      hasMinLength &&
      hasUppercase &&
      hasLowercase &&
      hasNumber &&
      hasSpecialChar;

    if (!isPasswordStrong) return false;
    if (password !== confirmPassword) return false;

    return true;
  };

  const isPersonalInfoStepValid = () => {
    if (!firstName || !lastName || !birthdayDate || !phone) return false;

    // Validate address
    if (
      !address.addressLine1 ||
      !address.city ||
      !address.stateProvinceCode ||
      !address.postalCode
    )
      return false;

    // Validate age (must be at least 18 years old)
    const age = calculateAge(birthdayDate);
    if (age < 18) return false;

    // Validate phone number format (+1 followed by 10 digits)
    const phoneDigits = phone.replace(/\D/g, '');
    if (phoneDigits.length !== 11 || !phoneDigits.startsWith('1')) return false;

    // If promo code checkbox is checked, validate that promo code is valid
    if (isPromoCode) {
      if (!howDidYouHearAboutUs.trim() || promoCodeError || !promoCodeValid) {
        return false;
      }
    }

    return true;
  };

  // Function to calculate age from birthday
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Function to get the maximum allowed birth date (18 years ago from today)
  const getMaxBirthDate = (): Date => {
    const today = new Date();
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() - 18);
    // Add one day to the date to make it inclusive
    maxDate.setDate(maxDate.getDate());
    return maxDate;
  };

  // Function to get the maximum allowed birth year
  const getMaxBirthYear = (): number => {
    const today = new Date();
    return today.getFullYear() - 18;
  };

  // Function to format phone number with +1 prefix
  const formatPhoneNumber = (value: string): string => {
    // If user tries to clear the field or remove +1, reset to +1
    if (value.length < 2 || !value.startsWith('+1')) {
      return '+1';
    }

    // Remove all non-digit characters except the + at the beginning
    const digits = value.slice(1).replace(/\D/g, '');

    // If no digits after +, return +1
    if (digits.length === 0) return '+1';

    // If starts with 1, keep it as is
    if (digits.startsWith('1')) {
      // Limit to 11 digits total (1 + 10 digits)
      const limitedDigits = digits.slice(0, 11);
      return `+${limitedDigits}`;
    } else {
      // If doesn't start with 1, add 1 prefix and limit to 10 additional digits
      const limitedDigits = digits.slice(0, 10);
      return `+1${limitedDigits}`;
    }
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
    // Clear any existing form errors when user starts typing
    if (error && error.includes('email')) {
      setError('');
    }
  };

  const handlePasswordChange = (newPassword: string) => {
    setPassword(newPassword);
    // Clear any existing form errors when user starts typing
    if (error && (error.includes('password') || error.includes('Password'))) {
      setError('');
    }
  };

  const handleConfirmPasswordChange = (newConfirmPassword: string) => {
    setConfirmPassword(newConfirmPassword);
    // Clear any existing form errors when user starts typing
    if (
      error &&
      (error.includes('password') ||
        error.includes('Password') ||
        error.includes('match'))
    ) {
      setError('');
    }
  };

  const handleFieldChange = (setter: (value: any) => void, value: any) => {
    setter(value);
    // Clear any existing form errors when user starts typing
    if (error) {
      setError('');
    }
  };

  const handlePromoCodeCheckboxChange = (checked: boolean) => {
    setIsPromoCode(checked);
    setPromoCodeError('');
    setPromoCodeValid(false);

    // If unchecking, clear any promo code validation errors
    if (!checked) {
      setPromoCodeError('');
      setPromoCodeValid(false);
    }

    // Clear general form errors
    if (error) {
      setError('');
    }
  };

  const handleReferralSourceChange = (value: string) => {
    setHowDidYouHearAboutUs(value);

    // Clear promo code validation state when user starts typing
    if (isPromoCode) {
      setPromoCodeError('');
      setPromoCodeValid(false);
    }

    // Clear general form errors
    if (error) {
      setError('');
    }
  };

  const handlePromoCodeBlur = async () => {
    // Only validate if promo code checkbox is checked and there's a value
    if (isPromoCode && howDidYouHearAboutUs.trim()) {
      await validatePromoCode(howDidYouHearAboutUs);
    }
  };

  const handleNextStep = async () => {
    if (currentStep === 'credentials') {
      if (!email || !password || !confirmPassword) {
        setError('Please fill in all fields');
        return;
      }

      // Simple email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        setError('Please enter a valid email address');
        return;
      }

      // Skip email checking for invites (email is pre-validated)
      if (!inviteData) {
        // Check if email already exists
        setEmailChecking(true);
        setError('');

        try {
          const emailCheck = await checkEmailExists(email);

          if (emailCheck.error) {
            setError(`Error checking email: ${emailCheck.error}`);
            setEmailChecking(false);
            return;
          }

          if (emailCheck.exists) {
            setError(
              'This email is already registered. Please use a different email or try logging in.'
            );
            setEmailChecking(false);
            return;
          }
        } catch (error) {
          setError('Failed to verify email availability. Please try again.');
          setEmailChecking(false);
          return;
        }

        setEmailChecking(false);
      }

      // Password validation
      const hasMinLength = password.length >= 12;
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasSpecialChar = /[^A-Za-z0-9]/.test(password);
      const isPasswordStrong =
        hasMinLength &&
        hasUppercase &&
        hasLowercase &&
        hasNumber &&
        hasSpecialChar;

      if (!isPasswordStrong) {
        setError('Password does not meet the strength requirements');
        return;
      }

      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      setError('');
      setCurrentStep('personalInfo');
      onStepChange?.('personalInfo');
    } else if (currentStep === 'personalInfo') {
      if (!firstName || !lastName || !birthdayDate) {
        setError('Please fill in all required fields');
        return;
      }

      // Validate address fields
      if (
        !address.addressLine1 ||
        !address.city ||
        !address.stateProvinceCode ||
        !address.postalCode
      ) {
        setError('Please fill in all required address fields');
        return;
      }

      // Validate address with Google API
      console.log('🔍 Validating address before registration...');
      setIsValidatingAddress(true);
      try {
        const addressValidationResult = await validateAddress({
          addressLine1: address.addressLine1,
          city: address.city,
          state: address.stateProvinceCode,
          postalCode: address.postalCode,
        });

        if (!addressValidationResult.isValid) {
          setError(`${addressValidationResult.issues.join(', ')}`);
          setIsValidatingAddress(false);
          return;
        }

        console.log('✅ Address validation passed for registration');
      } catch (error) {
        Sentry.captureException(error);
        console.error('❌ Address validation error:', error);
        setError(
          'Address validation failed. Please check your address and try again.'
        );
        setIsValidatingAddress(false);
        return;
      }
      setIsValidatingAddress(false);

      // Validate age (must be at least 18 years old)
      const age = calculateAge(birthdayDate);
      if (age < 18) {
        setError('You must be at least 18 years old to register');
        return;
      }

      // Check if user is at least 18 years old for service eligibility
      // Skip this check for invited users
      if (age < 18 && !inviteToken) {
        ageRestrictionModal.open();
        return;
      }

      // Validate phone number format (+1 followed by 10 digits)
      const phoneDigits = phone.replace(/\D/g, '');
      if (phoneDigits.length !== 11 || !phoneDigits.startsWith('1')) {
        setError(
          'Phone number must be in format +1XXXXXXXXXX (11 digits total)'
        );
        return;
      }

      // If promo code checkbox is checked, validate the promo code
      if (isPromoCode) {
        if (!howDidYouHearAboutUs.trim()) {
          setError('Please enter a promo code');
          return;
        }

        // If promo code hasn't been validated yet, validate it now
        if (!promoCodeValid) {
          const isValidPromoCode =
            await validatePromoCode(howDidYouHearAboutUs);
          if (!isValidPromoCode) {
            setError(promoCodeError || 'Invalid promo code');
            return;
          }
        }

        // Double check that promo code is still valid
        if (promoCodeError || !promoCodeValid) {
          setError(promoCodeError || 'Invalid promo code');
          return;
        }
      }

      setError('');
      await handleSignUp();
    }
  };

  const handlePreviousStep = () => {
    if (currentStep === 'personalInfo') {
      setCurrentStep('credentials');
      onStepChange?.('credentials');
    } else if (currentStep === 'confirmation') {
      setCurrentStep('personalInfo');
      onStepChange?.('personalInfo');
    }
  };

  const handleSignUp = async () => {
    setLoading(true);
    setError('');
    setCurrentStep('confirmation'); // Show confirmation step during loading
    onStepChange?.('confirmation');

    try {
      // Format address as a string for the state field
      const addressString = JSON.stringify(address);
      console.log('Address data:', address);

      // Get the state code from the address
      const stateCode = address.stateProvinceCode;
      const selectedState = usaStates.find(s => s.value === stateCode);
      const stateLabel = selectedState ? selectedState.label : stateCode;

      const result = await customSignUp({
        email,
        password,
        firstName,
        middleName,
        lastName,
        state: stateLabel,
        birthdayDate: birthdayDate!,
        phone,
        inviteToken: inviteToken || undefined,
        howDidYouHearAboutUs: howDidYouHearAboutUs || undefined,
        address: addressString || undefined,
        gender: gender || undefined,
      });

      if (result.success) {
        setError('');
        if (result.message && result.message.includes('invite')) {
          // Show success message briefly before redirect for invite users
          setIsSuccess(true);
          await signIn({ username: email, password });
          await refreshUser();

          // Add a small delay to show the completion state
          setTimeout(() => {
            if (inviteRole === 'WelonTrust') {
              router.push(routes.welon.dashboard);
            } else if (inviteRole === 'Administrator') {
              router.push(routes.admin.dashboard);
            } else if (inviteRole === 'CallCenter') {
              router.push(routes.callCenter.medicalIncident);
            } else if (inviteRole === 'MedicalReview') {
              router.push(routes.medical.medicalReview);
            } else {
              router.push(routes.member.onboarding);
            }
          }, 1500);
        } else {
          // You could show a success message here or redirect
          onComplete('confirmSignUp');
        }
      } else {
        setError(result.error || 'Failed to sign up');
        setCurrentStep('personalInfo'); // Go back to previous step on error
        onStepChange?.('personalInfo');
        setIsSuccess(false); // Reset success state
      }
    } catch (err) {
      Sentry.captureException(err);
      console.error('Error signing up:', err);
      if (typeof err === 'object' && err !== null && 'message' in err) {
        setError((err as { message: string }).message);
      } else {
        setError('Failed to sign up');
      }
      setCurrentStep('personalInfo'); // Go back to previous step on error
      onStepChange?.('personalInfo');
      setIsSuccess(false); // Reset success state
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='w-full'>
      <div className='mb-4'>
        <div className='relative w-full h-2 bg-background rounded-full overflow-hidden'>
          <div
            className='absolute top-0 left-0 h-full bg-primary transition-all duration-300'
            style={{ width: currentStep === 'credentials' ? '50%' : '100%' }}
          />
        </div>
        <div className='flex justify-between mt-1 text-xs text-muted-foreground'>
          <span>Account</span>
          <span>Personal Info</span>
        </div>
      </div>

      {currentStep === 'credentials' && (
        <div className='space-y-4 pt-4'>
          <div className='space-y-2'>
            <Label htmlFor='signup-email'>Email</Label>
            <div className='relative'>
              <Mail className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='signup-email'
                type='email'
                value={email}
                onChange={e => handleEmailChange(e.target.value)}
                className='pl-10'
                placeholder='Enter your email'
                readOnly={!!inviteData}
                required
              />
            </div>
            {inviteData && (
              <p className='text-xs text-muted-foreground'>
                Email is pre-filled from your invitation
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='signup-password'>Password</Label>
            <div className='relative'>
              <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='signup-password'
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={e => handlePasswordChange(e.target.value)}
                className='pl-10 pr-10'
                placeholder='Create a password'
                required
              />
              <Button
                type='button'
                variant='ghost'
                size='icon'
                className='absolute right-0 top-0 h-9 w-9'
                onClick={togglePasswordVisibility}
              >
                {showPassword ? (
                  <Eye className='h-4 w-4' />
                ) : (
                  <EyeOff className='h-4 w-4' />
                )}
              </Button>
            </div>
            <div className='space-y-1 mt-2'>
              <PasswordStrengthIndicator password={password} />
              <PasswordRequirements password={password} />
            </div>
          </div>
          <div className='space-y-2'>
            <Label htmlFor='confirm-password'>Confirm Password</Label>
            <div className='relative'>
              <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='confirm-password'
                type={showPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={e => handleConfirmPasswordChange(e.target.value)}
                className='pl-10'
                placeholder='Confirm your password'
                required
              />
            </div>
          </div>
          {error && <p className='text-destructive text-sm'>{error}</p>}
          <Button
            type='button'
            className='w-full'
            onClick={handleNextStep}
            disabled={loading || emailChecking || !isCredentialsStepValid()}
          >
            {emailChecking ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Checking email...
              </>
            ) : (
              'Next'
            )}
          </Button>
        </div>
      )}

      {currentStep === 'personalInfo' && (
        <div className='space-y-4 pt-4'>
          <div className='grid grid-cols-1 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='first-name'>First Name *</Label>
              <div className='relative'>
                <User className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='first-name'
                  value={firstName}
                  onChange={e =>
                    handleFieldChange(setFirstName, e.target.value)
                  }
                  className='pl-10'
                  placeholder='First name'
                  required
                />
              </div>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='middle-name'>Middle Name</Label>
              <div className='relative'>
                <User className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='middle-name'
                  value={middleName}
                  onChange={e =>
                    handleFieldChange(setMiddleName, e.target.value)
                  }
                  className='pl-10'
                  placeholder='Middle name (optional)'
                />
              </div>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='last-name'>Last Name *</Label>
              <div className='relative'>
                <User className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='last-name'
                  value={lastName}
                  onChange={e => handleFieldChange(setLastName, e.target.value)}
                  className='pl-10'
                  placeholder='Last name'
                  required
                />
              </div>
            </div>
          </div>

          {/* Address with Google Places Autocomplete */}
          <div className='space-y-4'>
            <h3 className='text-lg font-medium'>Address</h3>

            <StreetAutocomplete
              label='Street Address *'
              value={address.addressLine1 || ''}
              onChange={street => {
                setAddress({ ...address, addressLine1: street });
                if (error) setError('');
              }}
              onAddressSelect={components => {
                // Parse Google Places address components
                const addressData: any = {};

                components.forEach((component: any) => {
                  const types = component.types;
                  if (types.includes('street_number')) {
                    addressData.streetNumber = component.long_name;
                  } else if (types.includes('route')) {
                    addressData.route = component.long_name;
                  } else if (types.includes('locality')) {
                    addressData.city = component.long_name;
                  } else if (types.includes('administrative_area_level_1')) {
                    addressData.state = component.short_name;
                  } else if (types.includes('postal_code')) {
                    addressData.zip = component.long_name;
                  } else if (types.includes('administrative_area_level_2')) {
                    addressData.county = component.long_name;
                  }
                });

                // Update all address fields
                const newAddress = {
                  ...address,
                  addressLine1:
                    addressData.streetNumber && addressData.route
                      ? `${addressData.streetNumber} ${addressData.route}`
                      : addressData.route ||
                        addressData.streetNumber ||
                        address.addressLine1 ||
                        '',
                  city: addressData.city || address.city || '',
                  stateProvinceCode:
                    addressData.state || address.stateProvinceCode || '',
                  postalCode: addressData.zip || address.postalCode || '',
                };

                setAddress(newAddress);
                if (error) setError('');
              }}
              placeholder='Start typing your address...'
            />

            <div className='space-y-2'>
              <Label htmlFor='address2'>Address Line 2</Label>
              <Input
                id='address2'
                value={address.addressLine2 || ''}
                onChange={e =>
                  setAddress({ ...address, addressLine2: e.target.value })
                }
                placeholder='Apartment, suite, unit, building, floor, etc.'
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <CityAutocomplete
                label='City *'
                value={address.city || ''}
                onChange={city => setAddress({ ...address, city })}
                placeholder='Start typing a city...'
              />
              <div className='space-y-2'>
                <Label htmlFor='state'>State *</Label>
                <Input
                  id='state'
                  value={address.stateProvinceCode || ''}
                  onChange={e =>
                    setAddress({
                      ...address,
                      stateProvinceCode: e.target.value.toUpperCase(),
                    })
                  }
                  onBlur={() => {
                    // Trigger validation on any address component when state loses focus
                    if (
                      address.addressLine1 &&
                      address.city &&
                      address.stateProvinceCode &&
                      address.postalCode
                    ) {
                      console.log(
                        'State field blur - all address fields filled, validation will be triggered by other components'
                      );
                    }
                  }}
                  placeholder='CA'
                  maxLength={2}
                />
              </div>
              <ZipCodeAutocomplete
                label='ZIP Code *'
                value={address.postalCode || ''}
                onChange={postalCode => setAddress({ ...address, postalCode })}
                onZipSelect={zipData => {
                  setAddress({
                    ...address,
                    postalCode: zipData.zip,
                    city: zipData.city || address.city,
                    stateProvinceCode:
                      zipData.state || address.stateProvinceCode,
                  });
                  if (error) setError('');
                }}
                placeholder='12345'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='birthday'>Birthday *</Label>
            <DatePicker
              date={birthdayDate}
              setDate={date => handleFieldChange(setBirthdayDate, date)}
              showYearPicker={true}
              showMonthPicker={true}
              yearRange={{ from: 1900, to: getMaxBirthYear() }}
              calendarProps={{
                disabled: (date: Date) => date > getMaxBirthDate(),
              }}
            />
            <p className='text-xs text-muted-foreground'>
              You must be at least 18 years old to register
            </p>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='gender'>Gender (optional)</Label>
            <Select
              value={gender}
              onValueChange={value => handleFieldChange(setGender, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select gender (optional)' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='not-specified'>Prefer not to say</SelectItem>
                <SelectItem value='male'>Male</SelectItem>
                <SelectItem value='female'>Female</SelectItem>
                <SelectItem value='other'>Other</SelectItem>
              </SelectContent>
            </Select>
            <p className='text-xs text-muted-foreground'>
              This information is optional and helps us personalize your
              experience
            </p>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='phone'>Phone Number *</Label>
            <div className='relative'>
              <Phone className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='phone'
                type='tel'
                value={phone}
                onChange={e => {
                  const formattedValue = formatPhoneNumber(e.target.value);
                  handleFieldChange(setPhone, formattedValue);
                }}
                className='pl-10'
                placeholder='+1XXXXXXXXXX'
                maxLength={12}
                required
              />
            </div>
            <p className='text-xs text-muted-foreground'>
              US phone number format: +1 followed by 10 digits
            </p>
          </div>

          <div className='space-y-3'>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='promo-code-checkbox'
                checked={isPromoCode}
                onCheckedChange={handlePromoCodeCheckboxChange}
              />
              <Label
                htmlFor='promo-code-checkbox'
                className='text-sm font-medium'
              >
                I have a promo code
              </Label>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='referral-source'>
                {isPromoCode
                  ? 'Do you have a referral code? (optional)'
                  : 'How did you hear about us? (optional)'}
              </Label>
              <Input
                id='referral-source'
                type='text'
                value={howDidYouHearAboutUs}
                onChange={e => handleReferralSourceChange(e.target.value)}
                onBlur={handlePromoCodeBlur}
                placeholder={
                  isPromoCode
                    ? 'Enter your promo code (e.g., SAVE20)'
                    : 'e.g., Google search, friend referral...'
                }
                required={isPromoCode}
              />
              {isPromoCode && promoCodeError && (
                <p className='text-destructive text-sm'>{promoCodeError}</p>
              )}
              {isPromoCode && promoCodeValidating && (
                <p className='text-muted-foreground text-sm flex items-center gap-2'>
                  <Loader2 className='h-3 w-3 animate-spin' />
                  Validating promo code...
                </p>
              )}
              {isPromoCode &&
                !promoCodeValidating &&
                promoCodeValid &&
                howDidYouHearAboutUs.trim() && (
                  <p className='text-green-600 text-sm'>✓ Valid promo code</p>
                )}
            </div>
          </div>

          {error && <p className='text-destructive text-sm'>{error}</p>}
          <div className='flex gap-2'>
            <Button
              type='button'
              variant='outline'
              className='flex-1'
              onClick={handlePreviousStep}
            >
              Back
            </Button>
            <Button
              type='button'
              className='flex-1'
              onClick={handleNextStep}
              disabled={
                loading ||
                promoCodeValidating ||
                isValidatingAddress ||
                !isPersonalInfoStepValid()
              }
            >
              {loading ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Signing up...
                </>
              ) : isValidatingAddress ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Validating address...
                </>
              ) : promoCodeValidating ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Validating promo code...
                </>
              ) : (
                'Sign Up'
              )}
            </Button>
          </div>
        </div>
      )}

      {currentStep === 'confirmation' && (
        <div className='space-y-4 pt-4 text-center'>
          <div className='flex flex-col items-center space-y-4'>
            {isSuccess ? (
              <>
                <div className='h-8 w-8 rounded-full bg-green-100 flex items-center justify-center'>
                  <svg
                    className='h-5 w-5 text-green-600'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M5 13l4 4L19 7'
                    />
                  </svg>
                </div>
                <div>
                  <h3 className='text-lg font-semibold text-green-600'>
                    Account created successfully!
                  </h3>
                  <p className='text-sm text-muted-foreground mt-1'>
                    Redirecting you to your dashboard...
                  </p>
                </div>
              </>
            ) : (
              <>
                <Loader2 className='h-8 w-8 animate-spin text-primary' />
                <div>
                  <h3 className='text-lg font-semibold'>
                    Creating your account...
                  </h3>
                  <p className='text-sm text-muted-foreground mt-1'>
                    Please wait while we set up your account
                  </p>
                </div>
              </>
            )}
          </div>
          {error && (
            <div className='mt-4'>
              <p className='text-destructive text-sm'>{error}</p>
              <Button
                type='button'
                variant='outline'
                className='mt-2'
                onClick={() => {
                  setCurrentStep('personalInfo');
                  onStepChange?.('personalInfo');
                  setLoading(false);
                  setIsSuccess(false);
                }}
              >
                Go Back
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Age Restriction Modal */}
      <ConfirmationDialog
        {...ageRestrictionModal.modalProps}
        title='Service Eligibility'
        description='You must be at least 18 years old to register.'
        confirmLabel='I understand'
        onConfirm={() => ageRestrictionModal.close()}
        confirmVariant='default'
      />
    </div>
  );
}
