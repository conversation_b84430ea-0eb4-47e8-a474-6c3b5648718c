'use client';

import { But<PERSON> } from '@/components/ui/button';
import { HeaderMenuSkeleton } from '@/components/ui/skeletons';
import Link from 'next/link';
import routes from '@/utils/routes';
import { useAuth } from '@/app/context/AuthContext';
import { useRouter } from 'next/navigation';
import { UserRoleSwitcher } from './UserRoleSwitcher';
import { ThemeToggle } from './ThemeToggle';
import { NotificationBell } from '../../components/NotificationBell';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';

export default function Header() {
  const router = useRouter();
  const { user, loading, logout, userRoles, onboardingStatus } = useAuth();

  const handleSignOut = async () => {
    await logout();
    router.push(routes.login);
  };

  return (
    <header
      className={`fixed w-[100vw] top-0 left-0 right-0 z-50 border-b border-border ${isWelonTrust(userRoles) ? 'bg-[var(--background-welon-trust)]' : ''} ${isAdmin(userRoles) ? 'bg-[var(--background-admin)]' : ''} ${!isAdmin(userRoles) && !isWelonTrust(userRoles) ? 'bg-background' : ''}`}
    >
      <div className='container mx-auto flex h-16 items-center justify-between px-4'>
        <div className='flex items-center'>
          <Link href={routes.home} className='text-xl font-bold'>
            Childfree Trust
          </Link>
        </div>

        <nav className='flex items-center space-x-4'>
          <ThemeToggle />
          {loading ? (
            // Show skeleton during loading
            <HeaderMenuSkeleton />
          ) : user ? (
            <div className='flex items-center gap-4'>
              <NotificationBell />
              <UserRoleSwitcher
                user={user}
                onSignOut={handleSignOut}
                userRoles={userRoles}
                onboardingStatus={onboardingStatus}
              />
            </div>
          ) : (
            <Button asChild>
              <Link href={routes.login}>Sign in</Link>
            </Button>
          )}
        </nav>
      </div>
    </header>
  );
}
