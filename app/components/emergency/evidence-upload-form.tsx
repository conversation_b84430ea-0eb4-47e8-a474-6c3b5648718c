'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertCircle,
  CheckCircle2,
  Upload,
  FileText,
  Info,
} from 'lucide-react';
import { AccessTrigger } from './types';
import { User } from '@/types/account';

interface EvidenceUploadFormProps {
  onSubmit: (formData: {
    evidenceType: AccessTrigger;
    memberEmail: string;
    memberName: string;
    relationship: string;
    additionalInfo: string;
    file: File;
  }) => void;
  isSubmitting: boolean;
  error?: string;
  success?: string;
  initialEvidenceType?: AccessTrigger;
  selectedUser?: User | null;
}

export function EvidenceUploadForm({
  onSubmit,
  isSubmitting,
  error,
  success,
  initialEvidenceType = 'Death',
  selectedUser,
}: EvidenceUploadFormProps) {
  const [evidenceType, setEvidenceType] =
    useState<AccessTrigger>(initialEvidenceType);
  const [memberEmail, setMemberEmail] = useState('');
  const [memberName, setMemberName] = useState('');
  const [relationship, setRelationship] = useState('Welon Trust'); // Default to Welon Trust
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Auto-fill form when user is selected
  React.useEffect(() => {
    if (selectedUser) {
      setMemberEmail(selectedUser.email);
      setMemberName(selectedUser.name);
      setRelationship('Welon Trust'); // Always set to Welon Trust for Welon Trust users
    } else {
      // Clear fields when no user selected
      setMemberEmail('');
      setMemberName('');
      setRelationship('Welon Trust'); // Keep default
    }
  }, [selectedUser]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check file size (max 10MB)
      if (selectedFile.size > 10 * 1024 * 1024) {
        setFileError(
          'File size exceeds 10MB limit. Please select a smaller file.'
        );
        return;
      }

      // Check file type
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      ];
      if (!allowedTypes.includes(selectedFile.type)) {
        setFileError('Invalid file type. Please upload a PDF or DOCX.');
        return;
      }

      setFile(selectedFile);
      setFileError(null);
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!memberEmail.trim()) {
      errors.memberEmail = "Member's email address is required";
    } else {
      // Simple email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(memberEmail)) {
        errors.memberEmail = 'Please enter a valid email address';
      }
    }

    if (!memberName.trim()) {
      errors.memberName = "Member's full name is required";
    }

    if (!relationship) {
      errors.relationship = 'Please select your relationship to the member';
    }

    if (!file) {
      errors.file = `Please upload a ${evidenceType === 'Death' ? 'death certificate' : 'medical certificate'}`;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !file) {
      return;
    }

    onSubmit({
      evidenceType,
      memberEmail,
      memberName,
      relationship,
      additionalInfo,
      file,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Evidence Submission Form</CardTitle>
        <CardDescription>
          Please provide the required information and upload the necessary
          documentation.
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className='space-y-6'>
          {error && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className='bg-green-50 text-green-800 border-green-200'>
              <CheckCircle2 className='h-4 w-4' />
              <AlertTitle>Success</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <div className='space-y-2'>
            <Label>Evidence Type</Label>
            <RadioGroup
              value={evidenceType}
              onValueChange={value => setEvidenceType(value as AccessTrigger)}
              className='flex flex-col space-y-2'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='Death' id='death' />
                <Label htmlFor='death' className='font-normal'>
                  Death Certificate
                </Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='Incapacitation' id='incapacitation' />
                <Label htmlFor='incapacitation' className='font-normal'>
                  Medical Certificate (Incapacitation)
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='memberEmail'>Member's Email Address</Label>
            <Input
              id='memberEmail'
              type='email'
              value={memberEmail}
              onChange={e => setMemberEmail(e.target.value)}
              placeholder='<EMAIL>'
              className={formErrors.memberEmail ? 'border-destructive' : ''}
              readOnly={!!selectedUser}
            />
            {formErrors.memberEmail && (
              <p className='text-sm text-destructive'>
                {formErrors.memberEmail}
              </p>
            )}
            <p className='text-xs text-muted-foreground'>
              {selectedUser
                ? 'Auto-filled from selected member above.'
                : "The email address associated with the member's account."}
            </p>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='memberName'>Member's Full Name</Label>
            <Input
              id='memberName'
              value={memberName}
              onChange={e => setMemberName(e.target.value)}
              placeholder="Enter member's full name"
              className={formErrors.memberName ? 'border-destructive' : ''}
              readOnly={!!selectedUser}
            />
            {formErrors.memberName && (
              <p className='text-sm text-destructive'>
                {formErrors.memberName}
              </p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='relationship'>Your Relationship to Member</Label>
            <Select value={relationship} onValueChange={setRelationship}>
              <SelectTrigger
                id='relationship'
                className={formErrors.relationship ? 'border-destructive' : ''}
              >
                <SelectValue placeholder='Select relationship' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='Welon Trust'>Welon Trust</SelectItem>
                <SelectItem value='Family'>Family Member</SelectItem>
                <SelectItem value='Friend'>Friend</SelectItem>
                <SelectItem value='Legal'>Legal Representative</SelectItem>
                <SelectItem value='Medical'>Medical Professional</SelectItem>
                <SelectItem value='Other'>Other</SelectItem>
              </SelectContent>
            </Select>
            {formErrors.relationship && (
              <p className='text-sm text-destructive'>
                {formErrors.relationship}
              </p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='fileUpload'>
              Upload{' '}
              {evidenceType === 'Death'
                ? 'Death Certificate'
                : 'Medical Certificate'}
            </Label>
            <div
              className={`border-2 border-dashed rounded-md p-6 text-center ${fileError || formErrors.file ? 'border-destructive' : ''}`}
            >
              <FileText className='h-8 w-8 mx-auto mb-2 text-muted-foreground' />
              <p className='text-sm mb-2'>
                {file
                  ? file.name
                  : `Drag and drop your ${evidenceType === 'Death' ? 'death certificate' : 'medical certificate'} here, or click to browse`}
              </p>
              <p className='text-xs text-muted-foreground mb-4'>
                Accepted formats: PDF, DOCX. Maximum size: 10MB.
              </p>
              <Input
                id='fileUpload'
                type='file'
                className='hidden'
                accept='.pdf,.doc,.docx'
                onChange={handleFileChange}
              />
              <Button
                type='button'
                variant='outline'
                onClick={() => document.getElementById('fileUpload')?.click()}
              >
                <Upload className='h-4 w-4 mr-2' />
                Select File
              </Button>
            </div>
            {fileError && (
              <p className='text-sm text-destructive'>{fileError}</p>
            )}
            {formErrors.file && !fileError && (
              <p className='text-sm text-destructive'>{formErrors.file}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='additionalInfo'>
              Additional Information (Optional)
            </Label>
            <Textarea
              id='additionalInfo'
              value={additionalInfo}
              onChange={e => setAdditionalInfo(e.target.value)}
              placeholder='Provide any additional context or information that might be helpful'
              rows={4}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button type='submit' disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : 'Submit Evidence'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
