'use client';

import { useEffect, useState } from 'react';
import {
  getUserOnboardingStatus,
  OnboardingStep,
  getOnboardingAnswers,
  OnboardingAnswers,
  ChildStatus,
  EligibilityResponse,
} from '@/app/utils/userOnboarding';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle2, Circle, ArrowRight } from 'lucide-react';
import { useAuth } from '@/app/context/AuthContext';

interface OnboardingStatusProps {
  onContinue?: () => void;
}

export default function OnboardingStatus({
  onContinue,
}: OnboardingStatusProps) {
  const { userExternalId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState<OnboardingStep | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState('');
  const [answers, setAnswers] = useState<OnboardingAnswers | null>(null);

  useEffect(() => {
    async function fetchOnboardingStatus() {
      if (!userExternalId) {
        console.warn('No userExternalId available for onboarding status');
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      try {
        const status = await getUserOnboardingStatus(userExternalId);
        setCurrentStep(status.currentStep as OnboardingStep);
        setIsComplete(status.isComplete as boolean);

        // Get saved answers
        const savedAnswers = await getOnboardingAnswers(userExternalId);
        setAnswers(savedAnswers);
      } catch (err) {
        console.error('Error fetching onboarding status:', err);
        setError('Failed to load onboarding status');
      } finally {
        setLoading(false);
      }
    }

    fetchOnboardingStatus();
  }, [userExternalId]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading your profile...</CardTitle>
          <CardDescription>
            Please wait while we load your information
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Something went wrong</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (isComplete) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Welcome back!</CardTitle>
          <CardDescription>
            Your profile is complete and ready to go.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const steps = [
    { id: OnboardingStep.CHILD_STATUS, label: 'Are you Childfree?' },
    {
      id: OnboardingStep.ELIGIBILITY,
      label: 'Eligibility Questions',
    },
    { id: OnboardingStep.COMPLETED, label: 'All Set!' },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Complete Your Profile</CardTitle>
        <CardDescription>
          {currentStepIndex < 0 || currentStepIndex >= steps.length - 1
            ? 'Just a few more steps to complete your profile'
            : `Next: ${steps[currentStepIndex + 1]?.label}`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div className='space-y-2'>
            {steps.map((step, index) => (
              <div key={step.id} className='flex items-center gap-3'>
                {index < currentStepIndex ? (
                  <CheckCircle2 className='h-5 w-5 text-primary' />
                ) : index === currentStepIndex ? (
                  <Circle className='h-5 w-5 text-primary fill-primary' />
                ) : (
                  <Circle className='h-5 w-5 text-muted-foreground' />
                )}
                <span
                  className={
                    index <= currentStepIndex
                      ? 'font-medium'
                      : 'text-muted-foreground'
                  }
                >
                  {step.label}
                </span>
              </div>
            ))}
          </div>

          {/* Display saved answers if available */}
          {answers && (
            <div className='mt-6 space-y-4 border-t pt-4'>
              <h4 className='text-sm font-medium'>Your Answers:</h4>

              {answers.childStatus && (
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>
                    Are you Childfree?
                  </span>
                  <span className='font-medium'>
                    {answers.childStatus === ChildStatus.YES
                      ? 'Yes, I am Childfree'
                      : answers.childStatus === ChildStatus.NO
                        ? 'No, I have children'
                        : answers.childStatus}
                  </span>
                </div>
              )}

              {answers.eligibility && (
                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>US Resident:</span>
                    <span className='font-medium'>
                      {answers.eligibility.usResident ===
                      EligibilityResponse.YES
                        ? 'Yes'
                        : 'No'}
                    </span>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>
                      Medical Situation:
                    </span>
                    <span className='font-medium'>
                      {answers.eligibility.medicalSituation ===
                      EligibilityResponse.YES
                        ? 'Yes'
                        : 'No'}
                    </span>
                  </div>
                  <div className='flex justify-between text-sm'>
                    <span className='text-muted-foreground'>
                      Complex Financial:
                    </span>
                    <span className='font-medium'>
                      {answers.eligibility.complexFinancial ===
                      EligibilityResponse.YES
                        ? 'Yes'
                        : 'No'}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {onContinue && (
            <Button className='w-full mt-4' onClick={onContinue}>
              Continue <ArrowRight className='ml-2 h-4 w-4' />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
