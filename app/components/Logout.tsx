'use client';

import { useRouter } from 'next/navigation';
import React from 'react';
import { Button } from '@/components/ui/button';
import routes from '@/utils/routes';
import { useAuth } from '@/app/context/AuthContext';

const Logout = () => {
  const router = useRouter();
  const { logout } = useAuth();

  return (
    <Button
      onClick={async () => {
        await logout();
        router.push(routes.login);
      }}
    >
      Sign out
    </Button>
  );
};

export default Logout;
