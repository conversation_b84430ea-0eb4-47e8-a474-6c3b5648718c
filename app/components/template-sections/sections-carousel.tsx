'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Plus,
  Search,
  ChevronLeft,
  ChevronRight,
  FileText,
  Copy,
  Edit,
  Trash2,
  Archive,
} from 'lucide-react';
import { uploadData, getUrl, remove } from 'aws-amplify/storage';
import { getCurrentUser, fetchAuthSession } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { toast } from 'sonner';
import {
  TipTapEditorWithVariables,
  Variable,
} from '@/components/ui/tiptap-editor';
import { useQuery } from '@tanstack/react-query';
import { loadInterviewProgressV2 } from '@/app/utils/interviewV2Progress';
import {
  createVariablesFromInterviewV2,
  StepsData,
} from '@/lib/utils/interviewV2Variables';

const client = generateClient<Schema>();

// FUNCTION FOR LOAD VARIABLES FROM INTERVIEW V2
const getInterviewV2Variables = async () => {
  try {
    const progressData = await loadInterviewProgressV2();
    const stepsData = progressData.stepsData as StepsData;
    return createVariablesFromInterviewV2();
  } catch (error) {
    console.error('Error loading InterviewV2 variables:', error);
    return [];
  }
};

interface Section {
  id: string;
  name: string;
  content: string;
  s3Key?: string | null;
  createdAt: string;
  isArchived?: boolean;
  currentVersion?: number;
}

interface SectionsCarouselProps {
  templateId: string;
  onSectionSelect?: (content: string) => void;
}

export const SectionsCarousel: React.FC<SectionsCarouselProps> = ({
  templateId,
  onSectionSelect,
}) => {
  const [sections, setSections] = useState<Section[]>([]);
  const [filteredSections, setFilteredSections] = useState<Section[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [newSectionName, setNewSectionName] = useState('');
  const [newSectionContent, setNewSectionContent] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [editingSection, setEditingSection] = useState<Section | null>(null);
  const [activeTab, setActiveTab] = useState<'active' | 'archived'>('active');

  // Get InterviewV2 variables
  const { data: interviewV2Variables = [] } = useQuery({
    queryKey: ['interview-v2-variables'],
    queryFn: getInterviewV2Variables,
  });

  // Load sections on mount
  useEffect(() => {
    loadSections();
  }, [templateId]);

  // Filter sections based on search term and active tab
  useEffect(() => {
    let filteredByTab = sections.filter(section =>
      activeTab === 'active' ? !section.isArchived : section.isArchived
    );

    if (searchTerm.trim()) {
      filteredByTab = filteredByTab.filter(section =>
        section.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSections(filteredByTab);
    setCurrentIndex(0);
  }, [sections, searchTerm, activeTab]);

  const loadSections = async () => {
    try {
      setIsLoading(true);
      const { data: templateSections, errors } =
        await client.models.TemplateSection.list({
          filter: {
            templateId: { eq: templateId },
          },
        });

      if (errors) {
        console.error('Error loading sections:', errors);
        return;
      }

      const sectionsWithContent = await Promise.all(
        (templateSections || [])
          .filter(section => section !== null)
          .map(async section => {
            let content = '';

            // Get latest version content
            const { data: versions } = await client.models.SectionVersion.list({
              filter: { sectionId: { eq: section.id } },
            });

            const latestVersion = versions
              ?.filter(v => v !== null)
              .sort((a, b) => b.versionNumber - a.versionNumber)[0];

            if (latestVersion) {
              if (
                latestVersion.contentType === 'inline' &&
                latestVersion.content
              ) {
                content = latestVersion.content;
              } else if (latestVersion.s3ContentKey) {
                try {
                  const result = await getUrl({
                    path: latestVersion.s3ContentKey,
                  });
                  const response = await fetch(result.url.toString());
                  content = await response.text();
                } catch (error) {
                  console.error('Error loading content from S3:', error);
                  content = 'Error loading content';
                }
              }
            }

            return {
              id: section.id,
              name: section.name,
              content,
              s3Key: latestVersion?.s3ContentKey,
              createdAt: section.createdAt,
              isArchived: section.isActive === false,
              currentVersion: latestVersion?.versionNumber || 1,
            };
          })
      );

      setSections(sectionsWithContent);
    } catch (error) {
      console.error('Error loading sections:', error);
      toast.error('Failed to load sections');
    } finally {
      setIsLoading(false);
    }
  };

  const createSection = async () => {
    if (editingSection) {
      await updateSection();
      return;
    }

    if (!newSectionName.trim() || !newSectionContent.trim()) {
      toast.error('Name and content are required');
      return;
    }

    try {
      const user = await getCurrentUser();
      const now = new Date().toISOString();
      const sectionId = crypto.randomUUID();

      // Create section in database
      const { data: section, errors: sectionErrors } =
        await client.models.TemplateSection.create({
          id: sectionId,
          templateId,
          name: newSectionName,
          description: '',
          sectionType: 'text',
          orderIndex: sections.length,
          isRequired: false,
          conditionalLogic: null,
          createdBy: user.userId,
          createdAt: now,
          updatedAt: now,
          isActive: true,
        });

      if (sectionErrors || !section) {
        throw new Error('Failed to create section');
      }

      // Save content to S3
      const s3Key = `public/template-sections/${sectionId}/${Date.now()}.html`;
      await uploadData({
        path: s3Key,
        data: new Blob([newSectionContent], { type: 'text/html' }),
        options: {
          contentType: 'text/html',
          metadata: {
            sectionId,
            templateId,
            uploadedBy: user.userId,
          },
        },
      }).result;

      // Create version record
      await client.models.SectionVersion.create({
        sectionId,
        versionNumber: 1,
        content: '',
        s3ContentKey: s3Key,
        contentType: 's3_encrypted',
        createdAt: now,
        createdBy: user.userId,
      });

      toast.success('Section created successfully');

      // Reset form and reload
      setNewSectionName('');
      setNewSectionContent('');
      setIsCreating(false);
      await loadSections();
    } catch (error) {
      console.error('Error creating section:', error);
      toast.error('Failed to create section');
    }
  };

  const stripHtmlTags = (html: string): string => {
    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get text content and clean up extra whitespace
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  const handleSectionSelect = (section: Section, event?: React.MouseEvent) => {
    event?.stopPropagation();
    try {
      // Insert HTML content directly into the template editor
      onSectionSelect?.(section.content);
      toast.success(`Section "${section.name}" inserted into template`);
    } catch (error) {
      console.error('Failed to insert section:', error);
      toast.error('Failed to insert section into template');
    }
  };

  const handleCopyToClipboard = async (
    section: Section,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    try {
      // Strip HTML tags and get clean text
      const cleanText = stripHtmlTags(section.content);
      await navigator.clipboard.writeText(cleanText);
      toast.success(`Section "${section.name}" copied to clipboard`);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleEditSection = (section: Section, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingSection(section);
    setNewSectionName(section.name);
    setNewSectionContent(section.content);
    setIsCreating(true);
  };

  const handleArchiveSection = async (
    section: Section,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    const action = section.isArchived ? 'unarchive' : 'archive';
    if (
      !confirm(`Are you sure you want to ${action} section "${section.name}"?`)
    ) {
      return;
    }

    try {
      await client.models.TemplateSection.update({
        id: section.id,
        isActive: section.isArchived, // If archived (true), make active (true). If active (false), make archived (false)
        updatedAt: new Date().toISOString(),
      });

      toast.success(`Section "${section.name}" ${action}d successfully`);
      await loadSections();
    } catch (error) {
      console.error(`Error ${action}ing section:`, error);
      toast.error(`Failed to ${action} section`);
    }
  };

  const handleDeleteSection = async (
    section: Section,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    if (
      !confirm(
        `Are you sure you want to permanently delete section "${section.name}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      // Check if user is authenticated
      const currentUser = await getCurrentUser();
      console.log('Delete operation - Current user:', currentUser);

      // Get all versions to delete S3 files
      const { data: versions } = await client.models.SectionVersion.list({
        filter: { sectionId: { eq: section.id } },
      });

      // Delete all S3 files for this section
      if (versions) {
        for (const version of versions.filter(v => v !== null)) {
          if (version.s3ContentKey) {
            try {
              // Delete file from S3 using Amplify Storage
              console.log(
                `Attempting to delete S3 file: ${version.s3ContentKey}`
              );

              await remove({ path: version.s3ContentKey });
              console.log(
                `Successfully deleted S3 file: ${version.s3ContentKey}`
              );
            } catch (s3Error) {
              console.error('Error deleting S3 file:', s3Error);
              console.error(
                'S3 Error details:',
                JSON.stringify(s3Error, null, 2)
              );
              // Continue with deletion even if S3 deletion fails
            }
          }
        }
      }

      // Delete all versions
      if (versions) {
        for (const version of versions.filter(v => v !== null)) {
          await client.models.SectionVersion.delete({
            sectionId: version.sectionId,
            versionNumber: version.versionNumber,
          });
        }
      }

      // Delete the section
      await client.models.TemplateSection.delete({ id: section.id });

      toast.success(`Section "${section.name}" permanently deleted`);
      await loadSections();
    } catch (error) {
      console.error('Error deleting section:', error);
      toast.error('Failed to delete section');
    }
  };

  const updateSection = async () => {
    if (
      !editingSection ||
      !newSectionName.trim() ||
      !newSectionContent.trim()
    ) {
      toast.error('Name and content are required');
      return;
    }

    try {
      const user = await getCurrentUser();
      const now = new Date().toISOString();

      // Update section name
      await client.models.TemplateSection.update({
        id: editingSection.id,
        name: newSectionName,
        updatedAt: now,
      });

      // Get current latest version number
      const { data: versions } = await client.models.SectionVersion.list({
        filter: { sectionId: { eq: editingSection.id } },
      });

      const latestVersionNumber =
        versions
          ?.filter(v => v !== null)
          .reduce((max, v) => Math.max(max, v.versionNumber), 0) || 0;

      const newVersionNumber = latestVersionNumber + 1;

      // Save new content to S3
      const s3Key = `public/template-sections/${editingSection.id}/${Date.now()}.html`;
      await uploadData({
        path: s3Key,
        data: new Blob([newSectionContent], { type: 'text/html' }),
        options: {
          contentType: 'text/html',
          metadata: {
            sectionId: editingSection.id,
            templateId,
            uploadedBy: user.userId,
            version: newVersionNumber.toString(),
          },
        },
      }).result;

      // Create new version record
      await client.models.SectionVersion.create({
        sectionId: editingSection.id,
        versionNumber: newVersionNumber,
        content: '',
        s3ContentKey: s3Key,
        contentType: 's3_encrypted',
        createdAt: now,
        createdBy: user.userId,
      });

      toast.success('Section updated successfully');

      // Reset form and reload
      setEditingSection(null);
      setNewSectionName('');
      setNewSectionContent('');
      setIsCreating(false);
      await loadSections();
    } catch (error) {
      console.error('Error updating section:', error);
      toast.error('Failed to update section');
    }
  };

  const nextSection = () => {
    if (currentIndex < filteredSections.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const prevSection = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const visibleSections = filteredSections.slice(
    currentIndex,
    currentIndex + 3
  );

  if (isLoading) {
    return (
      <Card className='mb-6'>
        <CardContent className='p-4'>
          <div className='flex items-center justify-center'>
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
            <span className='ml-2 text-sm'>Loading sections...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='mb-6'>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-lg'>Template Sections</CardTitle>
          <Dialog open={isCreating} onOpenChange={setIsCreating}>
            <DialogTrigger asChild>
              <Button size='sm' className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Create Section
              </Button>
            </DialogTrigger>
            <DialogContent className='!w-[58vw] !min-w-[58vw] !max-w-[98vw] max-h-[95vh] overflow-y-auto p-6'>
              <DialogHeader>
                <DialogTitle>
                  {editingSection ? 'Edit Section' : 'Create New Section'}
                </DialogTitle>
              </DialogHeader>
              <div className='space-y-4'>
                <div>
                  <label className='text-sm font-medium mb-2 block'>
                    Section Name
                  </label>
                  <Input
                    value={newSectionName}
                    onChange={e => setNewSectionName(e.target.value)}
                    placeholder='Enter section name'
                  />
                </div>
                <div>
                  <label className='text-sm font-medium mb-2 block'>
                    Section Content
                  </label>
                  <div className='border rounded-md'>
                    <TipTapEditorWithVariables
                      content={newSectionContent}
                      onChange={setNewSectionContent}
                      placeholder='Enter section content with variables and conditional logic...'
                      variables={interviewV2Variables}
                      showVariables={true}
                    />
                  </div>
                  <p className='text-xs text-gray-500 mt-1'>
                    Use the Variables panel to insert placeholders and
                    conditional logic
                  </p>
                </div>
                <div className='flex gap-2'>
                  <Button onClick={createSection}>
                    {editingSection ? 'Update Section' : 'Create Section'}
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => {
                      setIsCreating(false);
                      setEditingSection(null);
                      setNewSectionName('');
                      setNewSectionContent('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search */}
        <div className='flex items-center gap-2 mb-4'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
            <Input
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              placeholder='Search sections...'
              className='pl-10'
            />
          </div>
        </div>

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={value => setActiveTab(value as 'active' | 'archived')}
          className='mb-4'
        >
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='active'>Active Sections</TabsTrigger>
            <TabsTrigger value='archived'>Archived Sections</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Carousel */}
        {filteredSections.length === 0 ? (
          <div className='text-center py-8 text-gray-500'>
            <FileText className='h-8 w-8 mx-auto mb-2 text-gray-400' />
            <p>No sections found</p>
            <p className='text-sm'>Create your first section to get started</p>
          </div>
        ) : (
          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={prevSection}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>

            <div className='flex-1 grid grid-cols-1 md:grid-cols-3 gap-3'>
              {visibleSections.map(section => (
                <Card
                  key={section.id}
                  className={`hover:shadow-md transition-shadow ${
                    section.isArchived ? 'opacity-60 bg-gray-50' : ''
                  }`}
                >
                  <CardContent className='p-3'>
                    <div className='flex items-start justify-between mb-2'>
                      <h4 className='font-medium text-sm truncate'>
                        {section.name}
                      </h4>
                      <div className='flex gap-1'>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={e => handleSectionSelect(section, e)}
                          className='h-6 w-6 p-0'
                          title='Insert into template'
                        >
                          <Copy className='h-3 w-3' />
                        </Button>
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={e => handleCopyToClipboard(section, e)}
                          className='h-6 w-6 p-0'
                          title='Copy to clipboard'
                        >
                          <FileText className='h-3 w-3' />
                        </Button>
                      </div>
                    </div>
                    <div className='text-xs text-gray-600 line-clamp-3 font-mono mb-2'>
                      {section.content.substring(0, 100)}...
                    </div>
                    <div className='flex items-center justify-between mb-2'>
                      <div className='flex gap-2'>
                        <Badge variant='outline' className='text-xs'>
                          {new Date(section.createdAt).toLocaleDateString()}
                        </Badge>
                        <Badge variant='secondary' className='text-xs'>
                          v{section.currentVersion}
                        </Badge>
                      </div>
                      <div className='flex gap-1'>
                        {/* Edit button - only for active sections */}
                        {!section.isArchived && (
                          <Button
                            size='sm'
                            variant='ghost'
                            onClick={e => handleEditSection(section, e)}
                            className='h-5 w-5 p-0'
                            title='Edit section'
                          >
                            <Edit className='h-3 w-3' />
                          </Button>
                        )}

                        {/* Archive/Unarchive button - always visible */}
                        <Button
                          size='sm'
                          variant='ghost'
                          onClick={e => handleArchiveSection(section, e)}
                          className='h-5 w-5 p-0 text-orange-500 hover:text-orange-700'
                          title={
                            section.isArchived
                              ? 'Unarchive section'
                              : 'Archive section'
                          }
                        >
                          <Archive className='h-3 w-3' />
                        </Button>

                        {/* Delete button - only for archived sections */}
                        {section.isArchived && (
                          <Button
                            size='sm'
                            variant='ghost'
                            onClick={e => handleDeleteSection(section, e)}
                            className='h-5 w-5 p-0 text-red-500 hover:text-red-700'
                            title='Permanently delete section'
                          >
                            <Trash2 className='h-3 w-3' />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Button
              variant='outline'
              size='sm'
              onClick={nextSection}
              disabled={currentIndex >= filteredSections.length - 3}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        )}

        {filteredSections.length > 0 && (
          <div className='text-center mt-3 text-xs text-gray-500'>
            Showing {currentIndex + 1}-
            {Math.min(currentIndex + 3, filteredSections.length)} of{' '}
            {filteredSections.length} sections
          </div>
        )}
      </CardContent>
    </Card>
  );
};
