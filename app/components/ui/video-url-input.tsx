/**
 * Video URL Input Component
 *
 * A specialized input component for video URLs with real-time validation
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '../../../components/ui/alert';

import { Loader2, CheckCircle, AlertCircle, Eye, EyeOff } from 'lucide-react';
import {
  validateVideoUrl,
  validateVideoUrlAsync,
  VideoValidationResult,
} from '@/utils/video-utils';
import { cn } from '@/lib/utils';

interface VideoUrlInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (result: VideoValidationResult) => void;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showPreview?: boolean;
  autoValidate?: boolean;
}

export function VideoUrlInput({
  value,
  onChange,
  onValidationChange,
  label = 'Video URL',
  placeholder = 'https://youtube.com/watch?v=...',
  required = false,
  disabled = false,
  className,
  showPreview = true,
  autoValidate = true,
}: VideoUrlInputProps) {
  const [validationResult, setValidationResult] =
    useState<VideoValidationResult>({ isValid: true });
  const [isValidating, setIsValidating] = useState(false);
  const [showValidation, setShowValidation] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);

  // Debounced validation
  const validateUrl = useCallback(
    async (url: string) => {
      if (!autoValidate || !url.trim()) {
        const emptyResult = { isValid: true };
        setValidationResult(emptyResult);
        onValidationChange?.(emptyResult);
        setShowValidation(false);
        return;
      }

      setIsValidating(true);
      setShowValidation(true);

      try {
        // First do quick sync validation
        const syncResult = validateVideoUrl(url);
        setValidationResult(syncResult);
        onValidationChange?.(syncResult);

        // If sync validation passes, do async validation
        if (syncResult.isValid) {
          const asyncResult = await validateVideoUrlAsync(url);
          setValidationResult(asyncResult);
          onValidationChange?.(asyncResult);
        }
      } catch (error) {
        const errorResult = {
          isValid: false,
          error: 'Validation failed due to network error',
        };
        setValidationResult(errorResult);
        onValidationChange?.(errorResult);
      } finally {
        setIsValidating(false);
      }
    },
    [autoValidate, onValidationChange]
  );

  // Debounce validation
  useEffect(() => {
    const timer = setTimeout(() => {
      validateUrl(value);
    }, 500);

    return () => clearTimeout(timer);
  }, [value, validateUrl]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Reset validation state when user starts typing
    if (newValue !== value) {
      setShowValidation(false);
      setIsValidating(false);
    }
  };

  const handleBlur = () => {
    if (value.trim()) {
      validateUrl(value);
    }
  };

  const getValidationIcon = () => {
    if (isValidating) {
      return <Loader2 className='h-4 w-4 animate-spin text-gray-400' />;
    }

    if (!showValidation || !value.trim()) {
      return null;
    }

    // Show error icon if there's an error OR if there's a warning (accessibility issues)
    if (!validationResult.isValid || validationResult.warning) {
      return <AlertCircle className='h-4 w-4 text-red-500' />;
    }

    // Only show green checkmark if validation is successful AND no warnings
    if (validationResult.isValid && !validationResult.warning) {
      return <CheckCircle className='h-4 w-4 text-green-500' />;
    }

    return null;
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label and Preview Button */}
      <div className='flex items-center justify-between'>
        <Label htmlFor='video-url-input'>
          {label}
          {required && <span className='text-red-500 ml-1'>*</span>}
        </Label>
        <div className='flex items-center gap-2'>
          {showPreview &&
            validationResult.isValid &&
            validationResult.videoInfo?.isEmbeddable && (
              <Button
                type='button'
                variant='ghost'
                size='sm'
                onClick={() => setPreviewVisible(!previewVisible)}
                className='h-6 px-2 text-xs'
              >
                {previewVisible ? (
                  <>
                    <EyeOff className='h-3 w-3 mr-1' />
                    Hide
                  </>
                ) : (
                  <>
                    <Eye className='h-3 w-3 mr-1' />
                    Preview
                  </>
                )}
              </Button>
            )}
        </div>
      </div>

      {/* Input Field */}
      <div className='relative'>
        <Input
          id='video-url-input'
          type='url'
          value={value}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'pr-10',
            showValidation &&
              (!validationResult.isValid || validationResult.warning) &&
              'border-red-500 focus:border-red-500',
            showValidation &&
              validationResult.isValid &&
              !validationResult.warning &&
              'border-green-500 focus:border-green-500'
          )}
        />

        {/* Validation Icon */}
        <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
          {getValidationIcon()}
        </div>
      </div>

      {/* Validation Messages */}
      {showValidation &&
        (validationResult.error || validationResult.warning) && (
          <Alert
            className={cn(
              validationResult.error
                ? 'border-red-200 bg-red-50'
                : 'border-yellow-200 bg-yellow-50'
            )}
          >
            <AlertCircle
              className={cn(
                'h-4 w-4',
                validationResult.error ? 'text-red-500' : 'text-yellow-500'
              )}
            />
            <AlertDescription
              className={cn(
                validationResult.error ? 'text-red-700' : 'text-yellow-700'
              )}
            >
              {validationResult.error || validationResult.warning}
            </AlertDescription>
          </Alert>
        )}

      {/* Video Preview */}
      {previewVisible &&
        validationResult.isValid &&
        validationResult.videoInfo?.isEmbeddable && (
          <div className='mt-4 p-4 border rounded-lg bg-gray-50'>
            <div className='aspect-video bg-black rounded overflow-hidden relative'>
              {validationResult.platform === 'youtube' &&
              validationResult.videoInfo?.embedUrl ? (
                <iframe
                  src={validationResult.videoInfo.embedUrl}
                  className='w-full h-full border-0'
                  allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
                  allowFullScreen
                  title='YouTube Video Preview'
                />
              ) : validationResult.platform === 'vimeo' &&
                validationResult.videoInfo?.embedUrl ? (
                <iframe
                  src={validationResult.videoInfo.embedUrl}
                  className='w-full h-full border-0'
                  allow='autoplay; fullscreen; picture-in-picture'
                  allowFullScreen
                  title='Vimeo Video Preview'
                />
              ) : (
                <div className='flex items-center justify-center h-full text-white'>
                  <div className='text-center'>
                    <div className='text-sm font-medium mb-2'>
                      Video Preview
                    </div>
                    <div className='text-xs mb-3'>
                      {validationResult.platform === 'youtube' &&
                        'YouTube Video'}
                      {validationResult.platform === 'vimeo' && 'Vimeo Video'}
                      {validationResult.platform === 'other' && 'Video Content'}
                    </div>
                    <a
                      href={value}
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-blue-300 hover:text-blue-100 underline text-xs'
                    >
                      Open in new tab →
                    </a>
                  </div>
                </div>
              )}
            </div>
            <p className='text-xs text-gray-600 mt-2'>
              {validationResult.platform === 'youtube' ||
              validationResult.platform === 'vimeo'
                ? "Video preview - if it doesn't load, click the link above to view in new tab"
                : 'Preview not available for this platform. Click link above to view video.'}
            </p>
          </div>
        )}
    </div>
  );
}
