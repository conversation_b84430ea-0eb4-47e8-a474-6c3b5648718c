'use client';

import React, { useState } from 'react';
import { HelpCircle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface TooltipInfoProps {
  content: string;
  className?: string;
}

export function TooltipInfo({ content, className = '' }: TooltipInfoProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            type='button'
            className={`inline-flex items-center justify-center w-4 h-4 text-muted-foreground hover:text-foreground transition-colors ${className}`}
          >
            <HelpCircle className='h-4 w-4' />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p className='max-w-xs text-sm'>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default TooltipInfo;
