'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
// import { Checkbox } from '@/components/ui/checkbox';
import { Headline, Subhead } from '@/components/ui/brand/typography';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import type { EmergencyContact } from '@/hooks/useEmergencyContacts';
import { PeopleLibrarySelector } from './PeopleLibrarySelector';
import type { Person } from '@/hooks/usePeopleLibrary';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipProvider,
//   TooltipTrigger,
// } from '@/components/ui/tooltip';
// import { Info } from 'lucide-react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';

const STORAGE_KEY = 'emergency-contact-form-data';

// Define the form schema to match Omit<EmergencyContact, 'id'>
// Simplified version - commented out complex fields
const contactFormSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  relationship: z.string().min(1, 'Relationship is required'),
  phoneNumber: z
    .string()
    .length(13, 'Phone number must be exactly 13 characters')
    .refine(val => /^\(\d{3}\)\d{3}-\d{4}$/.test(val), {
      message: 'Phone number must be in format (222)333-4444',
    }),
  emailAddress: z.string().email('Please enter a valid email address'),
  // COMMENTED OUT - Document access functionality removed
  // contactType: z.enum(['NotificationOnly', 'NotificationWithDocumentAccess']),
  // documentType: z
  //   .enum(['DeadDocument', 'CareDocument', 'All'])
  //   .refine(val => !!val, {
  //     message: 'Document type access is required',
  //   }),
  // isPrimaryForType: z.boolean().optional(),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

interface ContactFormProps {
  initialData?: Omit<EmergencyContact, 'id'>;
  onSubmit: (data: Omit<EmergencyContact, 'id'>) => Promise<void>;
  onChange?: (data: Omit<EmergencyContact, 'id'>) => void;
  isEditing: boolean;
  isLoading?: boolean;
  storageKey?: string;
}

export function ContactForm({
  initialData,
  onSubmit,
  onChange,
  isEditing,
  isLoading = false,
  storageKey,
}: ContactFormProps) {
  const router = useRouter();

  // Try to load saved data from localStorage first
  const loadSavedData = () => {
    if (typeof window !== 'undefined' && !isEditing) {
      try {
        const key = storageKey || STORAGE_KEY;
        const savedData = localStorage.getItem(key);
        if (savedData) {
          return JSON.parse(savedData);
        }
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
    return initialData;
  };

  const savedData = loadSavedData();

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      fullName: savedData?.fullName || initialData?.fullName || '',
      relationship: savedData?.relationship || initialData?.relationship || '',
      phoneNumber: savedData?.phoneNumber || initialData?.phoneNumber || '',
      emailAddress: savedData?.emailAddress || initialData?.emailAddress || '',
      // COMMENTED OUT - Document access defaults
      // contactType:
      //   savedData?.contactType ||
      //   initialData?.contactType ||
      //   'NotificationOnly',
      // documentType:
      //   savedData?.documentType || initialData?.documentType || 'All',
      // isPrimaryForType:
      //   savedData?.isPrimaryForType || initialData?.isPrimaryForType || false,
    },
  });

  // This function directly saves to localStorage
  const saveToLocalStorage = (data: ContactFormValues) => {
    if (typeof window !== 'undefined' && storageKey) {
      localStorage.setItem(storageKey, JSON.stringify(data));
      console.log(`Saved to localStorage (${storageKey}):`, data);

      if (onChange) {
        // Convert simplified form data to full EmergencyContact format
        const fullContactData: Omit<EmergencyContact, 'id'> = {
          ...data,
          contactType: 'NotificationOnly', // Always notification only
          documentType: 'All', // Default value (not used but required by type)
          isPrimaryForType: false, // Always false since we don't use primary contacts
        };
        onChange(fullContactData);
      }
    }
  };

  // Watch for all form changes
  const formValues = form.watch();

  // Save form data whenever any value changes
  useEffect(() => {
    saveToLocalStorage(formValues);
  }, [formValues]);

  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.length === 0) return '';
    if (digits.length <= 3) return `(${digits}`;
    if (digits.length <= 6) return `(${digits.slice(0, 3)})${digits.slice(3)}`;
    return `(${digits.slice(0, 3)})${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  };

  const handlePersonSelect = (person: Person) => {
    const personName =
      person.type === 'individual'
        ? `${person.firstName || ''} ${person.lastName || ''}`.trim()
        : person.entityName || '';

    form.setValue('fullName', personName);

    if (person.phoneNumber) {
      const formattedPhone = person.phoneNumber.includes('(')
        ? person.phoneNumber
        : formatPhoneNumber(person.phoneNumber);
      form.setValue('phoneNumber', formattedPhone);
    }

    toast.success(`Contact information filled from ${personName}`);
  };

  const handleSubmit = async (data: ContactFormValues) => {
    try {
      // Convert form data to full EmergencyContact format
      const fullContactData: Omit<EmergencyContact, 'id'> = {
        ...data,
        contactType: 'NotificationOnly', // Always notification only
        documentType: 'All', // Default value (not used but required by type)
        isPrimaryForType: false, // Always false since we don't use primary contacts
      };

      await onSubmit(fullContactData);
      // Clear localStorage on successful submission
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEY);
      }
      toast.success(
        `${data.fullName} has been ${isEditing ? 'updated' : 'added'}.`
      );
      router.push('/member/emergency-contacts');
    } catch (error: any) {
      toast.error(
        error.message ||
          `Failed to ${isEditing ? 'update' : 'add'} contact. Please try again.`
      );
      console.error(
        `Error ${isEditing ? 'updating' : 'adding'} contact:`,
        error
      );
    }
  };

  if (isLoading) {
    return (
      <div className='max-w-3xl mx-auto text-center py-12'>
        <div className='animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4'></div>
        <p>Loading contact information...</p>
      </div>
    );
  }

  return (
    <div className='max-w-3xl mx-auto'>
      <Card className='mb-6 p-6'>
        <Link
          href='/member/emergency-contacts'
          className='flex items-center text-muted-foreground hover:text-foreground mb-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Emergency Contacts
        </Link>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          {isEditing ? 'Update' : 'Add'} Emergency Contact
        </h1>
        {!isEditing && (
          <p className='text-[var(--custom-gray-dark)] mt-2'>
            Add a trusted individual who will be notified in case of emergency
          </p>
        )}
      </Card>

      {/* People Library Selector - only show when adding new contact */}
      {!isEditing && (
        <PeopleLibrarySelector
          onPersonSelect={handlePersonSelect}
          className='mb-6'
        />
      )}

      <Card>
        <CardHeader>
          <CardTitle>{isEditing ? 'Edit' : 'Contact'} Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-6'
            >
              <div className='space-y-4'>
                {/* Full Name */}
                <FormField
                  control={form.control}
                  name='fullName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter contact's full name"
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Relationship */}
                <FormField
                  control={form.control}
                  name='relationship'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Relationship</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value || ''}
                          onValueChange={value => {
                            field.onChange(value);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        >
                          <SelectTrigger className='w-full'>
                            <SelectValue placeholder='Select relationship' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Spouse / Partner'>
                              Spouse / Partner
                            </SelectItem>
                            <SelectItem value='Parent'>Parent</SelectItem>
                            <SelectItem value='Sibling'>Sibling</SelectItem>
                            <SelectItem value='Other Family Member'>
                              Other Family Member
                            </SelectItem>
                            <SelectItem value='Friend'>Friend</SelectItem>
                            <SelectItem value='Neighbor'>Neighbor</SelectItem>
                            <SelectItem value='Caregiver'>Caregiver</SelectItem>
                            <SelectItem value='Attorney'>Attorney</SelectItem>
                            <SelectItem value='Financial Planner / Advisor'>
                              Financial Planner / Advisor
                            </SelectItem>
                            <SelectItem value='Healthcare Proxy / Agent'>
                              Healthcare Proxy / Agent
                            </SelectItem>
                            <SelectItem value='Other'>Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone Number */}
                <FormField
                  control={form.control}
                  name='phoneNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='(222)333-4444'
                          {...field}
                          onChange={e => {
                            const formatted = formatPhoneNumber(e.target.value);
                            field.onChange(formatted);
                            const currentValues = form.getValues();
                            saveToLocalStorage({
                              ...currentValues,
                              phoneNumber: formatted,
                            });
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Address */}
                <FormField
                  control={form.control}
                  name='emailAddress'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type='email'
                          placeholder='<EMAIL>'
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* COMMENTED OUT - Contact Type Selection */}
                {/* <FormField
                  control={form.control}
                  name='contactType'
                  render={({ field }) => (
                    <FormItem className='space-y-3'>
                      <FormLabel>Contact Type</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={value => {
                            field.onChange(value);
                            const currentValues = form.getValues();
                            saveToLocalStorage({
                              ...currentValues,
                              contactType: value as
                                | 'NotificationOnly'
                                | 'NotificationWithDocumentAccess',
                            });
                          }}
                          defaultValue={field.value}
                          className='flex flex-col space-y-1'
                        >
                          <FormItem className='flex items-center space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='NotificationOnly' />
                            </FormControl>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className='flex items-center space-x-1 cursor-help font-normal'>
                                    <FormLabel className='m-0 mr-1'>
                                      Notification Only
                                    </FormLabel>
                                    <Info
                                      size={18}
                                      className='text-muted-foreground'
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-xs text-base'>
                                  Your emergency contact will be alerted in a
                                  critical situation but will not be able to
                                  view or open any of your documents. This is
                                  useful if you simply want them to know
                                  something has happened without giving them
                                  access to sensitive information.
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormItem>

                          <FormItem className='flex items-center space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='NotificationWithDocumentAccess' />
                            </FormControl>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className='flex items-center space-x-1 cursor-help font-normal'>
                                    <FormLabel className='m-0 mr-1'>
                                      Notification With Document Access
                                    </FormLabel>
                                    <Info
                                      size={18}
                                      className='text-muted-foreground'
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className='max-w-xs text-base'>
                                  Your emergency contact will be alerted and
                                  will be able to view the specific documents
                                  you've given them permission to access. This
                                  is useful if they need to act on your behalf —
                                  for example, showing your medical directive to
                                  a hospital or using your pet care
                                  instructions.
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                /> */}

                {/* COMMENTED OUT - Document Type Access */}
                {/* {form.getValues('contactType') ===
                  'NotificationWithDocumentAccess' && (
                  <FormField
                    control={form.control}
                    name='documentType'
                    render={({ field }) => (
                      <FormItem className='space-y-3'>
                        <FormLabel>Document Type Access</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={value => {
                              field.onChange(value);
                              const currentValues = form.getValues();
                              saveToLocalStorage({
                                ...currentValues,
                                documentType: value as
                                  | 'DeadDocument'
                                  | 'CareDocument'
                                  | 'All',
                              });
                            }}
                            defaultValue={field.value}
                            className='flex flex-col space-y-1'
                          >
                            <FormItem className='flex items-center space-x-3 space-y-0'>
                              <FormControl>
                                <RadioGroupItem value='DeadDocument' />
                              </FormControl>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className='flex items-center space-x-1 cursor-help font-normal'>
                                      <FormLabel className='m-0 mr-1'>
                                        Dead Document Access Only
                                      </FormLabel>
                                      <Info
                                        size={18}
                                        className='text-muted-foreground'
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className='max-w-xs text-base'>
                                    Your contact can only view documents that
                                    take effect after your death, such as your
                                    will, trust, or final arrangements.
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </FormItem>

                            <FormItem className='flex items-center space-x-3 space-y-0'>
                              <FormControl>
                                <RadioGroupItem value='CareDocument' />
                              </FormControl>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className='flex items-center space-x-1 cursor-help font-normal'>
                                      <FormLabel className='m-0 mr-1'>
                                        Care Document Access Only
                                      </FormLabel>
                                      <Info
                                        size={18}
                                        className='text-muted-foreground'
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className='max-w-xs text-base'>
                                    Your contact can only view documents related
                                    to your ongoing care while you're alive —
                                    for example, medical directives, medication
                                    lists, or daily care instructions.
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </FormItem>

                            <FormItem className='flex items-center space-x-3 space-y-0'>
                              <FormControl>
                                <RadioGroupItem value='All' />
                              </FormControl>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className='flex items-center space-x-1 cursor-help font-normal'>
                                      <FormLabel className='m-0 mr-1'>
                                        All Documents Access
                                      </FormLabel>
                                      <Info
                                        size={18}
                                        className='text-muted-foreground'
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className='max-w-xs text-base'>
                                    Your contact can view both care documents
                                    and documents that take effect after your
                                    death.
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )} */}

                {/* COMMENTED OUT - Primary Checkbox */}
                {/* {form.getValues('contactType') ===
                  'NotificationWithDocumentAccess' && (
                  <FormField
                    control={form.control}
                    name='isPrimaryForType'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={checked => {
                              const boolValue = checked === true;
                              field.onChange(boolValue);

                              // Update localStorage with explicit boolean
                              const currentValues = form.getValues();
                              saveToLocalStorage({
                                ...currentValues,
                                isPrimaryForType: boolValue,
                              });
                            }}
                          />
                        </FormControl>
                        <div className='space-y-1 leading-none'>
                          <FormLabel>Set as primary contact</FormLabel>
                          <p className='text-sm text-muted-foreground'>
                            This contact will be prioritized for emergencies.
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />
                )} */}
              </div>

              <div className='flex justify-end space-x-4 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => router.push('/member/emergency-contacts')}
                >
                  Cancel
                </Button>
                <Button type='submit' disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting
                    ? isEditing
                      ? 'Updating...'
                      : 'Adding...'
                    : isEditing
                      ? 'Update Contact'
                      : 'Add Contact'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
