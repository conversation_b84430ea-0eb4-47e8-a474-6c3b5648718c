'use client';

import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, UserPlus, Settings } from 'lucide-react';
import { usePeopleLibrary, Person } from '@/hooks/usePeopleLibrary';
import PersonModal from '@/app/components/interview-v2/PersonModal';
import { toast } from 'sonner';
import Link from 'next/link';

interface PeopleLibrarySelectorProps {
  onPersonSelect: (person: Person) => void;
  className?: string;
}

export function PeopleLibrarySelector({
  onPersonSelect,
  className,
}: PeopleLibrarySelectorProps) {
  const [selectedPersonId, setSelectedPersonId] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { people, isLoading, addPerson, isUpdating } = usePeopleLibrary();

  // Filter only individuals for emergency contacts
  const individuals = people.filter(person => person.type === 'individual');

  const getPersonName = (person: Person) => {
    if (person.type === 'individual') {
      const firstName = person.firstName || '';
      const middleName = person.middleName || '';
      const lastName = person.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return person.entityName || 'Unknown';
  };

  const handlePersonSelect = (personId: string) => {
    if (personId === 'create-new') {
      setIsModalOpen(true);
      return;
    }

    const selectedPerson = people.find(p => p.id === personId);
    if (selectedPerson) {
      onPersonSelect(selectedPerson);
      setSelectedPersonId('');
    }
  };

  const handleAddPerson = async (person: Omit<Person, 'id'>): Promise<void> => {
    try {
      const newPerson = await addPerson(person);
      onPersonSelect(newPerson);
      setIsModalOpen(false);
      toast.success('Person added and selected');
    } catch (error) {
      toast.error('Failed to add person');
      console.error('Error adding person:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className='py-6'>
          <div className='flex items-center justify-center'>
            <span>Loading People Library...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (individuals.length === 0) {
    return (
      <Card className={className}>
        <CardHeader className='pb-3'>
          <CardTitle className='text-lg flex items-center gap-2'>
            <User className='h-5 w-5' />
            Select from People Library
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-center py-6 text-muted-foreground'>
            <User className='h-12 w-12 mx-auto mb-3 opacity-50' />
            <p className='text-sm mb-3'>No people in your library yet</p>
            <div className='flex flex-col gap-2'>
              <Button
                onClick={() => setIsModalOpen(true)}
                disabled={isUpdating}
                className='flex items-center gap-2'
              >
                {isUpdating ? (
                  <>Loading...</>
                ) : (
                  <>
                    <UserPlus className='h-4 w-4' />
                    Add First Person
                  </>
                )}
              </Button>
              <Button variant='outline' asChild>
                <Link
                  href='/member/interviewV2'
                  className='flex items-center gap-2'
                >
                  <Settings className='h-4 w-4' />
                  Manage People Library
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className='pb-3'>
          <CardTitle className='text-lg flex items-center gap-2'>
            <User className='h-5 w-5' />
            Select from People Library
          </CardTitle>
          <p className='text-sm text-muted-foreground'>
            Choose someone from your People Library to auto-fill contact
            information
          </p>
        </CardHeader>
        <CardContent className='space-y-4'>
          <Select value={selectedPersonId} onValueChange={handlePersonSelect}>
            <SelectTrigger>
              <SelectValue placeholder='Select a person from your library' />
            </SelectTrigger>
            <SelectContent>
              {individuals.map(person => (
                <SelectItem key={person.id} value={person.id}>
                  <div className='flex items-center gap-2'>
                    <User className='h-4 w-4' />
                    <span>{getPersonName(person)}</span>
                    {person.phoneNumber && (
                      <span className='text-xs text-muted-foreground'>
                        • {person.phoneNumber}
                      </span>
                    )}
                  </div>
                </SelectItem>
              ))}
              <SelectItem value='create-new'>
                <div className='flex items-center gap-2 text-primary'>
                  <UserPlus className='h-4 w-4' />
                  <span>Add New Person</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          <div className='flex justify-center'>
            <Button variant='outline' asChild>
              <Link
                href='/member/interviewV2'
                className='flex items-center gap-2'
              >
                <Settings className='h-4 w-4' />
                Manage People Library
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      <PersonModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleAddPerson}
        defaultType='individual'
        filterType='individual'
      />
    </>
  );
}
