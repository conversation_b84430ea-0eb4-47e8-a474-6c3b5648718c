import * as Sentry from '@sentry/nextjs';
import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../../amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

Amplify.configure(outputs, { ssr: true });

// Generate the client with IAM auth mode for guest access
const client = generateClient<Schema>({
  authMode: 'iam',
});

export async function POST(request: NextRequest) {
  console.log('🔔 WEBHOOK RECEIVED at:', new Date().toISOString());
  console.log('🔔 Request URL:', request.url);
  console.log('🔔 Request method:', request.method);

  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    console.log('📥 Webhook body length:', body.length);
    console.log('🔐 Stripe signature present:', !!signature);

    if (!signature) {
      console.error('❌ No signature provided');
      return NextResponse.json(
        { error: 'No signature provided' },
        { status: 400 }
      );
    }

    console.log('� Forwarding webhook to Lambda for processing...');

    try {
      // Call Lambda function to process the webhook (including signature validation)
      const result = await client.mutations.processWebhook({
        body: body,
        signature: signature,
      });

      console.log('✅ Lambda processing result:', result);
    } catch (lambdaError) {
      Sentry.captureException(lambdaError);
      console.error('❌ Lambda processing failed:', lambdaError);
      Sentry.captureException(lambdaError);

      // If Lambda fails due to invalid signature, return 400
      if (
        lambdaError instanceof Error &&
        lambdaError.message.includes('Invalid Stripe signature')
      ) {
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 400 }
        );
      }
    }

    console.log('✅ Webhook processed successfully');
    return NextResponse.json({ received: true });
  } catch (error) {
    Sentry.captureException(error);
    console.error('❌ Webhook processing error:', error);
    console.error(
      '❌ Error stack:',
      error instanceof Error ? error.stack : 'No stack'
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
