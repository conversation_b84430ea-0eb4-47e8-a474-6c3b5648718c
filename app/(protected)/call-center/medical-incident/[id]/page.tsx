'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  CheckCircle,
  AlertCircle,
  User,
  Building2,
  UserCheck,
  Send,
  Mail,
  RefreshCw,
  Search,
  Phone,
  MapPin,
} from 'lucide-react';
import { toast } from 'sonner';
import { useQuery } from '@tanstack/react-query';
import { callCenterAPI } from '@/lib/api/callcenter';
import { useMedicalIncidents } from '@/hooks/useMedicalIncidents';
import { useParams } from 'next/navigation';
import { useDebounce } from '@/hooks/useDebounce';
import { DatePicker } from '@/components/ui/date-picker';

interface IncidentData {
  id: string;
  userId: string;
  // Step 1: Reporting Individual
  contactFirstName: string;
  contactLastName: string;
  contactPhone: string;
  relationshipToClient: string;
  relationshipToClientOther: string;
  clientStatus: string;

  // Step 2: Hospital Information
  hospitalName: string;
  hospitalAddress: string;
  hospitalFax: string;
  hospitalEmail: string;
  hospitalConfirmed: boolean;

  // Step 3: Client Information
  clientFirstName: string;
  clientLastName: string;
  clientDateOfBirth: string;
  selectedClient: Client | null;

  // Meta
  createdAt: string;
  sendFYIEmails: boolean;
  sendHospitalEmail: boolean;
  clientDoesNotExist: boolean;
}

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  birthdate?: string | null;
  email: string;
  phoneNumber?: string | null;
  // address: string;
}

interface ValidationErrors {
  contactFirstName?: string;
  contactLastName?: string;
  contactPhone?: string;
  hospitalName?: string;
  hospitalAddress?: string;
  hospitalFax?: string;
  hospitalEmail?: string;
}

const relationshipOptions = [
  'Spouse',
  'Partner',
  'Parent',
  'Child',
  'Sibling',
  'Friend',
  'Healthcare Provider',
  'Legal Representative',
  'Emergency Contact',
  'Other',
];

type EmailSending = 'fyi' | 'hospital' | 'support';

export default function MedicalIncidentPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [incidentData, setIncidentData] = useState<IncidentData>({
    id: '',
    userId: '',
    contactFirstName: '',
    contactLastName: '',
    contactPhone: '',
    relationshipToClient: '',
    relationshipToClientOther: '',
    clientStatus: '',
    hospitalName: '',
    hospitalAddress: '',
    hospitalFax: '',
    hospitalEmail: '',
    hospitalConfirmed: false,
    clientFirstName: '',
    clientLastName: '',
    clientDateOfBirth: '',
    selectedClient: null,
    createdAt: new Date().toISOString(),
    sendFYIEmails: false,
    sendHospitalEmail: false,
    clientDoesNotExist: false,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );
  const [searchResults, setSearchResults] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showClientSearch, setShowClientSearch] = useState(false);
  const [emailSending, setEmailSending] = useState<EmailSending[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const params = useParams();
  const { id } = params;

  const { fetchIncident, updateIncident, sendEmails } = useMedicalIncidents();

  const {
    data: users = [],
    isRefetching,
    isFetching,
  } = useQuery({
    queryKey: ['callcenter', 'users'],
    queryFn: callCenterAPI.getUsersList,
  });

  const debounsedIncidentData = useDebounce(incidentData, 1000);

  // Validation functions
  const validateField = (
    field: keyof IncidentData,
    value: string
  ): string | undefined => {
    if (!value.trim()) return undefined;
    switch (field) {
      case 'contactFirstName':
        // if (!value.trim()) return 'First name is required';
        if (value.trim().length < 2)
          return 'First name must be at least 2 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(value))
          return 'First name can only contain letters, spaces, hyphens, and apostrophes';
        return undefined;

      case 'contactLastName':
        // if (!value.trim()) return 'Last name is required';
        if (value.trim().length < 2)
          return 'Last name must be at least 2 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(value))
          return 'Last name can only contain letters, spaces, hyphens, and apostrophes';
        return undefined;

      case 'contactPhone':
        // if (!value.trim()) return 'Phone number is required';
        const phoneRegex =
          /^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)]{10,}$/;
        const cleanPhone = value.replace(/[\s\-\(\)]/g, '');
        if (cleanPhone.length < 10)
          return 'Phone number must be at least 10 digits';
        if (!/^[\d\s\-\(\)\+]+$/.test(value))
          return 'Phone number contains invalid characters';
        return undefined;

      case 'hospitalName':
        // if (!value.trim()) return 'Hospital name is required';
        if (value.trim().length < 3)
          return 'Hospital name must be at least 3 characters';
        return undefined;

      case 'hospitalAddress':
        // if (!value.trim()) return 'Hospital address is required';
        if (value.trim().length < 10)
          return 'Please provide a complete address';
        return undefined;

      case 'hospitalFax':
        if (value.trim() && !/^[\d\s\-\(\)\+]+$/.test(value))
          return 'Fax number contains invalid characters';
        if (value.trim()) {
          const cleanFax = value.replace(/[\s\-\(\)]/g, '');
          if (cleanFax.length < 10)
            return 'Fax number must be at least 10 digits';
        }
        return undefined;

      case 'hospitalEmail':
        if (value.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))
          return 'Please enter a valid email address';
        return undefined;

      default:
        return undefined;
    }
  };

  const requiredFields = [
    'contactFirstName',
    'contactLastName',
    'contactPhone',
    'hospitalName',
    'hospitalAddress',
    'hospitalFax',
    'hospitalEmail',
  ] as const;

  const initialValidation = (incidentData: IncidentData) => {
    const errors: ValidationErrors = {};
    requiredFields.forEach(field => {
      if (!incidentData[field]) return;

      const error = validateField(field, incidentData[field]);
      if (error) {
        errors[field as keyof ValidationErrors] = error;
      }
    });
    setValidationErrors(errors);
  };

  useEffect(() => {
    const fetchIncidentData = async () => {
      if (!id) return;

      const incident = await fetchIncident(id as string);

      if (!incident) return;

      setIncidentData({
          id: incident.id,
          userId: incident.userId,
          contactFirstName: incident.contactFirstName || '',
          contactLastName: incident.contactLastName || '',
          contactPhone: incident.contactPhone || '',
          relationshipToClient: incident.relationshipToClient || '',
          relationshipToClientOther: incident.relationshipToClientOther || '',
          clientStatus: incident.clientStatus || '',
          hospitalName: incident.hospitalName || '',
          hospitalAddress: incident.hospitalAddress || '',
          hospitalFax: incident.hospitalFax || '',
          hospitalEmail: incident.hospitalEmail || '',
          hospitalConfirmed: incident.hospitalConfirmed || false,
          clientFirstName: incident.clientFirstName || '',
          clientLastName: incident.clientLastName || '',
          clientDateOfBirth: incident.clientDateOfBirth || '',
          selectedClient: incident.selectedClient || null,
          createdAt: incident.createdAt,
          sendFYIEmails: incident.sendFYIEmails || false,
          sendHospitalEmail: incident.sendHospitalEmail || false,
          clientDoesNotExist: incident.clientDoesNotExist || false,
      });

      initialValidation({
          id: incident.id,
          userId: incident.userId,
          contactFirstName: incident.contactFirstName || '',
          contactLastName: incident.contactLastName || '',
          contactPhone: incident.contactPhone || '',
          relationshipToClient: incident.relationshipToClient || '',
          relationshipToClientOther: incident.relationshipToClientOther || '',
          clientStatus: incident.clientStatus || '',
          hospitalName: incident.hospitalName || '',
          hospitalAddress: incident.hospitalAddress || '',
          hospitalFax: incident.hospitalFax || '',
          hospitalEmail: incident.hospitalEmail || '',
          hospitalConfirmed: incident.hospitalConfirmed || false,
          clientFirstName: incident.clientFirstName || '',
          clientLastName: incident.clientLastName || '',
          clientDateOfBirth: incident.clientDateOfBirth || '',
          selectedClient: incident.selectedClient || null,
          createdAt: incident.createdAt,
          sendFYIEmails: incident.sendFYIEmails || false,
          sendHospitalEmail: incident.sendHospitalEmail || false,
          clientDoesNotExist: incident.clientDoesNotExist || false,
      });

      setSelectedClient(incident.selectedClient || null);

    };

    fetchIncidentData();
  }, [fetchIncident, id]);

  useEffect(() => {
    if (!debounsedIncidentData.id) return;

    updateIncident({
      ...debounsedIncidentData,
      updatedAt: new Date().toISOString(),
    });
  }, [debounsedIncidentData, updateIncident]);

  useEffect(() => {
    if (isFetching || isRefetching) return;
    if (!incidentData.clientFirstName && !incidentData.clientLastName &&
      !incidentData.clientDateOfBirth
    ) return;

    setIsSearching(true);
    if (!selectedClient) {
      setShowClientSearch(true);
    }
    
    setIncidentData(prev => {
      if (prev.selectedClient !== null && !selectedClient) {
        return { ...prev, selectedClient: null };
      }
      return prev;
    });

    // First, try to find exact matches
    const exactMatches = users.filter(user => {
      const firstNameExactMatch = !incidentData.clientFirstName || 
        (user.firstName && 
         user.firstName.toLowerCase() === incidentData.clientFirstName.toLowerCase());
      
      const lastNameExactMatch = !incidentData.clientLastName || 
        (user.lastName && 
         user.lastName.toLowerCase() === incidentData.clientLastName.toLowerCase());
      
      const dobExactMatch = !incidentData.clientDateOfBirth || 
        (user.birthdate && 
         user.birthdate.toLowerCase() === incidentData.clientDateOfBirth.toLowerCase());
      
      // All provided criteria must match exactly
      return firstNameExactMatch && lastNameExactMatch && dobExactMatch;
    });

    let filteredUsers;
    
    if (exactMatches.length > 0) {
      // If we have exact matches, return only those
      filteredUsers = exactMatches;
    } else {
      // If no exact matches, fall back to partial matching with OR logic
      filteredUsers = users.filter(user => {
        return (
          (incidentData.clientFirstName &&
            user.firstName &&
            user.firstName
              .toLowerCase()
              .includes(incidentData.clientFirstName.toLowerCase())) ||
          (incidentData.clientLastName &&
            user.lastName &&
            user.lastName
              .toLowerCase()
              .includes(incidentData.clientLastName.toLowerCase())) ||
          (incidentData.clientDateOfBirth &&
            user.birthdate &&
            user.birthdate
              .toLowerCase()
              .includes(incidentData.clientDateOfBirth.toLowerCase()))
        );
      });
    }

    setSearchResults(filteredUsers);
    setIsSearching(false);
  }, [incidentData.clientFirstName, incidentData.clientLastName, incidentData.clientDateOfBirth, users]);

  // Effect for clearing results when all fields are empty
  useEffect(() => {
    if (incidentData.clientFirstName || incidentData.clientLastName ||
      incidentData.clientDateOfBirth
    ) return;

    // clear only once when empty
    setSearchResults([]);
    setShowClientSearch(false);

    // prevent infinite loop by checking before updating
    setIncidentData(prev => {
      if (prev.selectedClient !== null) {
        return { ...prev, selectedClient: null };
      }
      return prev;
    });

    // if (incidentData.clientDateOfBirth !== '') {
    //   updateIncidentData('clientDateOfBirth', '');
    // }
  }, [incidentData.clientFirstName, incidentData.clientLastName, incidentData.clientDateOfBirth]);

  const updateIncidentData = (field: keyof IncidentData, value: any) => {
    setIncidentData(prev => ({ ...prev, [field]: value }));

    // Clear validation error when field is updated
    if (field in validationErrors) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as keyof ValidationErrors];
        return newErrors;
      });
    }
  };

  // Validate field on blur
  const handleFieldBlur = (field: keyof IncidentData, value: string) => {
    const error = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error,
    }));
  };

  const setNoInformation = () => {
    updateIncidentData('clientStatus', 'No Information');
  };

  const normalizeDate = (dateString: string) => {
    return new Date(dateString).toISOString().split('T')[0];
  };

  const selectClient = (client: Client) => {
    setSelectedClient(client);
    updateIncidentData('selectedClient', client);
    updateIncidentData('clientFirstName', client.firstName);
    updateIncidentData('clientLastName', client.lastName);
    updateIncidentData('clientDateOfBirth', normalizeDate(client.birthdate as string));
    updateIncidentData('clientDoesNotExist', false);
    setShowClientSearch(false);
  };

  const resetForm = () => {
    setIncidentData({
      id: incidentData.id,
      userId: incidentData.userId,
      contactFirstName: '',
      contactLastName: '',
      contactPhone: '',
      relationshipToClient: '',
      relationshipToClientOther: '',
      clientStatus: '',
      hospitalName: '',
      hospitalAddress: '',
      hospitalFax: '',
      hospitalEmail: '',
      hospitalConfirmed: false,
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
      createdAt: incidentData.createdAt,
      sendFYIEmails: false,
      sendHospitalEmail: false,
      clientDoesNotExist: false,
    });
    setValidationErrors({});
    setCurrentStep(1);
    setSearchResults([]);
    setShowClientSearch(false);
  };

  // Validation functions
  const isStep1Complete = () => {
    const requiredFields = [
      'contactFirstName',
      'contactLastName',
      'contactPhone',
    ] as const;
    const hasErrors = requiredFields.some(field =>
      validateField(field, incidentData[field])
    );

    return (
      incidentData.contactFirstName &&
      incidentData.contactLastName &&
      incidentData.contactPhone &&
      incidentData.relationshipToClient &&
      incidentData.clientStatus &&
      !hasErrors
    );
  };

  const isStep2Complete = () => {
    const hospitalNameError = validateField(
      'hospitalName',
      incidentData.hospitalName
    );
    const hospitalAddressError = validateField(
      'hospitalAddress',
      incidentData.hospitalAddress
    );
    const hospitalFaxError = validateField(
      'hospitalFax',
      incidentData.hospitalFax
    );
    const hospitalEmailError = validateField(
      'hospitalEmail',
      incidentData.hospitalEmail
    );

    return (
      incidentData.hospitalName &&
      incidentData.hospitalAddress &&
      (incidentData.hospitalFax || incidentData.hospitalEmail) &&
      incidentData.hospitalConfirmed &&
      !hospitalNameError &&
      !hospitalAddressError &&
      !hospitalFaxError &&
      !hospitalEmailError
    );
  };

  const isStep3Complete = () => {
    return (
      incidentData.clientFirstName &&
      incidentData.clientLastName &&
      incidentData.clientDateOfBirth &&
      incidentData.selectedClient
    );
  };

  const canEnableRouting = () => {
    return isStep2Complete() && isStep3Complete();
  };

  const canSendFYI = () => {
    return incidentData.selectedClient && incidentData.hospitalName;
  };

  const sendHospitalPacket = async () => {
    setEmailSending(prev => [...prev, 'hospital']);
    if (!canEnableRouting()) return;
    const result = await sendEmails(incidentData.id, 'hospital');
    const parsedResult =
      typeof result === 'string' ? JSON.parse(result) : result;

    if (!parsedResult.success) {
      toast.error(`Failed to send hospital packet. ${parsedResult.error ? parsedResult.error : ''}`);
      setEmailSending(prev => prev.filter(email => email !== 'hospital'));
      return;
    }

    updateIncidentData('sendHospitalEmail', true);
    setEmailSending(prev => prev.filter(email => email !== 'hospital'));
    toast.success('Hospital packet sent successfully');
  };

  const sendFYIEmails = async () => {
    setEmailSending(prev => [...prev, 'fyi']);
    if (!canSendFYI()) return;
    const result = await sendEmails(incidentData.id, 'fyi');
    const parsedResult =
      typeof result === 'string' ? JSON.parse(result) : result;

    if (!parsedResult.success) {
      toast.error('Failed to send FYI emails');
      setEmailSending(prev => prev.filter(email => email !== 'fyi'));
      return;
    }

    updateIncidentData('sendFYIEmails', true);
    setEmailSending(prev => prev.filter(email => email !== 'fyi'));
    toast.success('FYI emails sent to Admin, Client, and Support');
  };

  const sendSupportEmail = async () => {
    setEmailSending(prev => [...prev, 'support']);
    if (!incidentData.clientFirstName && !incidentData.clientLastName && !incidentData.clientDateOfBirth) return;
    const result = await sendEmails(incidentData.id, 'support', {
      firstName: incidentData.clientFirstName,
      lastName: incidentData.clientLastName,
      birthdate: incidentData.clientDateOfBirth,
    });
    const parsedResult =
      typeof result === 'string' ? JSON.parse(result) : result;

    if (!parsedResult.success) {
      toast.error('Failed to send support email');
      setEmailSending(prev => prev.filter(email => email !== 'support'));
      return;
    }

    updateIncidentData('clientDoesNotExist', true);
    setEmailSending(prev => prev.filter(email => email !== 'support'));
    toast.success('Support email sent successfully');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStepStatus = (step: number) => {
    switch (step) {
      case 1:
        return isStep1Complete() ? 'complete' : 'pending';
      case 2:
        return isStep2Complete() ? 'complete' : 'pending';
      case 3:
        return isStep3Complete() ? 'complete' : 'pending';
      case 4:
        return canEnableRouting() ? 'complete' : 'pending';
      default:
        return 'pending';
    }
  };

  const normalizeFieldName = (field: keyof IncidentData) => {
    switch (field) {
      case 'contactFirstName':
        return 'Contact First Name';
      case 'contactLastName':
        return 'Contact Last Name';
      case 'contactPhone':
        return 'Contact Phone Number';
      case 'hospitalName':
        return 'Hospital Name';
      case 'hospitalAddress':
        return 'Hospital Address';
      case 'hospitalFax':
        return 'Hospital Fax';
      case 'hospitalEmail':
        return 'Hospital Email';
      case 'clientFirstName':
        return 'Client First Name';
      case 'clientLastName':
        return 'Client Last Name';
      case 'clientDateOfBirth':
        return 'Client Date of Birth';
      default:
        return field;
    }
  };

  const validationErrorsBadges = () => {
    return Object.keys(validationErrors).filter(
      field => validationErrors[field as keyof ValidationErrors]
    ).map(field => (
      <Badge key={field} variant='destructive'>
        {normalizeFieldName(field as keyof IncidentData)}: {validationErrors[field as keyof ValidationErrors]}
      </Badge>
    ));
  };

  const clearClientData = () => {
    setIncidentData(prev => ({
      ...prev,
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
    }));
    setSelectedClient(null);
    setShowClientSearch(false);
    setSearchResults([]);
  };

  return (
    <div className='max-w-7xl mx-auto p-6 space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-start'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Medical Incident Report
          </h1>
          <p className='text-muted-foreground mt-1'>
            Emergency medical incident reporting and routing system
          </p>
        </div>
        <div className='text-right'>
          <Label className='text-sm text-muted-foreground'>
            Incident Started
          </Label>
          <div className='text-sm font-medium'>
            {formatDate(incidentData.createdAt)}
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={resetForm}
            className='mt-2'
            disabled={
              incidentData.sendFYIEmails || incidentData.sendHospitalEmail
            }
          >
            <RefreshCw className='h-4 w-4 mr-2' />
            Reset Incident Data
          </Button>
        </div>
      </div>

      {/* Incident Overview */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <AlertCircle className='h-5 w-5' />
            Incident Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-2'>
            <Badge
              variant={
                getStepStatus(1) === 'complete' ? 'default' : 'secondary'
              }
            >
              Reporter: {incidentData.contactFirstName}{' '}
              {incidentData.contactLastName}
            </Badge>
            {incidentData.relationshipToClient && (
              <Badge variant='outline'>
                Relationship: {incidentData.relationshipToClient}
              </Badge>
            )}
            {incidentData.hospitalName && (
              <Badge variant='outline'>
                Hospital: {incidentData.hospitalName}
              </Badge>
            )}
            {incidentData.selectedClient && (
              <Badge
                variant={
                  getStepStatus(3) === 'complete' ? 'default' : 'secondary'
                }
              >
                Client: {incidentData.selectedClient.firstName}{' '}
                {incidentData.selectedClient.lastName}
              </Badge>
            )}
            {validationErrorsBadges()}
          </div>
        </CardContent>
      </Card>

      {/* Step Progress */}
      <div className='grid grid-cols-4 gap-4'>
        {[1, 2, 3, 4].map(step => {
          const status = getStepStatus(step);
          const titles = [
            'Reporting Individual',
            'Hospital Information',
            'Confirm Client',
            'Routing & Logging',
          ];

          return (
            <Card
              key={step}
              className={`cursor-pointer transition-colors ${
                currentStep === step ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setCurrentStep(step)}
            >
              <CardContent className='p-4 text-center'>
                <div
                  className={`w-8 h-8 rounded-full mx-auto mb-2 flex items-center justify-center ${
                    status === 'complete'
                      ? 'bg-green-500 text-white'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {status === 'complete' ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    step
                  )}
                </div>
                <div className='text-sm font-medium'>{titles[step - 1]}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Step Content */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Step 1: Reporting Individual */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='h-5 w-5' />
                Step 1: Reporting Individual
              </CardTitle>
              <CardDescription>
                Information about the person reporting the incident
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='contactFirstName'>
                    Contact First Name <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                    id='contactFirstName'
                    value={incidentData.contactFirstName}
                    onChange={e =>
                      updateIncidentData('contactFirstName', e.target.value)
                    }
                    placeholder='First name'
                    disabled={
                      incidentData.sendFYIEmails ||
                      incidentData.sendHospitalEmail
                    }
                    className={
                      validationErrors.contactFirstName
                        ? 'border-destructive'
                        : ''
                    }
                    onBlur={e => handleFieldBlur('contactFirstName', e.target.value)}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='contactLastName'>
                    Contact Last Name <span className='text-red-500'>*</span>
                  </Label>
                  <Input
                    id='contactLastName'
                    value={incidentData.contactLastName}
                    onChange={e =>
                      updateIncidentData('contactLastName', e.target.value)
                    }
                    placeholder='Last name'
                    disabled={
                      incidentData.sendFYIEmails ||
                      incidentData.sendHospitalEmail
                    }
                    className={
                      validationErrors.contactLastName
                        ? 'border-destructive'
                        : ''
                    }
                    onBlur={e => handleFieldBlur('contactLastName', e.target.value)}
                  />
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='contactPhone'>
                  Contact Phone Number <span className='text-red-500'>*</span>
                </Label>
                <Input
                  id='contactPhone'
                  value={incidentData.contactPhone}
                  onChange={e =>
                    updateIncidentData('contactPhone', e.target.value)
                  }
                  placeholder='(*************'
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                  className={
                    validationErrors.contactPhone ? 'border-destructive' : ''
                  }
                  onBlur={e => handleFieldBlur('contactPhone', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='relationshipToClient'>
                  Relationship to Client <span className='text-red-500'>*</span>
                </Label>
                <Select
                  value={incidentData.relationshipToClient}
                  onValueChange={value =>
                    updateIncidentData('relationshipToClient', value)
                  }
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select relationship' />
                  </SelectTrigger>
                  <SelectContent>
                    {relationshipOptions.map(option => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {incidentData.relationshipToClient === 'Other' && (
                <div className='space-y-2'>
                  <Label htmlFor='relationshipToClientOther'>
                    Please specify
                  </Label>
                  <Input
                    id='relationshipToClientOther'
                    value={incidentData.relationshipToClientOther}
                    onChange={e =>
                      updateIncidentData('relationshipToClientOther', e.target.value)
                    }
                    placeholder='Relationship'
                    disabled={
                      incidentData.sendFYIEmails ||
                      incidentData.sendHospitalEmail
                    }
                  />
                </div>
                )}

              <div className='space-y-2'>
                <Label htmlFor='clientStatus'>
                  Current Status of Client{' '}
                  <span className='text-red-500'>*</span>
                </Label>
                <Textarea
                  id='clientStatus'
                  value={incidentData.clientStatus}
                  onChange={e =>
                    updateIncidentData('clientStatus', e.target.value)
                  }
                  placeholder='Type details (you may enter "No Information" if unknown)'
                  rows={3}
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                />
                {/* <Button
                  variant='outline'
                  size='sm'
                  onClick={setNoInformation}
                  className='mt-2'
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                >
                  Set "No Information"
                </Button> */}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Hospital Information */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Building2 className='h-5 w-5' />
                Step 2: Hospital Information
              </CardTitle>
              <CardDescription>
                Hospital details for routing medical information
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='hospitalName'>
                  Hospital Name <span className='text-red-500'>*</span>
                </Label>
                <Input
                  id='hospitalName'
                  value={incidentData.hospitalName}
                  onChange={e =>
                    updateIncidentData('hospitalName', e.target.value)
                  }
                  placeholder='Hospital name'
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                  className={
                    validationErrors.hospitalName ? 'border-destructive' : ''
                  }
                  onBlur={e => handleFieldBlur('hospitalName', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='hospitalAddress'>
                  Hospital Address <span className='text-red-500'>*</span>
                </Label>
                <Textarea
                  id='hospitalAddress'
                  value={incidentData.hospitalAddress}
                  onChange={e =>
                    updateIncidentData('hospitalAddress', e.target.value)
                  }
                  placeholder='Full hospital address'
                  rows={2}
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                  className={
                    validationErrors.hospitalAddress ? 'border-destructive' : ''
                  }
                  onBlur={e => handleFieldBlur('hospitalAddress', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='hospitalFax'>Hospital Fax</Label>
                <Input
                  id='hospitalFax'
                  value={incidentData.hospitalFax}
                  onChange={e =>
                    updateIncidentData('hospitalFax', e.target.value)
                  }
                  placeholder='(*************'
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                  className={
                    validationErrors.hospitalFax ? 'border-destructive' : ''
                  }
                  onBlur={e => handleFieldBlur('hospitalFax', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='hospitalEmail'>Hospital Email</Label>
                <Input
                  id='hospitalEmail'
                  type='email'
                  value={incidentData.hospitalEmail}
                  onChange={e =>
                    updateIncidentData('hospitalEmail', e.target.value)
                  }
                  placeholder='<EMAIL>'
                  disabled={
                    incidentData.sendFYIEmails || incidentData.sendHospitalEmail
                  }
                  className={
                    validationErrors.hospitalEmail ? 'border-destructive' : ''
                  }
                  onBlur={e => handleFieldBlur('hospitalEmail', e.target.value)}
                />
              </div>

              {!(incidentData.hospitalFax || incidentData.hospitalEmail) && (
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    <Badge variant='destructive' className='mr-2'>
                      Fax or Email Required
                    </Badge>
                    At least one contact method (fax or email) must be provided
                    for routing.
                  </AlertDescription>
                </Alert>
              )}

              <div className='space-y-2'>
                <Label>Hospital Information Confirmation</Label>
                <div className='flex gap-2'>
                  <Button
                    variant={
                      incidentData.hospitalConfirmed ? 'default' : 'outline'
                    }
                    size='sm'
                    onClick={() =>
                      updateIncidentData('hospitalConfirmed', true)
                    }
                    disabled={
                      incidentData.sendFYIEmails ||
                      incidentData.sendHospitalEmail
                    }
                  >
                    <CheckCircle className='h-4 w-4 mr-2' />
                    Yes, Confirmed
                  </Button>
                  <Button
                    variant={
                      !incidentData.hospitalConfirmed
                        ? 'destructive'
                        : 'outline'
                    }
                    size='sm'
                    onClick={() =>
                      updateIncidentData('hospitalConfirmed', false)
                    }
                    disabled={
                      incidentData.sendFYIEmails ||
                      incidentData.sendHospitalEmail
                    }
                  >
                    <AlertCircle className='h-4 w-4 mr-2' />
                    No, Needs Correction
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Confirm Client */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <UserCheck className='h-5 w-5' />
                Step 3: Confirm Childfree Trust Client
              </CardTitle>
              <CardDescription>
                Search and confirm the client identity
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              {!isStep1Complete() || !isStep2Complete() ? (
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    <Badge variant='destructive' className='mr-2'>
                      Needed for Lookup & Routing
                    </Badge>
                    Complete Steps 1 and 2 to enable client lookup.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='clientFirstName'>
                        Client First Name{' '}
                        <span className='text-red-500'>*</span>
                      </Label>
                      <Input
                        id='clientFirstName'
                        value={incidentData.clientFirstName}
                        onChange={e => {
                          updateIncidentData('clientFirstName', e.target.value)
                          setSelectedClient(null)
                        }}
                        placeholder='First name'
                        disabled={
                          incidentData.sendFYIEmails ||
                          incidentData.sendHospitalEmail
                        }
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor='clientLastName'>
                        Client Last Name <span className='text-red-500'>*</span>
                      </Label>
                      <Input
                        id='clientLastName'
                        value={incidentData.clientLastName}
                        onChange={e => {
                          updateIncidentData('clientLastName', e.target.value)
                          setSelectedClient(null)
                        }}
                        placeholder='Last name'
                        disabled={
                          incidentData.sendFYIEmails ||
                          incidentData.sendHospitalEmail
                        }
                      />
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='clientDateOfBirth'>
                      Date of Birth <span className='text-red-500'>*</span>
                    </Label>
                    <DatePicker
                      date={
                        incidentData.clientDateOfBirth
                          ? new Date(incidentData.clientDateOfBirth)
                          : undefined
                      }
                      setDate={date => {
                        updateIncidentData(
                          'clientDateOfBirth',
                          date?.toISOString()
                        );
                        setSelectedClient(null);
                      }
                        
                      }
                      disabled={
                        incidentData.sendFYIEmails ||
                        incidentData.sendHospitalEmail
                      }
                    />
                    {/* <Input
                      id='clientDateOfBirth'
                      type='date'
                      value={incidentData.clientDateOfBirth}
                      onChange={(e) => {
                        updateIncidentData('clientDateOfBirth', e.target.value)
                        setSelectedClient(null)
                      }}
                      disabled={
                          incidentData.sendFYIEmails ||
                          incidentData.sendHospitalEmail
                        }
                    /> */}
                  </div>

                  {/* Client Search Results */}
                  {showClientSearch && (
                    <div className='space-y-2'>
                      <Label className='flex items-center gap-2'>
                        <Search className='h-4 w-4' />
                        Client Search Results
                      </Label>

                      {isSearching ? (
                        <div className='text-center py-4'>
                          <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto'></div>
                          <p className='text-sm text-muted-foreground mt-2'>
                            Searching...
                          </p>
                        </div>
                      ) : searchResults.length > 0 ? (
                        <div className='border rounded-md'>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>DOB</TableHead>
                                {/* <TableHead>Contact</TableHead> */}
                                <TableHead>Action</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {searchResults.map(client => (
                                <TableRow
                                  key={client.id}
                                  className={
                                    incidentData.selectedClient?.id ===
                                    client.id
                                      ? 'bg-muted'
                                      : ''
                                  }
                                >
                                  <TableCell className='font-medium'>
                                    {client.firstName} {client.lastName}
                                  </TableCell>
                                  <TableCell>
                                    {new Date(
                                      client.birthdate ? client.birthdate : ''
                                    ).toLocaleDateString()}
                                  </TableCell>
                                  {/* <TableCell>
                                    <div className='text-sm'>
                                      {client.phoneNumber && (
                                        <div className='flex items-center gap-1'>
                                          <Phone className='h-3 w-3' />
                                          {client.phoneNumber}
                                        </div>
                                      )}
                                      <div className='flex items-center gap-1'>
                                        <Mail className='h-3 w-3' />
                                        {client.email}
                                      </div>
                                    </div>
                                  </TableCell> */}
                                  <TableCell>
                                    <Button
                                      size='sm'
                                      variant={
                                        incidentData.selectedClient?.id ===
                                        client.id
                                          ? 'default'
                                          : 'outline'
                                      }
                                      onClick={() => selectClient(client)}
                                      disabled={
                                        incidentData.sendFYIEmails ||
                                        incidentData.sendHospitalEmail
                                      }
                                    >
                                      {incidentData.selectedClient?.id ===
                                      client.id
                                        ? 'Selected'
                                        : 'Select'}
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <Alert>
                          <AlertCircle className='h-4 w-4' />
                          <AlertDescription>
                            No matching clients found. Please verify the name
                            spelling or contact support.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                  <div className='flex justify-between gap-2'>
                    <Button
                      onClick={clearClientData}
                      variant='outline'
                      className='text-xs'
                      size='sm'
                      disabled={
                        incidentData.sendFYIEmails ||
                        incidentData.sendHospitalEmail
                      }
                    >
                      Clear Client Information
                    </Button>
                    {!incidentData.selectedClient && <div className='flex justify-end'>
                      <Button
                        onClick={sendSupportEmail}
                        variant='outline'
                        size='sm'
                        className='text-xs'
                        disabled={
                          incidentData.sendFYIEmails ||
                          incidentData.sendHospitalEmail || 
                          emailSending.includes('support') || 
                          (!incidentData.clientFirstName && !incidentData.clientLastName && !incidentData.clientDateOfBirth)
                        }
                      >
                        {!emailSending.includes('support') && (
                          <Mail className='h-4 w-4 mr-2' />
                        )}
                        {emailSending.includes('support') && (
                          <div className='animate-spin rounded-full h-4 w-4 mr-2 border-b-2 border-primary'></div>
                        )}
                        Client Does Not Exist
                      </Button>
                    </div>}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 4: Routing & Logging */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Send className='h-5 w-5' />
                Step 4: Routing & Logging
              </CardTitle>
              <CardDescription>
                Send information to hospital and notify relevant parties
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Send Hospital Packet */}
              <div className='space-y-2'>
                <Label className='text-base font-medium'>
                  Send Information to Hospital
                  {!incidentData.sendHospitalEmail && <Badge variant='secondary'>
                    Not Sent
                  </Badge>}
                </Label>
                <p className='text-sm text-muted-foreground'>
                  Dispatches full incident packet (overview, POA, medical care,
                  medical notes) to validated hospital.
                </p>
                <Button
                  onClick={sendHospitalPacket}
                  disabled={
                    !canEnableRouting() ||
                    incidentData.sendHospitalEmail ||
                    emailSending.includes('hospital')
                  }
                  className='w-full'
                >
                  {!emailSending.includes('hospital') && (
                    <Send className='h-4 w-4 mr-2' />
                  )}
                  {emailSending.includes('hospital') && (
                    <div className='animate-spin rounded-full h-4 w-4 mr-2 border-b-2 text-white'></div>
                  )}
                  Send Hospital Packet
                </Button>
                {!canEnableRouting() && (
                  <Alert>
                    <AlertCircle className='h-4 w-4' />
                    <AlertDescription>
                      <Badge variant='destructive' className='mr-2'>
                        Enables Routing
                      </Badge>
                      Complete and confirm Steps 2-3 to enable hospital routing.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {/* Send FYI Emails */}
              <div className='space-y-2'>
                <Label className='text-base font-medium'>
                  Email Routing (FYI Only)
                  {!incidentData.sendFYIEmails && <Badge variant='secondary'>
                    Not Sent
                  </Badge>}
                </Label>
                <p className='text-sm text-muted-foreground'>
                  Sends informational (non-medical) notification to Admin,
                  Client, and Support Inbox.
                </p>
                <Button
                  variant='outline'
                  onClick={sendFYIEmails}
                  disabled={
                    !canSendFYI() ||
                    incidentData.sendFYIEmails ||
                    emailSending.includes('fyi')
                  }
                  className='w-full'
                >
                  {!emailSending.includes('fyi') && (
                    <Mail className='h-4 w-4 mr-2' />
                  )}
                  {emailSending.includes('fyi') && (
                    <div className='animate-spin rounded-full h-4 w-4 mr-2 border-b-2 border-primary'></div>
                  )}
                  Send FYI Emails
                </Button>
                {!canSendFYI() && (
                  <Alert>
                    <AlertCircle className='h-4 w-4' />
                    <AlertDescription>
                      Client must be selected and hospital name entered to send
                      FYI emails.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {/* Routing Information */}
              <div className='bg-blue-50 p-4 rounded-md space-y-2'>
                <h4 className='font-medium text-blue-900'>
                  Routing Information
                </h4>
                <ul className='text-sm text-blue-800 space-y-1'>
                  <li>
                    • Hospital receives: Complete incident overview, POA
                    documents, medical care directives, and medical notes
                  </li>
                  <li>
                    • FYI emails include: Basic incident information (no medical
                    details)
                  </li>
                  <li>
                    • All routing attempts are logged immutably for audit
                    purposes
                  </li>
                  <li>• Confirmations are sent to the reporting individual</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Incident Summary Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Incident Summary</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>Step 1: Reporting Individual</span>
                <Badge
                  variant={
                    getStepStatus(1) === 'complete' ? 'default' : 'secondary'
                  }
                >
                  {getStepStatus(1) === 'complete' ? 'Complete' : 'Pending'}
                </Badge>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>Step 2: Hospital Information</span>
                <Badge
                  variant={
                    getStepStatus(2) === 'complete' ? 'default' : 'secondary'
                  }
                >
                  {getStepStatus(2) === 'complete' ? 'Complete' : 'Pending'}
                </Badge>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>Step 3: Client Confirmation</span>
                <Badge
                  variant={
                    getStepStatus(3) === 'complete' ? 'default' : 'secondary'
                  }
                >
                  {getStepStatus(3) === 'complete' ? 'Complete' : 'Pending'}
                </Badge>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>Step 4: Routing Ready</span>
                <Badge
                  variant={
                    getStepStatus(4) === 'complete' ? 'default' : 'secondary'
                  }
                >
                  {getStepStatus(4) === 'complete' ? 'Ready' : 'Not Ready'}
                </Badge>
              </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm'>FYI Emails</span>
                  <Badge
                    variant={
                      incidentData.sendFYIEmails ? 'default' : 'secondary'
                    }
                  >
                    {incidentData.sendFYIEmails ? 'Sent' : 'Not Sent'}
                  </Badge>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm'>Hospital Email</span>
                  <Badge
                    variant={
                      incidentData.sendHospitalEmail ? 'default' : 'secondary'
                    }
                  >
                    {incidentData.sendHospitalEmail ? 'Sent' : 'Not Sent'}
                  </Badge>
                </div>
            </div>

            {incidentData.selectedClient && (
              <div className='border-t pt-4'>
                <Label className='text-sm font-medium'>Selected Client</Label>
                <div className='mt-2 p-3 bg-muted rounded-md'>
                  <div className='font-medium'>
                    {incidentData.selectedClient.firstName}{' '}
                    {incidentData.selectedClient.lastName}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    DOB:{' '}
                    {new Date(
                      incidentData.selectedClient.birthdate
                        ? incidentData.selectedClient.birthdate
                        : ''
                    ).toLocaleDateString()}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    {incidentData.selectedClient.email}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
