'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useMedicalIncidents } from '@/hooks/useMedicalIncidents';
import { useRouter } from 'next/navigation';
import { Plus } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export default function MedicalIncidentPage() {
  const { incidents, loading, error, createIncident } = useMedicalIncidents();
  const router = useRouter();

  const handleNewIncident = async () => {
    const newIncident = await createIncident({
      contactFirstName: '',
      contactLastName: '',
      contactPhone: '',
      relationshipToClient: '',
      clientStatus: '',
      hospitalName: '',
      hospitalAddress: '',
      hospitalFax: '',
      hospitalEmail: '',
      hospitalConfirmed: false,
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
      sendFYIEmails: false,
      sendHospitalEmail: false,
    });

    if (newIncident?.id) {
      router.push(`/call-center/medical-incident/${newIncident.id}`);
    }
  };

  return (
    <div className='max-w-7xl mx-auto p-6 space-y-6'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Medical Incident Reports
          </h1>
          <p className='text-muted-foreground mt-1'>
            Emergency medical incident reporting and routing system
          </p>
        </div>
        <Button onClick={handleNewIncident}>
          <Plus className='h-4 w-4 mr-2' />
          New Incident
        </Button>
      </div>
      <div className='border rounded-md'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reporting Individual</TableHead>
              <TableHead>Client Name</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Client Status</TableHead>
              <TableHead>Incident Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {incidents.map(incident => (
              <TableRow key={incident.id}>
                <TableCell className='font-medium'>
                  {incident.contactFirstName} {incident.contactLastName}
                </TableCell>
                <TableCell className='font-medium'>
                  {incident.selectedClient
                    ? incident.selectedClient.firstName +
                      ' ' +
                      incident.selectedClient.lastName
                    : 'Not Selected'}
                </TableCell>
                <TableCell>
                  {new Date(incident.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {new Date(incident.createdAt).toLocaleTimeString()}
                </TableCell>
                <TableCell>{incident.clientStatus}</TableCell>
                <TableCell>
                  {(incident.sendHospitalEmail || incident.sendFYIEmails) && (
                    <div className='flex flex-col space-y-1'>
                      <p>
                        {incident.sendHospitalEmail
                          ? 'Sent Email to Hospital'
                          : ''}
                      </p>
                      <p>{incident.sendFYIEmails ? 'Sent FYI Emails' : ''}</p>
                    </div>
                  )}
                  {!incident.sendHospitalEmail &&
                    !incident.sendFYIEmails &&
                    'Not Sent Emails'}
                </TableCell>
                <TableCell>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() =>
                      router.push(
                        `/call-center/medical-incident/${incident.id}`
                      )
                    }
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
