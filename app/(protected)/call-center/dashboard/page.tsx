'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { callCenterAPI, type GetUsersListResponse } from '@/lib/api/callcenter';
import { ColumnDef } from '@tanstack/react-table';
import {
  DataTable,
  DataTableColumnHeader,
  type DataTableConfig,
} from '@/components/ui/data-table/index';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

type User = GetUsersListResponse[number];

const columns: ColumnDef<User>[] = [
  {
    id: 'name',
    accessorFn: row =>
      [row.firstName, row.lastName].filter(Boolean).join(' ') || 'Unnamed User',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ getValue }) => (
      <span className='font-medium'>{getValue<string>()}</span>
    ),
    enableHiding: false,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Email' />
    ),
    cell: ({ row }) => (
      <span className='text-muted-foreground'>{row.original.email}</span>
    ),
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Actions' />
    ),
    cell: ({ row }) => (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant='secondary' size='sm'>
            Report
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Report</DialogTitle>
          </DialogHeader>
          <div className='text-sm text-muted-foreground'>
            Report modal content placeholder.
          </div>
        </DialogContent>
      </Dialog>
    ),
    enableHiding: false,
  },
];

const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Filter by email...',
  enableColumnVisibility: false,
  enablePagination: true,
  defaultPageSize: 10,
};

const Page: React.FC = () => {
  const {
    data: users = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['callcenter', 'users'],
    queryFn: callCenterAPI.getUsersList,
  });

  return (
    <div className='space-y-4'>
      <h1 className='text-2xl font-semibold'>Call Center Dashboard</h1>
      <DataTable<User>
        columns={columns}
        data={users}
        config={tableConfig}
        loading={isLoading}
        error={isError ? (error as Error)?.message || 'Unknown error' : null}
      />
    </div>
  );
};

export default Page;
