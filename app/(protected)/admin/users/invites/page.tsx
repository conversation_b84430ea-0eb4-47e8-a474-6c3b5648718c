'use client';

import React, { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Mail, Trash2, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { adminUsersAPI } from '@/lib/api/admin-users';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { InviteUserModal } from '@/app/components/dashboard/admin/invite-user-modal';

const client = generateClient<Schema>();

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Filter invites...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'pending', label: 'Pending' },
        { value: 'accepted', label: 'Accepted' },
        { value: 'expired', label: 'Expired' },
        { value: 'cancelled', label: 'Cancelled' },
      ],
    },
    {
      id: 'role',
      title: 'Role',
      options: [
        { value: 'WelonTrust', label: 'Welon Trust' },
        { value: 'Administrator', label: 'Administrator' },
        { value: 'Professional', label: 'Professional' },
      ],
    },
  ],
};

interface Invite {
  id: string;
  firstName: string;
  middleName?: string | null;
  lastName: string;
  email: string;
  role: string | null;
  subrole?: string | null;
  invitedBy: string;
  invitedByEmail: string;
  token: string;
  status: string | null;
  expiresAt: string;
  acceptedAt?: string | null;
  createdAt: string;
}

const InviteListPage = () => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['invite-list'],
    queryFn: adminUsersAPI.inviteList,
  });

  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Transform invites data to handle null status
  const transformedInvites = useMemo(
    () =>
      (data || []).map((invite: any) => ({
        ...invite,
        status: invite.status || 'pending',
        name: `${invite.firstName || ''} ${invite.middleName ? invite.middleName + ' ' : ''}${invite.lastName || ''}`.trim(),
      })),
    [data]
  );

  // Handle resend invite
  const handleResendInvite = async (invite: Invite) => {};

  // Handle delete invite
  const handleDeleteInvite = async (invite: Invite) => {
    try {
      await client.models.UserInvite.delete({ id: invite.id });
      toast.success('Invitation deleted successfully');
      await refetch();
    } catch (error) {
      console.error('Error deleting invite:', error);
      toast.error('Failed to delete invitation');
    }
  };

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'accepted':
        return 'default';
      case 'expired':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Define columns for the data table
  const columns: ColumnDef<Invite>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Name' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>{row.getValue('name')}</span>
      ),
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Email' />
      ),
      cell: ({ row }) => <span>{row.getValue('email')}</span>,
    },
    {
      accessorKey: 'role',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Role' />
      ),
      cell: ({ row }) => {
        const role = row.getValue('role') as string;
        return <span>{role === 'WelonTrust' ? 'Welon Trust' : role}</span>;
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge variant={getStatusBadgeVariant(status)}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Invited Date' />
      ),
      cell: ({ row }) => (
        <span className='text-sm text-muted-foreground'>
          {formatDate(row.getValue('createdAt'))}
        </span>
      ),
    },
    {
      accessorKey: 'expiresAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Expires At' />
      ),
      cell: ({ row }) => {
        const expiresAt = row.getValue('expiresAt') as string;
        const isExpired = new Date(expiresAt) < new Date();
        return (
          <span
            className={`text-sm ${isExpired ? 'text-red-600' : 'text-muted-foreground'}`}
          >
            {formatDate(expiresAt)}
          </span>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const invite = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {invite.status === 'pending' && (
                <DropdownMenuItem
                  onClick={() => handleResendInvite(invite)}
                  className='text-blue-600'
                >
                  <Mail className='mr-2 h-4 w-4' />
                  Resend Invitation
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => handleDeleteInvite(invite)}
                className='text-red-600'
              >
                <Trash2 className='mr-2 h-4 w-4' />
                Delete Invitation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className='space-y-4'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>
            Invite Management
          </h2>
          <p className='text-muted-foreground'>
            Manage user invitations and track their status
          </p>
        </div>
        <Button onClick={() => setIsInviteModalOpen(true)}>
          <UserPlus className='mr-2 h-4 w-4' />
          Invite User
        </Button>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={transformedInvites}
        config={tableConfig}
        loading={isLoading}
        error={error?.message || null}
      />

      <InviteUserModal
        open={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        onInviteSent={() => refetch()}
      />
    </div>
  );
};

export default InviteListPage;
