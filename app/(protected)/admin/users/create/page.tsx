'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { UserForm } from '@/components/dashboard/admin/user-form';
import { fetchWelonTrustUsers } from '@/lib/data/users';
import { User } from '@/types/account';
import { toast } from 'sonner';
import { createWelonTrustAssignment } from '@/lib/data/assignments';

export default function CreateUserPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [welonTrustUsers, setWelonTrustUsers] = useState<User[]>([]);

  // Load Welon Trust users for assignment
  React.useEffect(() => {
    const loadWelonTrustUsers = async () => {
      try {
        const users = await fetchWelonTrustUsers();
        setWelonTrustUsers(users);
      } catch (error) {
        console.error('Error loading Welon Trust users:', error);
      }
    };

    loadWelonTrustUsers();
  }, []);

  const handleSave = async (userData: any) => {
    setIsLoading(true);

    try {
      // Create the user with Amplify backend
      // const newUserData: Omit<User, 'id' | 'createdAt'> = {
      //   name: userData.name,
      //   email: userData.email,
      //   role: userData.role,
      //   subrole: userData.subrole,
      //   status: 'pending', // New users start as pending
      //   journeyStatus: 'onboarding',
      //   linkedAccounts: [],
      //   assignedWelonTrust: undefined,
      //   firstName: '',
      //   lastName: '',
      // };
      //
      // const createdUser = await createUser(newUserData);

      // Handle Welon Trust assignment if applicable
      // if (userData.role === 'Member' && userData.assignedWelonTrust) {
      //   if (
      //     userData.assignedWelonTrust === 'invite_new' &&
      //     userData.newWelonTrustEmail
      //   ) {
      //     // Create a new Welon Trust assignment with invitation
      //     await createWelonTrustAssignment(createdUser.id, {
      //       welonTrustUserId: 'pending-invitation', // Placeholder until invitation is accepted
      //       welonTrustName: 'Pending Invitation',
      //       welonTrustEmail: userData.newWelonTrustEmail,
      //       assignedBy: 'current-admin-user', // TODO: Get actual admin user ID from auth context
      //     });
      //
      //     toast.success(
      //       `User created successfully! Invitation sent to ${userData.newWelonTrustEmail}`
      //     );
      //   } else if (userData.assignedWelonTrust !== 'invite_new') {
      //     // Assign existing Welon Trust user
      //     const selectedWelonTrust = welonTrustUsers.find(
      //       wt => wt.id === userData.assignedWelonTrust
      //     );
      //     if (selectedWelonTrust) {
      //       await createWelonTrustAssignment(createdUser.id, {
      //         welonTrustUserId: selectedWelonTrust.id,
      //         welonTrustName: selectedWelonTrust.name,
      //         welonTrustEmail: selectedWelonTrust.email,
      //         assignedBy: 'current-admin-user', // TODO: Get actual admin user ID from auth context
      //       });
      //
      //       toast.success(
      //         `User created successfully! ${selectedWelonTrust.name} assigned as Welon Trust.`
      //       );
      //     } else {
      //       toast.success('User created successfully!');
      //     }
      //   }
      // } else {
      //   toast.success('User created successfully!');
      // }

      // Navigate back to users list
      router.push('/admin/users');
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error('Failed to create user. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return <UserForm mode='create' onSave={handleSave} isLoading={isLoading} />;
}
