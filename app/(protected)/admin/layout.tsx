'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { AdminGuard } from '@/lib/auth/admin-guard';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '@/components/ui/container';

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();

  // Pages that should have no padding
  const noPaddingPages = ['/admin/users'];
  const shouldRemovePadding = noPaddingPages.includes(pathname);

  return (
    <AdminGuard>
      <div className='flex bg-background'>
        <Sidebar userRole='Administrator' />
        <div className='flex-1'>
          <main>
            {shouldRemovePadding ? (
              <div className='h-full p-6'>{children}</div>
            ) : (
              <AdminContainer>{children}</AdminContainer>
            )}
          </main>
        </div>
      </div>
    </AdminGuard>
  );
}
