'use client';

import React from 'react';
import { AdminGuard } from '@/lib/auth';
import { AdminContainer } from '@/components/ui/container';
import { Sidebar } from '@/components/dashboard/sidebar';

export default function CallCenterLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <AdminGuard requiredRole={'MEDICALREVIEW'}>
      <div className='min-h-screen flex bg-background'>
        <Sidebar userRole='Medical Review' />
        <div className='flex-1'>
          <main>
            <AdminContainer>{children}</AdminContainer>
          </main>
        </div>
      </div>
    </AdminGuard>
  );
}
