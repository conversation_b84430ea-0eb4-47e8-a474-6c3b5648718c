'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { CheckIcon, Plus, X } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { MedicalReport, useMedicalReview } from '@/hooks/useMedicalReview';

export default function MedicalReviewPage() {
  const { medicalReports, loading, createMedicalReport } =
    useMedicalReview();
  const router = useRouter();

  const handleNewReport = async () => {
    const newReport = await createMedicalReport({
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
      medicalNotes: '',
      medicalPOAReviewed: false,
      medicalCareReportReviewed: false,
    });

    if (newReport?.id) {
      router.push(`/medical/medical-review/${newReport.id}`);
    }
  };

  return (
    <div className='max-w-7xl mx-auto p-6 space-y-6'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>Medical Review</h1>
          <p className='text-muted-foreground mt-1'>
            Select a client, review Medical POA, and My Care Report, add notes,
            then Save.
          </p>
        </div>
        <Button onClick={handleNewReport}>
          <Plus className='h-4 w-4 mr-2' />
          New Report
        </Button>
      </div>
      <MedicalReviewTable medicalReports={medicalReports} loading={loading} />
    </div>
  );
}

const MedicalReviewTable = ({
  medicalReports,
  loading,
}: {
  medicalReports: MedicalReport[];
  loading: boolean;
}) => {
  const router = useRouter();
  return (
    <div className='border rounded-md'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Client Name</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>POA Reviewed</TableHead>
            <TableHead>My Care Report Reviewed</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell
                colSpan={6}
                className='text-center py-6 text-muted-foreground'
              >
                Loading...
              </TableCell>
            </TableRow>
          ) : (
            medicalReports.map((report: any) => (
              <TableRow key={report.id}>
                <TableCell className='font-medium'>
                  {report.clientFirstName} {report.clientLastName}
                </TableCell>
                <TableCell>
                  {new Date(report.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {new Date(report.createdAt).toLocaleTimeString()}
                </TableCell>
                <TableCell>
                  {report.medicalPOAReviewed ? (
                    <CheckIcon className='h-4 w-4 text-green-500' />
                  ) : (
                    <X className='h-4 w-4 text-red-500' />
                  )}
                </TableCell>
                <TableCell>
                  {report.medicalCareReportReviewed ? (
                    <CheckIcon className='h-4 w-4 text-green-500' />
                  ) : (
                    <X className='h-4 w-4 text-red-500' />
                  )}
                </TableCell>
                <TableCell>
                  {report.medicalNotes ? (
                    <CheckIcon className='h-4 w-4 text-green-500' />
                  ) : (
                    <X className='h-4 w-4 text-red-500' />
                  )}
                </TableCell>
                <TableCell>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() =>
                      router.push(`/medical/medical-review/${report.id}`)
                    }
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};
