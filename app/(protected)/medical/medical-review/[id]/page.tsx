'use client';

import React, { useState, useEffect } from 'react';
import { MedicalReport, useMedicalReview } from '@/hooks/useMedicalReview';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CheckCircle,
  AlertCircle,
  UserCheck,
  Mail,
  RefreshCw,
  Search,
  Phone,
  MapPin,
} from 'lucide-react';
import { toast } from 'sonner';
import { callCenterAPI } from '@/lib/api/callcenter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useDebounce } from '@/hooks/useDebounce';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { cleanHTML } from '@/utils/cleanHTML';

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  birthdate?: string | null;
  email: string;
  phoneNumber?: string | null;
  // address: string;
}

export default function MedicalReviewPage() {
  const params = useParams();
  const { id } = params;
  const [medicalReport, setMedicalReport] = useState<
    Omit<MedicalReport, 'user'>
  >({
    id: '',
    userId: '',
    clientFirstName: '',
    clientLastName: '',
    clientDateOfBirth: '',
    selectedClient: null,
    medicalNotes: '',
    medicalPOAReviewed: false,
    medicalCareReportReviewed: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });
  const [searchResults, setSearchResults] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showClientSearch, setShowClientSearch] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const {
    fetchMedicalReport,
    updateMedicalReport,
    medicalData,
    fetchMedicalData,
    loading,
    medicalDataLoading,
    resetMedicalData,
  } = useMedicalReview();

  const debounsedMedicalReport = useDebounce(medicalReport, 1000);

  const {
    data: users = [],
    isRefetching,
    isFetching,
  } = useQuery({
    queryKey: ['callcenter', 'users'],
    queryFn: callCenterAPI.getUsersList,
  });

  useEffect(() => {
    const fetchMedicalReportData = async () => {
      if (!id) return;

      const report = await fetchMedicalReport(id as string);

      if (!report) return;

      setMedicalReport(report);
      setSelectedClient(report.selectedClient || null);
    };

    fetchMedicalReportData();
  }, [id, fetchMedicalReport]);

  useEffect(() => {
    if (!debounsedMedicalReport) return;
    if (!debounsedMedicalReport.id) return;

    updateMedicalReport({
      ...debounsedMedicalReport,
      updatedAt: new Date().toISOString(),
    });
  }, [debounsedMedicalReport, updateMedicalReport]);

  useEffect(() => {
    if (isFetching || isRefetching) return;
    if (!medicalReport.clientFirstName && !medicalReport.clientLastName &&
      !medicalReport.clientDateOfBirth
    ) return;

    setIsSearching(true);
    if (!selectedClient) {
      setShowClientSearch(true);
    }

    setMedicalReport(prev => {
      if (prev.selectedClient !== null && !selectedClient) {
        resetMedicalData();
        return { ...prev, selectedClient: null };
      }
      return prev;
    });

    // First, try to find exact matches
    const exactMatches = users.filter(user => {
      const firstNameExactMatch = !medicalReport.clientFirstName || 
        (user.firstName && 
         user.firstName.toLowerCase() === medicalReport.clientFirstName.toLowerCase());
      
      const lastNameExactMatch = !medicalReport.clientLastName || 
        (user.lastName && 
         user.lastName.toLowerCase() === medicalReport.clientLastName.toLowerCase());
      
      const dobExactMatch = !medicalReport.clientDateOfBirth || 
        (user.birthdate && 
         user.birthdate.toLowerCase() === medicalReport.clientDateOfBirth.toLowerCase());
      
      // All provided criteria must match exactly
      return firstNameExactMatch && lastNameExactMatch && dobExactMatch;
    });

    let filteredUsers;
    
    if (exactMatches.length > 0) {
      // If we have exact matches, return only those
      filteredUsers = exactMatches;
    } else {
      // If no exact matches, fall back to partial matching with OR logic
      filteredUsers = users.filter(user => {
        return (
          (medicalReport.clientFirstName &&
            user.firstName &&
            user.firstName
              .toLowerCase()
              .includes(medicalReport.clientFirstName.toLowerCase())) ||
          (medicalReport.clientLastName &&
            user.lastName &&
            user.lastName
              .toLowerCase()
              .includes(medicalReport.clientLastName.toLowerCase())) ||
          (medicalReport.clientDateOfBirth &&
            user.birthdate &&
            user.birthdate
              .toLowerCase()
              .includes(medicalReport.clientDateOfBirth.toLowerCase()))
        );
      });
    }

    setSearchResults(filteredUsers);
    setIsSearching(false);
  }, [medicalReport.clientFirstName, medicalReport.clientLastName, medicalReport.clientDateOfBirth, users]);

  // Effect for clearing results when all fields are empty
  useEffect(() => {
    if (medicalReport.clientFirstName || medicalReport.clientLastName ||
      medicalReport.clientDateOfBirth
    ) return;

    // clear only once when empty
    setSearchResults([]);
    setShowClientSearch(false);

    // prevent infinite loop by checking before updating
    setMedicalReport(prev => {
      if (prev.selectedClient !== null) {
        resetMedicalData();
        return { ...prev, selectedClient: null };
      }
      return prev;
    });

    // if (medicalReport.clientDateOfBirth !== '') {
    //   updateMedicalReportData('clientDateOfBirth', '');
    // }
  }, [medicalReport.clientFirstName, medicalReport.clientLastName, medicalReport.clientDateOfBirth]);

  useEffect(() => {
    const fetchMedicalDataForSelectedClient = async () => {
      if (!medicalReport.selectedClient) return;

      await fetchMedicalData(medicalReport.selectedClient.id);
    };

    fetchMedicalDataForSelectedClient();
  }, [medicalReport.selectedClient, fetchMedicalData]);

  const updateMedicalReportData = (field: keyof MedicalReport, value: any) => {
    setMedicalReport(prev => ({ ...prev, [field]: value }));
  };

  const normalizeDate = (dateString: string) => {
    return new Date(dateString).toISOString().split('T')[0];
  };

  const selectClient = (client: Client) => {
    setSelectedClient(client);
    updateMedicalReportData('selectedClient', client);
    updateMedicalReportData('clientFirstName', client.firstName);
    updateMedicalReportData('clientLastName', client.lastName);
    updateMedicalReportData('clientDateOfBirth', normalizeDate(client.birthdate as string));
    setShowClientSearch(false);
  };

  const resetForm = () => {
    setMedicalReport({
      id: medicalReport.id,
      userId: medicalReport.userId,
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
      medicalNotes: '',
      medicalPOAReviewed: false,
      medicalCareReportReviewed: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    setSearchResults([]);
    setShowClientSearch(false);
    resetMedicalData();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStepStatus = (step: number) => {
    switch (step) {
      case 1:
        return medicalReport.selectedClient ? 'complete' : 'pending';
      case 2:
        return medicalReport.medicalPOAReviewed ? 'complete' : 'pending';
      case 3:
        return medicalReport.medicalCareReportReviewed ? 'complete' : 'pending';
      case 4:
        return medicalReport.medicalNotes ? 'complete' : 'pending';
      default:
        return 'pending';
    }
  };

  const clearClientData = () => {
    setMedicalReport(prev => ({
      ...prev,
      clientFirstName: '',
      clientLastName: '',
      clientDateOfBirth: '',
      selectedClient: null,
    }));
    setSelectedClient(null);
    setShowClientSearch(false);
    setSearchResults([]);
    resetMedicalData();
  };

  return (
    <div className='max-w-7xl mx-auto p-6 space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-start'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>Medical Review</h1>
          <p className='text-muted-foreground mt-1'>
            Select a client, review Medical POA, and My Care Report, add notes,
            then Save.
          </p>
        </div>
        <div className='text-right'>
          <Label className='text-sm text-muted-foreground'>
            Medical Report Date
          </Label>
          <div className='text-sm font-medium'>
            {formatDate(medicalReport.createdAt)}
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={resetForm}
            className='mt-2'
          >
            <RefreshCw className='h-4 w-4 mr-2' />
            Reset Report
          </Button>
        </div>
      </div>
      {/* Step Progress */}
      <div className='grid grid-cols-4 gap-4'>
        {[1, 2, 3, 4].map(step => {
          const status = getStepStatus(step);
          const titles = [
            'Confirm Client',
            'Review Medical POA',
            'Review My Care Report',
            'Add Medical Review Notes',
          ];

          return (
            <Card key={step}>
              <CardContent className='p-4 text-center'>
                <div
                  className={`w-8 h-8 rounded-full mx-auto mb-2 flex items-center justify-center ${
                    status === 'complete'
                      ? 'bg-green-500 text-white'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {status === 'complete' ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    step
                  )}
                </div>
                <div className='text-sm font-medium'>{titles[step - 1]}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      {/* Medical Report Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <AlertCircle className='h-5 w-5' />
            Medical Report Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-2'>
            <Badge
              variant={
                getStepStatus(1) === 'complete' ? 'default' : 'secondary'
              }
            >
              Client: {medicalReport.clientFirstName}{' '}
              {medicalReport.clientLastName}
            </Badge>
            <Badge
              variant={
                getStepStatus(2) === 'complete' ? 'default' : 'secondary'
              }
            >
              Medical POA Reviewed:{' '}
              {medicalReport.medicalPOAReviewed ? 'Yes' : 'No'}
            </Badge>
            <Badge
              variant={
                getStepStatus(3) === 'complete' ? 'default' : 'secondary'
              }
            >
              My Care Report Reviewed:{' '}
              {medicalReport.medicalCareReportReviewed ? 'Yes' : 'No'}
            </Badge>
            <Badge
              variant={
                getStepStatus(4) === 'complete' ? 'default' : 'secondary'
              }
            >
              Medical Review Notes: {medicalReport.medicalNotes ? 'Yes' : 'No'}
            </Badge>
          </div>
        </CardContent>
      </Card>
      {/* Step 1: Client Lookup */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <UserCheck className='h-5 w-5' />
            Step 1: Confirm Childfree Trust Client
          </CardTitle>
          <CardDescription>
            Search and confirm the client identity
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='clientFirstName'>
                Client First Name <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='clientFirstName'
                value={medicalReport.clientFirstName as string}
                onChange={e =>
                  {updateMedicalReportData('clientFirstName', e.target.value)
                  setSelectedClient(null)}
                }
                placeholder='First name'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='clientLastName'>
                Client Last Name <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='clientLastName'
                value={medicalReport.clientLastName as string}
                onChange={e =>
                  {updateMedicalReportData('clientLastName', e.target.value)
                  setSelectedClient(null)}
                }
                placeholder='Last name'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='clientDateOfBirth'>
              Date of Birth <span className='text-red-500'>*</span>
            </Label>
            <DatePicker
              date={
                medicalReport.clientDateOfBirth
                  ? new Date(medicalReport.clientDateOfBirth)
                  : undefined
              }
              setDate={date => {
                updateMedicalReportData('clientDateOfBirth', date?.toISOString());
                setSelectedClient(null);
              }
                
              }
            />
            {/* <Input
              id='clientDateOfBirth'
              type='date'
              value={medicalReport.clientDateOfBirth as string}
              onChange={e =>
                {updateMedicalReportData('clientDateOfBirth', e.target.value)
                setSelectedClient(null)
              }}
            /> */}
          </div>

          {/* Client Search Results */}
          {showClientSearch && (
            <div className='space-y-2'>
              <Label className='flex items-center gap-2'>
                <Search className='h-4 w-4' />
                Client Search Results
              </Label>

              {isSearching ? (
                <div className='text-center py-4'>
                  <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto'></div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Searching...
                  </p>
                </div>
              ) : searchResults.length > 0 ? (
                <div className='border rounded-md'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>DOB</TableHead>
                        {/* <TableHead>Contact</TableHead> */}
                        <TableHead>Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {searchResults.map(client => (
                        <TableRow
                          key={client.id}
                          className={
                            medicalReport.selectedClient?.id === client.id
                              ? 'bg-muted'
                              : ''
                          }
                        >
                          <TableCell className='font-medium'>
                            {client.firstName} {client.lastName}
                          </TableCell>
                          <TableCell>
                            {new Date(
                              client.birthdate ? client.birthdate : ''
                            ).toLocaleDateString()}
                          </TableCell>
                          {/* <TableCell>
                            <div className='text-sm'>
                              {client.phoneNumber && (
                                <div className='flex items-center gap-1'>
                                  <Phone className='h-3 w-3' />
                                  {client.phoneNumber}
                                </div>
                              )}
                              <div className='flex items-center gap-1'>
                                <Mail className='h-3 w-3' />
                                {client.email}
                              </div>
                            </div>
                          </TableCell> */}
                          <TableCell>
                            <Button
                              size='sm'
                              variant={
                                medicalReport.selectedClient?.id === client.id
                                  ? 'default'
                                  : 'outline'
                              }
                              onClick={() => selectClient(client)}
                            >
                              {medicalReport.selectedClient?.id === client.id
                                ? 'Selected'
                                : 'Select'}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    No matching clients found. Please verify the name spelling
                    or contact support.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
          <div className='flex justify-end'>
            <Button
              onClick={clearClientData}
              variant='outline'
              size='sm'
            >
              Clear Client Information
            </Button>
          </div>
        </CardContent>
      </Card>
      {/* Step 2: Medical POA (interview data) */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <UserCheck className='h-5 w-5' />
            Step 2: Medical POA (view-only)
          </CardTitle>
          <CardDescription>Medical POA information</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {!medicalDataLoading && (
            <div className='border rounded-md p-4 text-sm'>
              <div
                className='prose'
                dangerouslySetInnerHTML={{
                  __html: cleanHTML(medicalData.interviewData),
                }}
              />
            </div>
          )}
          {medicalDataLoading && (
            <div className='text-center py-4'>
              <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto'></div>
              <p className='text-sm text-muted-foreground mt-2'>Loading...</p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Checkbox
            checked={medicalReport.medicalPOAReviewed as boolean}
            className='cursor-pointer'
            onCheckedChange={checked =>
              updateMedicalReportData('medicalPOAReviewed', checked as boolean)
            }
          />
          <span className='ml-2 text-sm'>I have reviewed the Medical POA</span>
        </CardFooter>
      </Card>
      {/* Step 3: Medical POA (care dacuments data) */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <UserCheck className='h-5 w-5' />
            Step 3: My Care Medical Report (view-only)
          </CardTitle>
          <CardDescription>My Care Report information</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {!medicalDataLoading && (
            <div className='border rounded-md p-4 text-sm'>
              <div
                className='prose'
                dangerouslySetInnerHTML={{
                  __html: cleanHTML(medicalData.careDocuments),
                }}
              />
            </div>
          )}
          {medicalDataLoading && (
            <div className='text-center py-4'>
              <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto'></div>
              <p className='text-sm text-muted-foreground mt-2'>Loading...</p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Checkbox
            checked={medicalReport.medicalCareReportReviewed as boolean}
            className='cursor-pointer'
            onCheckedChange={checked =>
              updateMedicalReportData(
                'medicalCareReportReviewed',
                checked as boolean
              )
            }
          />
          <span className='ml-2 text-sm'>
            I have reviewed the My Care Report
          </span>
        </CardFooter>
      </Card>
      {/* Step 4: Medical Review Notes */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <UserCheck className='h-5 w-5' />
            Step 4: Medical Review Notes
          </CardTitle>
          <CardDescription>
            Add any notes from your medical review
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <Textarea
            value={medicalReport.medicalNotes as string}
            onChange={e =>
              updateMedicalReportData('medicalNotes', e.target.value)
            }
            placeholder='Enter medical review notes...'
            rows={4}
          />
        </CardContent>
      </Card>
    </div>
  );
}
