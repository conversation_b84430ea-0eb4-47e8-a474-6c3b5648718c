'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { fetchAttorneys } from '@/lib/data/attorneys';
import { Attorney } from '@/types/attorney-reviews';
import { useAuth } from '@/app/context/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import { US_STATES } from '@/lib/api/attorneys';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';
import {
  Scale,
  Phone,
  Mail,
  MapPin,
  ArrowLeft,
  Search,
  CheckCircle,
  AlertCircle,
  // ExternalLink,
} from 'lucide-react';

function AttorneyListContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, userExternalId } = useAuth();
  const [attorneys, setAttorneys] = useState<Attorney[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCity, setSelectedCity] = useState<string>('all');
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  const [accessAuthorized, setAccessAuthorized] = useState(false);

  // Fetch user profile to get real state data
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId, userExternalId],
    queryFn: () => fetchUserProfileByCognitoId(userExternalId || ''),
    enabled: !!user?.userId && !!userExternalId,
  });

  // Get user state from profile or fallback to California
  const userStateFromProfile = userProfile?.state || 'California';

  // Convert state name to state code for filtering (since attorneys store state codes)
  const getStateCode = (stateName: string) => {
    const state = US_STATES.find(s => s.name === stateName);
    return state ? state.code : stateName;
  };

  // Get state code for filtering attorneys
  const userStateCode = getStateCode(userStateFromProfile);

  // Check if user accessed this page from document review
  useEffect(() => {
    const checkAccess = () => {
      // Check if user came from document review page
      const sessionFlag = sessionStorage.getItem('attorney-review-requested');
      const fromParam = searchParams.get('from');

      console.log('Access check:', {
        sessionFlag,
        fromParam,
        hasSessionFlag: sessionFlag === 'true',
        hasFromParam: fromParam === 'review',
        timestamp: new Date().toISOString(),
      });

      // Check both session storage and URL parameter
      const hasValidAccess = sessionFlag === 'true' || fromParam === 'review';

      if (!hasValidAccess) {
        console.log('Access denied - redirecting to documents page');
        // Redirect to document review if not authorized
        alert(
          'Attorney list can only be accessed through the "Request Attorney Review" button during document review.'
        );
        router.push('/member/documents');
        return;
      }

      console.log('Access authorized - setting state');
      setAccessAuthorized(true);
      // Clear the session flag after successful access
      sessionStorage.removeItem('attorney-review-requested');
    };

    // Add a small delay to ensure session storage is set
    const timer = setTimeout(checkAccess, 200);
    return () => clearTimeout(timer);
  }, [router, searchParams]);

  useEffect(() => {
    // Only load attorneys if access is authorized and user profile is available
    if (!accessAuthorized || !userProfile) return;

    // Load attorneys from Amplify backend
    const loadAttorneys = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all attorneys from backend
        const allAttorneys = await fetchAttorneys();

        // Filter attorneys by user's state and only active ones
        const stateFilteredAttorneys = allAttorneys.filter(
          attorney => attorney.state === userStateCode && attorney.isActive
        );

        setAttorneys(stateFilteredAttorneys);
      } catch (err) {
        console.error('Error loading attorneys:', err);
        setError('Failed to load attorneys. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadAttorneys();
  }, [accessAuthorized, userProfile, userStateCode]);

  // Filter attorneys based on search and filters
  const filteredAttorneys = attorneys.filter(attorney => {
    const matchesSearch =
      attorney.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attorney.firm.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCity =
      selectedCity === 'all' || attorney.city === selectedCity;
    const matchesSpecialty =
      selectedSpecialty === 'all' ||
      attorney.specialties.some(s =>
        s.toLowerCase().includes(selectedSpecialty.toLowerCase())
      );

    return matchesSearch && matchesCity && matchesSpecialty;
  });

  // Get unique cities and specialties for filters
  const cities = Array.from(new Set(attorneys.map(a => a.city))).sort();
  const specialties = Array.from(
    new Set(attorneys.flatMap(a => a.specialties))
  ).sort();

  // const handleContactAttorney = (attorney: Attorney) => {
  //   // In a real implementation, this would log the attorney review request
  //   alert(
  //     `Contacting ${attorney.name} at ${attorney.firm}. You will be redirected to proceed with signing.`
  //   );
  //   router.push('/member/documents');
  // };

  // const handleSkipAttorneyReview = () => {
  //   router.push('/member/documents');
  // };

  const handleProceedToSigning = () => {
    router.push('/member/documents');
  };

  const handleBackToReview = () => {
    router.push('/member/documents');
  };

  // Don't render anything if access is not authorized
  if (!accessAuthorized) {
    return null;
  }

  if (loading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-6xl mx-auto'>
          <div className='mb-8'>
            <Skeleton className='h-10 w-1/3 mb-2' />
            <Skeleton className='h-5 w-1/2' />
          </div>
          <div className='grid gap-4'>
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className='h-48 w-full' />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-6xl mx-auto'>
          <Alert variant='destructive'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <div className='flex items-center gap-4 mb-4'>
            <Button variant='outline' size='sm' onClick={handleBackToReview}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Review
            </Button>
          </div>
          <Headline className='mb-2'>
            Preferred Attorneys - {userStateFromProfile}
          </Headline>
          <Subhead className='text-muted-foreground'>
            Select a state-licensed attorney to review your estate planning
            documents. Attorney reviews are included in your membership.
          </Subhead>
        </div>

        {/* Information Card */}
        <Card className='mb-8 bg-blue-50 border-blue-200'>
          <CardHeader>
            <CardTitle className='text-blue-800 flex items-center gap-2'>
              <Scale className='h-5 w-5' />
              Attorney Review Process
            </CardTitle>
          </CardHeader>
          <CardContent className='text-blue-700'>
            <ul className='space-y-2'>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>1.</span>
                <span>Select an attorney from our preferred list below</span>
              </li>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>2.</span>
                <span>
                  Contact them directly using the provided information
                </span>
              </li>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>3.</span>
                <span>
                  Return to the platform after review to proceed with signing
                </span>
              </li>
            </ul>
            {/* <div className='mt-4 p-3 bg-blue-100 rounded-md'>
              <p className='text-sm font-medium'>
                Target completion: 5 business days • Included in membership
              </p>
            </div> */}
          </CardContent>
        </Card>

        {/* Search and Filters */}
        <Card className='mb-6'>
          <CardContent className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div className='relative'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search attorneys or firms...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
              <Select value={selectedCity} onValueChange={setSelectedCity}>
                <SelectTrigger>
                  <SelectValue placeholder='All Cities' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Cities</SelectItem>
                  {cities.map(city => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={selectedSpecialty}
                onValueChange={setSelectedSpecialty}
              >
                <SelectTrigger>
                  <SelectValue placeholder='All Specialties' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Specialties</SelectItem>
                  {specialties.map(specialty => (
                    <SelectItem key={specialty} value={specialty}>
                      {specialty}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {/* <Button
                variant='outline'
                onClick={handleSkipAttorneyReview}
                className='w-full'
              >
                Skip Attorney Review
              </Button> */}
            </div>
          </CardContent>
        </Card>

        {/* Attorney List */}
        {attorneys.length === 0 ? (
          // No attorneys available for user's state
          <Card>
            <CardContent className='p-8 text-center'>
              <AlertCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
              <p className='text-lg font-medium mb-2'>
                No attorneys available for your state
              </p>
              <p className='text-muted-foreground mb-4'>
                No attorneys available for your state. Proceed to signing or
                contact support.
              </p>
              <div className='flex gap-4 justify-center'>
                <Button onClick={handleProceedToSigning}>
                  Proceed to Signing
                </Button>
                <Button
                  variant='outline'
                  onClick={() => {
                    // In real implementation, this would open support contact form
                    alert(
                      'Contact support functionality would be implemented here'
                    );
                  }}
                >
                  Contact Support
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : filteredAttorneys.length === 0 ? (
          // No attorneys match search criteria
          <Card>
            <CardContent className='p-8 text-center'>
              <AlertCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
              <p className='text-lg font-medium mb-2'>No attorneys found</p>
              <p className='text-muted-foreground mb-4'>
                No attorneys match your current search criteria. Try adjusting
                your filters.
              </p>
              <Button
                variant='outline'
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCity('all');
                  setSelectedSpecialty('all');
                }}
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className='space-y-4'>
            {filteredAttorneys.map(attorney => (
              <Card
                key={attorney.id}
                className='hover:shadow-md transition-shadow'
              >
                <CardContent className='p-6'>
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-3 mb-2'>
                        <h3 className='text-xl font-semibold'>
                          {attorney.name}
                        </h3>
                        {attorney.isPreferred && (
                          <Badge
                            variant='secondary'
                            className='bg-green-100 text-green-800'
                          >
                            <CheckCircle className='h-3 w-3 mr-1' />
                            Preferred
                          </Badge>
                        )}
                        {attorney.rating && (
                          <Badge variant='outline'>★ {attorney.rating}</Badge>
                        )}
                      </div>
                      <p className='text-lg text-muted-foreground mb-3'>
                        {attorney.firm}
                      </p>

                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                        <div className='space-y-2'>
                          <div className='flex items-center gap-2 text-sm'>
                            <Phone className='h-4 w-4 text-muted-foreground' />
                            <span>{attorney.phone}</span>
                          </div>
                          <div className='flex items-center gap-2 text-sm'>
                            <Mail className='h-4 w-4 text-muted-foreground' />
                            <span>{attorney.email}</span>
                          </div>
                          <div className='flex items-center gap-2 text-sm'>
                            <MapPin className='h-4 w-4 text-muted-foreground' />
                            <span>
                              {attorney.address}, {attorney.city},{' '}
                              {attorney.state} {attorney.zipCode}
                            </span>
                          </div>
                        </div>
                        <div className='space-y-2'>
                          <div className='text-sm'>
                            <span className='font-medium'>Bar Number:</span>{' '}
                            {attorney.barNumber}
                          </div>
                          <div className='text-sm'>
                            <span className='font-medium'>Experience:</span>{' '}
                            {attorney.yearsExperience} years
                          </div>
                          <div className='text-sm'>
                            <span className='font-medium'>Specialties:</span>
                            <div className='flex flex-wrap gap-1 mt-1'>
                              {attorney.specialties.map((specialty, index) => (
                                <Badge
                                  key={index}
                                  variant='outline'
                                  className='text-xs'
                                >
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* <div className='ml-6'>
                      <Button
                        onClick={() => handleContactAttorney(attorney)}
                        className='bg-blue-600 hover:bg-blue-700'
                      >
                        <ExternalLink className='h-4 w-4 mr-2' />
                        Contact Attorney
                      </Button>
                    </div> */}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Footer Actions */}
        {/* <div className='mt-8 flex justify-between items-center'>
          <Button variant='outline' onClick={handleBackToReview}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Document Review
          </Button>
          <Button variant='outline' onClick={handleSkipAttorneyReview}>
            Skip Attorney Review & Proceed to Signing
          </Button>
        </div> */}
      </div>
    </div>
  );
}

export default function AttorneyListPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <AttorneyListContent />
    </Suspense>
  );
}
