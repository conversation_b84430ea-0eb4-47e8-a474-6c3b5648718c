'use client';

import { useState } from 'react';
import { ContactForm } from '@/components/emergency-contacts/ContactForm';
import {
  useEmergencyContacts,
  type EmergencyContact,
} from '@/hooks/useEmergencyContacts';
import { useRouter } from 'next/navigation';
import { ContactFormSkeleton } from '@/components/ui/skeletons/contact-form-skeleton';
import { useAuth } from '@/context/AuthContext';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import { useQuery } from '@tanstack/react-query';

// Define a storage key constant
const STORAGE_KEY = 'emergency-contact-form-data';

export default function AddContactPage() {
  const router = useRouter();
  const { user, userExternalId } = useAuth();
  const { addContact } = useEmergencyContacts();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId, userExternalId],
    queryFn: () => fetchUserProfileByCognitoId(userExternalId || ''),
    enabled: !!user?.userId && !!userExternalId,
  });

  const handleAddContact = async (formData: Omit<EmergencyContact, 'id'>) => {
    setIsSubmitting(true);
    try {
      await addContact(
        formData,
        userProfile?.firstName + ' ' + userProfile?.lastName
      );
      return; // The form component will handle navigation and toast
    } catch (error) {
      console.error('Error adding contact:', error);
      setIsSubmitting(false);
      throw error;
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      {isSubmitting ? (
        <ContactFormSkeleton />
      ) : (
        <ContactForm
          onSubmit={handleAddContact}
          isEditing={false}
          isLoading={false}
          storageKey={STORAGE_KEY}
        />
      )}
    </div>
  );
}
