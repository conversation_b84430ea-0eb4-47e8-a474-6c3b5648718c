'use client';

import { usePathname } from 'next/navigation';
import { Sidebar } from '@/components/dashboard/sidebar';
import { useRole } from '@/lib/roles/role-context';
import { useAuth } from '@/app/context/AuthContext';
import routes from '@/utils/routes';
import { AdminContainer } from '@/components/ui/container';
import { OnboardingGuard } from '@/lib/auth';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const { userContext } = useRole();
  const { onboardingStatus, userRoles, loading: isLoadingUser } = useAuth();

  // Check if current page is onboarding
  const isOnboardingPage = pathname === routes.member.onboarding;

  // Map role context to sidebar role format
  const getSidebarRole = ():
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional' => {
    if (!userContext) return 'Member';

    switch (userContext.role) {
      case 'administrator':
        return 'Administrator';
      case 'welon_trust':
        return 'Welon Trust';
      case 'linked_account':
        return 'Member';
      default:
        return 'Member';
    }
  };

  const userRole = getSidebarRole();

  // Show layout without sidebar for onboarding pages
  if (isOnboardingPage) {
    return (
      <OnboardingGuard>
        <div className='min-h-screen bg-background'>
          <main>
            <AdminContainer>{children}</AdminContainer>
          </main>
        </div>
      </OnboardingGuard>
    );
  }

  return (
    <OnboardingGuard>
        <div className='min-h-screen flex bg-background'>
          <Sidebar userRole={userRole} />
          <div className='flex-1'>
            <main>
              <AdminContainer>{children}</AdminContainer>
            </main>
          </div>
        </div>
    </OnboardingGuard>
  );
}
