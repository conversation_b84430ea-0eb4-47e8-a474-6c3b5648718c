'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import routes from '@/utils/routes';
import { UserDocumentsList } from '@/app/components/user-documents/user-documents-list';
import { useAuth } from '@/app/context/AuthContext';
import { useEmergencyContacts } from '@/hooks/useEmergencyContacts';
import { useDeadMansSwitch } from '@/hooks/useDeadMansSwitch';
import { useQuery } from '@tanstack/react-query';
import { getUserDocumentsByUserId } from '@/lib/api/documents';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { ExternalLink } from 'lucide-react';

const client = generateClient<Schema>();

// Function to check if user has completed InterviewV2
const checkUserInterviewStatus = async () => {
  try {
    // First check InterviewV2
    try {
      const { loadInterviewProgressV2 } = await import(
        '@/app/utils/interviewV2Progress'
      );
      const progressData = await loadInterviewProgressV2();

      // Check if InterviewV2 has any meaningful data
      const hasV2Data =
        progressData?.stepsData &&
        (progressData.stepsData.profile?.firstName ||
          progressData.stepsData.people?.length > 0 ||
          progressData.stepsData.emergency ||
          progressData.stepsData.afterYouDie ||
          progressData.stepsData.financial ||
          progressData.stepsData.medical ||
          progressData.stepsData.additional);

      if (hasV2Data) {
        return { hasCompletedInterview: true, hasStartedInterview: true };
      }
    } catch (error) {
      console.log('InterviewV2 not available, checking old interviews');
    }

    // Fallback to old interview check
    const currentUser = await getCurrentUser();

    const { data: users } = await client.models.User.list({
      filter: {
        cognitoId: {
          eq: currentUser.userId,
        },
      },
      selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
    });

    if (!users || users.length === 0) {
      return { hasCompletedInterview: false, hasStartedInterview: false };
    }

    const user = users[0];
    const interviewProgress = user.interviewProgress || [];

    // Check if user has any completed interviews
    const hasCompletedInterview = interviewProgress.some(
      progress => progress && progress.isCompleted === true
    );

    // Check if user has started any interview (has any progress)
    const hasStartedInterview = interviewProgress.some(
      progress => progress && progress.answers && progress.answers.length > 0
    );

    return { hasCompletedInterview, hasStartedInterview };
  } catch (error) {
    console.error('Error checking interview status:', error);
    return { hasCompletedInterview: false, hasStartedInterview: false };
  }
};

export default function DashboardPage() {
  const { user, userExternalId, userId } = useAuth();

  // Fetch real emergency contacts
  const { contacts } = useEmergencyContacts();

  // Fetch real DMS configuration
  const { config: dmsConfig, loading: dmsLoading } = useDeadMansSwitch();

  // Add state for carousel
  const [currentContactIndex, setCurrentContactIndex] = useState(0);

  const nextContact = () => {
    setCurrentContactIndex((prevIndex: number) =>
      prevIndex === contacts.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevContact = () => {
    setCurrentContactIndex((prevIndex: number) =>
      prevIndex === 0 ? contacts.length - 1 : prevIndex - 1
    );
  };

  const goToContact = (index: number) => {
    setCurrentContactIndex(index);
  };

  // Fetch user documents
  const { data: documents = [], isLoading: documentsLoading } = useQuery({
    queryKey: ['user-documents', userId],
    queryFn: () => getUserDocumentsByUserId(userId || ''),
    enabled: !!userId,
  });

  // Fetch user profile
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId, userExternalId],
    queryFn: () => fetchUserProfileByCognitoId(userExternalId || ''),
    enabled: !!user?.userId && !!userExternalId,
  });

  // Check interview status
  const { data: interviewStatus } = useQuery({
    queryKey: ['interview-status', user?.userId],
    queryFn: checkUserInterviewStatus,
    enabled: !!user?.userId,
  });

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Helper function to get check-in interval description
  const getCheckInInterval = (
    frequency?: string,
    customDays?: number | null
  ) => {
    if (!frequency) return '';

    switch (frequency) {
      case 'WEEKLY':
        return '7 days';
      case 'BIWEEKLY':
        return '14 days';
      case 'MONTHLY':
        return '30 days';
      case 'CUSTOM':
        return customDays ? `${customDays} days` : 'Custom interval';
      default:
        return frequency.toLowerCase();
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center gap-4 mb-8'>
        <div className='flex flex-col space-y-2 mr-12'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            Welcome to Your Childfree Trust Dashboard
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            You create estate documents and care plans. We step in as your next
            of kin when you need us.
          </p>
        </div>
      </div>

      {/* New Navigation Box */}
      <div className='bg-background p-6 rounded-lg shadow-sm mb-6 border'>
        <h2 className='text-xl font-semibold mb-3 text-[var(--foreground)]'>
          Start Here: Create Your Estate Planning Documents
        </h2>
        <p className='text-[var(--custom-gray-dark)] mb-4'>
          Begin your estate plan with our patented Childfree Trust® system. In
          just a few steps, you can complete your documents and join as a paid
          member for instant legal protection — with us serving as your medical
          and financial POA, trustee, and executor.
        </p>
        <div className='space-y-2'>
          <div className='flex items-start'>
            <span className='mr-2 font-medium'>1.</span>
            <Link
              className='text-left font-bold text-blue-600 hover:text-blue-800 hover:underline cursor-pointer flex items-center gap-1'
              href={routes.member.interviewV2}
            >
              <ExternalLink className='w-4 h-4' />
              Fill out "My Info"
            </Link>
          </div>
          <div className='flex items-start text-[var(--custom-gray-dark)]'>
            <span className='mr-2'>2.</span>
            <span>Complete your legal documents in the system</span>
          </div>
          <div className='flex items-start text-[var(--custom-gray-dark)]'>
            <span className='mr-2'>3.</span>
            <span>Fill out My Care</span>
          </div>
          <div className='flex items-start text-[var(--custom-gray-dark)]'>
            <span className='mr-2'>4.</span>
            <span>My Emergency Contacts</span>
          </div>
          <div className='flex items-start text-[var(--custom-gray-dark)]'>
            <span className='mr-2'>5.</span>
            <span>My Wellness Checks</span>
          </div>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* User Documents Section - Real documents assigned by Welon Trust */}
        <div className='lg:col-span-2'>
          <UserDocumentsList />
        </div>

        {/* Estate Planning Documents Section */}
        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Your Documents</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your estate planning documents
              </p>
            </div>
            {/* Show Start Interview button only if user hasn't completed any interview */}
            {!interviewStatus?.hasCompletedInterview && (
              <div className='flex items-center gap-2'>
                {/* <Button variant='default' size='lg' asChild>
                  <Link href={routes.member.interview}>
                    {interviewStatus?.hasStartedInterview
                      ? 'Continue Interview'
                      : 'Start Interview'}
                  </Link>
                </Button> */}
                <Button variant='outline' size='lg' asChild>
                  <Link href={routes.member.interviewV2}>Try Interview V2</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Real document status */}
          <div className='space-y-2 mb-3 flex-grow'>
            {documentsLoading ? (
              <div className='bg-[var(--background)] border-l-4 border-gray-400 p-2 rounded-r-lg text-sm'>
                <p>📄 Loading documents...</p>
              </div>
            ) : documents.length > 0 ? (
              <div className='bg-[var(--eggplant)]/10 border-l-4 border-[var(--eggplant)] p-2 rounded-r-lg text-sm'>
                <p>
                  📄 {documents.length} document
                  {documents.length !== 1 ? 's' : ''} available
                </p>
              </div>
            ) : interviewStatus?.hasCompletedInterview ? (
              <div className='bg-blue-50 border-l-4 border-blue-400 p-2 rounded-r-lg text-sm'>
                <p>📄 Interview completed. Documents are being processed.</p>
              </div>
            ) : interviewStatus?.hasStartedInterview ? (
              <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
                <p>
                  📄 Interview in progress. Complete it to generate documents.
                </p>
              </div>
            ) : (
              <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
                <p>
                  📄 No documents yet. Start your interview to create documents.
                </p>
              </div>
            )}
          </div>

          <div className='grid grid-cols-1 gap-2 mt-auto'>
            <Button variant='default' size='default' asChild>
              <Link href={routes.member.documents}>Manage Documents</Link>
            </Button>
            {/* <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/documents/create')}
            >
              Create New
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageReview)}
            >
              Review Documents
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageSign)}
            >
              Sign Documents
            </Button> */}
          </div>
        </div>

        {/* Shared Documents Section */}
        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Shared Documents</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Collaborate on joint estate planning
              </p>
            </div>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              View All
            </Button>
          </div>

          <div className='bg-blue-50 border-l-4 border-blue-400 p-2 rounded-r-lg mb-3 text-sm flex-grow'>
            <p>
              💡 Link accounts to create shared documents like joint trusts.
            </p>
          </div>

          <div className='flex gap-2 mt-auto'>
            <Button
              variant='secondary'
              size='default'
              className='flex-1'
              onClick={() =>
                router.push('/dashboard/member/settings/linked-accounts')
              }
            >
              Link Account
            </Button>
            <Button
              variant='default'
              size='default'
              className='flex-1'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              Shared Docs
            </Button>
          </div>
        </div> */}

        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Emergency Contacts</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage who can access your information
              </p>
            </div>
            <Button variant='default' size='default' asChild>
              <Link href={routes.member.emergencyContacts}>
                {contacts.length > 0 ? 'Manage' : 'Add'} Contacts
              </Link>
            </Button>
          </div>

          {contacts.length > 0 ? (
            <div className='bg-[var(--lime)]/20 border-l-4 border-[var(--lime)] p-2 rounded-r-lg text-sm'>
              <p>
                ✅ You've added {contacts.length} emergency contact
                {contacts.length !== 1 ? 's' : ''}.
              </p>
            </div>
          ) : (
            <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
              <p>⚠️ No emergency contacts added yet.</p>
            </div>
          )}

          {contacts.length > 0 && contacts[currentContactIndex] && (
            <div className='mt-2'>
              <div className='flex items-center'>
                {contacts.length > 1 && (
                  <button
                    onClick={prevContact}
                    className='flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors mr-2 shadow-sm'
                  >
                    <ChevronLeft className='w-4 h-4 text-gray-700' />
                  </button>
                )}

                <div className='flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200 flex-1'>
                  <div className='w-6 h-6 rounded-full bg-dark-blue text-[var(--off-black)] flex items-center justify-center mr-2 text-xs'>
                    {contacts[currentContactIndex].fullName.charAt(0)}
                  </div>
                  <div className='flex-1'>
                    <p className='font-medium text-[var(--off-black)] text-sm'>
                      {contacts[currentContactIndex].fullName}
                    </p>
                    <p className='text-xs text-[var(--off-black)]'>
                      {contacts[currentContactIndex].relationship}
                    </p>
                  </div>
                  {contacts[currentContactIndex].isPrimaryForType && (
                    <span className='ml-auto bg-success-green text-white text-xs px-2 py-0.5 rounded-full'>
                      Primary
                    </span>
                  )}
                </div>

                {contacts.length > 1 && (
                  <button
                    onClick={nextContact}
                    className='flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors ml-2 shadow-sm'
                  >
                    <ChevronRight className='w-4 h-4 text-gray-700' />
                  </button>
                )}
              </div>

              {contacts.length > 1 && (
                <div className='flex justify-center items-center mt-2 space-x-1'>
                  {contacts.map((_, index: number) => (
                    <button
                      key={index}
                      onClick={() => goToContact(index)}
                      className={`w-1.5 h-1.5 rounded-full transition-colors ${
                        index === currentContactIndex
                          ? 'bg-[var(--eggplant)]'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div> */}

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Wellness Checks</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Automated safety check system
              </p>
            </div>
            <Button variant='default' size='default' asChild>
              <Link href={routes.member.wellnessChecks}>
                {dmsLoading
                  ? 'Loading...'
                  : dmsConfig
                    ? dmsConfig.status === 'ACTIVE'
                      ? 'Manage'
                      : 'Resume'
                    : 'Configure'}
              </Link>
            </Button>
          </div>

          {dmsLoading ? (
            <div className='bg-[var(--background)] border-l-4 border-gray-400 p-2 rounded-r-lg text-sm'>
              <p>⏳ Loading Wellness Checks status...</p>
            </div>
          ) : dmsConfig?.status === 'ACTIVE' ? (
            <div className='bg-[var(--lime)]/20 border-l-4 border-[var(--lime)] p-2 rounded-r-lg text-sm space-y-1'>
              <p>✅ Your Wellness Checks is active and working.</p>
              {dmsConfig.lastCheckIn && (
                <p className='text-xs text-[var(--foreground)]'>
                  Last confirmed: {formatDate(dmsConfig.lastCheckIn)}
                </p>
              )}
              {dmsConfig.nextCheckIn && (
                <p className='text-xs text-[var(--foreground)]'>
                  Next check-in: {formatDate(dmsConfig.nextCheckIn)} (
                  {getCheckInInterval(
                    dmsConfig.frequency,
                    dmsConfig.customFrequencyDays
                  )}
                  )
                </p>
              )}
            </div>
          ) : (
            <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
              <p>⚠️ Your Wellness Checks is not configured yet.</p>
            </div>
          )}
        </div>

        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Subscription & Billing</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your subscription
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/billing')}
            >
              Manage Billing
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center justify-between'>
              <span className='font-medium'>Basic Plan</span>
              <span className='bg-dark-blue text-white px-2 py-0.5 rounded-full text-xs'>
                Monthly
              </span>
            </div>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Linking</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Share access with trusted individuals
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/account-linking')}
            >
              Manage Links
            </Button>
          </div>

          <div className='bg-amber-50 border-l-4 border-amber-400 p-2 rounded-r-lg text-sm'>
            <p>⚠️ No linked accounts yet.</p>
          </div>
        </div> */}

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Settings</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your profile and preferences
              </p>
            </div>
            <Button variant='default' size='default' asChild>
              <Link href={routes.member.settings}>Settings</Link>
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center'>
              <div className='w-8 h-8 rounded-full bg-dark-blue text-[var(--off-black)] flex items-center justify-center mr-2 text-sm font-medium'>
                {userProfile
                  ? `${userProfile.firstName.charAt(0)}${userProfile.lastName.charAt(0)}`
                  : user?.signInDetails?.loginId?.charAt(0)?.toUpperCase() ||
                    'U'}
              </div>
              <div>
                <p className='font-medium text-[var(--off-black)] text-sm'>
                  {userProfile
                    ? `${userProfile.firstName} ${userProfile.lastName}`
                    : user?.signInDetails?.loginId || 'User'}
                </p>
                <p className='text-xs text-[var(--off-black)]'>
                  {userProfile?.email ||
                    user?.signInDetails?.loginId ||
                    'No email'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Educational Content</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Videos, articles & guides
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/educational-content')}
            >
              Browse Content
            </Button>
          </div>

          <div className='bg-green-50 p-2 rounded-lg border border-green-200 text-sm'>
            <p>
              📚 Expand your knowledge with our comprehensive library of estate
              planning resources.
            </p>
          </div>
        </div> */}
      </div>
    </div>
  );
}
