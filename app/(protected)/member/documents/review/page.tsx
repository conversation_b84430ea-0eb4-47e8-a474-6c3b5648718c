'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { PDFViewer } from '@/components/documents/pdf-viewer';
import { Document } from '@/types/documents';
import {
  ArrowLeft,
  Eye,
  PenTool,
  Download,
  CheckCircle,
  AlertCircle,
  Users,
  FileText,
  RefreshCw,
  Info,
} from 'lucide-react';
import routes from '@/utils/routes';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  getDocumentById,
  DocumentResponse,
  signDocument,
} from '@/lib/api/documents';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { useMentalCapacityCheck } from '@/hooks/useMentalCapacityCheck';
import { isWelonTrust } from '@/lib/utils/admin-utils';

function DocumentReviewContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, userRoles } = useAuth();
  const documentId = searchParams.get('id');
  const mode = searchParams.get('mode'); // 'preview' or normal review

  // Check mental capacity for access control
  const { isOfSoundMind, isLoading: mentalCapacityLoading } =
    useMentalCapacityCheck();

  // Query for real document data
  const {
    data: realDocument,
    isLoading: realDocumentLoading,
    error: realDocumentError,
    refetch: refetchDocument,
  } = useQuery({
    queryKey: ['document', documentId],
    queryFn: () => getDocumentById(documentId!),
    enabled: !!documentId && !!user, // Only fetch for authenticated users with documentId
    refetchOnWindowFocus: false,
  });

  // Mutation for signing document
  const signDocumentMutation = useMutation({
    mutationFn: (docId: string) => signDocument(docId, 'electronic'),
    onSuccess: () => {
      toast.success('Document signed successfully!');
      refetchDocument(); // Refresh document data to show updated status
    },
    onError: error => {
      console.error('Error signing document:', error);
      toast.error('Failed to sign document. Please try again.');
    },
  });

  // Determine document and current state
  const document = realDocument ? convertDocumentResponse(realDocument) : null;
  const loading = realDocumentLoading;
  const error = realDocumentError;

  // Convert DocumentResponse to Document interface
  function convertDocumentResponse(docResponse: DocumentResponse): Document {
    return {
      id: docResponse.id,
      title: docResponse.title,
      type: docResponse.type as Document['type'],
      status: docResponse.status as Document['status'],
      dateCreated: docResponse.dateCreated,
      lastModified: docResponse.lastModified || docResponse.dateCreated,
      version: docResponse.version,
      content: docResponse.content,
      userId: docResponse.userId,
      fileUrl: docResponse.fileUrl,
      trackingNumber: docResponse.trackingNumber,
      signatureType: docResponse.signatureType as Document['signatureType'],
      executionDate: docResponse.executionDate,
    };
  }

  const handleDownload = async () => {
    if (!document) return;
    try {
      if (isPreviewMode) {
        toast.success('Preview downloaded successfully!');
      } else {
        toast.success('Document downloaded successfully!');
      }
    } catch (error) {
      toast.error('Failed to download document');
      console.error('Download error:', error);
    }
  };

  const handleRequestAttorneyReview = () => {
    // Set session flag to authorize access to attorney list
    sessionStorage.setItem('attorney-review-requested', 'true');
    // Also pass a URL parameter as backup
    router.push('/member/attorney-list?from=review');
  };

  const handleProceedToSigning = () => {
    if (!document) return;
    router.push(`${routes.documentsSign}?id=${document.id}`);
  };

  const handleBackToManage = () => {
    router.push(routes.documentsManage);
  };

  const handleEdit = () => {
    // Navigate to interview/edit page
    router.push('/member/interview');
  };

  // Handle missing document ID
  if (!documentId) {
    return (
      <Alert className='max-w-md mx-auto' variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>No document ID provided</AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center max-w-md'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Document
          </h3>
          <p className='text-red-600 mb-4'>
            {error instanceof Error
              ? error.message
              : 'Failed to load the document. Please try again.'}
          </p>
          <div className='flex gap-2 justify-center'>
            <Button
              onClick={() => window.location.reload()}
              variant='outline'
              className='border-red-300 text-red-700 hover:bg-red-50'
            >
              Try Again
            </Button>
            <Button
              onClick={() => refetchDocument()}
              variant='outline'
              disabled={realDocumentLoading}
              className='border-blue-300 text-blue-700 hover:bg-blue-50'
            >
              <RefreshCw className='h-4 w-4 mr-2' />
              {realDocumentLoading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Check mental capacity access control for regular users (not Welon Trust users)
  const isWelonTrustUser = isWelonTrust(userRoles);
  if (!isWelonTrustUser && isOfSoundMind === false) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center max-w-md mx-auto'>
          <div className='text-amber-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-amber-800 mb-2'>
            Document Access Restricted
          </h3>
          <p className='text-amber-700 mb-4'>
            Based on your responses in the interview, you are not eligible to
            manage documents directly. Please contact support for assistance
            with your legal documents.
          </p>
          <div className='space-y-2'>
            <Button
              onClick={() => router.push('/member/interviewV2')}
              variant='outline'
              className='border-amber-300 text-amber-700 hover:bg-amber-50'
            >
              Update Interview Responses
            </Button>
            <Button
              onClick={handleBackToManage}
              variant='ghost'
              className='text-amber-600 hover:text-amber-700'
            >
              Back to Documents
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <Alert className='max-w-md mx-auto' variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>Document not found</AlertDescription>
      </Alert>
    );
  }

  const isPreviewMode = mode === 'preview';

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleBackToManage}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
            Back to Documents
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--foreground)]'>
              {isPreviewMode ? 'Document Preview' : 'Document Review'}
            </h1>
            <p className='text-muted-foreground mt-1'>
              {isPreviewMode
                ? 'Preview your document with draft watermark'
                : 'Review your document before proceeding to signing'}
            </p>
          </div>
        </div>
        {/*<div className='flex items-center gap-2'>*/}
        {/*  <Button*/}
        {/*    variant='outline'*/}
        {/*    size='sm'*/}
        {/*    onClick={() => refetchDocument()}*/}
        {/*    disabled={realDocumentLoading}*/}
        {/*    className='flex items-center gap-2'*/}
        {/*  >*/}
        {/*    <RefreshCw className='h-4 w-4' />*/}
        {/*    {realDocumentLoading ? 'Refreshing...' : 'Refresh'}*/}
        {/*  </Button>*/}
        {/*  <Badge*/}
        {/*    className={*/}
        {/*      isPreviewMode*/}
        {/*        ? 'bg-gray-100 text-[var(--custom-gray-dark)]'*/}
        {/*        : 'bg-blue-100 text-blue-800'*/}
        {/*    }*/}
        {/*  >*/}
        {/*    {isPreviewMode ? 'PREVIEW MODE' : 'REVIEW MODE'}*/}
        {/*  </Badge>*/}
        {/*</div>*/}
      </div>

      {/* Instructions */}
      {!isPreviewMode && (
        <Alert>
          <CheckCircle className='h-4 w-4' />
          <AlertDescription>
            Please carefully review your approved document below. You can zoom,
            rotate, and navigate through the pages.
          </AlertDescription>
        </Alert>
      )}

      {isPreviewMode && (
        <Alert>
          <Eye className='h-4 w-4' />
          <AlertDescription>
            This is a preview of your document with a draft watermark. Use this
            to review the content before finalizing. The watermark will be
            removed in the final version.
          </AlertDescription>
        </Alert>
      )}

      {/* Document Viewer */}
      <PDFViewer
        documentHtml={document}
        onDownload={handleDownload}
        isDownloading={signDocumentMutation.isPending}
        className='mb-6'
      />

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            {isPreviewMode ? 'Preview Actions' : 'Review Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {isPreviewMode ? (
              <div className='flex flex-wrap gap-3'>
                {/*<Button*/}
                {/*  variant='outline'*/}
                {/*  onClick={handleDownload}*/}
                {/*  disabled={signDocumentMutation.isPending}*/}
                {/*  className='flex items-center gap-2'*/}
                {/*>*/}
                {/*  <Download className='h-4 w-4' />*/}
                {/*  {signDocumentMutation.isPending*/}
                {/*    ? 'Signing & Downloading...'*/}
                {/*    : 'Download Preview'}*/}
                {/*</Button>*/}
                {/* <Button
                  variant='outline'
                  onClick={handleEdit}
                  className='flex items-center gap-2'
                >
                  <PenTool className='h-4 w-4' />
                  Edit Document
                </Button>

                {document.status === 'approved' && (
                  <Button
                    onClick={handleProceedToSigning}
                    className='flex items-center gap-2 bg-green-600 hover:bg-green-700'
                  >
                    <PenTool className='h-4 w-4' />
                    Sign Document
                  </Button>
                )} */}
              </div>
            ) : (
              <>
                {/* Download & Sign Information */}
                {document.status !== 'signed' && (
                  <div className='bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4'>
                    <h4 className='font-semibold text-amber-900 mb-2 flex items-center gap-2'>
                      <Download className='h-4 w-4' />
                      Download & Sign
                    </h4>
                    <p className='text-sm text-amber-800'>
                      When you download this document, it will be automatically
                      marked as signed with an electronic signature. This action
                      will update the document status and record the signing
                      date.
                    </p>
                  </div>
                )}

                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <Card className='border-blue-200 bg-blue-50'>
                    <CardContent className='p-4'>
                      <h3 className='font-semibold text-blue-900 mb-2'>
                        Ready to Proceed?
                      </h3>
                      <p className='text-sm text-blue-800 mb-3'>
                        If you're satisfied with your document, proceed directly
                        to signing.
                      </p>
                      <Button
                        onClick={handleProceedToSigning}
                        className='w-full bg-blue-600 hover:bg-blue-700'
                      >
                        <PenTool className='h-4 w-4 mr-2' />
                        Proceed to Signing
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className='border-purple-200 bg-purple-50'>
                    <CardContent className='p-4'>
                      <h3 className='font-semibold text-purple-900 mb-2'>
                        Want Legal Review?
                      </h3>
                      <p className='text-sm text-purple-800 mb-3'>
                        Get your document reviewed by a qualified attorney
                        before signing.
                      </p>
                      <Button
                        variant='outline'
                        onClick={handleRequestAttorneyReview}
                        className='w-full border-purple-300 text-purple-700 hover:bg-purple-100'
                      >
                        <Users className='h-4 w-4 mr-2' />
                        Request Attorney Review
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <div className='flex flex-wrap gap-3 pt-4 border-t'>
                  {/* <Button
                    variant='outline'
                    onClick={handleEdit}
                    className='flex items-center gap-2'
                  >
                    <PenTool className='h-4 w-4' />
                    Edit Document
                  </Button> */}
                  {/*<Button*/}
                  {/*  variant='outline'*/}
                  {/*  onClick={handleDownload}*/}
                  {/*  disabled={signDocumentMutation.isPending}*/}
                  {/*  className='flex items-center gap-2'*/}
                  {/*>*/}
                  {/*  <Download className='h-4 w-4' />*/}
                  {/*  {signDocumentMutation.isPending*/}
                  {/*    ? 'Signing & Downloading...'*/}
                  {/*    : 'Download PDF'}*/}
                  {/*</Button>*/}
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Educational Content */}
      {!isPreviewMode && (
        <Card className='border-amber-200 bg-amber-50'>
          <CardContent className='p-4'>
            <h3 className='font-semibold text-amber-900 mb-2'>
              💡 Review Tips
            </h3>
            <ul className='text-sm text-amber-800 space-y-1'>
              <li>• Verify all personal information is correct</li>
              <li>
                • Check that beneficiaries and executors are properly named
              </li>
              <li>• Ensure asset distribution matches your intentions</li>
              <li>• Review any special instructions or conditions</li>
              <li>
                • Consider whether you need attorney review for complex
                situations
              </li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default function DocumentReviewPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <DocumentReviewContent />
    </Suspense>
  );
}
