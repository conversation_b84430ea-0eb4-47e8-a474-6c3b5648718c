'use client';

import React, { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, FileText, CheckCircle2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getMemberAvailableTemplates } from '@/lib/api/member-documents';
import { useAuth } from '@/app/context/AuthContext';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import { checkDocumentDuplicate } from '@/lib/api/documents';
import { finalizeDocumentsForTemplates } from '@/lib/utils/document-create/finalize-documents';
import { fetchAuthSession } from 'aws-amplify/auth';
import { jwtDecode } from 'jwt-decode';
import { loadInterviewProgressV2 } from '@/app/utils/interviewV2Progress';
import { normalizeStateName } from '@/app/utils/states';

export default function DocumentCreatePage() {
  const router = useRouter();
  const { user, userExternalId } = useAuth();

  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [duplicateWarnings, setDuplicateWarnings] = useState<{
    [templateId: string]: {
      show: boolean;
      existingDocument?: any;
      suggestedVersion?: string;
    };
  }>({});

  // Map template type to valid document type enum values (same as in finalize-documents.ts)
  const mapTemplateTypeToDocumentType = (templateType: string): string => {
    const typeMapping: Record<string, string> = {
      Will: 'Will',
      Trust: 'Trust',
      'Healthcare POA': 'Healthcare_POA',
      'Financial POA': 'Financial_POA',
      'Advance Directive': 'Advance_Directive',
      POA: 'Healthcare_POA', // Default POA to Healthcare_POA
      Medical: 'Healthcare_POA',
      Financial: 'Financial_POA',
      Healthcare: 'Healthcare_POA',
    };

    const mappedType = typeMapping[templateType] || 'Other';
    console.log(
      `Mapping template type "${templateType}" to document type "${mappedType}"`
    );
    return mappedType;
  };

  // Fetch user profile to get real state data
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', userExternalId],
    queryFn: () => fetchUserProfileByCognitoId(userExternalId || ''),
    enabled: !!userExternalId,
  });

  // Get user state from profile or fallback to California
  const getUserState = () => {
    console.log('User profile in create page:', userProfile);

    if (!userProfile?.state) return 'California';

    try {
      // If state is a JSON string (address object), parse it and extract stateProvinceCode
      const parsedState = JSON.parse(userProfile.state);
      console.log('===> Parsed user state:', parsedState);
      let stateCode;

      if (parsedState.stateProvinceCode && parsedState.stateProvinceCode < 4) {
        stateCode = parsedState.stateProvinceCode;
      } else {
        stateCode = 'CA';
      }
      // Convert state code to full name for template matching
      return normalizeStateName(stateCode);
    } catch {
      return '';
    }
  };

  const userState = getUserState();

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['templates', userState],
    queryFn: () => getMemberAvailableTemplates(userState),
    enabled: !!userState,
  });

  // Load interview data to check selected documents
  const { data: interviewData, isLoading: isLoadingInterview } = useQuery({
    queryKey: ['interview-v2-progress'],
    queryFn: loadInterviewProgressV2,
  });

  // Extract selected documents from interview data
  const interviewSelectedDocuments =
    interviewData?.stepsData?.profile?.selectedDocuments || null;

  // Map template types to interview document keys
  const mapTemplateTypeToInterviewKey = (
    templateType: string
  ): string | null => {
    const typeMapping: Record<string, string> = {
      Will: 'will',
      Trust: 'trust',
      'Healthcare POA': 'medicalPOA',
      'Financial POA': 'financialPOA',
      'Advance Directive': 'medicalPOA', // Map to medical POA
      POA: 'medicalPOA', // Default POA to medical POA
      Medical: 'medicalPOA',
      Financial: 'financialPOA',
      Healthcare: 'medicalPOA',
    };
    return typeMapping[templateType] || null;
  };

  const documentOptions = useMemo(() => {
    if (!templates || templates.length === 0) return [];

    // Filter templates based on interview selections if available
    let filteredTemplates = templates;
    if (interviewSelectedDocuments) {
      filteredTemplates = templates.filter(template => {
        const interviewKey = mapTemplateTypeToInterviewKey(template.type);
        return (
          interviewKey && interviewSelectedDocuments[interviewKey] === true
        );
      });
    }

    return filteredTemplates.map((template: any) => {
      const templateType = template.type;
      const templateStates = template.templateStates || [];
      const isGeneral =
        template.isGeneral === true || templateStates.includes('General');
      const hasUserState = templateStates.includes(userState);

      // Determine display suffix
      let displaySuffix = '';
      if (hasUserState) {
        displaySuffix = ` (${userState})`;
      } else if (isGeneral) {
        displaySuffix = ' (General)';
      }

      const displayName = `${templateType}${displaySuffix}`;

      return {
        id: template.id,
        name: displayName,
        type: templateType,
        template: template,
      };
    });
  }, [templates, userState, interviewSelectedDocuments]);

  const handleTemplateToggle = (templateId: string) => {
    setSelectedTemplates(prev => {
      if (prev.includes(templateId)) {
        return prev.filter(id => id !== templateId);
      } else {
        return [...prev, templateId];
      }
    });
    setError(null);
    // Clear any duplicate warnings for this template
    setDuplicateWarnings(prev => ({
      ...prev,
      [templateId]: { show: false },
    }));
  };

  const handleSelectAll = () => {
    const allTemplateIds = documentOptions.map(option => option.id);
    setSelectedTemplates(allTemplateIds);
    setError(null);
    // Clear all duplicate warnings
    setDuplicateWarnings({});
  };

  const handleDeselectAll = () => {
    setSelectedTemplates([]);
    setError(null);
    // Clear all duplicate warnings
    setDuplicateWarnings({});
  };

  const handleCreateDocuments = async (forceProceed: boolean = false) => {
    if (selectedTemplates.length === 0) {
      setError('Please select at least one document type');
      return;
    }

    try {
      setIsCreating(true);
      setError(null);
      setDuplicateWarnings({});

      // Check for duplicate documents first (unless forcing to proceed)
      if (!forceProceed && user?.userId) {
        const duplicateChecks = await Promise.all(
          selectedTemplates.map(async templateId => {
            const selectedTemplate = templates.find(
              template => template.id === templateId
            );
            if (!selectedTemplate) {
              throw new Error(`Template not found: ${templateId}`);
            }

            console.log(
              `Checking duplicates for template: ${selectedTemplate.type} (ID: ${templateId})`
            );

            // Map template type to document type before checking duplicates
            const mappedDocumentType = mapTemplateTypeToDocumentType(
              selectedTemplate.type
            );

            console.log(
              `Mapped "${selectedTemplate.type}" to "${mappedDocumentType}" for duplicate check`
            );

            const session = await fetchAuthSession();
            const token = session.tokens?.accessToken?.toString();

            if (!token) {
              throw new Error('No access token found');
            }

            const decodedToken = jwtDecode(token) as any;

            const duplicateCheck = await checkDocumentDuplicate(
              mappedDocumentType,
              decodedToken.externalId
            );

            console.log(
              `Duplicate check result for ${mappedDocumentType}:`,
              duplicateCheck
            );

            return {
              templateId,
              template: selectedTemplate,
              duplicateCheck,
            };
          })
        );

        // Check if any duplicates exist
        const duplicatesFound = duplicateChecks.filter(
          check => check.duplicateCheck.hasDuplicate
        );

        if (duplicatesFound.length > 0) {
          const warnings: { [key: string]: any } = {};
          duplicatesFound.forEach(({ templateId, duplicateCheck }) => {
            warnings[templateId] = {
              show: true,
              existingDocument: duplicateCheck.existingDocument,
              suggestedVersion: duplicateCheck.suggestedVersion,
            };
          });
          setDuplicateWarnings(warnings);
          setIsCreating(false);
          return;
        }
      }

      // Check if user has completed InterviewV2
      try {
        const { loadInterviewProgressV2 } = await import(
          '@/app/utils/interviewV2Progress'
        );
        const progressData = await loadInterviewProgressV2();

        // Check if InterviewV2 has any meaningful data
        const hasV2Data =
          progressData?.stepsData &&
          (progressData.stepsData.profile?.firstName ||
            progressData.stepsData.people?.length > 0 ||
            progressData.stepsData.emergency ||
            progressData.stepsData.afterYouDie ||
            progressData.stepsData.financial ||
            progressData.stepsData.medical ||
            progressData.stepsData.additional);

        if (!hasV2Data) {
          // Redirect to InterviewV2 if no meaningful data found
          router.push('/member/interviewV2');
          return;
        }
      } catch (error) {
        setError(
          'No interviews available. Please complete Interview V2 first.'
        );
        return;
      }

      // Get selected template objects
      const selectedTemplateObjects = selectedTemplates
        .map(templateId => templates.find(t => t.id === templateId))
        .filter(Boolean);

      // Create documents using the finalizeDocumentsForTemplates function
      if (!userExternalId) {
        throw new Error('User not authenticated');
      }
      await finalizeDocumentsForTemplates(
        userExternalId,
        selectedTemplateObjects
      );

      // Redirect to documents page after successful creation
      router.push('/member/documents');
    } catch (error) {
      console.error('Error creating documents:', error);
      setError('Failed to create documents. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-[var(--foreground)] mb-2'>
            Create Legal Documents
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Select one or more document types to create. We'll use your
            Interview V2 responses to generate personalized documents for each
            selected type.
          </p>
        </div>

        {/* Interview Document Selection Info */}
        {interviewSelectedDocuments && (
          <Alert className='mb-6 border-blue-200 bg-blue-50'>
            <AlertCircle className='h-4 w-4 text-blue-600' />
            <AlertTitle className='text-blue-800'>
              Documents Selected in Interview
            </AlertTitle>
            <AlertDescription className='text-blue-700'>
              <p className='mb-2'>
                Based on your interview responses, you selected the following
                documents to create:
              </p>
              <div className='flex flex-wrap gap-2'>
                {interviewSelectedDocuments.will && (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                    Last Will and Testament
                  </span>
                )}
                {interviewSelectedDocuments.trust && (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                    Revocable Trust
                  </span>
                )}
                {interviewSelectedDocuments.financialPOA && (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                    Financial Power of Attorney
                  </span>
                )}
                {interviewSelectedDocuments.medicalPOA && (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                    Medical Power of Attorney
                  </span>
                )}
              </div>
              <p className='mt-2 text-sm'>
                Only templates for these document types are shown below. To
                change your selection, please return to the interview and update
                your document preferences.
              </p>
            </AlertDescription>
          </Alert>
        )}

        {/* No Documents Selected Warning */}
        {interviewSelectedDocuments &&
          !Object.values(interviewSelectedDocuments).some(Boolean) && (
            <Alert variant='destructive' className='mb-6'>
              <AlertCircle className='h-4 w-4' />
              <AlertTitle>No Documents Selected</AlertTitle>
              <AlertDescription>
                <p className='mb-2'>
                  You haven't selected any documents to create in your
                  interview. Please return to the interview and select at least
                  one document type.
                </p>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => router.push('/member/interviewV2')}
                  className='mt-2'
                >
                  Return to Interview
                </Button>
              </AlertDescription>
            </Alert>
          )}

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Selection</CardTitle>
            <CardDescription>
              Choose one or more legal documents you want to create. Documents
              are prioritized for your state ({userState}) when available, with
              general documents as fallbacks. You can select multiple documents
              to create them all at once.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <label className='text-sm font-medium'>
                    Select Document Types to Create
                  </label>
                  {documentOptions.length > 0 && (
                    <div className='flex gap-2'>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={handleSelectAll}
                        disabled={
                          selectedTemplates.length === documentOptions.length
                        }
                      >
                        Select All
                      </Button>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={handleDeselectAll}
                        disabled={selectedTemplates.length === 0}
                      >
                        Deselect All
                      </Button>
                    </div>
                  )}
                </div>
                {isLoading ? (
                  <div className='flex items-center justify-center py-8'>
                    <div className='text-sm text-gray-500'>
                      Loading templates...
                    </div>
                  </div>
                ) : documentOptions.length === 0 ? (
                  <div className='flex items-center justify-center py-8'>
                    <div className='text-sm text-gray-500'>
                      {interviewSelectedDocuments
                        ? 'No templates available for the documents you selected in your interview'
                        : 'No templates available for your state'}
                    </div>
                  </div>
                ) : (
                  <div className='grid gap-3'>
                    {documentOptions.map(option => (
                      <div
                        key={option.id}
                        className={`flex items-start space-x-3 rounded-lg border p-4 transition-all ${
                          selectedTemplates.includes(option.id)
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <Checkbox
                          id={option.id}
                          checked={selectedTemplates.includes(option.id)}
                          onCheckedChange={checked => {
                            if (checked) {
                              setSelectedTemplates(prev => [
                                ...prev,
                                option.id,
                              ]);
                            } else {
                              setSelectedTemplates(prev =>
                                prev.filter(id => id !== option.id)
                              );
                            }
                            setError(null);
                            setDuplicateWarnings(prev => ({
                              ...prev,
                              [option.id]: { show: false },
                            }));
                          }}
                          className='mt-0.5 cursor-pointer'
                        />
                        <div
                          className='flex-1 min-w-0 cursor-pointer'
                          onClick={() => handleTemplateToggle(option.id)}
                        >
                          <div className='flex items-center space-x-2'>
                            <FileText className='h-4 w-4 text-gray-400 flex-shrink-0' />
                            <Label
                              htmlFor={option.id}
                              className='text-sm font-medium cursor-pointer'
                            >
                              {option.name}
                            </Label>
                          </div>
                          <p className='text-xs text-gray-500 mt-1'>
                            {option.type} document template
                          </p>
                        </div>
                        {selectedTemplates.includes(option.id) && (
                          <CheckCircle2 className='h-4 w-4 text-primary flex-shrink-0' />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className='space-y-2'>
                <label className='text-sm font-medium'>State</label>
                <div className='p-3 border rounded-md bg-gray-50'>
                  <p>Your state is {userState}</p>
                  <p className='text-xs text-[var(--custom-gray-dark)] mt-1'>
                    This is the state of residence listed in your profile. When
                    available, you’ll see documents tailored to your state’s
                    laws. Otherwise, you’ll see general documents that apply
                    nationwide.
                  </p>
                </div>
              </div>

              {error && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          <CardFooter className='flex justify-between items-center'>
            <div className='text-sm text-gray-500'>
              {selectedTemplates.length > 0 && (
                <span>
                  {selectedTemplates.length} document
                  {selectedTemplates.length === 1 ? '' : 's'} selected
                </span>
              )}
            </div>
            <Button
              onClick={() => handleCreateDocuments()}
              disabled={
                isCreating || selectedTemplates.length === 0 || isLoading
              }
            >
              {isCreating
                ? 'Creating Documents...'
                : `Create ${selectedTemplates.length || ''} Document${
                    selectedTemplates.length === 1 ? '' : 's'
                  }`}
            </Button>
          </CardFooter>
        </Card>

        {/* Duplicate Document Warnings */}
        {Object.entries(duplicateWarnings).map(
          ([templateId, warning]) =>
            warning.show && (
              <Alert
                key={templateId}
                className='border-yellow-200 bg-yellow-50 mb-6'
              >
                <AlertCircle className='h-4 w-4 text-yellow-600' />
                <AlertTitle className='text-yellow-800'>
                  Document Already Exists
                </AlertTitle>
                <AlertDescription className='text-yellow-700'>
                  <div className='space-y-3'>
                    <p>
                      You already have a document of type "
                      {warning.existingDocument?.type}" that is not yet
                      finalized.
                      {warning.existingDocument && (
                        <>
                          <br />
                          <strong>Existing document:</strong>{' '}
                          {warning.existingDocument.title}
                          <br />
                          <strong>Status:</strong>{' '}
                          {warning.existingDocument.status}
                          <br />
                          <strong>Created:</strong>{' '}
                          {new Date(
                            warning.existingDocument.dateCreated
                          ).toLocaleDateString()}
                        </>
                      )}
                    </p>
                    <p>
                      You can only create a new document of the same type after
                      the existing one is archived, signed, or shipped.
                    </p>
                    <div className='flex gap-2 mt-4'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          setDuplicateWarnings(prev => ({
                            ...prev,
                            [templateId]: { show: false },
                          }))
                        }
                      >
                        Cancel
                      </Button>
                      <Button
                        size='sm'
                        onClick={() => {
                          // Remove this template from selection
                          setSelectedTemplates(prev =>
                            prev.filter(id => id !== templateId)
                          );
                          setDuplicateWarnings(prev => ({
                            ...prev,
                            [templateId]: { show: false },
                          }));
                        }}
                      >
                        Remove from Selection
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )
        )}

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Multiple Document Creation
          </h3>
          <p className='text-blue-700 mb-4'>
            You can now create all the legal documents you need in one go —
            saving you time and effort. Our system uses the information you
            shared during your interview to make sure everything meets your
            state’s requirements.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              Pick as many document types as you want, and we’ll create them all
              at once.
            </li>
            <li>
              Whenever possible, we’ll use templates tailored to your state; if
              none are available, we’ll use our standard templates.
            </li>
            <li>
              Your documents are securely stored, so you can access them
              anytime.
            </li>
            <li>
              We’ll let you know if state laws change and your documents need an
              update.
            </li>
          </ul>
        </div>

        {error && (
          <Alert className='border-red-200 bg-red-50'>
            <AlertCircle className='h-4 w-4 text-red-600' />
            <AlertTitle className='text-red-800'>Error</AlertTitle>
            <AlertDescription className='text-red-700'>
              {error}
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
