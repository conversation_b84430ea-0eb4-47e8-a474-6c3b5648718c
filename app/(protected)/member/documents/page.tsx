'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DocumentStatusCard } from '@/components/documents/document-status-card';
import { Document } from '@/types/documents';
import { FileText, Search, Filter, Eye, PenTool } from 'lucide-react';
import routes from '@/utils/routes';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  DocumentResponse,
  recreateDocument,
  sendDocumentToWelon,
  retractDocument,
  deleteDocument,
  permanentlyDeleteDocument,
  getUserDocumentsByUserId,
} from '@/lib/api/documents';
import { useAuth } from '@/context/AuthContext';
import { downloadDocument } from '@/lib/utils/document-download';
import { toast } from 'sonner';
import { useMentalCapacityCheck } from '@/hooks/useMentalCapacityCheck';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { useDocumentSigning } from '@/hooks/useDocumentSigning';
import Link from 'next/link';
import { createNotificationWithEmail } from '@/lib/api/notifications';
import { fetchAuthSession } from 'aws-amplify/auth';
import { jwtDecode } from 'jwt-decode';
// Removed import - using API route instead

const client = generateClient<Schema>();

function DocumentsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, userId } = useAuth();

  // Check mental capacity for access control
  const { isOfSoundMind, isLoading: mentalCapacityLoading } =
    useMentalCapacityCheck();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | Document['type']>('all');
  const [showArchivedDocuments, setShowArchivedDocuments] = useState(false);
  const [sortBy, setSortBy] = useState<'dateCreated' | 'version'>(
    'dateCreated'
  );
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Handle URL filter parameter
  useEffect(() => {
    const filterParam = searchParams.get('filter');
    if (filterParam && filterParam !== 'all') {
      setStatusFilter(filterParam);
    }
  }, [searchParams]);

  // All possible document types
  const ALL_DOCUMENT_TYPES: Document['type'][] = [
    'Will',
    'Trust',
    'Healthcare_POA',
    'Financial_POA',
    'Advance_Directive',
  ];

  // Function to format document type for display
  const formatDocumentType = (type: Document['type']): string => {
    switch (type) {
      case 'Healthcare_POA':
        return 'Healthcare POA';
      case 'Financial_POA':
        return 'Financial POA';
      case 'Advance_Directive':
        return 'Advance Directive';
      default:
        return type;
    }
  };

  // Function to extract document type from title
  const extractTypeFromTitle = (title: string): Document['type'] => {
    const titleLower = title.toLowerCase();

    if (titleLower.includes('will')) {
      return 'Will';
    } else if (titleLower.includes('trust')) {
      return 'Trust';
    } else if (
      titleLower.includes('healthcare') ||
      titleLower.includes('medical')
    ) {
      return 'Healthcare_POA';
    } else if (
      titleLower.includes('financial') ||
      (titleLower.includes('poa') && !titleLower.includes('healthcare'))
    ) {
      return 'Financial_POA';
    } else if (
      titleLower.includes('advance directive') ||
      titleLower.includes('directive')
    ) {
      return 'Advance_Directive';
    } else {
      return 'Other';
    }
  };

  //LOADING STATES
  const [downloadingDocuments, setDownloadingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [resubmittingDocuments, setResubmittingDocuments] = useState<
    Set<string>
  >(new Set());
  const [retractingDocuments, setRetractingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [shippingDocuments, setShippingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(
    new Set()
  );

  // State for bulk shipping
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(
    new Set()
  );
  const [isBulkShipping, setIsBulkShipping] = useState(false);

  const {
    data: realDocuments,
    isLoading: realDocumentsLoading,
    error: realDocumentsError,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: ['user-documents', userId],
    queryFn: () => getUserDocumentsByUserId(userId ?? ''),
    enabled: !!userId,
    refetchOnWindowFocus: false,
  });

  // Mutation for recreating rejected documents
  const recreateMutation = useMutation({
    mutationFn: (docId: string) => recreateDocument(docId),
  });

  // Load real documents from database
  useEffect(() => {
    if (realDocuments) {
      // Convert real documents from database
      const convertedDocuments: Document[] = realDocuments.map(
        (doc: DocumentResponse) => ({
          id: doc.id,
          title: doc.title,
          type: extractTypeFromTitle(doc.title), // Parse type from title
          status: doc.status as Document['status'],
          dateCreated: doc.dateCreated,
          lastModified: doc.lastModified || doc.dateCreated,
          version: doc.version,
          content: doc.content,
          userId: doc.userId,
          fileUrl: doc.fileUrl,
          trackingNumber: doc.trackingNumber,
          signatureType: doc.signatureType as Document['signatureType'],
          executionDate: doc.executionDate,
          rejectionReason: doc.rejectionReason,
          templateId: doc.templateId,
          templateVersion: doc.templateVersion,
          // Signed document fields
          signedDocumentUrl: doc.signedDocumentUrl,
          signedAt: doc.signedAt,
          documentHash: doc.documentHash,
        })
      );

      setDocuments(convertedDocuments);
      setFilteredDocuments(convertedDocuments);
    } else if (!realDocuments && !realDocumentsLoading) {
      // No documents yet
      setDocuments([]);
      setFilteredDocuments([]);
    }
  }, [realDocuments, realDocumentsLoading]);

  // Determine loading state
  const loading = realDocumentsLoading;

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Show only archived documents when showArchivedDocuments is true, otherwise hide them
    if (showArchivedDocuments) {
      filtered = filtered.filter(doc => doc.status === 'archived');
    } else {
      filtered = filtered.filter(doc => doc.status !== 'archived');
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        doc =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(doc => doc.type === typeFilter);
    }

    // Sort documents
    filtered.sort((a, b) => {
      let comparison = 0;

      if (sortBy === 'dateCreated') {
        const dateA = new Date(a.dateCreated || a.lastModified || '').getTime();
        const dateB = new Date(b.dateCreated || b.lastModified || '').getTime();
        comparison = dateA - dateB;
      } else if (sortBy === 'version') {
        // Parse version numbers (e.g., "1.0", "2.1") for proper numeric comparison
        const parseVersion = (version: string) => {
          const parts = version.split('.').map(Number);
          return parts[0] * 1000 + (parts[1] || 0);
        };
        const versionA = parseVersion(a.version || '1.0');
        const versionB = parseVersion(b.version || '1.0');
        comparison = versionA - versionB;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    setFilteredDocuments(filtered);
  }, [
    documents,
    searchTerm,
    statusFilter,
    typeFilter,
    showArchivedDocuments,
    sortBy,
    sortOrder,
  ]);

  const handlePreview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}&mode=preview`);
  };

  const handleReview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}`);
  };

  const handleSign = (document: Document) => {
    router.push(`${routes.documentsSign}?id=${document.id}`);
  };

  const handleRecreate = async (document: Document) => {
    try {
      // Add document to resubmitting set
      setResubmittingDocuments(prev => new Set(prev).add(document.id));

      await recreateMutation.mutateAsync(document.id);

      toast.success(
        `Document "${document.title}" has been recreated with fresh data for review`
      );
      refetchDocuments(); // Refresh document data to show updated status
    } catch (error) {
      console.error('Error recreating document:', error);
      toast.error('Failed to recreate document. Please try again.');
    } finally {
      // Remove document from resubmitting set
      setResubmittingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleRetract = async (document: Document) => {
    try {
      console.log('===> Retracting document:', document.id);

      // Add document to retracting set
      setRetractingDocuments(prev => new Set(prev).add(document.id));

      await retractDocument(document.id);

      // Update local state
      const updatedDocuments = documents.map(doc =>
        doc.id === document.id
          ? {
              ...doc,
              status: 'draft' as Document['status'],
              lastModified: new Date().toISOString(),
            }
          : doc
      );
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success(
        `Document "${document.title}" has been retracted from review`
      );
    } catch (error) {
      Sentry.captureException(error);
      console.error('Error retracting document:', error);
      toast.error('Failed to retract document. Please try again.');
    } finally {
      // Remove document from retracting set
      setRetractingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  // Functions for bulk document selection
  const handleDocumentSelect = (documentId: string, selected: boolean) => {
    setSelectedDocuments(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(documentId);
      } else {
        newSet.delete(documentId);
      }
      return newSet;
    });
  };

  const handleSelectAll = (documents: Document[]) => {
    const signedDocuments = documents.filter(doc => doc.status === 'signed');
    setSelectedDocuments(new Set(signedDocuments.map(doc => doc.id)));
  };

  const handleDeselectAll = () => {
    setSelectedDocuments(new Set());
  };

  const getSignedDocuments = () => {
    return filteredDocuments.filter(doc => doc.status === 'signed');
  };

  // Bulk shipping function
  const handleBulkShip = async () => {
    if (selectedDocuments.size === 0) {
      toast.error('Please select at least one document to ship');
      return;
    }

    const documentsToShip = filteredDocuments.filter(doc =>
      selectedDocuments.has(doc.id)
    );

    try {
      setIsBulkShipping(true);

      // Get current user
      const session = await fetchAuthSession();
      const token = session.tokens?.accessToken?.toString();

      if (!token) {
        throw new Error('No access token found');
      }

      const decodedToken = jwtDecode(token) as any;

      // Get current user data
      const { data: userList, errors: listErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: decodedToken.externalId } },
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'phoneNumber',
            'assignedWelonTrustId',
            'shippingAddress.*',
          ],
        });

      if (listErrors) {
        console.error('❌ User query errors:', listErrors);
        throw new Error('Failed to fetch user data');
      }

      if (!userList || userList.length === 0) {
        console.error(
          '❌ No user found with cognitoId:',
          decodedToken.externalId
        );
        throw new Error('User not found');
      }

      const userData = userList[0];

      if (!userData.shippingAddress) {
        console.error('NO SHIPPING ADDRESS FOR USER');
        throw new Error('No shipping address found');
      }

      // Check if user has assigned WelonTrust
      if (!userData.assignedWelonTrustId) {
        console.error('❌ No WelonTrust assigned to this user');
        throw new Error('No WelonTrust assigned to this user');
      }

      // Get WelonTrust info using Lambda function
      console.log('🔍 Getting WelonTrust info via Lambda...');
      const welonTrustResult = await client.queries.getWelonTrustInfo({
        userId: userData.assignedWelonTrustId as string,
      });

      console.log('WelonTrust result:', welonTrustResult);

      if (welonTrustResult.errors) {
        console.error('❌ WelonTrust query errors:', welonTrustResult.errors);
        throw new Error(
          `Failed to get WelonTrust info: ${JSON.stringify(welonTrustResult.errors)}`
        );
      }

      // Parse the JSON response from Lambda
      let welonTrustData;
      try {
        welonTrustData =
          typeof welonTrustResult.data === 'string'
            ? JSON.parse(welonTrustResult.data)
            : welonTrustResult.data;
      } catch (parseError) {
        Sentry.captureException(parseError);
        console.error('❌ Failed to parse WelonTrust response:', parseError);
        console.error('Raw response:', welonTrustResult.data);
        throw new Error('Invalid response format from WelonTrust service');
      }

      console.log('Parsed WelonTrust data:', welonTrustData);

      if (!welonTrustData || !welonTrustData.success) {
        console.error('❌ WelonTrust Lambda error:', welonTrustData?.error);
        throw new Error(
          welonTrustData?.error || 'Failed to get WelonTrust info'
        );
      }

      const welonTrust = welonTrustData.welonTrust;

      // Prepare addresses for UPS API
      const fromAddress = {
        name: `${userData.firstName} ${userData.lastName}`,
        phone: userData.phoneNumber || '',
        addressLine1: userData.shippingAddress.addressLine1,
        addressLine2: userData.shippingAddress.addressLine2 || '',
        city: userData.shippingAddress.city,
        stateProvinceCode: userData.shippingAddress.stateProvinceCode,
        postalCode: userData.shippingAddress.postalCode,
        countryCode: userData.shippingAddress.countryCode,
      };

      const toAddress = {
        name: welonTrust.name,
        phone: welonTrust.phoneNumber || '',
        addressLine1: welonTrust.address.addressLine1,
        addressLine2: welonTrust.address.addressLine2 || '',
        city: welonTrust.address.city,
        stateProvinceCode: welonTrust.address.stateProvinceCode,
        postalCode: welonTrust.address.postalCode,
        countryCode: welonTrust.address.countryCode,
      };

      // Create package description with all document titles
      const documentTitles = documentsToShip.map(doc => doc.title).join(', ');
      const packageDescription = `Legal Documents: ${documentTitles}`;

      // Create UPS shipping label using Lambda function
      const { data: shippingApiResult, errors: shippingErrors } =
        await client.mutations.createUPSLabel({
          fromAddress: JSON.stringify(fromAddress),
          toAddress: JSON.stringify(toAddress),
          serviceCode: '03', // UPS Ground
          packageOptions: JSON.stringify({
            description: packageDescription,
            weight: Math.max(1, documentsToShip.length * 0.5).toString(),
          }),
        });

      if (shippingErrors) {
        console.error('❌ UPS shipping errors:', shippingErrors);
        throw new Error('Failed to create shipping label');
      }

      // Parse the result - Lambda returns JSON string
      let shippingResult;
      try {
        const parsed = JSON.parse(String(shippingApiResult));

        // If it's still a string, parse again (double-encoded JSON)
        if (typeof parsed === 'string') {
          shippingResult = JSON.parse(parsed);
        } else {
          shippingResult = parsed;
        }
      } catch (error) {
        Sentry.captureException(error);
        console.error('Failed to parse shipping result:', error);
        // If it's already an object, use it directly
        shippingResult = shippingApiResult;
      }

      if (!shippingResult.success) {
        throw new Error(
          shippingResult.error || 'Failed to create shipping label'
        );
      }

      // Extract the actual shipping data from the API response
      const shippingData = shippingResult.data;

      console.log('✅ Shipping label created:', {
        trackingNumber: shippingData.trackingNumber,
        hasTrackingNumber: !!shippingData.trackingNumber,
        trackingNumberType: typeof shippingData.trackingNumber,
        labelUrl: shippingData.labelUrl ? 'Present' : 'Missing',
        cost: shippingData.cost,
      });

      const shippingLabelData: any = {
        userId: userData.id,
        assignedWelonTrustId: welonTrust.cognitoId,
        documentIds: Array.from(selectedDocuments), // Array of document IDs
        trackingNumber: shippingData.trackingNumber || '',
        labelUrl: shippingData.labelUrl || '',
        fromAddress: JSON.stringify(fromAddress),
        toAddress: JSON.stringify(toAddress),
        serviceCode: '03',
        cost: JSON.stringify(
          shippingData.cost || { amount: '0.00', currency: 'USD' }
        ),
        status: 'created',
        createdAt: new Date().toISOString(),
        packageDescription: packageDescription,
        totalDocuments: documentsToShip.length,
      };

      console.log('💾 Saving shipping label data:', {
        userId: shippingLabelData.userId,
        assignedWelonTrustId: shippingLabelData.assignedWelonTrustId,
        trackingNumber: shippingLabelData.trackingNumber,
        hasLabelUrl: !!shippingLabelData.labelUrl,
        documentCount: shippingLabelData.documentIds.length,
      });

      const { data: shippingLabel, errors: labelErrors } =
        await client.models.ShippingLabel.create(shippingLabelData);

      if (labelErrors) {
        console.error('Error saving shipping label:', labelErrors);
        throw new Error('Failed to save shipping label');
      }

      // Update all selected documents status to 'shipped'
      const updatePromises = documentsToShip.map(document =>
        client.models.Document.update({
          id: document.id,
          status: 'shipped',
          trackingNumber: shippingData.trackingNumber,
        })
      );

      const updateResults = await Promise.all(updatePromises);
      const updateErrors = updateResults.filter(result => result.errors);

      if (updateErrors.length > 0) {
        console.error('Error updating some document statuses:', updateErrors);
      }

      toast.success(
        `${documentsToShip.length} document(s) have been shipped! Tracking: ${shippingData.trackingNumber}`
      );

      // Send notification about bulk shipping
      try {
        const currentUser = await getCurrentUser();
        const documentTitles = documentsToShip.map(doc => doc.title).join(', ');
        const notificationMessage = `Your documents (${documentTitles}) have been shipped successfully. Tracking number: ${shippingData.trackingNumber}`;
        const emailSubject = 'Documents Shipped - ChildFree Trust®';

        // Get user data for email
        const { data: userList } = await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: ['email', 'firstName', 'lastName'],
        });

        const userData = userList?.[0];
        if (userData?.email) {
          await createNotificationWithEmail(
            notificationMessage,
            currentUser.userId,
            userData.email,
            emailSubject,
            'system-bulk-shipping'
          );
        }
      } catch (notificationError) {
        console.error(
          'Error creating bulk shipping notification:',
          notificationError
        );
        // Continue even if notification fails
      }

      // Clear selection and refresh documents
      setSelectedDocuments(new Set());
      refetchDocuments();
    } catch (error) {
      Sentry.captureException(error);
      console.error('Error shipping documents:', error);
      toast.error(
        'Failed to ship documents: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    } finally {
      setIsBulkShipping(false);
    }
  };

  const handleShip = async (document: Document) => {
    try {
      // Add document to shipping set
      setShippingDocuments(prev => new Set(prev).add(document.id));

      // Handle different actions based on document status
      if (document.status === 'draft') {
        // Send for electronic review
        await sendDocumentToWelon(document.id);

        // Update local state
        const updatedDocuments = documents.map(doc =>
          doc.id === document.id
            ? {
                ...doc,
                status: 'sent' as Document['status'],
                lastModified: new Date().toISOString(),
              }
            : doc
        );
        setDocuments(updatedDocuments);
        setFilteredDocuments(updatedDocuments);

        toast.success('Document sent for electronic review!');
        return;
      }

      if (document.status === 'signed') {
        // Toggle document in bulk shipping selection
        setSelectedDocuments(prev => {
          const newSet = new Set(prev);
          if (newSet.has(document.id)) {
            newSet.delete(document.id);
            toast.success(
              `Document "${document.title}" removed from shipping queue`
            );
          } else {
            newSet.add(document.id);
            toast.success(
              `Document "${document.title}" added to shipping queue`
            );

            // Scroll to bulk shipping section
            setTimeout(() => {
              const bulkShippingElement = window.document.querySelector(
                '[data-bulk-shipping]'
              );
              if (bulkShippingElement) {
                bulkShippingElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
              }
            }, 100);
          }
          return newSet;
        });

        return;
      } else {
        toast.error('Document cannot be shipped in current status');
        return;
      }
    } catch (error) {
      console.error('Error adding document to shipping:', error);
      toast.error('Failed to add document to shipping queue');
    } finally {
      // Remove document from shipping set
      setShippingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      // Add document to downloading set
      setDownloadingDocuments(prev => new Set(prev).add(document.id));
      await downloadDocument(document);
    } catch (error) {
      console.error(error);
      toast.error('Something went wrong.');
    } finally {
      // Remove document from downloading set
      setDownloadingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleDelete = async (document: Document) => {
    // Show confirmation dialog for archiving
    const confirmed = window.confirm(
      `Are you sure you want to archive "${document.title}"?`
    );

    if (!confirmed) return;

    try {
      // Add document to deleting set
      setDeletingDocuments(prev => new Set(prev).add(document.id));

      await deleteDocument(document.id);

      // Update document status in local state instead of removing it
      const updatedDocuments = documents.map(doc =>
        doc.id === document.id
          ? {
              ...doc,
              status: 'archived' as Document['status'],
              lastModified: new Date().toISOString(),
            }
          : doc
      );
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success('Document archived successfully');
    } catch (error) {
      console.error('Error archiving document:', error);
      toast.error('Failed to archive document. Please try again.');
    } finally {
      // Remove document from deleting set
      setDeletingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handlePermanentDelete = async (document: Document) => {
    // Show strong confirmation dialog for permanent deletion
    const confirmed = window.confirm(
      `⚠️ PERMANENT DELETION WARNING ⚠️\n\nAre you absolutely sure you want to permanently delete "${document.title}"?\n\nThis action CANNOT be undone and the document will be lost forever.\n\nClick OK only if you are certain you want to proceed.`
    );

    if (!confirmed) return;

    try {
      // Add document to deleting set
      setDeletingDocuments(prev => new Set(prev).add(document.id));

      await permanentlyDeleteDocument(document.id);

      // Remove document from local state completely
      const updatedDocuments = documents.filter(doc => doc.id !== document.id);
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success('Document permanently deleted');
    } catch (error) {
      console.error('Error permanently deleting document:', error);
      toast.error('Failed to permanently delete document. Please try again.');
    } finally {
      // Remove document from deleting set
      setDeletingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  // Map database status to display status for counting
  const mapStatusForCounting = (status: string) => {
    switch (status) {
      case 'draft':
        return 'ready_for_review';
      case 'ready_for_signing':
        return 'ready_for_signing';
      case 'signed':
        return 'signed';
      case 'shipped':
        return 'shipped';
      case 'received':
        return 'approved';
      case 'archived':
        return 'archived';
      default:
        return status;
    }
  };

  const getStatusCounts = () => {
    const counts = {
      total: documents.length,
      ready_for_review: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_review'
      ).length,
      ready_for_signing: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_signing'
      ).length,
      shipped: documents.filter(
        d => mapStatusForCounting(d.status) === 'shipped'
      ).length,
      signed: documents.filter(d => mapStatusForCounting(d.status) === 'signed')
        .length,
      approved: documents.filter(
        d => mapStatusForCounting(d.status) === 'approved'
      ).length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();
  const filterParam = searchParams.get('filter');
  const isFiltered = filterParam && filterParam !== 'all';

  // Use all possible document types instead of only available ones
  const availableTypes = ALL_DOCUMENT_TYPES;

  const getFilterTitle = () => {
    if (filterParam === 'ready_for_review') return 'Documents Ready for Review';
    if (filterParam === 'ready_for_signing')
      return 'Documents Ready for Signing';
    return 'Document Management';
  };

  const getFilterDescription = () => {
    if (filterParam === 'ready_for_review')
      return 'Review your documents before proceeding to signing';
    if (filterParam === 'ready_for_signing')
      return 'Sign your approved documents';
    return 'Access, download, and manage your generated legal documents. View document history, track updates, and request modifications when needed.';
  };

  if (loading || mentalCapacityLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your documents...</p>
        </div>
      </div>
    );
  }

  // Block access if user is not of sound mind
  if (isOfSoundMind === false) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center max-w-md mx-auto'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-[var(--foreground)] mb-2'>
            Document Access Restricted
          </h3>
          <p className='text-[var(--custom-gray-dark)] mb-4'>
            Based on your responses in the interview, you are not eligible to
            manage documents directly. Please contact support for assistance
            with your legal documents.
          </p>
          <div className='space-x-2'>
            <Button
              onClick={() => router.push('/member/interviewV2')}
              variant='outline'
              className='border-gray-300 text-[var(--foreground)] hover:bg-gray-50'
            >
              Update Interview Responses
            </Button>
            <Button
              onClick={() => router.push('/member/dashboard')}
              variant='ghost'
              className='text-[var(--custom-gray-dark)] hover:text-[var(--foreground)]'
            >
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error state for documents query
  if (realDocumentsError) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Documents
          </h3>
          <p className='text-red-600 mb-4'>
            {realDocumentsError instanceof Error
              ? realDocumentsError.message
              : 'Failed to load your documents. Please try again.'}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant='outline'
            className='border-red-300 text-red-700 hover:bg-red-50'
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div id={'documents-to-print'} className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex flex-col space-y-2 mr-12'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            {getFilterTitle()}
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            {getFilterDescription()}
          </p>
          {isFiltered && (
            <div className='mt-2'>
              <Badge
                variant='outline'
                className='bg-blue-50 text-blue-700 border-blue-200'
              >
                Filtered from Dashboard
              </Badge>
            </div>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {isFiltered && (
            <Button
              variant='outline'
              onClick={() => router.push(routes.documentsManage)}
              className='flex items-center gap-2'
            >
              <Filter className='h-4 w-4' />
              Clear Filter
            </Button>
          )}
          <Button
            variant='outline'
            onClick={() => refetchDocuments()}
            disabled={realDocumentsLoading}
            className='flex items-center gap-2'
          >
            <Search className='h-4 w-4' />
            {realDocumentsLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Quick Actions for Filtered Views */}
      {isFiltered && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <h3 className='font-semibold text-blue-900 mb-2'>Quick Actions</h3>
          <div className='flex flex-wrap gap-2'>
            {filterParam === 'ready_for_review' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_review'
                    );
                    if (firstDoc) handleReview(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_review'
                    )
                  }
                  className='border-blue-300 text-blue-700 hover:bg-blue-100'
                >
                  <Eye className='h-4 w-4 mr-2' />
                  Review First Document
                </Button>
              </>
            )}
            {filterParam === 'ready_for_signing' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_signing'
                    );
                    if (firstDoc) handleSign(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_signing'
                    )
                  }
                  className='border-green-300 text-green-700 hover:bg-green-100'
                >
                  <PenTool className='h-4 w-4 mr-2' />
                  Sign First Document
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div className={`grid grid-cols-1 gap-4 md:grid-cols-5`}>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {statusCounts.total}
              </div>
              <div className='text-sm text-muted-foreground'>
                Total Documents
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {statusCounts.ready_for_review}
              </div>
              <div className='text-sm text-muted-foreground'>
                Ready for Review
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {statusCounts.shipped}
              </div>
              <div className='text-sm text-muted-foreground'>Shipped</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {statusCounts.signed}
              </div>
              <div className='text-sm text-muted-foreground'>'Signed'</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-emerald-600'>
                {statusCounts.approved}
              </div>
              <div className='text-sm text-muted-foreground'>Approved</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters & Sorting */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Filter className='h-4 w-4' />
            Filter & Sort
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Search */}
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search documents...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10 h-9'
            />
          </div>

          {/* Filters & Sorting in one row */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Statuses</SelectItem>
                <SelectItem value='draft'>Draft</SelectItem>
                <SelectItem value='ready_for_review'>
                  Ready for Review
                </SelectItem>
                <SelectItem value='ready_for_signing'>
                  Ready for Signing
                </SelectItem>
                <SelectItem value='signed'>Signed</SelectItem>
                <SelectItem value='shipped'>Shipped</SelectItem>
                <SelectItem value='received'>Received</SelectItem>
                <SelectItem value='approved'>Approved</SelectItem>
                <SelectItem value='archived'>Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={typeFilter}
              onValueChange={value =>
                setTypeFilter(value as 'all' | Document['type'])
              }
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                {availableTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {formatDocumentType(type)}
                  </SelectItem>
                ))}
                {availableTypes.length === 0 && (
                  <>
                    <SelectItem value='Will'>Will</SelectItem>
                    <SelectItem value='Trust'>Trust</SelectItem>
                    <SelectItem value='Healthcare POA'>
                      Healthcare POA
                    </SelectItem>
                    <SelectItem value='Financial POA'>Financial POA</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>

            <Select
              value={sortBy}
              onValueChange={(value: 'dateCreated' | 'version') =>
                setSortBy(value)
              }
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Sort by' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='dateCreated'>Date</SelectItem>
                <SelectItem value='version'>Version</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={sortOrder}
              onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Order' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='desc'>
                  {sortBy === 'dateCreated' ? 'Newest' : 'Highest'}
                </SelectItem>
                <SelectItem value='asc'>
                  {sortBy === 'dateCreated' ? 'Oldest' : 'Lowest'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions Row */}
          <div className='flex items-center justify-between pt-3 border-t'>
            <div className='flex items-center gap-2'>
              <Button
                variant={showArchivedDocuments ? 'default' : 'outline'}
                size='sm'
                onClick={() => setShowArchivedDocuments(!showArchivedDocuments)}
                className='h-8 text-xs'
              >
                {showArchivedDocuments ? 'Hide' : 'Show'} Archived
              </Button>
              {documents.filter(doc => doc.status === 'archived').length >
                0 && (
                <Badge variant='secondary' className='text-xs h-5'>
                  {documents.filter(doc => doc.status === 'archived').length}
                </Badge>
              )}
            </div>

            {(searchTerm ||
              statusFilter !== 'all' ||
              typeFilter !== 'all' ||
              sortBy !== 'dateCreated' ||
              sortOrder !== 'desc') && (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                  setSortBy('dateCreated');
                  setSortOrder('desc');
                }}
                className='h-8 text-xs text-gray-600 hover:text-gray-800'
              >
                Clear All
              </Button>
            )}
          </div>

          {/* Active Filters Status */}
          {(searchTerm ||
            statusFilter !== 'all' ||
            typeFilter !== 'all' ||
            showArchivedDocuments ||
            sortBy !== 'dateCreated' ||
            sortOrder !== 'desc') && (
            <div className='mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-1 text-xs text-blue-800 flex-wrap'>
                  <span className='font-medium'>
                    {filteredDocuments.length} of {documents.length}
                  </span>
                  {searchTerm && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      "{searchTerm}"
                    </Badge>
                  )}
                  {statusFilter !== 'all' && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      {statusFilter}
                    </Badge>
                  )}
                  {typeFilter !== 'all' && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      {formatDocumentType(typeFilter)}
                    </Badge>
                  )}
                  {showArchivedDocuments && (
                    <Badge
                      variant='outline'
                      className='bg-yellow-50 text-yellow-700 border-yellow-200 text-xs h-5'
                    >
                      Archived
                    </Badge>
                  )}
                  {(sortBy !== 'dateCreated' || sortOrder !== 'desc') && (
                    <Badge
                      variant='outline'
                      className='bg-purple-50 text-purple-700 border-purple-200 text-xs h-5'
                    >
                      {sortBy === 'dateCreated' ? 'Date' : 'Ver'}{' '}
                      {sortOrder === 'desc' ? '↓' : '↑'}
                    </Badge>
                  )}
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setTypeFilter('all');
                    setShowArchivedDocuments(false);
                  }}
                  className='text-blue-600 hover:text-blue-800'
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Shipping Controls */}
      {getSignedDocuments().length > 0 && (
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-4'>
                <h3 className='text-sm font-medium text-[var(--foreground)]'>
                  Bulk Shipping ({selectedDocuments.size} selected)
                </h3>
                <div className='flex gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleSelectAll(getSignedDocuments())}
                    disabled={isBulkShipping}
                  >
                    Select All Signed
                  </Button>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleDeselectAll}
                    disabled={isBulkShipping || selectedDocuments.size === 0}
                  >
                    Deselect All
                  </Button>
                </div>
              </div>
              <Button
                onClick={handleBulkShip}
                disabled={selectedDocuments.size === 0 || isBulkShipping}
                className='bg-blue-600 hover:bg-blue-700 text-[var(--off-white)]'
              >
                {isBulkShipping
                  ? 'Shipping...'
                  : `Ship ${selectedDocuments.size} Documents`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='p-8 text-center flex flex-col items-center'>
              <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No documents found
              </h3>
              <p className='text-muted-foreground mb-4'>
                {documents.length === 0
                  ? "You haven't created any documents yet."
                  : 'Try adjusting your search or filter criteria.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map(document => (
            <DocumentStatusCard
              key={document.id}
              document={document}
              isDownloadingPdf={downloadingDocuments.has(document.id)}
              isResubmitting={resubmittingDocuments.has(document.id)}
              isRetracting={retractingDocuments.has(document.id)}
              isShipping={shippingDocuments.has(document.id)}
              isDeleting={deletingDocuments.has(document.id)}
              onPreview={() => handlePreview(document)}
              onReview={() => handleReview(document)}
              onSign={() => handleSign(document)}
              onDownload={() => handleDownload(document)}
              onResubmit={() => handleRecreate(document)}
              onRetract={() => handleRetract(document)}
              onShip={() => handleShip(document)}
              onDelete={() => handleDelete(document)}
              onPermanentDelete={() => handlePermanentDelete(document)}
              isSelected={selectedDocuments.has(document.id)}
              isAddedToShipping={selectedDocuments.has(document.id)}
              onBulkSelect={handleDocumentSelect}
            />
          ))
        )}
      </div>
    </div>
  );
}

export default function DocumentsPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <DocumentsContent />
    </Suspense>
  );
}
