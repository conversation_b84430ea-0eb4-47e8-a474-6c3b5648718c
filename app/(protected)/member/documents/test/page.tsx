'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

const client = generateClient<Schema>();

const getDocumentById = async (documentId: string) => {
  try {
    const { data: document, errors } = await client.models.Document.get({
      id: documentId,
    });
    if (errors) {
      console.error('Error fetching document by id:', errors);
      throw new Error('Failed to fetch document');
    }
    return document;
  } catch (error) {
    console.error('Error fetching document by id:', error);
    throw error;
  }
};

const Page = () => {
  const [inputId, setInputId] = useState<string>('');
  const [documentId, setDocumentId] = useState<string | null>(null);

  const { data, isFetching, isError, error } = useQuery({
    queryKey: ['document', documentId],
    queryFn: () => getDocumentById(documentId as string),
    enabled: !!documentId,
  });

  return (
    <div style={{ padding: 16 }}>
      <h1>Test Page</h1>
      <div style={{ marginTop: 12, display: 'flex', gap: 8 }}>
        <input
          type='text'
          placeholder='Enter Document ID'
          value={inputId}
          onChange={e => setInputId(e.target.value)}
          style={{
            padding: 8,
            flex: '1 1 auto',
            border: '1px solid #ccc',
            borderRadius: 4,
          }}
        />
        <button
          onClick={() => setDocumentId(inputId.trim() ? inputId.trim() : null)}
          style={{
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: 4,
            background: '#f5f5f5',
            cursor: 'pointer',
          }}
        >
          Fetch
        </button>
      </div>

      <div
        style={{
          marginTop: 16,
          padding: 12,
          border: '1px solid #e5e7eb',
          borderRadius: 6,
          background: '#fafafa',
        }}
      >
        {!documentId ? (
          <span>Enter an ID and click Fetch.</span>
        ) : isFetching ? (
          <span>Loading...</span>
        ) : isError ? (
          <span style={{ color: 'crimson' }}>
            Error: {(error as Error)?.message ?? 'Failed to fetch document'}
          </span>
        ) : data ? (
          <pre
            style={{
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
            }}
          >
            {JSON.stringify(data, null, 2)}
          </pre>
        ) : (
          <span>No document found.</span>
        )}
      </div>
    </div>
  );
};

export default Page;
