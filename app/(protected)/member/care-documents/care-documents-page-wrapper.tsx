'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  Save,
  User,
  Briefcase,
  Stethoscope,
  Phone,
  AlertTriangle,
  Cat,
  Wallet,
  Shield,
  Home,
  Building,
  Heart,
  Bed,
  Flower,
  Gift,
  Upload,
} from 'lucide-react';
import PersonalIdentificationSection from '@/components/care-documents-new/main-sections/personal-identification-section';
import EmploymentSection from '@/components/care-documents-new/main-sections/employment-section';
import MedicalSection from '@/components/care-documents-new/main-sections/medical-section';
import EmergencyContactsSection from '@/components/care-documents-new/main-sections/emergency-contacts-section';
import ClientEmergencyIncidentsSection from '@/components/care-documents-new/main-sections/client-emergency-incidents-section';
import PetsSection from '@/components/care-documents-new/main-sections/pets-section';
import FinancialSection from '@/components/care-documents-new/main-sections/financial-section';
import InsuranceSection from '@/components/care-documents-new/main-sections/insurance-section';
import PropertySection from '@/components/care-documents-new/main-sections/property-section';
import ImportantToMeSection from '@/components/care-documents-new/main-sections/important-to-me-section';
import LongTermCareSection from '@/components/care-documents-new/main-sections/long-term-care-section';
import LastWishesSection from '@/components/care-documents-new/main-sections/last-wishes-section';
import LegacySection from '@/components/care-documents-new/main-sections/legacy-section';
import AssetInformationSection from '@/components/care-documents-new/main-sections/asset-information-section';
import UploadsSection from '@/components/care-documents-new/main-sections/uploads-section';
import {
  useCareDocumentsNew,
  CareDocumentSectionType,
} from '@/hooks/useCareDocumentsNew';
import {
  CareDocumentsProvider,
  SectionDataMap,
  useCareDocumentsContext,
} from '@/app/context/CareDocumentsContext';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

// Define section types for type safety
type SectionType = {
  id: string;
  title: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  sectionType: CareDocumentSectionType;
  disable: boolean;
  disableTooltip: string;
};

interface CareDocumentsPageWrapperProps {
  externalSectionsData?: SectionDataMap;
}

export function CareDocumentsPageWrapper({
  externalSectionsData,
}: CareDocumentsPageWrapperProps = {}) {
  return (
    <CareDocumentsProvider sectionsData={externalSectionsData}>
      <CareDocumentsPageContent viewOnlyMode={!!externalSectionsData} />
    </CareDocumentsProvider>
  );
}

interface CareDocumentsPageContentProps {
  viewOnlyMode?: boolean;
}

// Content component that uses the context
function CareDocumentsPageContent({
  viewOnlyMode,
}: CareDocumentsPageContentProps) {
  // State to track the active section
  const [activeSection, setActiveSection] = useState<string>(
    'personal-identification'
  );
  const [saving, setSaving] = useState(false);

  // Use the context
  const {
    sectionData,
    modifiedSections,
    saveModifiedSections,
    loading,
    error,
    documentId,
  } = useCareDocumentsContext();

  // Only show the big page loader during initial load (when we have no data yet)
  const isInitialLoading =
    loading && Object.keys(sectionData || {}).length === 0;

  // Ref for the scrollable content area
  const contentAreaRef = React.useRef<HTMLDivElement>(null);

  // Effect to scroll to top when active section changes
  useEffect(() => {
    if (contentAreaRef.current) {
      contentAreaRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [activeSection]);

  // Define all sections with their IDs, titles, icons, and components
  const sections: SectionType[] = [
    {
      id: 'personal-identification',
      title: 'Personal Identification',
      icon: <User size={18} />,
      component: <PersonalIdentificationSection disabled={viewOnlyMode} />,
      sectionType: 'PersonalIdentification',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'employment',
      title: 'Employment',
      icon: <Briefcase size={18} />,
      component: <EmploymentSection disabled={viewOnlyMode} />,
      sectionType: 'Employment',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'medical',
      title: 'Medical',
      icon: <Stethoscope size={18} />,
      component: <MedicalSection disabled={viewOnlyMode} />,
      sectionType: 'Medical',
      disable: false,
      disableTooltip:
        'We do not collect your medical information as we are not serving as your medical POA.',
    },
    {
      id: 'emergency-contacts',
      title: 'Emergency Contacts',
      icon: <Phone size={18} />,
      component: <EmergencyContactsSection disabled={viewOnlyMode} />,
      sectionType: 'EmergencyContacts',
      disable: false,
      disableTooltip: '',
    },
    // {
    //   id: 'client-emergency-incidents',
    //   title: 'Emergency Incidents',
    //   icon: <AlertTriangle size={18} />,
    //   component: <ClientEmergencyIncidentsSection />,
    //   sectionType: 'EmergencyIncidents',
    // },
    {
      id: 'pets',
      title: 'Pets',
      icon: <Cat size={18} />,
      component: <PetsSection disabled={viewOnlyMode} />,
      sectionType: 'Pets',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'financial',
      title: 'Financial',
      icon: <Wallet size={18} />,
      component: <FinancialSection disabled={viewOnlyMode} />,
      sectionType: 'Financial',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'insurance',
      title: 'Insurance',
      icon: <Shield size={18} />,
      component: <InsuranceSection disabled={viewOnlyMode} />,
      sectionType: 'Insurance',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'property',
      title: 'Property',
      icon: <Home size={18} />,
      component: <PropertySection disabled={viewOnlyMode} />,
      sectionType: 'Property',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'asset-information',
      title: 'Asset Information',
      icon: <Building size={18} />,
      component: <AssetInformationSection disabled={viewOnlyMode} />,
      sectionType: 'AssetInformation',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'important-to-me',
      title: 'Important to Me',
      icon: <Heart size={18} />,
      component: <ImportantToMeSection disabled={viewOnlyMode} />,
      sectionType: 'ImportantToMe',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'long-term-care',
      title: 'Long Term Care',
      icon: <Bed size={18} />,
      component: <LongTermCareSection disabled={viewOnlyMode} />,
      sectionType: 'LongTermCare',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'last-wishes',
      title: 'Last Wishes',
      icon: <Flower size={18} />,
      component: <LastWishesSection disabled={viewOnlyMode} />,
      sectionType: 'LastWishes',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'legacy',
      title: 'Legacy',
      icon: <Gift size={18} />,
      component: <LegacySection disabled={viewOnlyMode} />,
      sectionType: 'Legacy',
      disable: false,
      disableTooltip: '',
    },
    {
      id: 'uploads',
      title: 'Uploads',
      icon: <Upload size={18} />,
      component: <UploadsSection disabled={viewOnlyMode} />,
      sectionType: 'Uploads',
      disable: false,
      disableTooltip: '',
    },
  ];

  // Get the current active section component
  const activeComponent = sections.find(
    section => section.id === activeSection
  )?.component;

  // Handle saving all sections
  const handleSave = async () => {
    if (!documentId) {
      toast.error('No document to save');
      return;
    }

    // Simple validation: Check if at least one identifying document is selected
    const personalIdData = sectionData.PersonalIdentification;
    if (
      personalIdData &&
      (!personalIdData.identifyingDocuments ||
        personalIdData.identifyingDocuments.length === 0)
    ) {
      toast.error(
        'Please select at least one form of identifying document in the Personal Identification section.'
      );
      return;
    }

    setSaving(true);
    try {
      // Only save sections that have been modified
      if (modifiedSections.size > 0) {
        await saveModifiedSections();
        toast.success('Care documents saved successfully');
      } else {
        toast.info('No changes to save');
      }
    } catch (err) {
      console.error('Error saving care documents:', err);
      toast.error('Failed to save care documents');
    } finally {
      setSaving(false);
    }
  };

  // Popup warning before user leaves the page
  useEffect(() => {
    const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
      event.preventDefault();
      event.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return (
    <div className='flex h-screen overflow-hidden'>
      {/* Main Content */}
      <div className='flex-1 flex flex-col overflow-hidden'>
        {/* Fixed Header */}
        {!viewOnlyMode && (
          <div className=' z-10 pb-6'>
            <div className='w-full mx-auto'>
              <div className='flex items-center justify-between gap-4'>
                <div>
                  <h1 className='text-3xl font-bold text-[var(--foreground)]'>
                    Create Your Care Documents
                  </h1>
                  <p className='text-[var(--custom-gray-dark)] mt-2'>
                    With just a few clicks, create comprehensive care documents
                    to outline your wishes and preferences for various aspects
                    of your life.
                  </p>
                </div>
                {/* Save Button */}
                <div>
                  <Button
                    className='flex items-center gap-2 min-w-[180px]'
                    onClick={handleSave}
                    disabled={saving || loading || !document}
                  >
                    <Save className='h-4 w-4 mr-2' />
                    {saving
                      ? 'Saving...'
                      : loading
                        ? 'Auto-Saving...'
                        : 'Save Care Documents'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
        <div className='flex gap-2 overflow-y-auto'>
          {/* Sidebar */}
          <aside className='w-64 sticky bg-white border-r border-gray-200 rounded-lg max-h-[100dvh] shadow-sm overflow-y-auto'>
            <div className='p-6'>
              <h2 className='text-lg font-semibold mb-6'>Care Documents</h2>
              <nav className='space-y-1'>
                {sections.map(section => (
                  <button
                    key={section.id}
                    onClick={() => {
                      if (!section.disable) {
                        setActiveSection(section.id);
                        if (modifiedSections.size > 0) {
                          handleSave();
                        }
                      }
                    }}
                    className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg cursor-pointer transition-colors
                      ${
                        activeSection === section.id
                          ? 'bg-[var(--eggplant)]/10 text-[var(--eggplant)] shadow-[inset_4px_0_0_var(--eggplant)]'
                          : 'hover:bg-gray-100 text-gray-700 hover:text-[var(--eggplant)]'
                      }
                      ${section.disable ? 'opacity-30' : ''}
                    `}
                  >
                    <span className='mr-3'>{section.icon}</span>
                    {section.disable && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className='truncate'>{section.title}</span>
                        </TooltipTrigger>
                        <TooltipContent className='max-w-[300px] break-words bg-background text-foreground border shadow-md'>
                          {section.disableTooltip}
                        </TooltipContent>
                      </Tooltip>
                    )}
                    {!section.disable && (
                      <span className='truncate'>{section.title}</span>
                    )}
                  </button>
                ))}
              </nav>
            </div>
          </aside>
          {/* Scrollable Content */}
          <div ref={contentAreaRef} className='flex-1 overflow-y-auto'>
            <div className='container'>
              <div className='max-w-4xl mx-auto'>
                {/* Show loading state */}
                {isInitialLoading ? (
                  <div className='flex items-center justify-center min-h-[400px]'>
                    <div className='text-center'>
                      <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
                      <p className='text-muted-foreground'>
                        Loading your documents...
                      </p>
                    </div>
                  </div>
                ) : (
                  !error && activeComponent
                )}

                {/* Show error state */}
                {error && <p className='text-red-500'>Error: {error}</p>}

                {/* Info Section */}
                {!viewOnlyMode && <Card className='bg-blue-50 border-blue-200 mt-8'>
                  <CardContent className='p-6'>
                    <h3 className='font-semibold text-blue-900 mb-2'>
                      About Care Documents
                    </h3>
                    <div className='space-y-2 text-sm text-blue-800'>
                      <p>
                        • Care documents are designed to be updated regularly to
                        keep information current
                      </p>
                      <p>
                        • All information is securely stored and can only be
                        accessed by those you authorize
                      </p>
                      <p>
                        • You can update your care documents at any time to
                        reflect changes in your preferences
                      </p>
                      <p>
                        • Consider reviewing your care documents annually or
                        after major life events
                      </p>
                    </div>
                  </CardContent>
                </Card>}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
