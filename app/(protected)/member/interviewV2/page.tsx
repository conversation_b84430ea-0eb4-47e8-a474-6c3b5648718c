'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import {
  RotateCcw,
  <PERSON>Left,
  ArrowRight,
  Loader2,
  User,
  Edit,
  Heart,
} from 'lucide-react';
import ProgressSidebar from '@/components/interview-v2/ProgressSidebar';
import PeopleLibrary from '@/components/interview-v2/PeopleLibrary';
import ProfileSection from '@/components/interview-v2/sections/ProfileSection';
import EmergencySection from '@/components/interview-v2/sections/EmergencySection';
import AfterYouDieSection from '@/components/interview-v2/sections/AfterYouDieSection';
import FinancialDecisionsSection from '@/components/interview-v2/sections/FinancialDecisionsSection';
import MedicalDecisionsSection from '@/components/interview-v2/sections/MedicalDecisionsSection';
import AdditionalConsiderationsSection from '@/components/interview-v2/sections/AdditionalConsiderationsSection';
import ReviewSection from '@/components/interview-v2/sections/ReviewSection';

import {
  loadInterviewProgressV2,
  saveInterviewProgressV2,
  resetInterviewProgressV2,
} from '@/app/utils/interviewV2Progress';
import { useUserActionSave } from '@/app/hooks/useAutoSave';

// Person/Entity types for People Library
export type PersonType = 'individual' | 'charity' | 'business';

export interface Person {
  id: string;
  type: PersonType;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  entityName?: string;
  dateOfBirth?: string;
  ein?: string;
  phoneNumber?: string;
  address?: string;
  relationshipType?: string; // e.g., 'Spouse', 'Friend', 'Family', 'Business Partner', etc.
}

// Main data structure for interview progress
type StepsData = {
  // People Library - central repository
  people?: Person[];

  // Subsection completion tracking
  completedSubsections?: {
    [sectionKey: string]: {
      [subsectionKey: string]: boolean;
    };
  };

  // Your Profile section
  profile?: {
    firstName?: string;
    middleName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    dateOfBirth?: string;
    address?: {
      street?: string;
      street2?: string;
      county?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    maritalStatus?: 'married' | 'single' | 'widowed';
    spouse?: Person; // Reference to spouse in People Library
    soundMind?: boolean;
    selectedDocuments?: {
      will?: boolean;
      trust?: boolean;
      financialPOA?: boolean;
      medicalPOA?: boolean;
    };
  };

  // Emergency Key Considerations
  emergency?: {
    dependents?: Array<{
      id: string;
      person: Person; // Reference to person in People Library (relationshipType is stored in person)
      careInstructions: string;
    }>;
    pets?: Array<{
      id: string;
      name: string;
      primaryGuardian?: Person; // Required field
      secondaryGuardian?: Person; // Optional backup
      petTrustSupport?: number; // Dollar amount for pet care
      takerOfLastResort?: Person; // Final guardian field
    }>;
    noDependents?: boolean;
    noPets?: boolean;
  };

  // After You Die section
  afterYouDie?: {
    executor?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    successorTrustee?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    properties?: Array<{
      id: string;
      type: string;
      address: string;
      title: string;
      ownership: number;
      coOwners?: Array<{ person: Person; percentage: number }>;
      recipient?: Person;
    }>;
    specificGifts?: Array<{
      id: string;
      recipient: Person;
      type: 'financial' | 'other';
      bankName?: string;
      accountNumber?: string;
      accountType?: 'IRA' | 'Brokerage' | 'Savings' | 'Checking' | 'Other';
      distributionMethod?: 'fixed' | 'percentage';
      amount?: number;
      description?: string;
    }>;
    mainBeneficiaries?: Array<{
      id: string;
      primaryBeneficiary: Person;
      percentage: number;
      successors: Array<{ person: Person; isDescendants?: boolean }>;
      distributionStages?: Array<{ age: number; percentage: number }>;
    }>;
    lastResortBeneficiary?: Person;
    noOwnedProperties?: boolean;
  };

  // Financial Decisions
  financial?: {
    agent?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    powers?: Record<string, boolean>;
  };

  // Medical Decisions
  medical?: {
    proxy?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    directives?: Record<string, boolean>;
    proxyAuthority?: string;
    wantConsultation?: boolean;
    consultationPeople?: Person[];
    organDonation?: {
      enabled?: boolean;
      specificOrgans?: string[];
      limitations?: string;
    };
    medicalInfoAccessPeople?: Person[];
  };

  // Additional Considerations
  additional?: {
    personalProperty?: 'spouse' | 'list';
    burial?: 'buried' | 'cremated' | 'other';
    burialOther?: string;
    guardian?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    legalProvisions?: {
      disclaimerTrust?: boolean;
      drugAbuse?: boolean;
      mineralRights?: boolean;
    };
  };

  // Review section
  review?: {
    completed?: boolean;
    submitted?: boolean; // Track if interview has been submitted and locked
  };
};

export default function InterviewV2Page() {
  const baseItems = useMemo(
    () => [
      { key: 'profile', label: 'My Profile' },
      { key: 'emergency', label: 'Emergency Key Considerations' },
      { key: 'afterYouDie', label: 'After I Die' },
      { key: 'financial', label: 'Financial Decisions' },
      { key: 'medical', label: 'Medical Decisions' },
      { key: 'additional', label: 'Additional Considerations' },
      { key: 'review', label: 'Review & Finalize' },
    ],
    []
  );

  const [active, setActive] = useState<string>('profile');
  const [stepsData, setStepsData] = useState<StepsData>({
    people: [],
  });
  const [doneStepsData, setDoneStepsData] = useState<StepsData>({});

  const [savingNext, setSavingNext] = useState<boolean>(false);
  const [resetting, setResetting] = useState<boolean>(false);
  const [isInterviewSubmitted, setIsInterviewSubmitted] =
    useState<boolean>(false);

  // Function to check if a step is completed
  const isStepCompleted = useCallback(
    (stepKey: string, data: StepsData): boolean => {
      const stepData = data[stepKey as keyof StepsData];

      if (!stepData) return false;

      switch (stepKey) {
        case 'profile':
          const profile = stepData as StepsData['profile'];

          // Check basic required fields
          const basicFieldsComplete = !!(
            profile?.firstName &&
            profile?.lastName &&
            profile?.email &&
            profile?.dateOfBirth &&
            profile?.address?.street &&
            profile?.address?.city &&
            profile?.address?.state &&
            profile?.address?.zip &&
            profile?.maritalStatus &&
            profile?.soundMind !== undefined &&
            profile?.selectedDocuments
          );

          // If marital status is married, spouse must be selected
          if (profile?.maritalStatus === 'married' && !profile?.spouse) {
            return false;
          }

          return basicFieldsComplete;

        case 'emergency':
          const emergency = stepData as StepsData['emergency'];

          // Check if all pets have primary guardians
          const petsValid =
            !emergency?.pets?.length ||
            emergency.pets.every(pet => pet.primaryGuardian);

          // Complete if user has dependents/pets OR has explicitly checked "no dependents/pets"
          // AND all pets have guardians
          return !!(
            (emergency?.dependents?.length ||
              emergency?.pets?.length ||
              emergency?.noDependents ||
              emergency?.noPets) &&
            petsValid
          );

        case 'afterYouDie':
          const afterYouDie = stepData as StepsData['afterYouDie'];
          const selectedDocs = data.profile?.selectedDocuments;

          // Check executor completion if will is selected
          let executorComplete = true;
          if (selectedDocs?.will) {
            executorComplete = !!(
              afterYouDie?.executor?.useCompany === true ||
              (afterYouDie?.executor?.useCompany === false &&
                afterYouDie?.executor?.primary)
            );
          }

          // Check successor trustee completion if trust is selected
          let trusteeComplete = true;
          if (selectedDocs?.trust) {
            trusteeComplete = !!(
              afterYouDie?.successorTrustee?.useCompany === true ||
              (afterYouDie?.successorTrustee?.useCompany === false &&
                afterYouDie?.successorTrustee?.primary)
            );
          }

          // Check if properties and beneficiaries are completed
          const propertiesComplete = !!(
            afterYouDie?.properties?.length || afterYouDie?.noOwnedProperties
          );
          const beneficiariesComplete =
            !!afterYouDie?.mainBeneficiaries?.length;

          // Check if specific gifts are valid (if any exist)
          const specificGiftsValid =
            !afterYouDie?.specificGifts?.length ||
            afterYouDie.specificGifts.every(gift => {
              if (gift.type === 'financial') {
                return (
                  gift.bankName &&
                  gift.accountType &&
                  gift.amount &&
                  gift.amount > 0
                );
              }
              return true; // For 'other' type gifts, no additional validation needed
            });

          // If either will or trust is selected, require all components
          if (selectedDocs?.will || selectedDocs?.trust) {
            return (
              executorComplete &&
              trusteeComplete &&
              propertiesComplete &&
              beneficiariesComplete &&
              specificGiftsValid
            );
          }

          // If neither is selected, consider complete if any data exists and specific gifts are valid
          return !!(
            (afterYouDie?.executor ||
              afterYouDie?.successorTrustee ||
              afterYouDie?.properties?.length ||
              afterYouDie?.mainBeneficiaries?.length ||
              afterYouDie?.noOwnedProperties) &&
            specificGiftsValid
          );

        case 'financial':
          const financial = stepData as StepsData['financial'];
          const financialRequired =
            data.profile?.selectedDocuments?.financialPOA;
          if (financialRequired) {
            return !!(
              financial?.agent?.useCompany === true ||
              (financial?.agent?.useCompany === false &&
                financial?.agent?.primary)
            );
          }
          return !!(financial?.agent?.primary || financial?.agent?.useCompany);

        case 'medical':
          const medical = stepData as StepsData['medical'];
          const medicalRequired = data.profile?.selectedDocuments?.medicalPOA;

          // Check if proxy is properly configured
          const proxyComplete = !!(
            medical?.proxy?.useCompany === true ||
            (medical?.proxy?.useCompany === false && medical?.proxy?.primary)
          );

          // Check if all medical directives are answered
          const medicalDirectives = [
            'life_sustaining',
            'artificial_nutrition',
            'pain_relief',
            'antibiotics',
            'dialysis',
            'ventilator',
          ];
          const directivesComplete = medicalDirectives.every(
            directive => medical?.directives?.[directive] !== undefined
          );

          // Check if proxy authority is selected
          const proxyAuthorityComplete = !!medical?.proxyAuthority;

          // Check if consultation preference is answered
          const consultationPreferenceComplete =
            medical?.wantConsultation !== undefined;

          // Check if organ donation preference is answered
          const organDonationComplete =
            medical?.organDonation?.enabled !== undefined;

          if (medicalRequired) {
            return (
              proxyComplete &&
              directivesComplete &&
              proxyAuthorityComplete &&
              consultationPreferenceComplete &&
              organDonationComplete
            );
          }

          return (
            proxyComplete &&
            directivesComplete &&
            proxyAuthorityComplete &&
            consultationPreferenceComplete &&
            organDonationComplete
          );

        case 'additional':
          const additional = stepData as StepsData['additional'];
          return !!(additional?.personalProperty || additional?.burial);

        case 'review':
          // Review is completed when user has filled most sections
          return !!(data.profile?.firstName && data.profile?.email);

        default:
          return false;
      }
    },
    []
  );

  // Filter items based on selected documents
  const items = useMemo(() => {
    const selectedDocs = stepsData.profile?.selectedDocuments;

    return baseItems
      .filter(item => {
        // Always show profile, emergency, additional, and review
        if (
          ['profile', 'emergency', 'additional', 'review'].includes(item.key)
        ) {
          return true;
        }

        // Show afterYouDie if will or trust is selected
        if (item.key === 'afterYouDie') {
          return selectedDocs?.will || selectedDocs?.trust;
        }

        // Show financial if financialPOA is selected
        if (item.key === 'financial') {
          return selectedDocs?.financialPOA;
        }

        // Show medical if medicalPOA is selected
        if (item.key === 'medical') {
          return selectedDocs?.medicalPOA;
        }

        return true;
      })
      .map(it => ({
        ...it,
        completed:
          it.key === 'review'
            ? isInterviewSubmitted
            : isStepCompleted(it.key, doneStepsData),
        disabled: false, // We'll add logic for disabled state later
      }));
  }, [
    baseItems,
    stepsData.profile?.selectedDocuments,
    doneStepsData,
    isStepCompleted,
    isInterviewSubmitted,
  ]);

  // React Query: load interview progress
  const queryClient = useQueryClient();
  const {
    data: progress,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
    retry: 2,
  });

  // Initialize local UI state from loaded progress once
  const initializedRef = useRef(false);
  const [dataInitialized, setDataInitialized] = useState(false);

  useEffect(() => {
    if (progress !== undefined && !initializedRef.current) {
      setActive(progress?.currentStep || 'profile');
      setStepsData((progress?.stepsData || {}) as StepsData);
      setDoneStepsData((progress?.stepsData || {}) as StepsData);

      // Check if interview was previously submitted
      const stepsData = (progress?.stepsData || {}) as StepsData;
      setIsInterviewSubmitted(!!stepsData.review?.submitted);

      initializedRef.current = true;

      // Mark data as initialized immediately after state updates
      setDataInitialized(true);
    }
  }, [progress]);

  // Function to check if interview should be marked as completed
  const shouldMarkAsCompleted = useCallback(
    (data: StepsData): boolean => {
      const selectedDocs = data.profile?.selectedDocuments;
      if (!selectedDocs || !Object.values(selectedDocs).some(Boolean)) {
        console.log('shouldMarkAsCompleted: No documents selected');
        return false; // No documents selected
      }

      // Check all required sections based on selected documents
      const requiredSections = [
        { key: 'profile', required: true },
        { key: 'emergency', required: false }, // Optional but should be addressed
        {
          key: 'afterYouDie',
          required: selectedDocs.will || selectedDocs.trust,
        },
        { key: 'financial', required: selectedDocs.financialPOA },
        { key: 'medical', required: selectedDocs.medicalPOA },
        { key: 'additional', required: false }, // Optional
      ];

      const requiredOnly = requiredSections.filter(section => section.required);
      const completionStatus = requiredOnly.map(section => ({
        ...section,
        completed: isStepCompleted(section.key, data),
      }));

      const allCompleted = completionStatus.every(section => section.completed);

      console.log('shouldMarkAsCompleted: Completion check:', {
        selectedDocs,
        requiredSections: completionStatus,
        allCompleted,
        timestamp: new Date().toLocaleTimeString(),
      });

      return allCompleted;
    },
    [isStepCompleted]
  );

  // User action save functionality - only saves when user makes changes
  const {
    isSaving: isUserActionSaving,
    forceSave,
    saveAfterUserAction,
  } = useUserActionSave({
    data: stepsData,
    currentStep: active,
    onSaveSuccess: savedData => {
      // Update doneStepsData when save succeeds
      setDoneStepsData((savedData?.stepsData || {}) as StepsData);
    },
    shouldMarkAsCompleted,
  });

  // Remove the automatic save effect - we'll save only on user actions

  const updateStep = useCallback(
    (key: keyof StepsData, partial: Record<string, any> | any[]) => {
      console.log(`Updating step data for ${key}:`, partial);

      setStepsData(prev => {
        const newData = {
          ...prev,
          [key]:
            key === 'people' && Array.isArray(partial)
              ? partial
              : Array.isArray(partial)
                ? partial
                : { ...(prev[key] as object | undefined), ...partial },
        };

        console.log('New stepsData after update:', newData);
        return newData;
      });

      // Trigger save after user action
      console.log('Triggering saveAfterUserAction...');
      saveAfterUserAction();
    },
    [saveAfterUserAction]
  );

  // Function to mark subsection as completed
  const markSubsectionComplete = useCallback(
    (sectionKey: string, subsectionKey: string, completed: boolean = true) => {
      setStepsData(prev => ({
        ...prev,
        completedSubsections: {
          ...prev.completedSubsections,
          [sectionKey]: {
            ...prev.completedSubsections?.[sectionKey],
            [subsectionKey]: completed,
          },
        },
      }));
    },
    []
  );

  // Function to check if subsection is completed
  const isSubsectionCompleted = useCallback(
    (sectionKey: string, subsectionKey: string): boolean => {
      return (
        stepsData.completedSubsections?.[sectionKey]?.[subsectionKey] || false
      );
    },
    [stepsData.completedSubsections]
  );

  // Mutations
  const saveMutation = useMutation({
    mutationFn: async (payload: {
      nextStep?: string;
      stepsData: StepsData;
    }) => {
      // Check if interview should be marked as completed
      const shouldComplete = shouldMarkAsCompleted(payload.stepsData);
      const status = shouldComplete ? 'completed' : 'in_progress';

      return await saveInterviewProgressV2({
        status,
        currentStep: payload.nextStep ?? active,
        stepsData: payload.stepsData,
      });
    },
    onSuccess: (data, variables) => {
      queryClient.setQueryData(['interviewV2Progress'], data);
      // Update doneStepsData with the current stepsData to reflect changes immediately
      setDoneStepsData(variables.stepsData);
      // Invalidate mental capacity check cache to ensure Document Management page reflects changes
      queryClient.invalidateQueries({ queryKey: ['mentalCapacityCheck'] });
    },
  });

  const resetMutation = useMutation({
    mutationFn: resetInterviewProgressV2,
    onSuccess: data => {
      queryClient.setQueryData(['interviewV2Progress'], data);
      setDoneStepsData({});
    },
  });

  const persist = useCallback(
    async (nextStep?: string) => {
      try {
        await saveMutation.mutateAsync({ nextStep, stepsData });
      } catch (e) {
        console.error('Failed to save interview progress v2', e);
      }
    },
    [saveMutation, stepsData]
  );

  const isNextDisabled =
    savingNext ||
    resetMutation.isPending ||
    items.findIndex(i => i.key === active) === items.length - 1;
  const isResetDisabled = resetting || saveMutation.isPending;

  // Conditional rendering moved to the end - all hooks must be called first

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPrev = async () => {
    const idx = items.findIndex(i => i.key === active);
    if (idx > 0) {
      const target = items[idx - 1].key;
      await forceSave(); // Force save before navigation
      setActive(target);
      scrollToTop();
    }
  };

  const goToNext = async () => {
    const idx = items.findIndex(i => i.key === active);
    if (idx < items.length - 1) {
      const target = items[idx + 1].key;
      try {
        setSavingNext(true);
        await forceSave(); // Force save before navigation
        setActive(target);
        scrollToTop();
      } finally {
        setSavingNext(false);
      }
    }
  };

  const handleReset = async () => {
    if (confirm('Reset the interview? This will clear all entered data.')) {
      try {
        setResetting(true);
        await resetMutation.mutateAsync();
        setStepsData({});
        setActive('profile');
        setIsInterviewSubmitted(false);
      } catch (e) {
        console.error('Failed to reset interview progress v2', e);
      } finally {
        setResetting(false);
      }
    }
  };

  const handleSubmitInterview = useCallback(async () => {
    try {
      // Mark interview as submitted
      setStepsData(prevStepsData => {
        const updatedData = {
          ...prevStepsData,
          review: {
            ...prevStepsData.review,
            submitted: true,
            completed: true,
          },
        };

        // Save the submitted state immediately
        saveMutation.mutate({
          stepsData: updatedData,
          nextStep: active,
        });

        return updatedData;
      });
      setIsInterviewSubmitted(true);
    } catch (e) {
      console.error('Failed to submit interview', e);
    }
  }, [saveMutation, active]);

  const handleUnlockInterview = useCallback(async () => {
    if (
      confirm(
        'This will unlock your answers for editing and allow you to generate new documents. Continue?'
      )
    ) {
      try {
        // Mark interview as not submitted
        setStepsData(prevStepsData => {
          const updatedData = {
            ...prevStepsData,
            review: {
              ...prevStepsData.review,
              submitted: false,
            },
          };

          // Save the unlocked state immediately
          saveMutation.mutate({
            stepsData: updatedData,
            nextStep: active,
          });

          return updatedData;
        });
        setIsInterviewSubmitted(false);
      } catch (e) {
        console.error('Failed to unlock interview', e);
      }
    }
  }, [saveMutation, active]);

  // Handle loading state
  if (isLoading && !initializedRef.current) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='grid grid-cols-1 md:grid-cols-12 gap-6'>
          <div className='md:col-span-3'>
            <div className='space-y-3'>
              <div className='h-6 w-40 bg-accent rounded animate-pulse' />
              {Array.from({ length: 7 }).map((_, idx) => (
                <div
                  key={idx}
                  className='h-4 w-32 bg-accent rounded animate-pulse'
                />
              ))}
            </div>
          </div>
          <div className='md:col-span-9'>
            <Card className='mb-4'>
              <CardHeader className='flex flex-col gap-1'>
                <CardTitle className='text-base'>
                  <span className='inline-flex items-center gap-2'>
                    <Loader2 className='h-4 w-4 animate-spin text-primary' />
                    Loading interview...
                  </span>
                </CardTitle>
                <div className='text-muted-foreground text-sm'>
                  Fetching your saved progress
                </div>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='h-6 w-2/3 bg-accent rounded animate-pulse' />
                  <div className='h-4 w-1/2 bg-accent rounded animate-pulse' />
                  <div className='h-10 w-full bg-accent rounded animate-pulse' />
                  <div className='h-10 w-full bg-accent rounded animate-pulse' />
                  <div className='h-10 w-1/3 bg-accent rounded animate-pulse' />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (isError && !initializedRef.current) {
    return (
      <div className='container mx-auto py-20 px-4'>
        <div className='max-w-xl mx-auto text-center space-y-4'>
          <div className='text-red-600 font-medium'>
            Failed to load interview progress
          </div>
          <div className='text-sm text-muted-foreground'>
            {(error as Error | undefined)?.message ||
              'An unexpected error occurred.'}
          </div>
          <div className='flex items-center justify-center gap-3'>
            <Button variant='outline' onClick={() => refetch()}>
              Try again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-full mx-auto'>
      <div className='flex items-start justify-between gap-4 mb-6'>
        <div className='flex flex-col space-y-2 mr-12 mb-0'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            My Info & Wishes: Childfree Estate Plan Interview
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Tell us about yourself and your preferences, and we’ll use that
            information to create your state-specific legal documents —
            including your Will, Trust, and Financial & Medical Powers of
            Attorney.
          </p>
        </div>
        {isInterviewSubmitted ? (
          <Button
            variant='outline'
            onClick={handleUnlockInterview}
            className='flex items-center gap-2'
            disabled={resetting}
          >
            <Edit className='h-4 w-4' /> I want to change my answers & generate
            new documents
          </Button>
        ) : (
          <Button
            variant='destructive'
            onClick={handleReset}
            className='flex items-center gap-2'
            disabled={isResetDisabled}
          >
            {resetting ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' /> Resetting…
              </>
            ) : (
              <>
                <RotateCcw className='h-4 w-4' /> Reset Interview
              </>
            )}
          </Button>
        )}
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-16 gap-6 max-w-screen-2xl mx-auto'>
        {/* Left Panel - Navigation + Your Story + People Library */}
        <div className='lg:col-span-5'>
          <div className='sticky top-18 max-h-[calc(100vh-2rem)] overflow-y-auto space-y-6'>
            {/* Navigation */}
            <ProgressSidebar
              items={items}
              current={active}
              onSelect={async key => {
                await forceSave();
                setActive(key);
                scrollToTop();
              }}
            />

            {/* People Library */}
            <PeopleLibrary
              people={Array.isArray(stepsData.people) ? stepsData.people : []}
              onChange={people => updateStep('people', people)}
              interviewData={stepsData}
              readOnly={isInterviewSubmitted}
            />
          </div>
        </div>

        {/* Right Panel - Main Content */}
        <div className='lg:col-span-11'>
          <Card className='mb-4'>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <div className='flex flex-col'>
                  <div className='text-sm text-muted-foreground'>
                    Step {items.findIndex(i => i.key === active) + 1} of{' '}
                    {items.length}
                  </div>
                  <CardTitle className='text-lg mt-1'>
                    {items.find(i => i.key === active)?.label}
                  </CardTitle>
                </div>
                {isUserActionSaving && (
                  <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                    <div className='animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full' />
                    Saving...
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <Tabs
                value={active}
                onValueChange={async v => {
                  await forceSave();
                  setActive(v);
                  scrollToTop();
                }}
                className='w-full'
              >
                <TabsContent value='profile'>
                  {!dataInitialized ? (
                    <div className='flex items-center justify-center p-8'>
                      <div className='animate-spin h-8 w-8 border-2 border-current border-t-transparent rounded-full' />
                      <span className='ml-2'>Loading your information...</span>
                    </div>
                  ) : (
                    <ProfileSection
                      value={stepsData.profile || {}}
                      onChange={p => updateStep('profile', p)}
                      people={stepsData.people || []}
                      onPeopleChange={people => updateStep('people', people)}
                      dataInitialized={dataInitialized}
                      readOnly={isInterviewSubmitted}
                    />
                  )}
                </TabsContent>
                <TabsContent value='emergency'>
                  <EmergencySection
                    value={stepsData.emergency || {}}
                    onChange={p => updateStep('emergency', p)}
                    people={stepsData.people || []}
                    onPeopleChange={people => updateStep('people', people)}
                    readOnly={isInterviewSubmitted}
                  />
                </TabsContent>
                <TabsContent value='afterYouDie'>
                  <AfterYouDieSection
                    value={stepsData.afterYouDie || {}}
                    onChange={p => updateStep('afterYouDie', p)}
                    people={stepsData.people || []}
                    onPeopleChange={people => updateStep('people', people)}
                    selectedDocuments={stepsData.profile?.selectedDocuments}
                    markSubsectionComplete={markSubsectionComplete}
                    isSubsectionCompleted={isSubsectionCompleted}
                    readOnly={isInterviewSubmitted}
                  />
                </TabsContent>
                <TabsContent value='financial'>
                  <FinancialDecisionsSection
                    value={stepsData.financial || {}}
                    onChange={p => updateStep('financial', p)}
                    people={stepsData.people || []}
                    onPeopleChange={people => updateStep('people', people)}
                    readOnly={isInterviewSubmitted}
                  />
                </TabsContent>
                <TabsContent value='medical'>
                  <MedicalDecisionsSection
                    value={stepsData.medical || {}}
                    onChange={p => updateStep('medical', p)}
                    people={stepsData.people || []}
                    onPeopleChange={people => updateStep('people', people)}
                    readOnly={isInterviewSubmitted}
                  />
                </TabsContent>
                <TabsContent value='additional'>
                  <AdditionalConsiderationsSection
                    value={stepsData.additional || {}}
                    onChange={p => updateStep('additional', p)}
                    people={stepsData.people || []}
                    onPeopleChange={people => updateStep('people', people)}
                    isMarried={stepsData.profile?.maritalStatus === 'married'}
                    readOnly={isInterviewSubmitted}
                  />
                </TabsContent>
                <TabsContent value='review'>
                  <ReviewSection
                    stepsData={stepsData}
                    onSave={persist}
                    onNavigateToSection={section => {
                      setActive(section);
                      scrollToTop();
                    }}
                    onSubmitInterview={handleSubmitInterview}
                    isInterviewSubmitted={isInterviewSubmitted}
                  />
                </TabsContent>
              </Tabs>

              <div className='mt-6 flex items-center justify-between'>
                <Button
                  variant='outline'
                  onClick={goToPrev}
                  disabled={
                    savingNext ||
                    resetting ||
                    items.findIndex(i => i.key === active) === 0
                  }
                >
                  <ArrowLeft className='h-4 w-4 mr-2' /> Back
                </Button>
                {/* Only show Next button if not on the review page */}
                {active !== 'review' && (
                  <Button onClick={goToNext} disabled={isNextDisabled}>
                    {savingNext ? (
                      <>
                        <Loader2 className='h-4 w-4 mr-2 animate-spin' />{' '}
                        Saving…
                      </>
                    ) : (
                      <>
                        Next <ArrowRight className='h-4 w-4 ml-2' />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
