'use client';

import { Suspense, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  ChildStatus,
  EligibilityResponse,
  EligibilityAnswers,
  getOnboardingAnswers,
  OnboardingAnswers,
  OnboardingStep,
  saveOnboardingAnswer,
  saveEligibilityAnswers,
  isReviewRequired,
  completeOnboarding,
  updateUserOnboardingStep,
  getResourcesForNonChildfreeUsers,
} from '@/app/utils/userOnboarding';
import { type AdminResource } from '@/app/utils/adminResources';
import {
  ArrowRight,
  ArrowLeft,
  Info,
  AlertTriangle,
  Mail,
  Phone,
} from 'lucide-react';
import { useAuth } from '@/app/context/AuthContext';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import { AuthGuard } from '@/lib/auth/auth-guard';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { getSafeVideoInfo, getPlayerSizeClasses } from '@/utils/video-utils';

function OnboardingPageContent() {
  const { onboardingStatus, loading, updateOnboardingStatus, userExternalId } =
    useAuth();
  const { getContentForOnboarding } = useEducationalContent();
  const router = useRouter();

  const [childStatus, setChildStatus] = useState<ChildStatus | undefined>(
    undefined
  );

  const [eligibilityAnswers, setEligibilityAnswers] =
    useState<EligibilityAnswers>({
      usResident: undefined,
      medicalSituation: undefined,
      complexFinancial: undefined,
    });
  const [savedAnswers, setSavedAnswers] = useState<OnboardingAnswers | null>(
    null
  );
  const [loadingAnswers, setLoadingAnswers] = useState(true);
  const [adminResources, setAdminResources] = useState<AdminResource[]>([]);
  const [eligibilitySubmitted, setEligibilitySubmitted] = useState(false);

  // Load existing answers if available
  useEffect(() => {
    async function loadAnswers() {
      if (!userExternalId) {
        console.warn('No userExternalId available for loading answers');
        setLoadingAnswers(false);
        return;
      }

      try {
        const [answers, resources] = await Promise.all([
          getOnboardingAnswers(userExternalId),
          getResourcesForNonChildfreeUsers(),
        ]);

        if (answers) {
          setSavedAnswers(answers);
          if (answers.childStatus) {
            setChildStatus(answers.childStatus);
          }
          if (answers.eligibility) {
            setEligibilityAnswers(answers.eligibility);
          }
        }

        setAdminResources(resources);
      } catch (error) {
        console.error('Error loading saved answers:', error);
      } finally {
        setLoadingAnswers(false);
      }
    }

    if (!loading && userExternalId) {
      loadAnswers();
    }
  }, [loading, onboardingStatus, userExternalId]);

  const steps = [
    {
      id: OnboardingStep.CHILD_STATUS,
      label: 'Are you Childfree?',
      description:
        'Childfree Trust is specifically designed for individuals who are childfree or permanently childless.',
    },
    {
      id: OnboardingStep.ELIGIBILITY,
      label: 'Eligibility Questions',
      description:
        'Please answer these questions to help us determine if our services are the right fit for your situation.',
    },
  ];

  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Handle going back from Review Required screen to eligibility questions
  const handleGoBackFromReview = () => {
    // Reset eligibility submitted flag so user can change answers
    setEligibilitySubmitted(false);
    // Go to eligibility step
    setCurrentStepIndex(
      steps.findIndex(step => step.id === OnboardingStep.ELIGIBILITY)
    );
  };

  useEffect(() => {
    // Only redirect to dashboard if onboarding is completed AND eligibility is acceptable
    if (onboardingStatus === OnboardingStep.COMPLETED) {
      // Check if user has eligibility answers that require review
      if (
        savedAnswers?.eligibility &&
        isReviewRequired(savedAnswers.eligibility)
      ) {
        // Don't redirect - stay on onboarding to show review required
        return;
      }
      router.push(routes.member.dashboard);
    }

    // Set current step index based on onboarding status
    // But if onboarding is marked as completed but eligibility requires review,
    // show the eligibility step instead
    if (
      onboardingStatus === OnboardingStep.COMPLETED &&
      savedAnswers?.eligibility &&
      isReviewRequired(savedAnswers.eligibility)
    ) {
      setCurrentStepIndex(
        steps.findIndex(step => step.id === OnboardingStep.ELIGIBILITY)
      );
    } else {
      setCurrentStepIndex(
        steps.findIndex(step => step.id === onboardingStatus)
      );
    }
  }, [onboardingStatus, savedAnswers, router]);

  const currentStepData = steps[currentStepIndex] || steps[0];

  const progressValue = (currentStepIndex / steps.length) * 100;

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to save the current step's answer and move to the next step
  const handleCompleteStep = async () => {
    if (!userExternalId) {
      console.error('User external ID not available');
      return;
    }

    setIsSubmitting(true);
    try {
      if (currentStepData.id === OnboardingStep.CHILD_STATUS && childStatus) {
        // Check if user selected "No, I have children" and block immediately
        if (childStatus === ChildStatus.NO) {
          // Save the answer first so the blocking message will show on reload
          await saveOnboardingAnswer(
            userExternalId,
            OnboardingStep.CHILD_STATUS,
            childStatus,
            savedAnswers || undefined
          );

          // Update the saved answers state to trigger the blocking UI immediately
          setSavedAnswers(prev => ({
            ...prev,
            childStatus: ChildStatus.NO,
          }));

          setIsSubmitting(false);
          return; // Stop here, don't proceed to next step
        }

        // For Childfree users, proceed normally
        await saveOnboardingAnswer(
          userExternalId,
          OnboardingStep.CHILD_STATUS,
          childStatus,
          savedAnswers || undefined
        );

        setCurrentStepIndex(prevState => prevState + 1);
        setIsSubmitting(false);
      } else if (currentStepData.id === OnboardingStep.ELIGIBILITY) {
        // Check if all eligibility questions are answered
        if (
          eligibilityAnswers.usResident &&
          eligibilityAnswers.medicalSituation &&
          eligibilityAnswers.complexFinancial
        ) {
          // Save eligibility answers with current saved answers including childStatus
          const currentAnswers = {
            ...savedAnswers,
            childStatus: childStatus,
          };
          await saveEligibilityAnswers(
            userExternalId,
            eligibilityAnswers,
            currentAnswers
          );

          // Set flag that eligibility was submitted
          setEligibilitySubmitted(true);

          // Check if review is required
          if (isReviewRequired(eligibilityAnswers)) {
            // Review required - DO NOT complete onboarding
            // The component will show the review required screen
            setIsSubmitting(false);
            return; // Stop here, don't complete onboarding
          } else {
            // Only complete onboarding if eligibility answers are acceptable (NO, NO, NO)
            await completeOnboarding(userExternalId);
            // Update the auth context to reflect the completion
            await updateOnboardingStatus();
            router.push(routes.member.dashboard);
          }
        }
      }
    } catch (error) {
      console.error('===> Error saving onboarding answer:', error);
      setIsSubmitting(false);
    }
  };

  if (loading || loadingAnswers) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-lg'>Loading...</div>
      </div>
    );
  }

  // Check if user is not Childfree and show blocking message
  const isNotChildfree = savedAnswers?.childStatus === ChildStatus.NO;

  // Check if review is required and show review required message
  // Check both saved answers and current form answers
  const currentEligibilityComplete =
    eligibilityAnswers.usResident &&
    eligibilityAnswers.medicalSituation &&
    eligibilityAnswers.complexFinancial;
  const isReviewRequiredState =
    (savedAnswers?.eligibility && isReviewRequired(savedAnswers.eligibility)) ||
    (eligibilitySubmitted &&
      currentEligibilityComplete &&
      isReviewRequired(eligibilityAnswers));

  // Show blocking message if user is not Childfree
  if (isNotChildfree) {
    return (
      <div className='min-h-screen bg-background'>
        <div className='container max-w-2xl mx-auto py-10 px-4 sm:px-6'>
          <div className='flex items-center justify-center min-h-[60vh]'>
            <div className='bg-card rounded-lg border shadow-sm p-8 text-center space-y-6'>
              <div className='flex justify-center'>
                <AlertTriangle className='h-16 w-16 text-orange-500' />
              </div>

              <div className='space-y-4'>
                <h1 className='text-2xl font-bold text-foreground'>
                  Access Restricted
                </h1>
                <p className='text-lg text-muted-foreground'>
                  Our website is exclusively designed for Childfree individuals.
                </p>
                <p className='text-muted-foreground'>
                  Based on your response, this service may not be suitable for
                  your needs.
                </p>
              </div>

              {/* Show admin resources if available */}
              {adminResources.length > 0 && (
                <div className='bg-muted/50 rounded-lg p-4 space-y-3'>
                  <h3 className='font-medium text-foreground'>
                    Recommended Resources
                  </h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Here are some resources that might be helpful for your
                    situation:
                  </p>
                  <div className='space-y-2'>
                    {adminResources.map(resource => (
                      <div
                        key={resource.id}
                        className='border rounded p-3 bg-background'
                      >
                        <div className='flex items-start justify-between'>
                          <div className='flex-1'>
                            <h4 className='font-medium text-sm'>
                              {resource.title}
                            </h4>
                            {resource.description && (
                              <p className='text-xs text-muted-foreground mt-1'>
                                {resource.description}
                              </p>
                            )}
                          </div>
                          <a
                            href={resource.url}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-primary hover:underline text-sm font-medium ml-2'
                          >
                            Visit →
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className='bg-muted/50 rounded-lg p-4 space-y-3'>
                <h3 className='font-medium text-foreground'>
                  Need assistance?
                </h3>
                <p className='text-sm text-muted-foreground'>
                  If you believe this is an error or have questions about our
                  services, please contact our support team at{' '}
                  <a
                    href='mailto:<EMAIL>'
                    className='text-primary hover:underline font-medium'
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>

              <div className='flex justify-center'>
                <Button
                  onClick={() => {
                    // Reset the child status and saved answers to allow user to go back
                    setChildStatus(undefined);
                    setSavedAnswers(prev => ({
                      ...prev,
                      childStatus: undefined,
                    }));
                    setCurrentStepIndex(0);
                  }}
                  variant='outline'
                  size='lg'
                >
                  Go Back
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show review required message if review is needed
  if (isReviewRequiredState) {
    return (
      <div className='min-h-screen bg-background'>
        <div className='container max-w-2xl mx-auto py-10 px-4 sm:px-6'>
          <div className='flex items-center justify-center min-h-[60vh]'>
            <div className='bg-card rounded-lg border shadow-sm p-8 text-center space-y-6'>
              <div className='flex justify-center'>
                <AlertTriangle className='h-16 w-16 text-orange-500' />
              </div>

              <div className='space-y-4'>
                <h1 className='text-2xl font-bold text-foreground'>
                  Review Required
                </h1>
              </div>

              {/* Onboarding Video */}
              {(() => {
                const onboardingVideos = getContentForOnboarding();
                const video =
                  onboardingVideos.length > 0 ? onboardingVideos[0] : null;

                if (video && video.contentUrl) {
                  return (
                    <div className='bg-muted/50 rounded-lg p-6 space-y-4'>
                      <h3 className='text-lg font-medium text-center'>
                        {video.title}
                      </h3>
                      {video.description && (
                        <p className='text-sm text-muted-foreground text-center'>
                          {video.description}
                        </p>
                      )}
                      <div className={getPlayerSizeClasses('medium')}>
                        <div className='relative w-full aspect-video rounded-lg overflow-hidden bg-black'>
                          <iframe
                            src={
                              getSafeVideoInfo(video.contentUrl || '', false)
                                .embedUrl
                            }
                            title={video.title || 'Onboarding Video'}
                            className='w-full h-full'
                            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
                            allowFullScreen
                          />
                        </div>
                      </div>
                    </div>
                  );
                }

                // Fallback to placeholder if no video
                return (
                  <div className='bg-muted/50 rounded-lg p-6 flex items-center justify-center min-h-[200px]'>
                    <div className='text-center space-y-2'>
                      <div className='w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto'>
                        <svg
                          className='w-8 h-8 text-primary'
                          fill='currentColor'
                          viewBox='0 0 20 20'
                        >
                          <path
                            fillRule='evenodd'
                            d='M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z'
                            clipRule='evenodd'
                          />
                        </svg>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        No video available
                      </p>
                    </div>
                  </div>
                );
              })()}

              {/* Merged Message and Contact Section */}
              <div className='bg-muted/50 rounded-lg p-6 space-y-6'>
                <p className='text-muted-foreground'>
                  One or more of your answers requires further discussion and
                  review if this service is appropriate for you. Please set up
                  an appointment here to discuss your personal scenario:
                </p>

                {/* Scheduling Link Placeholder */}
                <div className='flex justify-center'>
                  <div className='bg-primary/10 border border-primary/20 rounded-lg p-4 text-center'>
                    <p className='text-sm text-muted-foreground'>
                      Scheduling link placeholder
                    </p>
                  </div>
                </div>

                {/* Email Contact */}
                <div className='flex items-center justify-center gap-2 text-sm'>
                  <Mail className='h-4 w-4 text-primary' />
                  <a
                    href='mailto:<EMAIL>'
                    className='text-primary hover:underline font-medium'
                  >
                    <EMAIL>
                  </a>
                </div>

                {/* Back Button */}
                <div className='mt-6'>
                  <Button
                    onClick={handleGoBackFromReview}
                    variant='outline'
                    size='lg'
                    className='w-full sm:w-auto'
                  >
                    <ArrowLeft className='mr-2 h-5 w-5' />
                    Change My Answers
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-background'>
      <div className='container max-w-3xl mx-auto py-10 px-4 sm:px-6'>
        <div className='space-y-6'>
          {/* Header */}
          <div className='text-center space-y-2'>
            <h1 className='text-3xl font-bold'>Complete Your Profile</h1>
            <p className='text-muted-foreground'>
              Please follow these steps to complete your profile setup
            </p>
          </div>

          {/* Content */}
          <div className='bg-card rounded-lg border shadow-sm p-6'>
            {/* Progress Bar */}
            <div className='mb-6'>
              <Progress value={progressValue} className='h-2' />
            </div>

            {/* Current Step Content */}
            <div className='rounded-lg border p-6 bg-muted/50 mb-8'>
              <div className='flex items-start gap-4 mb-6'>
                <Info className='h-6 w-6 text-primary mt-0.5 flex-shrink-0' />
                <div>
                  <h3 className='text-xl font-medium mb-2'>
                    {currentStepData.label}
                  </h3>
                  <p className='text-muted-foreground'>
                    {currentStepData.description}
                  </p>
                </div>
              </div>
              {currentStepData.id === OnboardingStep.CHILD_STATUS && (
                <div className='mt-6 space-y-4'>
                  <RadioGroup
                    value={childStatus as string}
                    onValueChange={(value: string) =>
                      setChildStatus(value as ChildStatus)
                    }
                    className='space-y-3'
                  >
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'yes' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.YES}
                        id='childfree-yes'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-yes'
                        className='flex-1 cursor-pointer text-base'
                      >
                        Yes, I have no biological or adopted children
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'no' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.NO}
                        id='childfree-no'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-no'
                        className='flex-1 cursor-pointer text-base'
                      >
                        No, I have children
                      </Label>
                    </div>
                    {/* <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'planning' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.PLANNING}
                        id='childfree-planning'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-planning'
                        className='flex-1 cursor-pointer text-base'
                      >
                        I'm planning to remain Childfree
                      </Label>
                    </div> */}
                  </RadioGroup>
                </div>
              )}

              {currentStepData.id === OnboardingStep.ELIGIBILITY && (
                <div className='mt-6 space-y-6'>
                  {/* US Resident Question */}
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <h4 className='font-medium text-foreground'>
                        Are you currently a United States Resident?
                      </h4>
                      <p className='text-sm text-muted-foreground'>
                        A U.S. resident is a person who lives in the United
                        States, including its 50 states, the District of
                        Columbia, or on a U.S. military base located abroad, and
                        has the legal right to reside there.
                      </p>
                    </div>
                    <RadioGroup
                      value={eligibilityAnswers.usResident as string}
                      onValueChange={(value: string) =>
                        setEligibilityAnswers(prev => ({
                          ...prev,
                          usResident: value as EligibilityResponse,
                        }))
                      }
                      className='space-y-2'
                    >
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.usResident === EligibilityResponse.YES ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.YES}
                          id='us-resident-yes'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='us-resident-yes'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          Yes
                        </Label>
                      </div>
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.usResident === EligibilityResponse.NO ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.NO}
                          id='us-resident-no'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='us-resident-no'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          No
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Medical Situation Question */}
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <h4 className='font-medium text-foreground'>
                        Have you been told by a medical professional that you
                        are in cognitive decline and need a POA immediately, or
                        that you have less than 6 months to live?
                      </h4>
                      <p className='text-sm text-muted-foreground'>
                        These complex medical situations require unique
                        consideration.
                      </p>
                    </div>
                    <RadioGroup
                      value={eligibilityAnswers.medicalSituation as string}
                      onValueChange={(value: string) =>
                        setEligibilityAnswers(prev => ({
                          ...prev,
                          medicalSituation: value as EligibilityResponse,
                        }))
                      }
                      className='space-y-2'
                    >
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.medicalSituation === EligibilityResponse.YES ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.YES}
                          id='medical-yes'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='medical-yes'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          Yes
                        </Label>
                      </div>
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.medicalSituation === EligibilityResponse.NO ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.NO}
                          id='medical-no'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='medical-no'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          No
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Complex Financial Question */}
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <h4 className='font-medium text-foreground'>
                        Do you currently have a complex financial situation?
                      </h4>
                      <div className='text-sm text-muted-foreground space-y-1'>
                        <p>
                          A complex financial situation is defined as having and
                          intending to keep:
                        </p>
                        <ul className='list-disc list-inside ml-2 space-y-1'>
                          <li>Farmland or timberland</li>
                          <li>International real estate</li>
                          <li>Partial ownership in a business</li>
                        </ul>
                      </div>
                    </div>
                    <RadioGroup
                      value={eligibilityAnswers.complexFinancial as string}
                      onValueChange={(value: string) =>
                        setEligibilityAnswers(prev => ({
                          ...prev,
                          complexFinancial: value as EligibilityResponse,
                        }))
                      }
                      className='space-y-2'
                    >
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.complexFinancial === EligibilityResponse.YES ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.YES}
                          id='complex-financial-yes'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='complex-financial-yes'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          Yes
                        </Label>
                      </div>
                      <div
                        className={`flex items-center space-x-3 rounded-md border p-3 transition-colors ${eligibilityAnswers.complexFinancial === EligibilityResponse.NO ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                      >
                        <RadioGroupItem
                          value={EligibilityResponse.NO}
                          id='complex-financial-no'
                          className='h-4 w-4'
                        />
                        <Label
                          htmlFor='complex-financial-no'
                          className='flex-1 cursor-pointer text-sm'
                        >
                          No
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className='flex justify-between'>
              {/* Back Button - only show if not on first step */}
              {currentStepIndex > 0 && (
                <Button
                  onClick={() => setCurrentStepIndex(prev => prev - 1)}
                  variant='outline'
                  size='lg'
                  disabled={loading || loadingAnswers || isSubmitting}
                >
                  <ArrowLeft className='mr-2 h-5 w-5' />
                  Back
                </Button>
              )}

              {/* Continue Button */}
              <Button
                onClick={handleCompleteStep}
                disabled={
                  loading ||
                  loadingAnswers ||
                  isSubmitting ||
                  (currentStepData.id === OnboardingStep.CHILD_STATUS &&
                    !childStatus) ||
                  (currentStepData.id === OnboardingStep.ELIGIBILITY &&
                    (!eligibilityAnswers.usResident ||
                      !eligibilityAnswers.medicalSituation ||
                      !eligibilityAnswers.complexFinancial))
                }
                size='lg'
                className={`w-full sm:w-auto ${currentStepIndex === 0 ? 'ml-auto' : ''}`}
              >
                {isSubmitting ? 'Processing...' : 'Continue'}
                {!isSubmitting && <ArrowRight className='ml-2 h-5 w-5' />}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OnboardingPage() {
  return (
    <Suspense>
      <AuthGuard fallbackMessage='You need to be logged in to access onboarding.'>
        <OnboardingPageContent />
      </AuthGuard>
    </Suspense>
  );
}
