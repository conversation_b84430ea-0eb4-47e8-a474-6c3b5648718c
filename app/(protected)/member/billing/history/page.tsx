'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, FileDown, Search } from 'lucide-react';
import { Headline, Subhead } from '@workspace/ui/brand';
import routes from '@/utils/routes';

// Temporary interfaces for history page
interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  status: string;
}

interface UserBillingInfo {
  transactions: Transaction[];
}

export default function BillingHistoryPage() {
  const router = useRouter();
  const [billingInfo, setBillingInfo] = useState<UserBillingInfo | null>(null);
  const [filteredTransactions, setFilteredTransactions] = useState<
    Transaction[]
  >([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    // TODO: Implement real API calls to fetch billing history
    const emptyBillingInfo: UserBillingInfo = {
      transactions: [],
    };
    setBillingInfo(emptyBillingInfo);
    setFilteredTransactions([]);
  }, []);

  useEffect(() => {
    if (!billingInfo) return;

    let filtered = [...billingInfo.transactions];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        tx =>
          tx.description.toLowerCase().includes(query) ||
          tx.id.toLowerCase().includes(query)
      );
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'last30':
          filterDate.setDate(now.getDate() - 30);
          break;
        case 'last90':
          filterDate.setDate(now.getDate() - 90);
          break;
        case 'last365':
          filterDate.setDate(now.getDate() - 365);
          break;
      }

      filtered = filtered.filter(tx => new Date(tx.date) >= filterDate);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(tx => tx.status === statusFilter);
    }

    setFilteredTransactions(filtered);
  }, [billingInfo, searchQuery, dateFilter, statusFilter]);

  const handleViewReceipt = (transactionId: string) => {
    // In a real implementation, this would open a receipt viewer
    console.log(`Viewing receipt for transaction ${transactionId}`);
    alert(`Viewing receipt for transaction ${transactionId}`);
  };

  const handleDownloadReceipt = (transactionId: string) => {
    // In a real implementation, this would download a receipt
    console.log(`Downloading receipt for transaction ${transactionId}`);
    alert(`Downloading receipt for transaction ${transactionId}`);
  };

  const handleDownloadAllReceipts = () => {
    // In a real implementation, this would download all receipts
    console.log('Downloading all receipts');
    alert('Downloading all receipts');
  };

  if (!billingInfo) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-center h-64'>
            <p className='text-muted-foreground'>Loading billing history...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Button
            variant='outline'
            onClick={() => router.push(routes.member.billing)}
            className='mb-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Billing
          </Button>
          <Headline className='mb-2'>Billing History</Headline>
          <Subhead className='text-muted-foreground'>
            View and download your transaction history and receipts.
          </Subhead>
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Filter Transactions</CardTitle>
            <CardDescription>
              Use the filters below to find specific transactions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='search'>Search</Label>
                <div className='relative'>
                  <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                  <Input
                    id='search'
                    placeholder='Search transactions...'
                    className='pl-8'
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='dateFilter'>Date Range</Label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger id='dateFilter'>
                    <SelectValue placeholder='Select date range' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All Time</SelectItem>
                    <SelectItem value='last30'>Last 30 Days</SelectItem>
                    <SelectItem value='last90'>Last 90 Days</SelectItem>
                    <SelectItem value='last365'>Last Year</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='statusFilter'>Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id='statusFilter'>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All Statuses</SelectItem>
                    <SelectItem value='Successful'>Successful</SelectItem>
                    <SelectItem value='Failed'>Failed</SelectItem>
                    <SelectItem value='Pending'>Pending</SelectItem>
                    <SelectItem value='Refunded'>Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className='mb-4 flex justify-between items-center'>
          <h2 className='text-xl font-semibold'>Transaction History</h2>
          <Button variant='outline' onClick={handleDownloadAllReceipts}>
            <FileDown className='mr-2 h-4 w-4' />
            Download All Receipts
          </Button>
        </div>

        <Card>
          <CardContent className='py-8 text-center'>
            <p className='text-muted-foreground'>
              No transaction history available. Transaction history will be
              displayed here once you have an active subscription.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
