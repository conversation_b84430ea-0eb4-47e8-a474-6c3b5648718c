'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  AlertTriangle,
  CheckCircle2,
  AlertCircle,
} from 'lucide-react';
import { Headline, Subhead } from '@workspace/ui/brand';
import routes from '@/utils/routes';

// Temporary interface for cancel page
interface UserBillingInfo {
  subscription?: {
    tier: string;
    amount: number;
    nextBillingDate: string;
  };
}

export default function CancelSubscriptionPage() {
  const router = useRouter();
  const [billingInfo, setBillingInfo] = useState<UserBillingInfo | null>(null);
  const [cancelReason, setCancelReason] = useState('');
  const [otherReason, setOtherReason] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const [isCanceled, setIsCanceled] = useState(false);
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  useEffect(() => {
    // TODO: Implement real API calls to fetch subscription data
    // For now, redirect to main billing page since cancellation is handled there
    router.push(routes.member.billing);
  }, [router]);

  const handleCancelReasonChange = (value: string) => {
    setCancelReason(value);
  };

  const handleConfirmCancel = () => {
    if (!cancelReason) {
      setAlert({
        type: 'error',
        message: 'Please select a reason for cancellation.',
      });
      return;
    }

    setIsConfirming(true);
  };

  const handleCancelSubscription = () => {
    // In a real implementation, this would call an API to cancel the subscription
    setTimeout(() => {
      setIsCanceled(true);
      setIsConfirming(false);
    }, 1000);
  };

  const handleGoToDashboard = () => {
    router.push(routes.member.dashboard);
  };

  const handleGoToBilling = () => {
    router.push(routes.member.billing);
  };

  if (!billingInfo) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-3xl mx-auto'>
          <div className='flex items-center justify-center h-64'>
            <p className='text-muted-foreground'>
              Loading subscription information...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!billingInfo.subscription) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-3xl mx-auto'>
          <Button
            variant='outline'
            onClick={() => router.push(routes.member.billing)}
            className='mb-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Billing
          </Button>

          <Card>
            <CardHeader>
              <CardTitle>No Active Subscription</CardTitle>
              <CardDescription>
                You don't have an active subscription to cancel.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                If you'd like to subscribe to our services, please visit the
                subscription page.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => router.push(routes.member.billingSubscribe)}
              >
                Subscribe Now
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  if (isCanceled) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-3xl mx-auto'>
          <Card className='text-center py-8'>
            <CardContent>
              <div className='flex flex-col items-center justify-center space-y-4'>
                <div className='rounded-full bg-green-2010c/20 p-3'>
                  <CheckCircle2 className='h-12 w-12 text-green-2010c' />
                </div>
                <h2 className='text-2xl font-bold'>Subscription Canceled</h2>
                <p className='text-muted-foreground max-w-md mx-auto'>
                  Your subscription has been successfully canceled. You will
                  continue to have access until the end of your current billing
                  period on{' '}
                  {new Date(
                    billingInfo.subscription.nextBillingDate
                  ).toLocaleDateString()}
                  .
                </p>
                <div className='flex space-x-4 mt-4'>
                  <Button variant='outline' onClick={handleGoToBilling}>
                    Go to Billing
                  </Button>
                  <Button onClick={handleGoToDashboard}>Go to Dashboard</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-3xl mx-auto'>
        <div className='mb-8'>
          <Button
            variant='outline'
            onClick={() => router.push(routes.member.billing)}
            className='mb-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Billing
          </Button>
          <Headline className='mb-2'>Cancel Subscription</Headline>
          <Subhead className='text-muted-foreground'>
            We're sorry to see you go. Please let us know why you're canceling.
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        {isConfirming ? (
          <Card>
            <CardHeader>
              <CardTitle className='text-destructive flex items-center'>
                <AlertTriangle className='mr-2 h-5 w-5' />
                Confirm Cancellation
              </CardTitle>
              <CardDescription>
                Are you sure you want to cancel your subscription?
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <p>
                  Your subscription will be canceled immediately, but you will
                  continue to have access until the end of your current billing
                  period on{' '}
                  {new Date(
                    billingInfo.subscription.nextBillingDate
                  ).toLocaleDateString()}
                  .
                </p>
                <p>
                  After this date, you will no longer have access to the premium
                  features of Childfree Trust.
                </p>
              </div>
            </CardContent>
            <CardFooter className='flex justify-end space-x-2'>
              <Button variant='outline' onClick={() => setIsConfirming(false)}>
                Go Back
              </Button>
              <Button variant='destructive' onClick={handleCancelSubscription}>
                Confirm Cancellation
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Why are you canceling?</CardTitle>
              <CardDescription>
                Your feedback helps us improve our service.
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <RadioGroup
                value={cancelReason}
                onValueChange={handleCancelReasonChange}
                className='space-y-3'
              >
                <div className='flex items-start space-x-2'>
                  <RadioGroupItem
                    value='too-expensive'
                    id='too-expensive'
                    className='mt-1'
                  />
                  <div className='grid gap-1.5'>
                    <Label htmlFor='too-expensive' className='font-medium'>
                      It's too expensive
                    </Label>
                    <p className='text-sm text-muted-foreground'>
                      The subscription cost is higher than I expected or can
                      afford.
                    </p>
                  </div>
                </div>
                <div className='flex items-start space-x-2'>
                  <RadioGroupItem
                    value='not-using'
                    id='not-using'
                    className='mt-1'
                  />
                  <div className='grid gap-1.5'>
                    <Label htmlFor='not-using' className='font-medium'>
                      I'm not using it enough
                    </Label>
                    <p className='text-sm text-muted-foreground'>
                      I don't use the service frequently enough to justify the
                      cost.
                    </p>
                  </div>
                </div>
                <div className='flex items-start space-x-2'>
                  <RadioGroupItem
                    value='missing-features'
                    id='missing-features'
                    className='mt-1'
                  />
                  <div className='grid gap-1.5'>
                    <Label htmlFor='missing-features' className='font-medium'>
                      Missing features I need
                    </Label>
                    <p className='text-sm text-muted-foreground'>
                      The service doesn't have features that are important to
                      me.
                    </p>
                  </div>
                </div>
                <div className='flex items-start space-x-2'>
                  <RadioGroupItem
                    value='found-alternative'
                    id='found-alternative'
                    className='mt-1'
                  />
                  <div className='grid gap-1.5'>
                    <Label htmlFor='found-alternative' className='font-medium'>
                      Found an alternative service
                    </Label>
                    <p className='text-sm text-muted-foreground'>
                      I'm switching to a different service that better meets my
                      needs.
                    </p>
                  </div>
                </div>
                <div className='flex items-start space-x-2'>
                  <RadioGroupItem value='other' id='other' className='mt-1' />
                  <div className='grid gap-1.5 w-full'>
                    <Label htmlFor='other' className='font-medium'>
                      Other reason
                    </Label>
                    <Textarea
                      placeholder='Please tell us more...'
                      value={otherReason}
                      onChange={e => setOtherReason(e.target.value)}
                      disabled={cancelReason !== 'other'}
                      className={cancelReason !== 'other' ? 'opacity-50' : ''}
                    />
                  </div>
                </div>
              </RadioGroup>
            </CardContent>
            <CardFooter className='flex justify-end space-x-2'>
              <Button
                variant='outline'
                onClick={() => router.push(routes.member.billing)}
              >
                Keep Subscription
              </Button>
              <Button variant='destructive' onClick={handleConfirmCancel}>
                Cancel Subscription
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
