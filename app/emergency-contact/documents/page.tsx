'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckCircle,
  XCircle,
  Loader2,
  FileText,
  Eye,
  Download,
  Calendar,
  ZoomIn,
  ZoomOut,
  RotateCw,
  X,
  FileType,
} from 'lucide-react';
import { getUrl } from 'aws-amplify/storage';
import { cleanHTML } from '@/utils/cleanHTML';

function DocumentsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<any[]>([]);
  const [contactInfo, setContactInfo] = useState<any | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);

  const token = searchParams.get('token');
  const email = searchParams.get('email');

  useEffect(() => {
    const verifyAndFetchDocuments = async () => {
      if (!token || !email) {
        setError('Invalid access link. Missing token or email.');
        setLoading(false);
        setVerifying(false);
        return;
      }

      try {
        const client = generateClient<Schema>({
          authMode: 'iam',
        });

        // Call our new Lambda function that handles everything
        const result = await client.mutations.verifyAndFetchDocuments({
          email: decodeURIComponent(email),
          token,
        });

        const resultData = result.data;

        // Try to parse if it's a string
        let parsedResultData: any = resultData;
        if (typeof resultData === 'string') {
          try {
            parsedResultData = JSON.parse(resultData);
            console.log('Parsed result data:', parsedResultData);
          } catch (e) {
            console.log('Failed to parse result data as JSON:', e);
          }
        }

        if (parsedResultData?.success) {
          setVerifying(false);
          setSuccess(true);

          // Set contact info from the response
          setContactInfo(parsedResultData.contact);

          // Set documents from the response
          setDocuments(parsedResultData.documents);
        } else {
          setError(
            parsedResultData?.error || 'Failed to verify emergency access'
          );
        }
      } catch (err: any) {
        console.error('Verification error:', err);
        setError(
          'An error occurred while verifying your emergency access: ' +
            (err.message || '')
        );
      } finally {
        setLoading(false);
      }
    };

    verifyAndFetchDocuments();
  }, [token, email]);

  const handleViewDocument = (documentId: string) => {
    const document = documents.find(doc => doc.id === documentId);
    if (document) {
      setSelectedDocument(document);
      setViewerOpen(true);
    }
  };

   const handleViewFile = async (fileKey: string) => {
      try {
        if (!token || !email) {
            setError('Invalid access link. Missing token or email.');
            setLoading(false);
            setVerifying(false);
            return;
        }

        const client = generateClient<Schema>({
              authMode: 'iam',
            });
        // Use our new Lambda function to get a pre-signed URL
        const result = await client.mutations.getEmergencyFileUrl({
          email,
          token,
          fileKey,
        });

        let parsedResultData: any = result.data;

        if (typeof result.data === 'string') {
          try {
            parsedResultData = JSON.parse(result.data);
            // console.log('Parsed result data:', parsedResultData);
          } catch (e) {
            console.log('Failed to parse result data as JSON:', e);
          }
        }

        if (!parsedResultData.success) {
          throw new Error(parsedResultData.error || 'Failed to access file');
        }

        // Open the pre-signed URL in a new tab
        window.open(parsedResultData.url, '_blank');
      } catch (error: any) {
        console.error('Error viewing document:', error);
        // setError(`Failed to access file: ${error.message}`);
        // throw new Error(`Failed to access file: ${error.message}` || 'Failed to access file');
      }
    };

  const handleDownloadDocument = (documentId: string) => {
    // In a real implementation, this would download the document
    console.log('Download document:', documentId);
    // You would implement actual document download functionality here
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleContinue = () => {
    // Redirect to a thank you page or home
    router.push('/');
  };

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex flex-col items-center justify-center p-6'>
            <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
            {verifying ? (
              <p className='text-center text-muted-foreground'>
                Verifying your emergency access...
              </p>
            ) : (
              <p className='text-center text-muted-foreground'>
                Loading documents...
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!success || error) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardHeader className='text-center'>
            <div className='flex justify-center mb-4'>
              <XCircle className='h-12 w-12 text-red-500' />
            </div>
            <CardTitle>Verification Failed</CardTitle>
            <CardDescription>
              {error || 'We were unable to verify your emergency access.'}
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Button onClick={handleContinue} className='w-full'>
                Back to Home
              </Button>
              <p className='text-xs text-center text-muted-foreground'>
                If you continue to have issues, please contact support.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen p-6 bg-gray-50'>
      <Card className='w-full max-w-4xl mx-auto mb-6'>
        <CardHeader className='text-center'>
          <div className='flex justify-center mb-4'>
            <CheckCircle className='h-12 w-12 text-green-500' />
          </div>
          <CardTitle>Emergency Access Verified</CardTitle>
          <CardDescription>
            You have been granted access to the following documents.
          </CardDescription>
        </CardHeader>
      </Card>

      <div className='w-full max-w-4xl mx-auto'>
        <div className='space-y-6'>
          <div className='flex justify-between items-center'>
            <h2 className='text-xl font-semibold'>Available Documents</h2>
            <Badge className='bg-blue-600'>Emergency Access</Badge>
          </div>

          {documents.length === 0 ? (
            <Card className='bg-muted/50'>
              <CardContent className='py-6 text-center'>
                <p className='text-muted-foreground'>No documents available.</p>
              </CardContent>
            </Card>
          ) : (
            <div className='grid gap-4'>
              {documents.map(document => (
                <Card key={document.id} className='overflow-hidden'>
                  <CardHeader className='pb-2'>
                    <CardTitle className='text-lg flex items-center'>
                      <FileText className='mr-2 h-5 w-5 text-muted-foreground' />
                      {document.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='flex justify-between items-center text-sm'>
                      <span className='text-muted-foreground'>
                        {document.type ||
                          (document.documentType
                            ? 'Care Document'
                            : 'Document')}
                      </span>
                      <span className='text-muted-foreground'>
                        Last updated:{' '}
                        {formatDate(
                          document.updatedAt ||
                            document.lastModified ||
                            document.dateCreated
                        )}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter className='bg-muted/20 pt-2 pb-2 flex justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleViewDocument(document.id)}
                    >
                      <Eye className='mr-1 h-4 w-4' />
                      View
                    </Button>
                    {/* <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleDownloadDocument(document.id)}
                    >
                      <Download className='mr-1 h-4 w-4' />
                      Download
                    </Button> */}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
        {/* Document Viewer Dialog */}
        {selectedDocument && (
          <DocumentViewer
            document={selectedDocument}
            isOpen={viewerOpen}
            onClose={() => setViewerOpen(false)}
            onDownload={handleDownloadDocument}
            onFileView={handleViewFile}
          />
        )}
      </div>
    </div>
  );
}

export default function DocumentsPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-gray-50'>
          <Card className='w-full max-w-md'>
            <CardContent className='flex flex-col items-center justify-center p-6'>
              <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
              <p className='text-center text-muted-foreground'>Loading...</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <DocumentsContent />
    </Suspense>
  );
}

function DocumentViewer({
  document,
  isOpen,
  onClose,
  onDownload,
  onFileView,
}: {
  document: any;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (documentId: string) => void;
  onFileView: (fileKey: string) => void;
}) {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [loadingFiles, setLoadingFiles] = useState<Record<string, boolean>>({});
  const [fileErrors, setFileErrors] = useState<Record<string, string>>({});

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Determine if this is a care document or regular document
  const isCareDocument = document.hasOwnProperty('documentType');

  const renderDocumentContent = () => {
    return (
      <div className='bg-background border rounded-lg shadow-inner p-8 min-h-[800px] relative'>
        <div
          className='transition-transform duration-200 w-full'
          style={{
            transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
            transformOrigin: 'center center',
          }}
        >
          {/* Document content */}
          <div className='max-w-2xl mx-auto space-y-6 text-[var(--custom-gray-dark)]'>
            <div className='text-center border-b pb-4'>
              <h1 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {document.title}
              </h1>
              <p className='text-sm text-[var(--custom-gray-medium)] mt-2'>
                Document Type:{' '}
                {isCareDocument ? 'Care Document' : document.type || 'Document'}
              </p>
            </div>

            <div className='space-y-4'>
              <div className='bg-gray-50 p-4 rounded'>
                <h2 className='font-semibold text-lg mb-2'>
                  Document Information
                </h2>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='font-medium'>Document ID:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {document.id}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>User ID:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {document.userId}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>Created:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {formatDate(document.createdAt || document.dateCreated)}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>Last Updated:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {formatDate(document.updatedAt || document.lastModified)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Document content based on type */}
              <div className='border p-4 rounded bg-white'>
                <h3 className='font-semibold text-lg mb-4'>Document Content</h3>
                {isCareDocument ? (
                  // Care document content with proper rendering of answers
                  <div className='space-y-4'>
                    {document.answers?.map((answer: any) => {
                      // Skip if no answer value
                      if (!answer) return null;
                      if (!answer.answer && !answer.fileKeys?.length)
                        return null;

                      // Handle multiple answers (split by separator)
                      const answerValues = answer.answer
                        ? answer.answer.includes('|||')
                          ? answer.answer.split('|||')
                          : [answer.answer]
                        : [];

                      return (
                        <div
                          key={answer.questionId}
                          className='border-b pb-3 last:border-0 last:pb-0'
                        >
                          <h4 className='font-medium text-sm'>
                            {answer.questionText}
                          </h4>
                          <div className='mt-1 text-sm'>
                            {answerValues.map((value: string, idx: number) => (
                              <p key={idx}>{value}</p>
                            ))}
                          </div>

                          {/* Display files if any */}
                          {answer.fileKeys && answer.fileKeys.length > 0 && (
                            <div className='mt-2 flex flex-wrap gap-2'>
                              {answer.fileKeys.map(
                                (fileKey: string, idx: number) => {
                                  if (!fileKey) return null;
                                  const fileName =
                                    answer.fileNames?.[idx] || 'File';
                                  const isLoading = loadingFiles[fileKey];
                                  const error = fileErrors[fileKey];
                                  return (
                                    <div
                                      key={fileKey}
                                      className={`flex items-center gap-1 rounded-md ${error ? 'bg-red-100 text-red-700' : 'bg-muted'} px-2 py-1 text-xs ${isLoading ? 'opacity-70' : 'cursor-pointer hover:bg-muted/80'}`}
                                      onClick={async () => {
                                        if (isLoading) return; // Prevent multiple clicks
                                        if (error) {
                                          // Clear error on retry
                                          setFileErrors(prev => ({ ...prev, [fileKey]: '' }));
                                        }
                                        try {
                                          setLoadingFiles(prev => ({ ...prev, [fileKey]: true }));
                                          // Clear any previous errors
                                          setFileErrors(prev => ({ ...prev, [fileKey]: '' }));
                                          await onFileView(fileKey);
                                        } catch (error) {
                                          console.error(
                                            'Error opening file:',
                                            error
                                          );
                                          // Set error message
                                          setFileErrors(prev => ({ 
                                            ...prev, 
                                            [fileKey]: error instanceof Error ? error.message : 'Failed to open file'
                                          }));
                                        } finally {
                                          setLoadingFiles(prev => ({ ...prev, [fileKey]: false }));
                                        }
                                      }}
                                      title={error ? `Error: ${error}` : 'Click to view file'}
                                    >
                                      {isLoading ? (
                                        <Loader2 className='h-3 w-3 animate-spin mr-1' />
                                      ) : error ? (
                                        <XCircle className='h-3 w-3 text-red-600 mr-1' />
                                      ) : (
                                        <FileText className='h-3 w-3 mr-1' />
                                      )}
                                      <span>{fileName}</span>
                                    </div>
                                  );
                                }
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                    {(!document.answers || document.answers.length === 0) && (
                      <div className='text-center py-4 text-muted-foreground'>
                        <p>No content available for this care document.</p>
                      </div>
                    )}
                  </div>
                ) : // Regular document content
                document.content ? (
                  <div
                    className='prose max-w-none text-sm leading-relaxed'
                    dangerouslySetInnerHTML={{ __html: cleanHTML(document.content) }}
                  />
                ) : (
                  <div className='text-center py-4 text-muted-foreground'>
                    <p>No content available for this document.</p>
                    <p className='text-sm mt-2'>
                      You may need to download the document to view its
                      contents.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-6xl max-h-[90vh] p-0'>
        <DialogHeader className='p-6 pb-4 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <FileText className='h-6 w-6 text-blue-600' />
              <div>
                <DialogTitle className='text-xl'>{document.title}</DialogTitle>
                <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                  <div className='flex items-center gap-1'>
                    <FileType className='h-4 w-4' />
                    <span>
                      {isCareDocument ? 'Living' : document.type || 'Document'}
                    </span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Calendar className='h-4 w-4' />
                    <span>
                      Updated{' '}
                      {formatDate(
                        document.updatedAt ||
                          document.lastModified ||
                          document.dateCreated
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {/* <Badge variant='outline'>{isCareDocument ? 'Care Document' : (document.type || 'Document')}</Badge> */}
          </div>
        </DialogHeader>

        {/* Toolbar */}
        <div className='flex items-center justify-center px-6 py-3 bg-muted/50 border-b'>
          <div className='flex items-center gap-2'>
            <Button variant='outline' size='sm' onClick={handleZoomOut}>
              <ZoomOut className='h-4 w-4' />
            </Button>
            <span className='text-sm font-medium min-w-[60px] text-center'>
              {zoom}%
            </span>
            <Button variant='outline' size='sm' onClick={handleZoomIn}>
              <ZoomIn className='h-4 w-4' />
            </Button>
            <Button variant='outline' size='sm' onClick={handleRotate}>
              <RotateCw className='h-4 w-4' />
            </Button>
          </div>

          {/* <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onDownload(document.id)}
            >
              <Download className='h-4 w-4 mr-1' />
              Download
            </Button>
            <Button variant='outline' size='sm' onClick={onClose}>
              <X className='h-4 w-4' />
            </Button>
          </div> */}
        </div>

        {/* Document Content */}
        <div className='flex-1 overflow-auto p-6 bg-gray-100 max-h-[calc(90vh-200px)] rounded-lg'>
          <div className='h-full overflow-y-auto overflow-x-auto'>
            {renderDocumentContent()}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
