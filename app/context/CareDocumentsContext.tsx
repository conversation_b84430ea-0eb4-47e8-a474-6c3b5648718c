'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useMemo,
} from 'react';
import {
  useCareDocumentsNew,
  CareDocumentSectionType,
  UploadedFile,
} from '@/hooks/useCareDocumentsNew';
import { useDebounce } from '@/hooks/useDebounce';

export type SectionDataMap = {
  [key in CareDocumentSectionType]?: any;
};

interface CareDocumentsContextType {
  sectionData: SectionDataMap;
  modifiedSections: Set<CareDocumentSectionType>;
  updateSectionData: (sectionType: CareDocumentSectionType, data: any) => void;
  saveSection: (
    sectionType: CareDocumentSectionType,
    uploadedFiles?: UploadedFile[]
  ) => Promise<void>;
  saveModifiedSections: () => Promise<void>;
  loading: boolean;
  error: string | null;
  documentId: string | null;
}

const CareDocumentsContext = createContext<
  CareDocumentsContextType | undefined
>(undefined);

interface CareDocumentsProviderProps {
  children: ReactNode;
  sectionsData?: SectionDataMap; // Optional prop for external sections data
}

export const CareDocumentsProvider: React.FC<CareDocumentsProviderProps> = ({
  children,
  sectionsData: externalSectionsData,
}) => {
  const [sectionData, setSectionData] = useState<SectionDataMap>({});
  const [modifiedSections, setModifiedSections] = useState<
    Set<CareDocumentSectionType>
  >(new Set());
  const {
    document,
    loading,
    error,
    updateSection,
    updateMultipleSections,
    getSection,
    createDocument,
  } = useCareDocumentsNew();

  const debouncedModifiedSections = useDebounce(modifiedSections, 1000);

  // Determine if we should use external data or load from database
  const useExternalData = externalSectionsData !== undefined;

  useEffect(() => {
    if (debouncedModifiedSections && !useExternalData) {
      saveModifiedSections(true);
    }
  }, [debouncedModifiedSections, useExternalData]);

  // Initialize document and load section data when component mounts
  useEffect(() => {
    if (!useExternalData && !loading && !document && !error) {
      createDocument().catch(err => {
        console.error('Failed to create document:', err);
      });
    }
  }, [loading, document, error, createDocument, useExternalData]);

  // Load section data from external prop or document
  useEffect(() => {
    if (useExternalData && externalSectionsData) {
      // Use external sections data
      setSectionData(externalSectionsData);
      // Don't mark as modified when using external data initially
      setModifiedSections(new Set());
    } else if (!useExternalData && document) {
      // Load from database document
      const loadedData: SectionDataMap = {};

      // For each section type, try to load its data
      document.data.forEach(section => {
        if (section.sectionType) {
          try {
            const sectionContent = JSON.parse(section.content);
            loadedData[section.sectionType as CareDocumentSectionType] =
              sectionContent;
          } catch (err) {
            console.error(`Error parsing ${section.sectionType} data:`, err);
          }
        }
      });

      setSectionData(loadedData);
      // Clear modified sections after loading from database
      setModifiedSections(new Set());
    }
  }, [document, externalSectionsData, useExternalData]);

  // Update section data in local state and mark as modified
  const updateSectionData = (
    sectionType: CareDocumentSectionType,
    data: any
  ) => {
    setSectionData(prev => ({
      ...prev,
      [sectionType]: data,
    }));

    // Only mark as modified if not using external data (or allow modifications based on your needs)
    if (!useExternalData) {
      setModifiedSections(prev => {
        const newSet = new Set(prev);
        newSet.add(sectionType);
        return newSet;
      });
    }
  };

  // Save a specific section data to the database
  const saveSection = async (
    sectionType: CareDocumentSectionType,
    uploadedFiles: UploadedFile[] = []
  ) => {
    if (useExternalData) {
      console.warn('Cannot save section when using external sections data');
      return;
    }

    if (!document) {
      throw new Error('No document available');
    }

    const data = sectionData[sectionType] || {};
    await updateSection(sectionType, data, uploadedFiles);

    // Remove from modified sections after saving
    setModifiedSections(prev => {
      const newSet = new Set(prev);
      newSet.delete(sectionType);
      return newSet;
    });
  };

  // Save only modified sections
  const saveModifiedSections = async (isAutoSave?: boolean) => {
    if (useExternalData) {
      console.warn('Cannot save sections when using external sections data');
      return;
    }

    if (!document) {
      return;
    }

    // Convert Set to Array for iteration
    const sectionsToSave = Array.from(modifiedSections);

    if (sectionsToSave.length === 0) {
      return; // Nothing to save
    }

    try {
      // Prepare the sections data for bulk update
      const sectionsData = sectionsToSave
        .filter(sectionType => sectionData[sectionType])
        .map(sectionType => ({
          sectionType,
          content: sectionData[sectionType],
          uploadedFiles: [], // We're not handling files in bulk update for simplicity
        }));

      // Clear all modified sections after saving
      setModifiedSections(new Set());

      // Use the new hook function to update all sections at once
      await updateMultipleSections(sectionsData, isAutoSave);

      
    } catch (err) {
      console.error('Error saving modified sections:', err);
      throw err;
    }
  };

  return (
    <CareDocumentsContext.Provider
      value={{
        sectionData,
        modifiedSections,
        updateSectionData,
        saveSection,
        saveModifiedSections,
        loading: useExternalData ? false : loading, // No loading when using external data
        error: useExternalData ? null : error, // No error when using external data
        documentId: useExternalData ? null : (document?.id || null),
      }}
    >
      {children}
    </CareDocumentsContext.Provider>
  );
};

export const useCareDocumentsContext = () => {
  const context = useContext(CareDocumentsContext);
  if (context === undefined) {
    throw new Error(
      'useCareDocumentsContext must be used within a CareDocumentsProvider'
    );
  }
  return context;
};