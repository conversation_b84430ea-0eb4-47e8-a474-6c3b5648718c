'use client';

import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import type { AuthUser } from 'aws-amplify/auth';
import { getCurrentUser, signOut, fetchAuthSession } from 'aws-amplify/auth';
import { fetchUserByEmail } from '@/lib/data/users';
import type { User } from '@/types/account';
import {
  getUserOnboardingStatus,
  OnboardingStep,
} from '@/utils/userOnboarding';
import InactivityTimer from '@/app/components/InactivityTimer';
import { jwtDecode } from 'jwt-decode';

// Define the shape of the context
interface AuthContextType {
  user: AuthUser | null;
  userExternalId: string | null;
  userEmail: string | null;
  userId: string | null;
  onboardingStatus: OnboardingStep;
  loading: boolean;
  refreshUser: () => Promise<boolean>;
  logout: () => Promise<void>;
  userRoles: string[];
  userSubrole: string | null;
  updateOnboardingStatus: () => Promise<void>;
  isRememberMeSession: boolean;
  userPersonalInfo: User | null;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  userId: null,
  userEmail: null,
  userExternalId: null,
  loading: true,
  onboardingStatus: OnboardingStep.CHILD_STATUS,
  refreshUser: async () => false,
  logout: async () => {},
  userRoles: [],
  userSubrole: null,
  updateOnboardingStatus: async () => {},
  isRememberMeSession: false,
  userPersonalInfo: null,
});

// Custom hook to use the auth context
export function useAuth() {
  return useContext(AuthContext);
}

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userExternalId, setUserExternalId] = useState<string | null>(null);
  const [userPersonalInfo, setUserPersonalInfo] = useState<User | null>(null);
  const [userRoles, setRoles] = useState<string[]>([]);
  const [userSubrole, setUserSubrole] = useState<string | null>(null);
  const [onboardingStatus, setOnboardingStatus] = useState(
    OnboardingStep.CHILD_STATUS
  );
  const [isRememberMeSession, setIsRememberMeSession] = useState(false);

  // LOADING STATES
  const [loading, setLoading] = useState(true);

  // Function to fetch the current authenticated user
  const fetchUser = async () => {
    try {
      // Check if user selected "Remember Me" to determine refresh strategy
      const rememberMe = localStorage.getItem('rememberMe') === 'true';
      setIsRememberMeSession(rememberMe);

      // Only force refresh tokens if "Remember Me" was selected
      // Otherwise, let tokens expire naturally after 5 minutes
      const [session, userData] = await Promise.all([
        fetchAuthSession({ forceRefresh: rememberMe }),
        getCurrentUser(),
      ]);
      const userEmail = userData.username;
      const userFullData = await fetchUserByEmail(userEmail);
      console.log('===> AUTH CONTEXT USER DATA:', userFullData);
      setUserId(userFullData?.id ?? null);
      setUserSubrole(userFullData?.subrole ?? null);
      setUserEmail(userEmail);
      setUserPersonalInfo(userFullData);

      const token = session.tokens?.accessToken?.toString();
      if (!token) {
        throw new Error('No access token found');
      }

      const decodedToken = jwtDecode(token) as any;

      setUserExternalId(decodedToken.externalId);

      // Extract user groups from the session
      const groupsPayload =
        session.tokens?.accessToken?.payload['cognito:groups'];
      const groups = Array.isArray(groupsPayload)
        ? (groupsPayload as string[])
        : [];
      setRoles(groups);

      // Fetch onboarding status after we have user data (since it needs the user)
      if (decodedToken.externalId) {
        const onboardingStatus = await getUserOnboardingStatus(
          decodedToken.externalId
        );

        const isValidStep = Object.values(OnboardingStep).includes(
          onboardingStatus.currentStep as OnboardingStep
        );
        setOnboardingStatus(
          isValidStep
            ? (onboardingStatus.currentStep as OnboardingStep)
            : OnboardingStep.CHILD_STATUS
        );
      }

      setUser(userData);
    } catch (error) {
      console.log('===> Not signed in');
      setUser(null);
      setUserSubrole(null);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh the user data
  async function refreshUser() {
    setLoading(true);
    try {
      await fetchUser();
      // Return true if user is authenticated, false otherwise
      return !!user;
    } catch (error) {
      console.error('Error refreshing user:', error);
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Function to handle logout
  async function logout() {
    try {
      await signOut();
      setUser(null);
      setUserSubrole(null);
      setIsRememberMeSession(false);
      // Clear remember me preference on logout
      localStorage.removeItem('rememberMe');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  // Function to update only the onboarding status
  async function updateOnboardingStatus() {
    try {
      if (!userExternalId) {
        console.warn(
          'No userExternalId available for onboarding status update'
        );
        return;
      }

      const onboardingStatus = await getUserOnboardingStatus(userExternalId);

      const isValidStep = Object.values(OnboardingStep).includes(
        onboardingStatus.currentStep as OnboardingStep
      );
      setOnboardingStatus(
        isValidStep
          ? (onboardingStatus.currentStep as OnboardingStep)
          : OnboardingStep.CHILD_STATUS
      );
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  }

  // Fetch user on initial load
  useEffect(() => {
    fetchUser();
  }, []);

  const value: AuthContextType = {
    user,
    loading,
    onboardingStatus,
    userId,
    userExternalId,
    userEmail,
    refreshUser,
    logout,
    userRoles,
    userSubrole,
    updateOnboardingStatus,
    isRememberMeSession,
    userPersonalInfo,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      <InactivityTimer timeoutMinutes={30} warningMinutes={25} />
    </AuthContext.Provider>
  );
}
