// @ts-nocheck

'use client';

import * as Sentry from '@sentry/nextjs';
import { generateClient } from 'aws-amplify/data';
import { type Schema } from '@/amplify/data/resource';
import { getAdminResources, type AdminResource } from './adminResources';

export enum OnboardingStep {
  CHILD_STATUS = 'child_status',
  ELIGIBILITY = 'eligibility',
  COMPLETED = 'completed',
}

// Child status options
export enum ChildStatus {
  YES = 'yes',
  NO = 'no',
  PLANNING = 'planning',
}

// Estate size options (deprecated - keeping for backward compatibility)
export enum EstateSize {
  UNDER_100K = 'under100k',
  BETWEEN_100K_500K = '100k-500k',
  BETWEEN_500K_1M = '500k-1m',
  OVER_1M = 'over1m',
}

// Eligibility question responses
export enum EligibilityResponse {
  YES = 'yes',
  NO = 'no',
}

// Interface for eligibility answers
export interface EligibilityAnswers {
  usResident?: EligibilityResponse;
  medicalSituation?: EligibilityResponse;
  complexFinancial?: EligibilityResponse;
}

// Interface for onboarding answers
export interface OnboardingAnswers {
  childStatus?: ChildStatus;
  estateSize?: EstateSize; // deprecated - keeping for backward compatibility
  eligibility?: EligibilityAnswers;
  signupCompleted?: boolean;
  signupDate?: string;
  lastUpdated?: string;
}

// Helper function to get the current user's onboarding status
export async function getUserOnboardingStatus(
  userExternalId: string
): Promise<Schema['UserOnboarding']['type']> {
  try {
    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's onboarding record using externalId
    const { data: onboardingRecord } = await client.models.UserOnboarding.get({
      cognitoId: userExternalId,
    });

    if (!onboardingRecord) {
      console.log('===> No onboarding record found, returning default values');
      return {
        currentStep: OnboardingStep.CHILD_STATUS,
        isComplete: false,
        metadata: null,
      };
    }

    // Parse metadata if it exists
    let parsedMetadata = onboardingRecord.metadata;
    if (typeof parsedMetadata === 'string' && parsedMetadata) {
      try {
        parsedMetadata = JSON.parse(parsedMetadata);
      } catch (e) {
        Sentry.captureException(e);
        console.error('Error parsing metadata:', e);
      }
    }

    // Return the existing onboarding record
    return {
      currentStep: onboardingRecord.currentStep,
      isComplete: onboardingRecord.isComplete,
      metadata: parsedMetadata,
    };
  } catch (error) {
    Sentry.captureException(error);
    console.error('Error getting user onboarding status:', error);
    // Return default values if there's an error
    return {
      currentStep: OnboardingStep.CHILD_STATUS,
      isComplete: false,
      metadata: null,
    };
  }
}

// Helper function to update the user's onboarding step
export async function updateUserOnboardingStep(
  userExternalId: string,
  step: OnboardingStep,
  metadata?: any
) {
  try {
    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's onboarding record using externalId
    const { data: onboardingRecord } = await client.models.UserOnboarding.get({
      cognitoId: userExternalId,
    });

    // Ensure metadata is properly formatted for JSON storage
    const formattedMetadata = metadata ? JSON.stringify(metadata) : null;

    // If no record exists, create a new one with the provided step
    if (!onboardingRecord) {
      console.log('===> Creating new onboarding record');
      const { data: newOnboarding, errors: createErrors } =
        await client.models.UserOnboarding.create({
          cognitoId: userExternalId,
          currentStep: step,
          isComplete: step === OnboardingStep.COMPLETED,
          metadata: formattedMetadata,
          lastUpdated: new Date().toISOString(),
        });

      if (createErrors && createErrors.length > 0) {
        console.error('===> Create errors:', createErrors);
        throw new Error(
          `Failed to create onboarding record: ${createErrors[0].message}`
        );
      }

      if (!newOnboarding) {
        throw new Error('Failed to create onboarding record: No data returned');
      }

      return newOnboarding;
    }

    // Update the existing onboarding record
    const { data: updatedOnboarding, errors } =
      await client.models.UserOnboarding.update({
        cognitoId: userExternalId,
        currentStep: step,
        isComplete: step === OnboardingStep.COMPLETED,
        metadata: formattedMetadata || onboardingRecord.metadata,
        lastUpdated: new Date().toISOString(),
      });

    if (errors && errors.length > 0) {
      console.error('===> Update errors:', errors);
      throw new Error(
        `Failed to update onboarding record: ${errors[0].message}`
      );
    }

    if (!updatedOnboarding) {
      throw new Error('Failed to update onboarding record: No data returned');
    }

    return updatedOnboarding;
  } catch (error) {
    Sentry.captureException(error);
    console.error('===> Error updating user onboarding step:', error);
    throw error;
  }
}

// Helper function to check if onboarding is complete
export async function isOnboardingComplete(userExternalId: string) {
  const status = await getUserOnboardingStatus(userExternalId);
  return status.isComplete;
}

// Helper function to save onboarding answers
export async function saveOnboardingAnswer(
  userExternalId: string,
  step: OnboardingStep,
  answer: string,
  existingAnswers?: OnboardingAnswers
): Promise<Schema['UserOnboarding']['type']> {
  try {
    // Get existing metadata or create a new object
    const metadata: OnboardingAnswers = existingAnswers || {};

    // Update the appropriate field based on the step
    switch (step) {
      case OnboardingStep.CHILD_STATUS:
        // Validate that answer is a valid ChildStatus value
        if (Object.values(ChildStatus).includes(answer as ChildStatus)) {
          metadata.childStatus = answer as ChildStatus;
        } else {
          console.error(`Invalid ChildStatus value: ${answer}`);
          throw new Error(`Invalid ChildStatus value: ${answer}`);
        }
        break;
      case OnboardingStep.ESTATE:
        // Validate that answer is a valid EstateSize value (deprecated)
        if (Object.values(EstateSize).includes(answer as EstateSize)) {
          metadata.estateSize = answer as EstateSize;
        } else {
          console.error(`Invalid EstateSize value: ${answer}`);
          throw new Error(`Invalid EstateSize value: ${answer}`);
        }
        break;
      case OnboardingStep.ELIGIBILITY:
        // For eligibility step, answer should be a JSON string of EligibilityAnswers
        try {
          const eligibilityAnswers = JSON.parse(answer) as EligibilityAnswers;
          metadata.eligibility = eligibilityAnswers;
        } catch (e) {
          Sentry.captureException(e);
          console.error(`Invalid eligibility answers: ${answer}`);
          throw new Error(`Invalid eligibility answers: ${answer}`);
        }
        break;
      default:
        console.warn(`Unhandled step: ${step}`);
        break;
    }

    // Determine next step
    let nextStep: OnboardingStep;
    if (step === OnboardingStep.CHILD_STATUS) {
      nextStep = OnboardingStep.ELIGIBILITY;
    } else if (step === OnboardingStep.ELIGIBILITY) {
      // Don't automatically advance to COMPLETED for eligibility
      // The completion should be handled explicitly after checking eligibility requirements
      nextStep = OnboardingStep.ELIGIBILITY;
    } else if (step === OnboardingStep.ESTATE) {
      // Legacy support - if somehow estate step is still used
      nextStep = OnboardingStep.COMPLETED;
    } else {
      nextStep = step;
    }

    // Update the onboarding record with the new metadata
    const updatedRecord = await updateUserOnboardingStep(
      userExternalId,
      nextStep,
      metadata
    );

    if (!updatedRecord) {
      throw new Error('Failed to update onboarding record');
    }

    return updatedRecord;
  } catch (error) {
    Sentry.captureException(error);
    console.error('===> Error saving onboarding answer:', error);
    throw error;
  }
}

// Helper function to save eligibility answers
export async function saveEligibilityAnswers(
  userExternalId: string,
  eligibilityAnswers: EligibilityAnswers,
  existingAnswers?: OnboardingAnswers
): Promise<Schema['UserOnboarding']['type']> {
  const answersJson = JSON.stringify(eligibilityAnswers);
  return saveOnboardingAnswer(
    userExternalId,
    OnboardingStep.ELIGIBILITY,
    answersJson,
    existingAnswers
  );
}

// Helper function to check if review is required based on eligibility answers
export function isReviewRequired(
  eligibilityAnswers: EligibilityAnswers
): boolean {
  // Only acceptable answers are: Yes, No, No (US resident: Yes, Medical: No, Complex financial: No)
  // Any other combination requires review
  const isAcceptable =
    eligibilityAnswers.usResident === EligibilityResponse.YES &&
    eligibilityAnswers.medicalSituation === EligibilityResponse.NO &&
    eligibilityAnswers.complexFinancial === EligibilityResponse.NO;

  return !isAcceptable;
}

// Function to complete onboarding (only call when eligibility is acceptable)
export async function completeOnboarding(
  userExternalId: string
): Promise<void> {
  try {
    const client = generateClient<Schema>();

    const { data: updatedOnboarding, errors } =
      await client.models.UserOnboarding.update({
        cognitoId: userExternalId,
        currentStep: OnboardingStep.COMPLETED,
        isComplete: true,
        lastUpdated: new Date().toISOString(),
      });

    if (errors && errors.length > 0) {
      console.error('===> Complete onboarding errors:', errors);
      throw new Error(`Failed to complete onboarding: ${errors[0].message}`);
    }

    if (!updatedOnboarding) {
      throw new Error('Failed to complete onboarding: No data returned');
    }
  } catch (error) {
    Sentry.captureException(error);
    console.error('Error completing onboarding:', error);
    throw error;
  }
}

// Helper function to get onboarding answers
export async function getOnboardingAnswers(
  userExternalId: string
): Promise<OnboardingAnswers | null> {
  try {
    const status = await getUserOnboardingStatus(userExternalId);

    if (!status.metadata) {
      console.log('===> No metadata found in onboarding status');
      return null;
    }

    // Ensure metadata is properly parsed
    let metadata = status.metadata;
    if (typeof metadata === 'string') {
      try {
        metadata = JSON.parse(metadata);
      } catch (e) {
        Sentry.captureException(e);
        console.error('Error parsing metadata string:', e);
        return null;
      }
    }

    // Validate the metadata structure
    const answers: OnboardingAnswers = {};

    // Copy valid properties
    if (
      metadata.childStatus &&
      Object.values(ChildStatus).includes(metadata.childStatus)
    ) {
      answers.childStatus = metadata.childStatus;
    }

    if (
      metadata.estateSize &&
      Object.values(EstateSize).includes(metadata.estateSize)
    ) {
      answers.estateSize = metadata.estateSize;
    }

    // Handle eligibility answers
    if (metadata.eligibility && typeof metadata.eligibility === 'object') {
      const eligibility = metadata.eligibility as EligibilityAnswers;
      if (
        (eligibility.usResident &&
          Object.values(EligibilityResponse).includes(
            eligibility.usResident
          )) ||
        (eligibility.medicalSituation &&
          Object.values(EligibilityResponse).includes(
            eligibility.medicalSituation
          )) ||
        (eligibility.complexFinancial &&
          Object.values(EligibilityResponse).includes(
            eligibility.complexFinancial
          ))
      ) {
        answers.eligibility = eligibility;
      }
    }

    if (typeof metadata.signupCompleted === 'boolean') {
      answers.signupCompleted = metadata.signupCompleted;
    }

    if (metadata.signupDate) {
      answers.signupDate = metadata.signupDate;
    }

    if (metadata.lastUpdated) {
      answers.lastUpdated = metadata.lastUpdated;
    }

    return Object.keys(answers).length > 0 ? answers : null;
  } catch (error) {
    Sentry.captureException(error);
    console.error('Error getting onboarding answers:', error);
    return null;
  }
}

// Helper function to get admin resources for non-childfree users
export async function getResourcesForNonChildfreeUsers(): Promise<
  AdminResource[]
> {
  try {
    return await getAdminResources();
  } catch (error) {
    Sentry.captureException(error);
    console.error('Error getting admin resources:', error);
    return [];
  }
}
