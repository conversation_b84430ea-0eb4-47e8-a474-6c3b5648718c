'use client';

import { generateClient } from 'aws-amplify/data';
import { type Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

export interface AdminResource {
  id: string;
  title: string;
  description?: string | null;
  url: string;
  resourceType: 'website' | 'person' | 'organization' | 'other';
  isActive: boolean;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface NonChildfreeUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  childStatus: string;
  onboardingMetadata?: any;
}

// Get all admin resources
export async function getAdminResources(): Promise<AdminResource[]> {
  try {
    const client = generateClient<Schema>();
    const { data: resources, errors } = await client.models.AdminResource.list({
      filter: {
        isActive: {
          eq: true,
        },
      },
    });

    if (errors) {
      console.error('Error fetching admin resources:', errors);
      throw new Error('Failed to fetch admin resources');
    }

    return resources
      .filter(resource => resource.id) // Filter out resources without id
      .map(resource => ({
        id: resource.id!,
        title: resource.title,
        description: resource.description,
        url: resource.url,
        resourceType: resource.resourceType as AdminResource['resourceType'],
        isActive: resource.isActive ?? true,
        displayOrder: resource.displayOrder ?? 0,
        createdAt: resource.createdAt,
        updatedAt: resource.updatedAt,
        createdBy: resource.createdBy,
      }))
      .sort((a, b) => a.displayOrder - b.displayOrder);
  } catch (error) {
    console.error('Error getting admin resources:', error);
    throw error;
  }
}

// Create new admin resource
export async function createAdminResource(
  resource: Omit<AdminResource, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>
): Promise<AdminResource> {
  try {
    const user = await getCurrentUser();
    const client = generateClient<Schema>();

    const now = new Date().toISOString();

    const { data: newResource, errors } =
      await client.models.AdminResource.create({
        title: resource.title,
        description: resource.description || null,
        url: resource.url,
        resourceType: resource.resourceType,
        isActive: resource.isActive,
        displayOrder: resource.displayOrder,
        createdAt: now,
        updatedAt: now,
        createdBy: user.userId,
      });

    if (errors || !newResource) {
      console.error('Error creating admin resource:', errors);
      throw new Error('Failed to create admin resource');
    }

    return {
      id: newResource.id!,
      title: newResource.title,
      description: newResource.description,
      url: newResource.url,
      resourceType: newResource.resourceType as AdminResource['resourceType'],
      isActive: newResource.isActive ?? true,
      displayOrder: newResource.displayOrder ?? 0,
      createdAt: newResource.createdAt,
      updatedAt: newResource.updatedAt,
      createdBy: newResource.createdBy,
    };
  } catch (error) {
    console.error('Error creating admin resource:', error);
    throw error;
  }
}

// Update admin resource
export async function updateAdminResource(
  id: string,
  updates: Partial<Omit<AdminResource, 'id' | 'createdAt' | 'createdBy'>>
): Promise<AdminResource> {
  try {
    const client = generateClient<Schema>();

    const now = new Date().toISOString();

    const { data: updatedResource, errors } =
      await client.models.AdminResource.update({
        id,
        ...updates,
        updatedAt: now,
      });

    if (errors || !updatedResource) {
      console.error('Error updating admin resource:', errors);
      throw new Error('Failed to update admin resource');
    }

    return {
      id: updatedResource.id!,
      title: updatedResource.title,
      description: updatedResource.description,
      url: updatedResource.url,
      resourceType:
        updatedResource.resourceType as AdminResource['resourceType'],
      isActive: updatedResource.isActive ?? true,
      displayOrder: updatedResource.displayOrder ?? 0,
      createdAt: updatedResource.createdAt,
      updatedAt: updatedResource.updatedAt,
      createdBy: updatedResource.createdBy,
    };
  } catch (error) {
    console.error('Error updating admin resource:', error);
    throw error;
  }
}

// Delete admin resource
export async function deleteAdminResource(id: string): Promise<void> {
  try {
    const client = generateClient<Schema>();

    const { errors } = await client.models.AdminResource.delete({ id });

    if (errors) {
      console.error('Error deleting admin resource:', errors);
      throw new Error('Failed to delete admin resource');
    }
  } catch (error) {
    console.error('Error deleting admin resource:', error);
    throw error;
  }
}

// Get users with non-childfree responses
export async function getNonChildfreeUsers(): Promise<NonChildfreeUser[]> {
  try {
    const client = generateClient<Schema>();

    // Get all onboarding records with childStatus = 'no'
    const { data: onboardingRecords, errors: onboardingErrors } =
      await client.models.UserOnboarding.list();

    if (onboardingErrors) {
      console.error('Error fetching onboarding records:', onboardingErrors);
      throw new Error('Failed to fetch onboarding records');
    }

    // Filter records with childStatus = 'no'
    const nonChildfreeRecords = onboardingRecords.filter(record => {
      if (!record.metadata) return false;

      let metadata: any = record.metadata;
      if (typeof metadata === 'string') {
        try {
          metadata = JSON.parse(metadata);
        } catch (e) {
          return false;
        }
      }

      return (
        metadata &&
        typeof metadata === 'object' &&
        metadata.childStatus === 'no'
      );
    });

    // Get user details for these records
    const { data: users, errors: userErrors } = await client.models.User.list();

    console.log('===> Users:', users);

    if (userErrors) {
      console.error('Error fetching users:', userErrors);
      throw new Error('Failed to fetch users');
    }

    // Match users with non-childfree onboarding records
    const nonChildfreeUsers: NonChildfreeUser[] = [];

    for (const record of nonChildfreeRecords) {
      const user = users.find(u => u.cognitoId === record.cognitoId);

      if (user) {
        let metadata: any = record.metadata;

        console.log('===> Metadata type:', typeof metadata);

        if (typeof metadata === 'string') {
          try {
            metadata = JSON.parse(metadata);
          } catch (e) {
            metadata = {};
          }
        }

        nonChildfreeUsers.push({
          id: user.id!,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          createdAt: user.createdAt,
          childStatus:
            (metadata &&
              typeof metadata === 'object' &&
              metadata.childStatus) ||
            'no',
          onboardingMetadata: metadata,
        });
      }
    }

    return nonChildfreeUsers.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  } catch (error) {
    console.error('Error getting non-childfree users:', error);
    throw error;
  }
}
