/**
 * Video Utilities
 *
 * Utility functions for handling different video platforms
 */

export interface VideoInfo {
  type: 'youtube' | 'vimeo' | 'html5' | 'iframe' | 'unknown';
  embedUrl: string;
  isEmbeddable: boolean;
  videoId?: string;
  requiresIframe?: boolean;
  fallbackMessage?: string;
}

/**
 * Extract YouTube video ID from URL
 */
export const getYouTubeVideoId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Check if it's a valid YouTube domain - EXACT match only
    const validYouTubeDomains = [
      'youtube.com',
      'www.youtube.com',
      'm.youtube.com',
      'youtu.be',
    ];

    if (!validYouTubeDomains.includes(hostname)) {
      return null;
    }

    // Extract video ID based on URL format
    if (hostname === 'youtu.be') {
      // Format: https://youtu.be/VIDEO_ID
      const videoId = urlObj.pathname.slice(1).split('?')[0];
      return videoId.length === 11 ? videoId : null;
    } else {
      // Format: https://youtube.com/watch?v=VIDEO_ID
      // Must have /watch path and v parameter
      if (!urlObj.pathname.startsWith('/watch')) {
        return null;
      }
      const videoId = urlObj.searchParams.get('v');
      return videoId && videoId.length === 11 ? videoId : null;
    }
  } catch {
    return null;
  }
};

/**
 * Extract Vimeo video ID from URL
 */
export const getVimeoVideoId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Check if it's a valid Vimeo domain
    const validVimeoDomains = [
      'vimeo.com',
      'www.vimeo.com',
      'player.vimeo.com',
    ];

    if (!validVimeoDomains.includes(hostname)) {
      return null;
    }

    // Extract video ID from pathname
    // Handle formats like /123456789, /video/123456789, /123456789?param=value
    const pathMatch = urlObj.pathname.match(/\/(?:video\/)?(\d+)/);
    if (pathMatch && pathMatch[1]) {
      const videoId = pathMatch[1];
      // Vimeo video IDs are typically 6-11 digits (extended range for newer videos)
      return /^\d{6,11}$/.test(videoId) ? videoId : null;
    }

    return null;
  } catch {
    return null;
  }
};

/**
 * Check if URL is a YouTube video
 */
export const isYouTubeVideo = (url: string): boolean => {
  return getYouTubeVideoId(url) !== null;
};

/**
 * Check if URL is a Vimeo video
 */
export const isVimeoVideo = (url: string): boolean => {
  return getVimeoVideoId(url) !== null;
};

/**
 * Get YouTube embed URL
 */
export const getYouTubeEmbedUrl = (url: string, autoPlay = false): string => {
  const videoId = getYouTubeVideoId(url);
  if (videoId) {
    const autoplayParam = autoPlay ? '&autoplay=1' : '';
    return `https://www.youtube.com/embed/${videoId}?rel=0${autoplayParam}&modestbranding=1&showinfo=0`;
  }
  return url;
};

/**
 * Get Vimeo embed URL
 */
export const getVimeoEmbedUrl = (url: string, autoPlay = false): string => {
  const videoId = getVimeoVideoId(url);
  if (videoId) {
    const autoplayParam = autoPlay ? '&autoplay=1' : '';
    return `https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0${autoplayParam}`;
  }
  return url;
};

/**
 * Get YouTube thumbnail URLs in order of preference
 */
export const getYouTubeThumbnailUrls = (videoId: string): string[] => [
  `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/default.jpg`,
];

/**
 * Get Vimeo thumbnail URLs in order of preference
 */
export const getVimeoThumbnailUrls = (videoId: string): string[] => [
  `https://i.vimeocdn.com/video/${videoId}_640x360.jpg`,
  `https://i.vimeocdn.com/video/${videoId}_295x166.jpg`,
  `https://i.vimeocdn.com/video/${videoId}.jpg`,
  `https://vumbnail.com/${videoId}.jpg`, // Fallback to third-party service
];

/**
 * Get Vimeo thumbnail URL (primary)
 */
export const getVimeoThumbnailUrl = (videoId: string): string => {
  return getVimeoThumbnailUrls(videoId)[0];
};

/**
 * Check if URL might be embeddable (contains common video hosting patterns)
 */
export const isLikelyEmbeddableVideo = (url: string): boolean => {
  const embeddablePatterns = [
    /wistia\.com/i,
    /jwplayer\.com/i,
    /brightcove\.com/i,
    /kaltura\.com/i,
    /vidyard\.com/i,
    /loom\.com/i,
    /streamable\.com/i,
    /dailymotion\.com/i,
    /twitch\.tv/i,
    /facebook\.com.*\/videos/i,
    /instagram\.com.*\/p\//i,
    /tiktok\.com/i,
  ];

  return embeddablePatterns.some(pattern => pattern.test(url));
};

/**
 * Get comprehensive video information
 */
export const getVideoInfo = (url: string, autoPlay = false): VideoInfo => {
  if (!url) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'No video URL provided',
    };
  }

  const youtubeId = getYouTubeVideoId(url);
  if (youtubeId) {
    return {
      type: 'youtube',
      embedUrl: getYouTubeEmbedUrl(url, autoPlay),
      isEmbeddable: true,
      videoId: youtubeId,
      requiresIframe: true,
    };
  }

  const vimeoId = getVimeoVideoId(url);
  if (vimeoId) {
    return {
      type: 'vimeo',
      embedUrl: getVimeoEmbedUrl(url, autoPlay),
      isEmbeddable: true,
      videoId: vimeoId,
      requiresIframe: true,
    };
  }

  // Check if it's a direct video file
  if (url.match(/\.(mp4|webm|ogg|mov|avi|mkv)$/i)) {
    return {
      type: 'html5',
      embedUrl: url,
      isEmbeddable: false,
      requiresIframe: false,
    };
  }

  // Check if it might be embeddable from other platforms
  if (isLikelyEmbeddableVideo(url)) {
    return {
      type: 'iframe',
      embedUrl: url,
      isEmbeddable: true,
      requiresIframe: true,
      fallbackMessage:
        'This video platform may require additional configuration',
    };
  }

  // Unknown video type - try iframe first, then fallback
  return {
    type: 'unknown',
    embedUrl: url,
    isEmbeddable: true, // Try iframe first
    requiresIframe: true,
    fallbackMessage: 'Video format not recognized. Attempting to display...',
  };
};

/**
 * Get thumbnail URL for any video platform
 */
export const getVideoThumbnailUrl = (url: string): string | null => {
  const youtubeId = getYouTubeVideoId(url);
  if (youtubeId) {
    return getYouTubeThumbnailUrls(youtubeId)[0]; // Return highest quality
  }

  const vimeoId = getVimeoVideoId(url);
  if (vimeoId) {
    return getVimeoThumbnailUrl(vimeoId);
  }

  return null;
};

/**
 * Format video duration in MM:SS format
 */
export const formatVideoDuration = (seconds?: number): string => {
  if (!seconds) return 'N/A';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Format reading time for articles
 */
export const formatReadingTime = (minutes?: number): string => {
  if (!minutes) return 'N/A';
  return `${minutes} min read`;
};

/**
 * Get player size container classes
 */
export const getPlayerSizeClasses = (
  size: 'small' | 'medium' | 'large'
): string => {
  switch (size) {
    case 'small':
      return 'w-full max-w-md mx-auto';
    case 'medium':
      return 'w-full max-w-2xl mx-auto';
    case 'large':
      return 'w-full max-w-4xl mx-auto';
    default:
      return 'w-full max-w-2xl mx-auto';
  }
};

/**
 * Get player size label
 */
export const getPlayerSizeLabel = (
  size: 'small' | 'medium' | 'large'
): string => {
  switch (size) {
    case 'small':
      return 'Small';
    case 'medium':
      return 'Medium';
    case 'large':
      return 'Large';
    default:
      return 'Medium';
  }
};

/**
 * Cycle through player sizes
 */
export const getNextPlayerSize = (
  currentSize: 'small' | 'medium' | 'large'
): 'small' | 'medium' | 'large' => {
  const sizes = ['small', 'medium', 'large'] as const;
  const currentIndex = sizes.indexOf(currentSize);
  const nextIndex = (currentIndex + 1) % sizes.length;
  return sizes[nextIndex];
};

/**
 * Validate and sanitize video URL
 */
export const sanitizeVideoUrl = (url: string): string => {
  if (!url) return '';

  try {
    // Try to create a URL object to validate
    const urlObj = new URL(url);

    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      console.warn('Invalid protocol for video URL:', urlObj.protocol);
      return '';
    }

    return url.trim();
  } catch (error) {
    console.warn('Invalid video URL:', url, error);
    return '';
  }
};

/**
 * Check if URL is safe to embed
 */
export const isSafeToEmbed = (url: string): boolean => {
  const sanitizedUrl = sanitizeVideoUrl(url);
  if (!sanitizedUrl) return false;

  try {
    const urlObj = new URL(sanitizedUrl);

    // Block potentially dangerous domains
    const blockedDomains = ['localhost', '127.0.0.1', '0.0.0.0', 'file://'];

    const hostname = urlObj.hostname.toLowerCase();
    return !blockedDomains.some(blocked => hostname.includes(blocked));
  } catch {
    return false;
  }
};

/**
 * Get safe video information with validation
 */
export const getSafeVideoInfo = (url: string, autoPlay = false): VideoInfo => {
  const sanitizedUrl = sanitizeVideoUrl(url);

  if (!sanitizedUrl) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'Invalid video URL provided',
    };
  }

  if (!isSafeToEmbed(sanitizedUrl)) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'Video URL is not safe to embed',
    };
  }

  return getVideoInfo(sanitizedUrl, autoPlay);
};

/**
 * Video URL validation result
 */
export interface VideoValidationResult {
  isValid: boolean;
  error?: string;
  warning?: string;
  videoInfo?: VideoInfo;
  platform?: 'youtube' | 'vimeo' | 'other' | 'unknown';
}

/**
 * Check if URL is from a known video platform
 */
export const isKnownVideoPlatform = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Known video platforms - EXACT matches only to prevent spoofing
    const knownPlatforms = [
      'youtube.com',
      'www.youtube.com',
      'm.youtube.com',
      'youtu.be',
      'vimeo.com',
      'www.vimeo.com',
      'player.vimeo.com',
      'wistia.com',
      'www.wistia.com',
      'fast.wistia.com',
      'jwplayer.com',
      'content.jwplatform.com',
      'brightcove.com',
      'players.brightcove.net',
      'kaltura.com',
      'cdnapisec.kaltura.com',
      'vidyard.com',
      'play.vidyard.com',
      'loom.com',
      'www.loom.com',
      'streamable.com',
      'dailymotion.com',
      'www.dailymotion.com',
      'twitch.tv',
      'www.twitch.tv',
    ];

    // Use EXACT match only - no subdomain wildcards to prevent spoofing
    return knownPlatforms.includes(hostname);
  } catch {
    return false;
  }
};

/**
 * Validate video URL comprehensively
 */
export const validateVideoUrl = (url: string): VideoValidationResult => {
  // Check if URL is empty
  if (!url || url.trim() === '') {
    return {
      isValid: false,
      error: 'Video URL is required',
    };
  }

  // Check for spaces in URL
  if (url.includes(' ')) {
    return {
      isValid: false,
      error: 'Video URL cannot contain spaces',
    };
  }

  const trimmedUrl = url.trim();

  // Basic URL format validation
  try {
    const urlObj = new URL(trimmedUrl);

    // Check protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return {
        isValid: false,
        error: 'Video URL must use HTTP or HTTPS protocol',
      };
    }
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid URL format',
    };
  }

  // Check if URL is safe to embed
  if (!isSafeToEmbed(trimmedUrl)) {
    return {
      isValid: false,
      error: 'Video URL is not safe to embed (blocked domain)',
    };
  }

  // Check if it's from a known video platform
  if (!isKnownVideoPlatform(trimmedUrl)) {
    // Provide helpful error messages for common mistakes
    try {
      const urlObj = new URL(trimmedUrl);
      const hostname = urlObj.hostname.toLowerCase();

      // Check for common YouTube domain mistakes
      if (hostname.includes('youtube') || hostname.includes('youtu')) {
        if (
          hostname === 'youtube.co' ||
          hostname === 'youtub.com' ||
          hostname === 'wwbe.com'
        ) {
          return {
            isValid: false,
            error:
              'Invalid YouTube domain. Please use youtube.com, www.youtube.com, or youtu.be',
          };
        }
        return {
          isValid: false,
          error: `"${hostname}" is not a valid YouTube domain. Please use youtube.com, www.youtube.com, or youtu.be`,
        };
      }

      // Check for common Vimeo domain mistakes
      if (hostname.includes('vimeo')) {
        return {
          isValid: false,
          error: `"${hostname}" is not a valid Vimeo domain. Please use vimeo.com or www.vimeo.com`,
        };
      }

      return {
        isValid: false,
        error: `"${hostname}" is not a supported video platform. Please use YouTube, Vimeo, or other supported platforms.`,
      };
    } catch {
      return {
        isValid: false,
        error: 'URL is not from a supported video platform',
      };
    }
  }

  // Get video information
  const videoInfo = getSafeVideoInfo(trimmedUrl);

  // Determine platform
  let platform: 'youtube' | 'vimeo' | 'other' | 'unknown' = 'unknown';
  if (videoInfo.type === 'youtube') {
    platform = 'youtube';
  } else if (videoInfo.type === 'vimeo') {
    platform = 'vimeo';
  } else if (videoInfo.type === 'html5' || videoInfo.type === 'iframe') {
    platform = 'other';
  }

  // For YouTube and Vimeo, ensure we can extract video ID
  if (platform === 'youtube' && !getYouTubeVideoId(trimmedUrl)) {
    // Check if it looks like a YouTube URL but is malformed
    try {
      const urlObj = new URL(trimmedUrl);
      if (urlObj.pathname.includes('watch') && !urlObj.searchParams.get('v')) {
        return {
          isValid: false,
          error: 'YouTube URL is missing video ID parameter (?v=VIDEO_ID)',
          videoInfo,
          platform,
        };
      }
    } catch {}

    return {
      isValid: false,
      error: 'Invalid YouTube URL format - cannot extract video ID',
      videoInfo,
      platform,
    };
  }

  if (platform === 'vimeo' && !getVimeoVideoId(trimmedUrl)) {
    return {
      isValid: false,
      error: 'Invalid Vimeo URL format - cannot extract video ID',
      videoInfo,
      platform,
    };
  }

  // Check if video is embeddable
  if (!videoInfo.isEmbeddable && videoInfo.type === 'unknown') {
    return {
      isValid: false,
      error: 'Video URL format is not supported or recognized',
      videoInfo,
      platform,
    };
  }

  // Success case with potential warnings
  let warning: string | undefined;
  if (videoInfo.type === 'iframe' || videoInfo.type === 'unknown') {
    warning =
      'Video platform may require additional configuration for proper display';
  }

  return {
    isValid: true,
    warning,
    videoInfo,
    platform,
  };
};

/**
 * Async validation that checks if video URL is accessible
 */
export const validateVideoUrlAsync = async (
  url: string
): Promise<VideoValidationResult> => {
  // First do synchronous validation
  const syncResult = validateVideoUrl(url);
  if (!syncResult.isValid) {
    return syncResult;
  }

  const trimmedUrl = url.trim();

  try {
    // For YouTube videos, try to check if video exists using oEmbed API
    const youtubeId = getYouTubeVideoId(trimmedUrl);
    if (youtubeId) {
      try {
        // Use YouTube's oEmbed API to check if video exists
        const oembedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${youtubeId}&format=json`;
        const response = await fetch(oembedUrl);

        if (!response.ok) {
          return {
            ...syncResult,
            isValid: false,
            error:
              'YouTube video not found, is private, or has been removed. Please check the URL.',
          };
        }
      } catch (error) {
        // If oEmbed fails, try thumbnail method as fallback
        try {
          const thumbnailUrl = `https://img.youtube.com/vi/${youtubeId}/mqdefault.jpg`;
          const thumbnailResponse = await fetch(thumbnailUrl, {
            method: 'HEAD',
          });

          if (!thumbnailResponse.ok) {
            return {
              ...syncResult,
              isValid: false,
              error:
                'YouTube video not found, is private, or has been removed. Please check the URL.',
            };
          }
        } catch (thumbnailError) {
          return {
            ...syncResult,
            warning:
              'Could not verify video accessibility. Please ensure the video exists and is publicly accessible.',
          };
        }
      }
    }

    // For Vimeo videos, try to check if video exists using oEmbed API
    const vimeoId = getVimeoVideoId(trimmedUrl);
    if (vimeoId) {
      try {
        // Use Vimeo's oEmbed API to check if video exists
        const oembedUrl = `https://vimeo.com/api/oembed.json?url=${encodeURIComponent(trimmedUrl)}`;
        const response = await fetch(oembedUrl);

        if (!response.ok) {
          return {
            ...syncResult,
            isValid: false,
            error:
              'Video not found or is private. Please check the URL and video privacy settings.',
          };
        }

        const oembedData = await response.json();
        if (!oembedData || !oembedData.video_id) {
          return {
            ...syncResult,
            isValid: false,
            error:
              'Video not found or is private. Please check the URL and video privacy settings.',
          };
        }

        return {
          ...syncResult,
          warning: undefined,
        };
      } catch (error) {
        // If oEmbed fails, fall back to basic validation
        return {
          ...syncResult,
          warning:
            'Could not verify video accessibility. Please ensure the video exists and is publicly accessible.',
        };
      }
    }

    // For other URLs, we can't easily validate without CORS issues
    if (syncResult.platform === 'other' || syncResult.platform === 'unknown') {
      return {
        ...syncResult,
        warning: 'Video accessibility could not be verified',
      };
    }

    return syncResult;
  } catch (error) {
    // If network check fails, return the sync result with a warning
    return {
      ...syncResult,
      warning: 'Could not verify video accessibility (network error)',
    };
  }
};
