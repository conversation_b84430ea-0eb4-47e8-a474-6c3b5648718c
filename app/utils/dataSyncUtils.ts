'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { updateUserAttributes } from 'aws-amplify/auth';
import {
  getUserDataForMyProfileInterviewStep,
  updateUserProfile,
} from '@/lib/data/users';
import {
  loadInterviewProgressV2,
  saveInterviewProgressV2,
} from './interviewV2Progress';

const client = generateClient<Schema>();

// Types for different data sources
export interface ProfileSyncData {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  phoneNumber?: string;
  birthdate?: string;
}

export interface AddressSyncData {
  street?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  county?: string;
}

export interface InterviewProfileData {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: {
    street?: string;
    street2?: string;
    city?: string;
    state?: string;
    zip?: string;
    county?: string;
  };
}

export interface UserAddressData {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  stateProvinceCode?: string;
  postalCode?: string;
  countryCode?: string;
}

/**
 * Sync profile data from Settings to Interview V2
 */
export async function syncSettingsToInterviewV2(
  profileData: ProfileSyncData
): Promise<void> {
  try {
    console.log('Syncing settings to Interview V2:', profileData);

    // Load current interview progress
    const currentProgress = await loadInterviewProgressV2();

    // Ensure stepsData exists and has proper structure
    const stepsData = currentProgress.stepsData || {};
    const currentProfile = stepsData.profile || {};

    // Update profile section with new data
    const updatedProfile = {
      ...currentProfile,
      firstName: profileData.firstName || currentProfile.firstName,
      middleName: profileData.middleName || currentProfile.middleName,
      lastName: profileData.lastName || currentProfile.lastName,
      phone: profileData.phoneNumber || currentProfile.phone,
      dateOfBirth: profileData.birthdate || currentProfile.dateOfBirth,
    };

    // Save updated progress
    await saveInterviewProgressV2({
      ...currentProgress,
      stepsData: {
        ...stepsData,
        profile: updatedProfile,
      },
    });

    console.log('Successfully synced settings to Interview V2');
  } catch (error) {
    console.error('Error syncing settings to Interview V2:', error);
    throw error;
  }
}

/**
 * Sync profile data from Interview V2 to Settings
 */
export async function syncInterviewV2ToSettings(
  interviewData: InterviewProfileData,
  userExternalId: string
): Promise<void> {
  try {
    console.log('Syncing Interview V2 to settings:', interviewData);

    // Load current user data to get user ID
    const userData = await getUserDataForMyProfileInterviewStep(userExternalId);
    if (!userData) {
      throw new Error('User data not found');
    }

    // Get user from database
    const { data: users, errors } = await client.models.User.list({
      filter: { email: { eq: userData.email } },
      selectionSet: [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'phoneNumber',
        'birthdate',
      ] as const,
    });

    if (errors || !users || users.length === 0) {
      throw new Error('User not found in database');
    }

    const user = users[0];

    // Prepare update data
    const updateData: any = {};
    if (interviewData.firstName !== undefined)
      updateData.firstName = interviewData.firstName;
    if (interviewData.middleName !== undefined)
      updateData.middleName = interviewData.middleName;
    if (interviewData.lastName !== undefined)
      updateData.lastName = interviewData.lastName;
    if (interviewData.phone !== undefined)
      updateData.phoneNumber = interviewData.phone;
    if (interviewData.dateOfBirth !== undefined)
      updateData.birthdate = interviewData.dateOfBirth;

    // Update user profile in DynamoDB only (no Cognito updates)
    if (Object.keys(updateData).length > 0) {
      await updateUserProfile(user.id, updateData);
    }

    console.log('Successfully synced Interview V2 to settings');
  } catch (error) {
    console.error('Error syncing Interview V2 to settings:', error);
    throw error;
  }
}

/**
 * Sync address data from Shipping to Interview V2
 */
export async function syncShippingToInterviewV2(
  addressData: UserAddressData
): Promise<void> {
  try {
    console.log('Syncing shipping address to Interview V2:', addressData);

    // Load current interview progress
    const currentProgress = await loadInterviewProgressV2();

    // Ensure stepsData exists and has proper structure
    const stepsData = currentProgress.stepsData || {};
    const currentProfile = stepsData.profile || {};
    const currentAddress = currentProfile.address || {};

    // Update profile address with new data
    const updatedAddress = {
      street: addressData.addressLine1 || currentAddress.street,
      street2: addressData.addressLine2 || currentAddress.street2,
      city: addressData.city || currentAddress.city,
      state: addressData.stateProvinceCode || currentAddress.state,
      zip: addressData.postalCode || currentAddress.zip,
      county: currentAddress.county, // Keep existing county
      country: addressData.countryCode || 'US',
    };

    const updatedProfile = {
      ...currentProfile,
      address: updatedAddress,
    };

    // Save updated progress
    await saveInterviewProgressV2({
      ...currentProgress,
      stepsData: {
        ...stepsData,
        profile: updatedProfile,
      },
    });

    console.log('Successfully synced shipping address to Interview V2');
  } catch (error) {
    console.error('Error syncing shipping address to Interview V2:', error);
    throw error;
  }
}

/**
 * Sync address data from Interview V2 to Shipping
 */
export async function syncInterviewV2ToShipping(
  addressData: AddressSyncData,
  userExternalId: string
): Promise<void> {
  try {
    console.log(
      'Syncing Interview V2 address to user primary address fields:',
      addressData
    );

    // Load current user data to get user ID
    const userData = await getUserDataForMyProfileInterviewStep(userExternalId);
    if (!userData) {
      throw new Error('User data not found');
    }

    // Get user from database
    const { data: users, errors } = await client.models.User.list({
      filter: { email: { eq: userData.email } },
      selectionSet: ['id'] as const,
    });

    if (errors || !users || users.length === 0) {
      throw new Error('User not found in database');
    }

    const user = users[0];

    // Map interview address to primary user address fields
    const addressForSave = {
      addressLine1: addressData.street || '',
      addressLine2: addressData.street2 || '',
      city: addressData.city || '',
      stateProvinceCode: addressData.state || '',
      postalCode: addressData.zip || '',
      countryCode: 'US',
    } as const;

    // Also update the embedded shippingAddress field for consistency across the app
    const shippingAddress = {
      addressLine1: addressForSave.addressLine1,
      addressLine2: addressForSave.addressLine2,
      city: addressForSave.city,
      stateProvinceCode: addressForSave.stateProvinceCode,
      postalCode: addressForSave.postalCode,
      countryCode: addressForSave.countryCode,
    } as const;

    await client.models.User.update({
      id: user.id,
      ...addressForSave,
      shippingAddress: shippingAddress as any,
    });

    console.log(
      'Successfully synced Interview V2 address to user primary address fields'
    );
  } catch (error) {
    console.error(
      'Error syncing Interview V2 address to user primary address fields:',
      error
    );
    throw error;
  }
}
