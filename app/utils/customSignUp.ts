import { signUp } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { v4 as uuidv4 } from 'uuid';

export interface CustomSignUpRequest {
  email: string;
  password: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  state: string;
  birthdayDate: Date;
  phone: string;
  inviteToken?: string; // Optional invite token for invited users
  howDidYouHearAboutUs?: string; // Optional referral source
  address?: string;
  gender?: string; // Optional gender field
}

export interface CustomSignUpResponse {
  success: boolean;
  error?: string;
  message?: string;
}

/**
 * Custom sign-up function that creates user in Cognito but doesn't auto-confirm
 * Instead, it generates a verification token and sends a custom email
 */
export const customSignUp = async (
  request: CustomSignUpRequest
): Promise<CustomSignUpResponse> => {
  try {
    const client = generateClient<Schema>({
      authMode: 'iam',
    });

    // Age validation function
    const calculateAge = (birthDate: Date): number => {
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        age--;
      }

      return age;
    };

    // Generate a secure externalId token using uuid v4
    const generateExternalId = (): string => uuidv4();

    // Validate age (must be 18 or older)
    if (request.birthdayDate) {
      const age = calculateAge(request.birthdayDate);
      if (age < 18) {
        return {
          success: false,
          error: 'You must be at least 18 years old to register',
        };
      }
    } else {
      return {
        success: false,
        error: 'Birthday is required',
      };
    }

    // Format phone number to E.164 format for AWS Cognito
    const formattedPhone = request.phone.startsWith('+1')
      ? request.phone
      : `+1${request.phone.replace(/\D/g, '')}`;

    // Format date to YYYY-MM-DD for AWS Cognito
    const formattedBirthday = request.birthdayDate
      ? request.birthdayDate.toISOString().split('T')[0]
      : '';

    // Create a temporary externalId token to store on the Cognito user
    const externalIdToken = generateExternalId();

    if (request.inviteToken) {
      const userAttributes: any = {
        email: request.email,
        given_name: request.firstName,
        family_name: request.lastName,
        address: request.address,
        birthdate: formattedBirthday,
        phone_number: formattedPhone,
        'custom:invite_token': request.inviteToken,
        'custom:referral_source': request.howDidYouHearAboutUs || 'No referral',
        'custom:externalId': externalIdToken,
      };

      // Add middle name if provided
      if (request.middleName && request.middleName.trim()) {
        userAttributes.middle_name = request.middleName;
      }

      // Only add gender if it's provided
      if (request.gender && request.gender.trim()) {
        userAttributes.gender = request.gender;
      }

      await signUp({
        username: request.email,
        password: request.password,
        options: {
          userAttributes,
          autoSignIn: false,
        },
      });

      const { errors } = await client.mutations.acceptInvite({
        inviteToken: request.inviteToken,
        email: request.email,
      });

      if (errors) {
        throw new Error('Failed to accept invite');
      }

      return {
        success: true,
        message: 'Account created successfully after invite',
      };
    } else {
      // Create user in Cognito (this will be unconfirmed)
      const userAttributes: any = {
        email: request.email,
        given_name: request.firstName,
        family_name: request.lastName,
        address: request.address,
        birthdate: formattedBirthday,
        phone_number: formattedPhone,
        'custom:referral_source': request.howDidYouHearAboutUs || 'No referral',
        'custom:externalId': externalIdToken,
      };

      // Add middle name if provided
      if (request.middleName && request.middleName.trim()) {
        userAttributes.middle_name = request.middleName;
      }

      // Only add gender if it's provided
      if (request.gender && request.gender.trim()) {
        userAttributes.gender = request.gender;
      }

      await signUp({
        username: request.email,
        password: request.password,
        options: {
          userAttributes,
          autoSignIn: false,
        },
      });

      // Send verification email (token will be generated automatically on backend)
      const emailResult = await client.mutations.sendEmail({
        to: request.email,
        subject: 'Verify Your Email - Childfree Trust®',
        message: `Welcome to Childfree, ${request.firstName}!\n\nPlease verify your email address by clicking the link below to complete your registration.\n\nThis link will expire in 24 hours.`,
        emailType: 'accountConfirmation',
        isNewAccount: true,
        baseUrl: window.location.origin,
      });

      const emailData = emailResult.data as any;

      // Try to parse if it's a string
      let parsedEmailData = emailData;
      if (typeof emailData === 'string') {
        try {
          parsedEmailData = JSON.parse(emailData);
          console.log('Parsed email data:', parsedEmailData);
        } catch (e) {
          console.log('Failed to parse email data as JSON:', e);
        }
      }

      if (!parsedEmailData?.success) {
        throw new Error(
          parsedEmailData?.error || 'Failed to send verification email'
        );
      }

      // Note: Invite status will be updated to 'accepted' by the postSignUpTrigger

      return {
        success: true,
        message:
          'Account created successfully. Please check your email to verify your account.',
      };
    }
  } catch (error) {
    console.error('Custom sign-up error:', error);

    let errorMessage = 'Failed to create account';
    if (typeof error === 'object' && error !== null && 'message' in error) {
      errorMessage = (error as { message: string }).message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};
