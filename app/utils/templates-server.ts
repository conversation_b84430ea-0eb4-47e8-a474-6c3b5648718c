import type { Schema } from '@/amplify/data/resource';
import { runWithAmplifyServerContext } from '@/app/utils/utils';
import { generateServerClientUsingCookies } from '@aws-amplify/adapter-nextjs/data';
import { getCurrentUser } from 'aws-amplify/auth/server';
import { cookies } from 'next/headers';
import outputs from '@/amplify_outputs.json';

type TemplateVersion = Schema['TemplateVersion']['type'];

/**
 * Server-side function to fetch a specific template and its latest version
 * @param templateId - The ID of the template to fetch
 * @returns Promise<{template: Template, latestVersion: TemplateVersion | undefined}>
 * @throws Error if template is not found or there's an issue fetching data
 */
export async function getTemplateServer(templateId: string) {
  try {
    const client = generateServerClientUsingCookies<Schema>({
      config: outputs,
      cookies,
    });

    const { data: template } = await client.models.Template.get({
      id: templateId,
    });

    if (!template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Get the latest version using reduce instead of sort
    const latestVersion =
      versions.length > 0
        ? versions.reduce((latest, current) =>
            current.versionNumber > latest.versionNumber ? current : latest
          )
        : undefined;

    return {
      template,
      latestVersion,
    };
  } catch (error) {
    console.error(`Error fetching template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Server-side function to fetch all versions for a specific template
 * @param templateId - The ID of the template to fetch versions for
 * @returns Promise<TemplateVersion[]> Array of all template versions
 * @throws Error if there's an issue fetching template versions
 */
export async function getTemplateVersionsServer(
  templateId: string
): Promise<TemplateVersion[]> {
  try {
    const client = generateServerClientUsingCookies<Schema>({
      config: outputs,
      cookies,
    });

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Sort versions by version number in descending order (newest first)
    return versions.sort((a, b) => b.versionNumber - a.versionNumber);
  } catch (error) {
    console.error(`Error fetching template versions for ${templateId}:`, error);
    throw new Error('Failed to fetch template versions');
  }
}

/**
 * Server-side function to fetch all templates with their latest versions
 * @returns Promise<TemplateWithVersion[]> Array of templates with their latest versions
 * @throws Error if there's an issue fetching templates
 */
export async function fetchTemplatesServer() {
  try {
    const client = generateServerClientUsingCookies<Schema>({
      config: outputs,
      cookies,
    });

    const { data: templatesData } = await client.models.Template.list();

    if (!templatesData) {
      return [];
    }

    // Fetch latest versions for all templates in parallel
    const templatesWithVersions = await Promise.all(
      templatesData.map(async template => {
        try {
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: {
              templateId: { eq: template.id },
            },
          });

          // Get the latest version
          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        } catch (error) {
          console.error(
            `Error fetching versions for template ${template.id}:`,
            error
          );
          return {
            ...template,
            latestVersion: undefined,
          };
        }
      })
    );

    return templatesWithVersions;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw new Error('Failed to fetch templates');
  }
}
