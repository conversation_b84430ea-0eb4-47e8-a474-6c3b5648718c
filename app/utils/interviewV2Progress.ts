'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';
import { jwtDecode } from 'jwt-decode';
import { v4 as uuidv4 } from 'uuid';

export type InterviewProgressV2Data = {
  status?: string | null;
  currentStep?: string | null;
  stepsData?: any;
  createdAt?: string | null;
  updatedAt?: string | null;
};

const client = generateClient<Schema>();

async function getCurrentUserRecord() {
  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  if (!token) {
    throw new Error('No access token found');
  }

  const decodedToken = jwtDecode(token) as any;

  const { data: users, errors } = await client.models.User.list({
    filter: { cognitoId: { eq: decodedToken.externalId } },
    selectionSet: [
      'id',
      'interviewProgressV2.status',
      'interviewProgressV2.currentStep',
      'interviewProgressV2.stepsData',
      'interviewProgressV2.createdAt',
      'interviewProgressV2.updatedAt',
      'cognitoId',
    ] as const,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  const user = users?.[0];
  if (!user) throw new Error('User not found for current session');
  return user as {
    id: string;
    interviewProgressV2?: InterviewProgressV2Data | null;
    cognitoId: string;
  };
}

// Function to check for and load linked account data
async function checkForLinkedAccountData(userId: string) {
  try {
    // Fetch linked accounts where isAccepted is true
    const { data: linkedAccounts, errors } =
      await client.models.LinkedAccountWithSharedFields.list({
        filter: {
          linkedUserId: { eq: userId },
          isAccepted: { eq: true },
        },
      });

    if (errors || !linkedAccounts || linkedAccounts.length === 0) {
      return null;
    }

    // Process the first accepted linked account
    const linkedAccount = linkedAccounts[0];

    // Parse shared fields
    let sharedFields;
    try {
      sharedFields = JSON.parse(linkedAccount.sharedFields);
    } catch (e) {
      console.error('Error parsing shared fields:', e);
      return null;
    }

    // Transform shared fields to stepsData structure
    return transformSharedFieldsToStepsData(sharedFields);
  } catch (error) {
    console.error('Error checking for linked account data:', error);
    return null;
  }
}

// Transform shared fields to stepsData structure
function transformSharedFieldsToStepsData(sharedFields: any[]) {
  if (
    !sharedFields ||
    !Array.isArray(sharedFields) ||
    sharedFields.length === 0
  ) {
    return null;
  }

  // Initialize stepsData structure
  const stepsData: any = {
    profile: {},
    emergency: {},
    afterYouDie: {},
    financial: {},
    medical: {},
    additional: {},
    people: [],
    completedSubsections: {},
  };

  // Process each shared field and map to the appropriate section in stepsData
  sharedFields
    .filter(field => field.documentType === 'Interview')
    .forEach(field => {
      const { id, answer } = field;

      // Skip fields without id or answer
      if (!id || answer === undefined) return;

      // Parse the path from the id (e.g., "profile.firstName" -> ["profile", "firstName"])
      const path = id.split('.');

      // Handle array paths like "people[0].firstName"
      if (path[0] === 'people' && path[1]?.includes('[')) {
        const indexMatch = path[1].match(/\[(\d+)\]/);
        if (indexMatch) {
          const index = parseInt(indexMatch[1]);
          const property = path[1].split('[')[0];

          // Ensure the array exists
          if (!stepsData.people) stepsData.people = [];

          // Ensure the object at the index exists
          if (!stepsData.people[index]) stepsData.people[index] = {};

          // Set the property
          if (path.length === 3) {
            stepsData.people[index][path[2]] = parseFieldValue(answer);
          } else {
            stepsData.people[index][property] = parseFieldValue(answer);
          }
        }
        return;
      }

      // Handle nested paths
      let current = stepsData;
      for (let i = 0; i < path.length - 1; i++) {
        let segment = path[i];

        // Handle array segments like "pets[2]"
        if (segment.includes('[')) {
          const [arrayName, indexStr] = segment.split('[');
          const index = parseInt(indexStr.replace(']', ''), 10);

          if (!current[arrayName]) current[arrayName] = [];
          if (!current[arrayName][index]) {
            current[arrayName][index] = { id: uuidv4() }; // assign unique id
          }

          current = current[arrayName][index];
        } else {
          if (!current[segment]) current[segment] = {};
          current = current[segment];
        }
      }

      // Set the final property
      const lastSegment = path[path.length - 1];
      current[lastSegment] = parseFieldValue(answer);
    });

  return stepsData;
}

// Parse field value to the appropriate type
function parseFieldValue(value: string) {
  if (value === 'true') return true;
  if (value === 'false') return false;
  if (value === '') return '';
  if (value === 'null') return null;

  // Try to parse as number
  const num = Number(value);
  if (!isNaN(num) && value.trim() !== '') return num;

  // Try to parse as JSON
  try {
    return JSON.parse(value);
  } catch {
    // Return as string if all else fails
    return value;
  }
}

export async function loadInterviewProgressV2(): Promise<InterviewProgressV2Data> {
  try {
    const user = await getCurrentUserRecord();
    const progress = (user.interviewProgressV2 ||
      {}) as InterviewProgressV2Data;

    // Parse stepsData if it is stored as a JSON string
    let stepsData: any = progress.stepsData;
    if (typeof stepsData === 'string') {
      try {
        stepsData = JSON.parse(stepsData as string);
      } catch {
        stepsData = {};
      }
    }

    // If no existing data or empty data, check for linked account data
    if (!stepsData || Object.keys(stepsData).length < 2) {
      const linkedAccountData = await checkForLinkedAccountData(user.cognitoId);
      if (linkedAccountData) {
        console.log('Using linked account data for interview prefill');
        stepsData = linkedAccountData;
      }
    }

    return {
      status: progress.status ?? 'not_started',
      currentStep: progress.currentStep ?? 'profile',
      stepsData: stepsData ?? {},
      createdAt: progress.createdAt ?? new Date().toISOString(),
      updatedAt: progress.updatedAt ?? new Date().toISOString(),
    };
  } catch (e) {
    console.error('Failed to load InterviewProgressV2:', e);
    return {
      status: 'not_started',
      currentStep: 'profile',
      stepsData: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }
}

export async function saveInterviewProgressV2(
  partial: Partial<InterviewProgressV2Data>,
  options?: { mergeStepsData?: boolean }
): Promise<InterviewProgressV2Data> {
  const { mergeStepsData = true } = options || {};
  const user = await getCurrentUserRecord();
  const existing = (user.interviewProgressV2 || {}) as InterviewProgressV2Data;

  // Ensure existing stepsData is an object
  const existingSteps = (() => {
    if (typeof existing.stepsData === 'string') {
      try {
        return JSON.parse(existing.stepsData);
      } catch {
        return {};
      }
    }
    return existing.stepsData || {};
  })();

  const incomingSteps = partial.stepsData || {};
  const mergedSteps = mergeStepsData
    ? { ...existingSteps, ...incomingSteps }
    : incomingSteps === undefined
      ? existingSteps
      : incomingSteps;

  const next: InterviewProgressV2Data = {
    status: partial.status ?? existing.status ?? 'in_progress',
    currentStep: partial.currentStep ?? existing.currentStep ?? 'profile',
    stepsData: mergedSteps,
    createdAt: existing.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const { data, errors } = await client.models.User.update({
    id: user.id,
    interviewProgressV2: {
      ...next,
      // Important: store JSON as string for a.json() fields
      stepsData: JSON.stringify(next.stepsData || {}),
    } as any,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  if (!data) throw new Error('User update returned no data');
  return next;
}

export async function resetInterviewProgressV2(): Promise<InterviewProgressV2Data> {
  const user = await getCurrentUserRecord();
  const reset: InterviewProgressV2Data = {
    status: 'not_started',
    currentStep: 'profile',
    stepsData: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  const { data, errors } = await client.models.User.update({
    id: user.id,
    interviewProgressV2: {
      ...reset,
      stepsData: JSON.stringify({}),
    } as any,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  if (!data) throw new Error('User update returned no data');
  return reset;
}
