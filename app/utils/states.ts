export const usaStates = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
  { value: 'DC', label: 'District of Columbia' },
  { value: 'AS', label: 'American Samoa' },
  { value: 'GU', label: 'Guam' },
  { value: 'MP', label: 'Northern Mariana Islands' },
  { value: 'PR', label: 'Puerto Rico' },
  { value: 'VI', label: 'U.S. Virgin Islands' },
];

/**
 * Convert state code to full state name
 * @param stateCode - Two-letter state code (e.g., 'CA')
 * @returns Full state name (e.g., 'California') or the original input if not found
 */
export const getStateNameFromCode = (stateCode: string): string => {
  const state = usaStates.find(
    s => s.value.toLowerCase() === stateCode.toLowerCase()
  );
  return state ? state.label : stateCode;
};

/**
 * Convert full state name to state code
 * @param stateName - Full state name (e.g., 'California')
 * @returns Two-letter state code (e.g., 'CA') or the original input if not found
 */
export const getStateCodeFromName = (stateName: string): string => {
  const state = usaStates.find(
    s => s.label.toLowerCase() === stateName.toLowerCase()
  );
  return state ? state.value : stateName;
};

/**
 * Normalize state input - handles both codes and names, returns full name
 * @param stateInput - Either state code or full name
 * @returns Full state name
 */
export const normalizeStateName = (stateInput: string): string => {
  if (!stateInput) return '';

  // First try to find by code
  const byCode = usaStates.find(
    s => s.value.toLowerCase() === stateInput.toLowerCase()
  );
  if (byCode) return byCode.label;

  // Then try to find by name
  const byName = usaStates.find(
    s => s.label.toLowerCase() === stateInput.toLowerCase()
  );
  if (byName) return byName.label;

  // Return original if not found
  return stateInput;
};

/**
 * Normalize state input - handles both codes and names, returns code
 * @param stateInput - Either state code or full name
 * @returns Two-letter state code
 */
export const normalizeStateCode = (stateInput: string): string => {
  if (!stateInput) return '';

  // First try to find by code
  const byCode = usaStates.find(
    s => s.value.toLowerCase() === stateInput.toLowerCase()
  );
  if (byCode) return byCode.value;

  // Then try to find by name
  const byName = usaStates.find(
    s => s.label.toLowerCase() === stateInput.toLowerCase()
  );
  if (byName) return byName.value;

  // Return original if not found
  return stateInput;
};
