import * as Sentry from '@sentry/nextjs';
import { getSecret } from '@/lib/utils/secrets';

export interface AddressValidationResult {
  isValid: boolean;
  issues: string[];
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  deliveryConfidence: 'CONFIRMED' | 'LIKELY' | 'UNLIKELY';
  correctedAddress?: {
    addressLine1: string;
    city: string;
    state: string;
    postalCode: string;
  };
}

export interface AddressToValidate {
  addressLine1: string;
  city: string;
  state: string;
  postalCode: string;
}

export async function validateAddress(
  address: AddressToValidate
): Promise<AddressValidationResult> {
  console.log('🚀 validateAddress called with:', address);

  if (
    !address.addressLine1 ||
    !address.city ||
    !address.state ||
    !address.postalCode
  ) {
    const issues = [];
    if (!address.addressLine1) issues.push('Street address is required');
    if (!address.city) issues.push('City is required');
    if (!address.state) issues.push('State is required');
    if (!address.postalCode) issues.push('ZIP code is required');

    console.log('❌ Validation failed - missing fields:', issues);
    return {
      isValid: false,
      issues,
      confidence: 'LOW',
      deliveryConfidence: 'UNLIKELY',
    };
  }

  try {
    let apiKey: string;
    try {
      apiKey = await getSecret('GOOGLE_API_KEY');
      console.log('🔑 Got API key from secret');
    } catch (error) {
      Sentry.captureException(error);
      apiKey = process.env.NEXT_PUBLIC_GOOGLE_API_KEY || '';
      console.log('🔑 Using fallback API key:', !!apiKey);
    }

    if (!apiKey) {
      console.log('❌ No API key available');
      throw new Error('Google API key not available');
    }

    const requestBody = {
      address: {
        regionCode: 'US',
        languageCode: 'en',
        postalCode: address.postalCode,
        administrativeArea: address.state,
        locality: address.city,
        addressLines: [address.addressLine1],
      },
      enableUspsCass: true,
    };

    console.log('📤 Sending validation request:', requestBody);

    const response = await fetch(
      `https://addressvalidation.googleapis.com/v1:validateAddress?key=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      }
    );

    console.log('📥 API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API error response:', errorText);
      throw new Error(`Address validation failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('📋 API response data:', data);

    const result = data.result;
    const verdict = result?.verdict;
    const addressComponents = result?.address?.addressComponents || [];
    const uspsData = result?.uspsData;

    console.log('🔍 Full result structure:', JSON.stringify(result, null, 2));
    console.log('🔍 Verdict:', verdict);
    console.log('🏢 USPS Data:', uspsData);

    const issues: string[] = [];
    let confidence: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';

    console.log('🔍 Checking address components confirmation levels:');

    addressComponents.forEach((component: any) => {
      const componentType = component.componentType || '';
      const componentName = component.componentName?.text || '';
      const confirmationLevel = component.confirmationLevel || '';

      console.log(
        `� ${componentType}: "${componentName}" - ${confirmationLevel}`
      );

      if (
        confirmationLevel === 'UNCONFIRMED_BUT_PLAUSIBLE' ||
        confirmationLevel === 'UNCONFIRMED_AND_SUSPICIOUS'
      ) {
        issues.push(
          `${componentType.replace(/_/g, ' ')} "${componentName}" unconfirmed`
        );
        confidence = 'LOW';
      } else if (confirmationLevel === 'CONFIRMED_BUT_EDITED') {
        issues.push(
          `${componentType.replace(/_/g, ' ')} corrected to "${componentName}"`
        );
        if (confidence === 'HIGH') confidence = 'MEDIUM';
      }
    });

    // Extract corrected address if available
    let correctedAddress: AddressValidationResult['correctedAddress'];
    if (addressComponents.length > 0) {
      const corrected = {
        addressLine1: '',
        city: '',
        state: '',
        postalCode: '',
      };

      console.log(
        '🔧 Processing address components for corrections:',
        addressComponents
      );

      addressComponents.forEach((component: any) => {
        const types = component.componentType || '';
        const name = component.componentName?.text || '';
        const confirmationLevel = component.confirmationLevel || '';

        console.log(
          `📍 Component: ${types} = "${name}" (confidence: ${confirmationLevel})`
        );

        if (types === 'route' || types === 'street_number') {
          if (types === 'street_number') {
            corrected.addressLine1 = name + ' ' + corrected.addressLine1;
          } else {
            corrected.addressLine1 += name;
          }
        } else if (types === 'locality') {
          corrected.city = name;
        } else if (types === 'administrative_area_level_1') {
          corrected.state = name;
        } else if (types === 'postal_code') {
          corrected.postalCode = name;
        }
      });

      // Clean up the address line
      corrected.addressLine1 = corrected.addressLine1.trim();

      if (
        corrected.addressLine1 ||
        corrected.city ||
        corrected.state ||
        corrected.postalCode
      ) {
        correctedAddress = corrected;
        console.log('Corrected address extracted:', correctedAddress);

        // Add specific correction details
        if (corrected.addressLine1 !== address.addressLine1) {
          issues.push(
            `🔧 Street address corrected to: "${corrected.addressLine1}"`
          );
        }
        if (corrected.city !== address.city) {
          issues.push(`🔧 City corrected to: "${corrected.city}"`);
        }
        if (corrected.state !== address.state) {
          issues.push(`🔧 State corrected to: "${corrected.state}"`);
        }
        if (corrected.postalCode !== address.postalCode) {
          issues.push(`🔧 ZIP code corrected to: "${corrected.postalCode}"`);
        }
      }
    }

    // Simple validation: address is valid if all components are CONFIRMED
    const allComponentsConfirmed = addressComponents.every(
      (component: any) => component.confirmationLevel === 'CONFIRMED'
    );

    // Check if we have all required components (street, city, state, zip)
    const hasRequiredComponents =
      addressComponents.some((c: any) => c.componentType === 'route') &&
      addressComponents.some((c: any) => c.componentType === 'locality') &&
      addressComponents.some(
        (c: any) => c.componentType === 'administrative_area_level_1'
      ) &&
      addressComponents.some((c: any) => c.componentType === 'postal_code');

    const deliveryConfidence: AddressValidationResult['deliveryConfidence'] =
      allComponentsConfirmed && hasRequiredComponents
        ? 'CONFIRMED'
        : hasRequiredComponents
          ? 'LIKELY'
          : 'UNLIKELY';

    // Simple validation logic - address is valid if has required components and all are confirmed
    const isValid = hasRequiredComponents && allComponentsConfirmed;

    console.log(`🔍 Simple validation result:`);
    console.log('  - Has required components:', hasRequiredComponents);
    console.log('  - All components confirmed:', allComponentsConfirmed);
    console.log('  - Final result:', isValid ? 'VALID' : 'INVALID');

    if (!isValid) {
      if (!hasRequiredComponents) {
        issues.push('Missing required address components');
      }
      if (!allComponentsConfirmed) {
        issues.push('Some components unconfirmed');
      }
    }

    const validationResult: AddressValidationResult = {
      isValid,
      issues,
      confidence,
      deliveryConfidence,
      correctedAddress,
    };

    console.log('Final validation result:', validationResult);

    return validationResult;
  } catch (error) {
    Sentry.captureException(error);
    console.error('❌ Address validation error:', error);
    return {
      isValid: false,
      issues: ['Address validation failed. Please check your address.'],
      confidence: 'LOW',
      deliveryConfidence: 'UNLIKELY',
    };
  }
}

// Helper function to check if address needs validation
export function shouldValidateAddress(
  address: Partial<AddressToValidate>
): boolean {
  return !!(
    address.addressLine1 &&
    address.city &&
    address.state &&
    address.postalCode
  );
}

// Helper function to format validation issues for display
export function formatValidationIssues(
  result: AddressValidationResult
): string {
  if (result.isValid) {
    let message = 'Address is valid';

    // Add delivery confidence info
    switch (result.deliveryConfidence) {
      case 'CONFIRMED':
        message += ' and confirmed deliverable';
        break;
      case 'LIKELY':
        message += ' and likely deliverable';
        break;
      case 'UNLIKELY':
        message += ' but delivery is unlikely';
        break;
    }

    if (result.confidence !== 'HIGH') {
      message += ` (Confidence: ${result.confidence})`;
    }

    return message;
  }

  let message = `❌ Address validation failed:\n`;
  message += `Confidence: ${result.confidence} | Delivery: ${result.deliveryConfidence}\n\n`;
  message += result.issues.map(issue => `${issue}`).join('\n');

  if (result.correctedAddress) {
    message += '\n\n📝 Suggested corrections:';
    message += `\n${result.correctedAddress.addressLine1}`;
    message += `\n${result.correctedAddress.city}, ${result.correctedAddress.state} ${result.correctedAddress.postalCode}`;
  }

  return message;
}
