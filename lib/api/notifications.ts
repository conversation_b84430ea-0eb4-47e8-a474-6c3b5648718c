import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchAuthSession } from 'aws-amplify/auth';
import { jwtDecode } from 'jwt-decode';

const client = generateClient<Schema>();

export interface NotificationData {
  id: string;
  message: string;
  recipient: string;
  author: string;
  createdAt: string;
  isRead: boolean;
}

export async function createNotification(
  message: string,
  recipient: string,
  author?: string
): Promise<NotificationData> {
  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  if (!token) {
    throw new Error('No access token found');
  }

  const decodedToken = jwtDecode(token) as any;

  const { data, errors } = await client.models.Notification.create({
    message,
    recipient,
    author: author || decodedToken.externalId,
    createdAt: new Date().toISOString(),
    isRead: false,
  });

  if (errors) throw new Error(errors[0].message);
  return data as NotificationData;
}

export async function getNotifications(
  recipient: string
): Promise<NotificationData[]> {
  console.log('API: Getting notifications for recipient:', recipient);

  try {
    // Use the recipient secondary index for efficient filtering
    const { data, errors } =
      await client.models.Notification.listNotificationByRecipient({
        recipient,
      });

    console.log('API: Notification query result:', {
      dataCount: data?.length,
      errors,
    });

    if (errors) throw new Error(errors[0].message);

    // Sort by createdAt descending (newest first)
    const sortedNotifications = (data as NotificationData[]).sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return sortedNotifications;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
}

export async function markAsRead(id: string): Promise<void> {
  const { errors } = await client.models.Notification.update({
    id,
    isRead: true,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function markAllAsRead(): Promise<void> {
  console.log('API: Marking all notifications as read for current user');

  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  if (!token) {
    throw new Error('No access token found');
  }

  const decodedToken = jwtDecode(token) as any;

  const notifications = await getNotifications(decodedToken.externalId);

  // Only update unread notifications
  const unreadNotifications = notifications.filter(n => !n.isRead);

  console.log(
    `API: Marking ${unreadNotifications.length} notifications as read`
  );

  for (const notification of unreadNotifications) {
    await markAsRead(notification.id);
  }
}

export async function deleteNotification(id: string): Promise<void> {
  console.log('API: Deleting notification:', id);
  const { errors } = await client.models.Notification.delete({
    id,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function deleteAllNotifications(): Promise<void> {
  console.log('API: Deleting all notifications for current user');

  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  if (!token) {
    throw new Error('No access token found');
  }

  const decodedToken = jwtDecode(token) as any;

  const notifications = await getNotifications(decodedToken.externalId);

  console.log(`API: Deleting ${notifications.length} notifications`);

  for (const notification of notifications) {
    await deleteNotification(notification.id);
  }
}

export function subscribeToNotifications(
  recipient: string,
  onNotification: (notification: NotificationData) => void
) {
  console.log('Setting up subscription for recipient:', recipient);

  // Subscribe to all notifications and filter client-side to avoid GraphQL filter issues
  return client.models.Notification.onCreate().subscribe({
    next: data => {
      if (data && data.recipient === recipient) {
        console.log('Subscription: Received notification for recipient:', data);
        onNotification(data as NotificationData);
      }
    },
    error: error => console.error('Subscription error:', error),
  });
}

/**
 * Send email notification to user
 */
export async function sendEmailNotification(
  recipientEmail: string,
  subject: string,
  message: string
): Promise<void> {
  try {
    console.log('Sending email notification:', { recipientEmail, subject });

    const result = await client.mutations.sendEmail({
      to: recipientEmail,
      subject: subject,
      message: message,
      emailType: 'notification',
    });

    if (!result.data) {
      throw new Error('Failed to send email notification');
    }

    console.log('Email notification sent successfully');
  } catch (error) {
    console.error('Error sending email notification:', error);
    throw error;
  }
}

/**
 * Create notification and optionally send email
 */
export async function createNotificationWithEmail(
  message: string,
  recipient: string,
  recipientEmail?: string,
  emailSubject?: string,
  author?: string
): Promise<NotificationData> {
  try {
    // Create in-app notification
    const notification = await createNotification(message, recipient, author);

    // Send email notification if email is provided
    if (recipientEmail && emailSubject) {
      try {
        await sendEmailNotification(recipientEmail, emailSubject, message);
      } catch (emailError) {
        console.error(
          'Failed to send email notification, but in-app notification was created:',
          emailError
        );
        // Don't throw error here - in-app notification was successful
      }
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification with email:', error);
    throw error;
  }
}

/**
 * Find all users who have documents based on a specific template
 */
export async function getUsersWithDocumentsFromTemplate(
  templateId: string
): Promise<
  Array<{
    userId: string;
    cognitoId: string;
    email: string;
    firstName: string;
    lastName: string;
  }>
> {
  try {
    console.log(`Finding users with documents from template: ${templateId}`);

    // Use the templateId secondary index for efficient filtering
    const { data: documents, errors } =
      await client.models.Document.listDocumentByTemplateId({
        templateId,
      });

    if (errors) {
      console.error('Error fetching documents by template:', errors);
      throw new Error('Failed to fetch documents by template');
    }

    if (!documents || documents.length === 0) {
      console.log(`No documents found for template: ${templateId}`);
      return [];
    }

    // Get unique user IDs from documents
    const uniqueUserIds = [...new Set(documents.map(doc => doc.userId))];
    console.log(
      `Found ${uniqueUserIds.length} unique users with documents from template`
    );

    // Fetch user details for all users with better error handling
    console.log(`Fetching details for ${uniqueUserIds.length} users...`);

    const userPromises = uniqueUserIds.map(async (userId, index) => {
      try {
        console.log(
          `Fetching user ${index + 1}/${uniqueUserIds.length}: ${userId}`
        );

        const { data: user, errors: userErrors } = await client.models.User.get(
          {
            id: userId,
          }
        );

        if (userErrors && userErrors.length > 0) {
          console.error(`GraphQL errors fetching user ${userId}:`, userErrors);
          return null;
        }

        if (!user) {
          console.warn(`User ${userId} not found in database`);
          return null;
        }

        // Only return users who have cognitoId (are registered)
        if (!user.cognitoId) {
          console.log(
            `Skipping user ${userId} - no cognitoId (not registered)`
          );
          return null;
        }

        // Validate required fields
        if (!user.email || !user.firstName || !user.lastName) {
          console.warn(`User ${userId} missing required fields:`, {
            email: !!user.email,
            firstName: !!user.firstName,
            lastName: !!user.lastName,
          });
          return null;
        }

        console.log(
          `✓ User ${userId} (${user.email}) is valid for notifications`
        );

        return {
          userId: user.id,
          cognitoId: user.cognitoId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        };
      } catch (error) {
        console.error(`Exception fetching user ${userId}:`, error);
        return null;
      }
    });

    const users = await Promise.all(userPromises);
    const validUsers = users.filter(user => user !== null);

    console.log(`User validation results:`);
    console.log(`- Total unique users from documents: ${uniqueUserIds.length}`);
    console.log(`- Valid registered users: ${validUsers.length}`);
    console.log(`- Skipped users: ${uniqueUserIds.length - validUsers.length}`);

    console.log(
      `Found ${validUsers.length} valid registered users with documents from template`
    );
    return validUsers;
  } catch (error) {
    console.error('Error finding users with documents from template:', error);
    throw error;
  }
}

/**
 * Send template update notifications to all users who have documents based on the template
 */
export async function notifyUsersAboutTemplateUpdate(
  templateId: string,
  templateName: string,
  templateType: string
): Promise<{ successCount: number; errorCount: number; errors: string[] }> {
  try {
    console.log(
      `Starting template update notifications for template: ${templateId} (${templateName})`
    );

    // Find all users who have documents from this template
    const users = await getUsersWithDocumentsFromTemplate(templateId);

    if (users.length === 0) {
      console.log('No users found with documents from this template');
      return { successCount: 0, errorCount: 0, errors: [] };
    }

    console.log(
      `Found ${users.length} users to notify:`,
      users.map(u => u.email)
    );

    const message = `The template "${templateName}" (${templateType}) that was used to create your documents has been updated. Please review your documents to see if any updates are needed.`;
    const emailSubject = 'Template Update Notification - ChildFree Trust®';

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Send notifications to all users in parallel
    const notificationPromises = users.map(async user => {
      try {
        await createNotificationWithEmail(
          message,
          user.cognitoId,
          user.email,
          emailSubject,
          'system-template-update'
        );
        console.log(`Notification sent successfully to user: ${user.email}`);
        return { success: true, user: user.email };
      } catch (error) {
        const errorMessage = `Failed to send notification to ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    });

    const results = await Promise.all(notificationPromises);

    // Count successes and errors
    results.forEach(result => {
      if (result.success) {
        successCount++;
      } else {
        errorCount++;
        errors.push(result.error ?? 'Unknown error occurred');
      }
    });

    console.log(
      `Template update notifications completed: ${successCount} success, ${errorCount} errors`
    );

    return { successCount, errorCount, errors };
  } catch (error) {
    console.error('Error sending template update notifications:', error);
    throw error;
  }
}
