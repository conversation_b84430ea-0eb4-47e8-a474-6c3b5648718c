import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';

import type { DMSFailedUser } from '@/app/(protected)/admin/emergency/page';

const client = generateClient<Schema>();

export const adminUsersAPI = {
  async inviteList() {
    try {
      // Fetch only required fields for invites
      const { data: invites, errors } = await client.models.UserInvite.list({
        selectionSet: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'email',
          'role',
          'subrole',
          'status',
          'createdAt',
          'expiresAt',
          'invitedBy',
        ] as const,
      });

      if (errors) {
        throw new Error(errors[0].message);
      }
      if (!invites) {
        return [];
      }

      return invites;
    } catch (error) {
      console.error('Error fetching invites:', error);
      throw error;
    }
  },

  async fetchDMSFailedUsers(): Promise<DMSFailedUser[]> {
    try {
      // Fetch users with failed DMS checks - only required fields
      const { data: users, errors } = await client.models.User.list({
        filter: {
          isDmsCheckSuccessful: {
            eq: false,
          },
        },
        selectionSet: [
          'id',
          'firstName',
          'lastName',
          'email',
          'status',
          'isDmsCheckSuccessful',
          'createdAt',
          'role',
          'cognitoId',
        ] as const,
      });

      if (errors) {
        console.error('Error fetching DMS failed users:', errors);
        throw Error('Failed to fetch DMS failed users');
      }

      if (!users || users.length === 0) {
        return [];
      }

      // Fetch DeadMansSwitch data for each user
      const usersWithDMSData = await Promise.all(
        users.map(async user => {
          let lastCheckIn: string | null = null;
          let nextCheckIn: string | null = null;

          if (user.email) {
            try {
              // Fetch DeadMansSwitch data for this user - only required fields
              const { data: dmsData } = await client.models.DeadMansSwitch.list(
                {
                  filter: {
                    userId: { eq: user.email },
                  },
                  selectionSet: ['lastCheckIn', 'nextCheckIn'] as const,
                }
              );

              if (dmsData && dmsData.length > 0) {
                lastCheckIn = dmsData[0].lastCheckIn;
                nextCheckIn = dmsData[0].nextCheckIn;
              }
            } catch (dmsError) {
              console.warn(
                `Failed to fetch DMS data for user ${user.id}:`,
                dmsError
              );
              // Continue without DMS data rather than failing the entire request
            }
          }

          return {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            status: user.status || 'pending',
            isDmsCheckSuccessful: user.isDmsCheckSuccessful || false,
            createdAt: user.createdAt,
            role: user.role || 'Member',
            lastCheckIn,
            nextCheckIn,
            cognitoId: user.cognitoId,
          };
        })
      );

      return usersWithDMSData;
    } catch (error) {
      console.error('Error fetching DMS failed users:', error);
      throw new Error('Failed to fetch DMS failed users');
    }
  },

  async resetDMSCheckIn(userId: string, email: string): Promise<boolean> {
    try {
      const now = new Date();

      // First, get the user's DMS configuration to calculate next check-in
      const { data: dmsData } = await client.models.DeadMansSwitch.list({
        filter: {
          userId: { eq: email },
        },
        selectionSet: ['id', 'frequency', 'customFrequencyDays'] as const,
      });

      if (!dmsData || dmsData.length === 0) {
        throw new Error('No DMS configuration found for user');
      }

      const dmsConfig = dmsData[0];

      // Calculate next check-in date based on frequency
      const calculateNextCheckInDate = (
        frequency?: string,
        customDays?: number | null,
        fromDate: Date = new Date()
      ): Date => {
        const freq = frequency?.toUpperCase();
        const MS_PER_DAY = 24 * 60 * 60 * 1000;
        const FREQUENCY_TO_DAYS: Record<string, number> = {
          WEEKLY: 7,
          BIWEEKLY: 14,
          MONTHLY: 30,
        };

        if (freq === 'CUSTOM' && customDays && customDays > 0) {
          return new Date(fromDate.getTime() + customDays * MS_PER_DAY);
        }

        if (freq === 'MONTHLY') {
          const nextMonth = new Date(fromDate);
          nextMonth.setMonth(nextMonth.getMonth() + 1);
          return nextMonth;
        }

        const days = FREQUENCY_TO_DAYS[freq ?? ''] ?? 7;
        return new Date(fromDate.getTime() + days * MS_PER_DAY);
      };

      const nextCheckInDate = calculateNextCheckInDate(
        dmsConfig.frequency ?? undefined,
        dmsConfig.customFrequencyDays ?? undefined,
        now
      );

      // Update the DeadMansSwitch record
      const dmsUpdateResult = await client.models.DeadMansSwitch.update({
        id: dmsConfig.id,
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
        status: 'ACTIVE',
      });

      if (!dmsUpdateResult.data) {
        throw new Error('Failed to update DMS configuration');
      }

      // First, get the user's current status and Welon Trust assignment
      const { data: currentUserData } = await client.models.User.list({
        filter: { id: { eq: userId } },
        selectionSet: [
          'id',
          'cognitoId',
          'firstName',
          'lastName',
          'email',
          'assignedWelonTrustId',
          'isDmsCheckSuccessful',
        ] as const,
      });

      if (!currentUserData || currentUserData.length === 0) {
        throw new Error('User not found');
      }

      const currentUser = currentUserData[0];
      const previousDmsStatus = currentUser.isDmsCheckSuccessful;

      // Update the User's isDmsCheckSuccessful field
      const userUpdateResult = await client.models.User.update({
        id: userId,
        isDmsCheckSuccessful: true,
      });

      if (!userUpdateResult.data) {
        throw new Error('Failed to update user DMS status');
      }

      // Create notification for Welon Trust user if status changed from false to true
      if (previousDmsStatus === false && currentUser.assignedWelonTrustId) {
        try {
          const message = `${currentUser.firstName} ${currentUser.lastName} has successfully completed their check-in and their Dead Man's Switch is now active.`;

          await client.models.Notification.create({
            message,
            recipient: currentUser.assignedWelonTrustId,
            author: 'system-dms-recovery',
            createdAt: new Date().toISOString(),
            isRead: false,
          });

          console.log(
            `Created recovery notification for Welon Trust user ${currentUser.assignedWelonTrustId} about user ${currentUser.cognitoId}`
          );
        } catch (notificationError) {
          console.error(
            'Failed to create Welon Trust recovery notification:',
            notificationError
          );
          // Don't fail the entire operation if notification fails
        }
      }

      return true;
    } catch (error) {
      console.error('Error resetting DMS check-in:', error);
      throw new Error('Failed to reset DMS check-in');
    }
  },
};
