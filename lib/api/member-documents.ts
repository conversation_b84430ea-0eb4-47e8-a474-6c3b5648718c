'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

// TODO: Remove this after tests and get from user profile
const DEFAULT_TEST_STATE = 'California';

// FUNCTION RETURN ALL AVAILABLE FOR MEMBER TEMPLATES
export const getMemberAvailableTemplates = async (userState?: string) => {
  try {
    const targetState = userState || DEFAULT_TEST_STATE;

    // Get all active templates and filter them client-side for better control
    let { data: allActiveTemplates } = await client.models.Template.list({
      filter: {
        isActive: { eq: true },
        isDraft: { eq: false },
      },
    });

    // Group templates by type and apply priority logic
    const templatesByType = new Map<string, any[]>();

    // First, group all templates by type
    allActiveTemplates?.forEach(template => {
      const templateStates = template.templateStates || [];
      const hasUserState = templateStates.includes(targetState);
      const hasGeneral =
        templateStates.includes('General') || template.isGeneral === true;

      // Only include templates that match user's state OR are general
      if (hasUserState || hasGeneral) {
        if (!templatesByType.has(template.type)) {
          templatesByType.set(template.type, []);
        }
        templatesByType.get(template.type)!.push({
          ...template,
          hasUserState,
          hasGeneral,
        });
      }
    });

    // For each type, select the best template (user's state > general)
    const templates: any[] = [];
    templatesByType.forEach(templatesOfType => {
      // First try to find template for user's state
      const userStateTemplate = templatesOfType.find(t => t.hasUserState);
      if (userStateTemplate) {
        templates.push(userStateTemplate);
      } else {
        // If no user state template, use general
        const generalTemplate = templatesOfType.find(t => t.hasGeneral);
        if (generalTemplate) {
          templates.push(generalTemplate);
        }
      }
    });

    // Sort templates: user's state first, then General (though we already selected the best ones)
    const sortedTemplates = templates.sort((a, b) => {
      // Priority: user's state > general
      if (a.hasUserState && !b.hasUserState) return -1;
      if (!a.hasUserState && b.hasUserState) return 1;

      // Both have same priority, sort by type for consistency
      return a.type.localeCompare(b.type);
    });

    if (sortedTemplates.length === 0) {
      return [];
    }

    // Fetch latest versions for all templates in parallel
    const templatesWithVersions = await Promise.all(
      sortedTemplates.map(async template => {
        try {
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: {
              templateId: { eq: template.id },
            },
          });

          // Get the latest version
          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        } catch (error) {
          console.error(
            `Error fetching versions for template ${template.id}:`,
            error
          );
          return {
            ...template,
            latestVersion: undefined,
          };
        }
      })
    );

    return templatesWithVersions;
  } catch (error) {
    console.error('Error getting template:', error);
    return [];
  }
};

// FUNCTION RETURN ALL FORMATTED INTERVIEW ANSWERS FOR MEMBER
export async function getMemberInterviewAnswers() {
  try {
    const currentUser = await getCurrentUser();

    // Fetch user data by Cognito ID
    const { data: users } = await client.models.User.list({
      filter: {
        cognitoId: {
          eq: currentUser.userId,
        },
      },
      selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
    });

    if (!users || users.length === 0) {
      return [];
    }

    const user = users[0];

    // Find the LATEST completed interview (not the first one)
    const completedInterviews =
      user.interviewProgress?.filter(
        p => p !== null && p.isCompleted === true
      ) || [];

    // Get the most recent completed interview by completedAt date
    const latestInterviewAnswers =
      completedInterviews.length > 0
        ? completedInterviews.reduce((latest, current) => {
            if (!latest) return current;
            if (!current) return latest;

            // Compare completedAt dates - if no completedAt, use startedAt
            const latestDate = latest.completedAt || latest.startedAt;
            const currentDate = current.completedAt || current.startedAt;

            if (!latestDate && !currentDate) {
              // If both have no dates, prefer the one with more answers (more recent)
              const latestAnswerCount = latest.answers?.length || 0;
              const currentAnswerCount = current.answers?.length || 0;
              return currentAnswerCount >= latestAnswerCount ? current : latest;
            }

            if (!latestDate) return current;
            if (!currentDate) return latest;

            const latestTime = new Date(latestDate).getTime();
            const currentTime = new Date(currentDate).getTime();

            // If dates are the same, prefer the one with more answers
            if (latestTime === currentTime) {
              const latestAnswerCount = latest.answers?.length || 0;
              const currentAnswerCount = current.answers?.length || 0;
              return currentAnswerCount >= latestAnswerCount ? current : latest;
            }

            return currentTime > latestTime ? current : latest;
          })
        : null;

    if (!latestInterviewAnswers) {
      return [];
    } else {
      return [];
    }
  } catch (error) {
    console.error('Error fetching user answers:', error);
    return [];
  }
}
