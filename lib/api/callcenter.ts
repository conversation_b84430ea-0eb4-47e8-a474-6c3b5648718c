import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import * as Sentry from '@sentry/nextjs';

const client = generateClient<Schema>();

export type GetUsersListResponse = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  birthdate?: string | null;
  phoneNumber?: string | null;
  // addresses: {
  //   addressLine1: string;
  //   addressLine2: string;
  //   city: string;
  //   stateProvinceCode: string;
  //   postalCode: string;
  //   countryCode: string;
  // }[];
}[];

export const callCenterAPI = {
  async getUsersList(): Promise<GetUsersListResponse> {
    try {
      //fetch only member users
      const { data: users, errors } = await client.models.User.list({
        selectionSet: [
          'id',
          'firstName',
          'lastName',
          'email',
          'birthdate',
          'phoneNumber',
        ] as const,
        filter: {
          role: {
            eq: 'Member',
          },
        },
      });

      if (errors) {
        console.error('Error fetching DMS failed users:', errors);
        throw Error('Failed to fetch DMS failed users');
      }

      if (!users || users.length === 0) {
        return [];
      }

      return users;
    } catch (error) {
      console.error('Error fetching users:', error);
      Sentry.captureException(error);
      throw error;
    }
  },
};
