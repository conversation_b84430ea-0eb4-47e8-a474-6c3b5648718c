'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';
import { uploadData } from 'aws-amplify/storage';
import { cleanTipTapHTML } from '../utils/html-cleaner';
import {
  fetchUserByCognitoId,
  fetchUserById,
  getAssignedToUserWelonTrustCognitoId,
} from '@/lib/data/users';
import {
  createNotification,
  createNotificationWithEmail,
} from '@/lib/api/notifications';
import {
  finalizeDocuments,
  createDocumentFromSpecificTemplate,
} from '../utils/document-create/finalize-documents';
import { buildFullHtml } from '@/lib/utils/pdf-generation';
import { jwtDecode } from 'jwt-decode';
import * as Sentry from '@sentry/nextjs';
import { SectionDataMap } from '@/context/CareDocumentsContext';
import { CareDocumentSectionType } from '@/hooks/useCareDocumentsNew';

// Generate the Amplify data client
const client = generateClient<Schema>({});

// Use auto-generated Document type from Amplify schema
type DocumentType = Schema['Document']['type'];

// Types for document creation - based on the schema but with optional fields for creation
export interface CreateDocumentData {
  title: string;
  type: DocumentType['type'];
  status?: DocumentType['status'];
  version: string;
  content: string; // Populated template content
  fileUrl?: string;
  trackingNumber?: string;
  signatureType?: DocumentType['signatureType'];
  executionDate?: string;
  templateId?: string;
  templateVersion?: number; // Version of template used when document was created
  templateContent?: string;
  documentState?: string;
  assignedWelonTrustId?: string;
}

export interface DocumentResponse {
  id: string;
  title: string;
  type: string;
  status: string;
  dateCreated: string;
  lastModified?: string;
  version: string;
  userId: string;
  content: string;
  fileUrl?: string;
  trackingNumber?: string;
  signatureType?: string;
  executionDate?: string;
  createdAt?: string;
  updatedAt?: string;
  rejectionReason?: string;
  templateId?: string;
  templateVersion?: number;
  documentState?: string;
  // New fields for electronic signing
  signedDocumentUrl?: string;
  signedAt?: string;
  documentHash?: string;
}

export interface DocumentDuplicateCheckResult {
  hasDuplicate: boolean;
  existingDocument?: DocumentResponse;
  suggestedVersion: string;
}

/**
 * Calculate the next version based on existing documents
 */
function calculateNextVersion(existingDocuments: any[]): string {
  const versions = existingDocuments
    .map(doc => doc.version || '1.0')
    .map(version => {
      const parts = version.split('.');
      return parseFloat(parts[0] + '.' + (parts[1] || '0'));
    })
    .sort((a, b) => b - a);

  const highestVersion = versions[0] || 1.0;
  const nextVersion = Math.floor(highestVersion) + 1;
  return `${nextVersion}.0`;
}

/**
 * Check if a document of the same type already exists with active status
 * Active status means not archived, signed, or shipped
 */
export async function checkDocumentDuplicate(
  documentType: string,
  cognitoUserId: string
): Promise<DocumentDuplicateCheckResult> {
  try {
    // Convert Cognito ID to internal user ID
    const internalUser = await fetchUserByCognitoId(cognitoUserId);
    if (!internalUser) {
      throw new Error('User not found');
    }

    // Get all documents for this user, then filter by type
    const { data: allDocuments, errors } =
      await client.models.Document.listDocumentByUserId({
        userId: internalUser.id,
      });

    if (errors) {
      console.error('Error checking for duplicate documents:', errors);
      throw new Error('Failed to check for existing documents');
    }

    // Filter by document type
    const existingDocuments =
      allDocuments?.filter(doc => doc.type === documentType) || [];

    if (!existingDocuments || existingDocuments.length === 0) {
      return {
        hasDuplicate: false,
        suggestedVersion: '1.0',
      };
    }

    // Check if any existing document has active status (not archived, signed, or shipped)
    const activeStatuses = [
      'draft',
      'sent',
      'approved',
      'rejected',
      'ready_for_review',
      'under_review',
      'ready_for_signing',
    ];
    const activeDocument = existingDocuments.find(doc =>
      activeStatuses.includes((doc.status as string) || '')
    );

    if (activeDocument) {
      return {
        hasDuplicate: true,
        existingDocument: {
          id: activeDocument.id || '',
          title: activeDocument.title || '',
          type: (activeDocument.type as string) || '',
          status: (activeDocument.status as string) || '',
          dateCreated: activeDocument.dateCreated || '',
          lastModified: activeDocument.lastModified || undefined,
          version: activeDocument.version || '1.0',
          userId: activeDocument.userId || '',
          content: activeDocument.content || '',
          fileUrl: activeDocument.fileUrl || undefined,
          trackingNumber: activeDocument.trackingNumber || undefined,
          signatureType: (activeDocument.signatureType as string) || undefined,
          executionDate: activeDocument.executionDate || undefined,
          createdAt: activeDocument.createdAt || undefined,
          updatedAt: activeDocument.updatedAt || undefined,
          rejectionReason: activeDocument.rejectionReason || undefined,
          templateId: activeDocument.templateId || undefined,
          documentState: activeDocument.documentState || undefined,
          signedDocumentUrl: activeDocument.signedDocumentUrl || undefined,
          signedAt: activeDocument.signedAt || undefined,
          documentHash: activeDocument.documentHash || undefined,
        },
        suggestedVersion: calculateNextVersion(existingDocuments),
      };
    }

    // If no active documents, calculate next version based on all existing documents
    return {
      hasDuplicate: false,
      suggestedVersion: calculateNextVersion(existingDocuments),
    };
  } catch (error) {
    console.error('Error in checkDocumentDuplicate:', error);
    throw error;
  }
}

/**
 * Create a new document in the database with automatic version management and duplicate validation
 */
export async function createDocumentWithValidation(
  documentData: CreateDocumentData,
  userId: string | null,
  allowDuplicate: boolean = false
): Promise<DocumentResponse> {
  try {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Check for duplicate documents
    const duplicateCheck = await checkDocumentDuplicate(
      documentData.type as string,
      userId
    );

    if (duplicateCheck.hasDuplicate && !allowDuplicate) {
      const error = new Error('DUPLICATE_DOCUMENT_EXISTS') as any;
      error.duplicateCheck = duplicateCheck;
      throw error;
    }

    return await createDocument(documentData, userId);
  } catch (error) {
    throw error;
  }
}

/**
 * Create a new document in the database with automatic version management
 */
export async function createDocument(
  documentData: CreateDocumentData,
  userId: string | null
): Promise<DocumentResponse> {
  try {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const assignedWelonTrustId =
      await getAssignedToUserWelonTrustCognitoId(userId);

    const now = new Date().toISOString();

    console.log('===> SAVING DOCUMENT FOR USER WITH ID', userId);

    // Use the version from documentData or default to 1.0
    const finalVersion =
      documentData.version === 'auto' ? '1.0' : documentData.version || '1.0';

    // Clean HTML content before saving
    const cleanedContent = documentData.content
      ? cleanTipTapHTML(documentData.content)
      : '';

    // Create the document
    const { data, errors } = await client.models.Document.create({
      title: documentData.title,
      type: documentData.type,
      status: documentData.status || 'draft',
      dateCreated: now,
      lastModified: now,
      version: finalVersion, // Use automatically calculated version
      content: cleanedContent,
      userId: userId,
      fileUrl: documentData.fileUrl || null, // Will be updated after PDF generation
      trackingNumber: documentData.trackingNumber || null,
      signatureType: documentData.signatureType || null,
      assignedWelonTrustId: assignedWelonTrustId,
      executionDate: documentData.executionDate
        ? new Date(documentData.executionDate).toISOString()
        : null,
      templateId: documentData.templateId || null,
      templateVersion: documentData.templateVersion || null,
      documentState: documentData.documentState || null,
      createdAt: now,
      updatedAt: now,
    });

    console.log('===> createDocument DOCUMENT CREATED', data);
    console.log('===> createDocument DOCUMENT CREATED ERRORS', errors);

    if (errors) {
      console.error('Errors creating document:', errors);
      throw new Error(
        'Failed to create document: ' + errors.map(e => e.message).join(', ')
      );
    }

    if (!data) {
      throw new Error('Failed to create document: No data returned');
    }

    // Generate PDF for the document if it has content
    if (cleanedContent && data.id) {
      try {
        console.log(`Generating PDF for document ${data.id}`);

        // Build full HTML for PDF generation
        const fullHtml = buildFullHtml({
          id: data.id,
          title: data.title,
          type: data.type!,
          status: data.status!,
          content: cleanedContent,
          dateCreated: data.dateCreated,
          version: data.version!,
        } as any);

        console.log('Generated HTML for PDF:', fullHtml);

        // Call Lambda function to generate PDF
        const pdfResult = await client.mutations.generatePdfDoc({
          documentId: data.id,
          html: fullHtml,
          isSignedVersion: false,
          userId: data.userId, // Pass user ID for proper S3 path
        });

        console.log('PDF generation result:', pdfResult);
      } catch (pdfError) {
        console.error('Error generating PDF for document:', pdfError);
        // Don't fail document creation if PDF generation fails
      }
    }

    // Log the creation in DocumentUpdateLog
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: data.id!,
        userId: userId,
        changeType: 'Created',
        changeDescription: `Document "${documentData.title}" created`,
        previousVersion: 0,
        newVersion: 1,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document creation:', logError);
      // Continue even if logging fails
    }

    // Send notification to member about document creation
    try {
      const session = await fetchAuthSession();
      const token = session.tokens?.accessToken?.toString();

      if (!token) {
        throw new Error('No access token found');
      }

      const decodedToken = jwtDecode(token) as any;

      const memberUser = await fetchUserByCognitoId(decodedToken.externalId);

      if (memberUser) {
        const notificationMessage = `Your document "${documentData.title}" has been created successfully and is ready for review.`;
        const emailSubject = 'New Document Created - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          userId,
          memberUser.email,
          emailSubject,
          'system-document-created'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating document creation notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error creating document:', error);
    throw error;
  }
}

const DOCUMENT_LIST_MEMBER_SELECTION_SET = [
  'id',
  'title',
  'type',
  'status',
  'dateCreated',
  'version',
  'content',
  'userId',
  'fileUrl',
  'trackingNumber',
  'signatureType',
  'executionDate',
  'lastModified',
  'createdAt',
  'updatedAt',
  'rejectionReason',
  'templateId',
  'templateVersion',
  'documentState',
] as const;

const transformDocumentData = (doc: any): DocumentResponse => ({
  id: doc.id!,
  title: doc.title,
  type: doc.type!,
  status: doc.status!,
  dateCreated: doc.dateCreated,
  lastModified: doc.lastModified || undefined,
  version: doc.version,
  content: doc.content,
  userId: doc.userId,
  fileUrl: doc.fileUrl || undefined,
  trackingNumber: doc.trackingNumber || undefined,
  signatureType: doc.signatureType || undefined,
  executionDate: doc.executionDate || undefined,
  createdAt: doc.createdAt || undefined,
  updatedAt: doc.updatedAt || undefined,
  rejectionReason: doc.rejectionReason || undefined,
  templateId: doc.templateId || undefined,
  templateVersion: doc.templateVersion || undefined,
  documentState: doc.documentState || undefined,
});

/**
 * Get all documents for the current user
 */
export const getUserDocuments = async (): Promise<DocumentResponse[]> => {
  try {
    const { data, errors } = await client.models.Document.list({
      selectionSet: DOCUMENT_LIST_MEMBER_SELECTION_SET,
    });

    if (errors) {
      console.error('getUserDocuments Errors fetching documents:', errors);
      throw new Error(' Failed to fetch documents');
    }

    return data.map(transformDocumentData);
  } catch (error) {
    console.error('Error fetching user documents:', error);
    throw error;
  }
};

/**
 * Get all documents for user by userId
 */
export const getUserDocumentsByUserId = async (
  userId: string
): Promise<DocumentResponse[]> => {
  try {
    const { data, errors } = await client.models.Document.listDocumentByUserId(
      {
        userId,
      },
      {
        selectionSet: DOCUMENT_LIST_MEMBER_SELECTION_SET,
      }
    );

    if (errors) {
      console.error('getUserDocuments Errors fetching documents:', errors);
      throw new Error(' Failed to fetch documents');
    }

    return data.map(transformDocumentData);
  } catch (error) {
    console.error('Error fetching user documents:', error);
    throw error;
  }
};

/**
 * Get documents for Welon Trust users - documents from assigned users that need review or have been processed
 */
export async function getWelonTrustDocuments(): Promise<DocumentResponse[]> {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    const currentUser = await fetchUserByCognitoId(decodedToken.externalId);

    if (!currentUser) {
      return [];
    }

    // First, get all users assigned to this Welon Trust user
    const { data: assignments, errors: assignmentErrors } =
      await client.models.WelonTrustAssignment.list({
        filter: {
          welonTrustUserId: {
            eq: currentUser.id,
          },
        },
      });

    if (assignmentErrors) {
      console.error(
        'Errors fetching Welon Trust assignments:',
        assignmentErrors
      );
      throw new Error('Failed to fetch assigned users');
    }

    if (!assignments || assignments.length === 0) {
      // No assigned users, return empty array
      return [];
    }

    // Extract user IDs from assignments
    const assignedUserIds = assignments.map(assignment => assignment.userId);

    // Create OR filter for all assigned user IDs
    const userFilters = assignedUserIds.map(userId => ({
      userId: { eq: userId },
    }));

    // Fetch documents from assigned users that need review or have been processed
    const allDocuments = [];
    const errors = [];

    // Get documents for each assigned user using listDocumentByUserId
    for (const userId of assignedUserIds) {
      const { data: userDocuments, errors: userErrors } =
        await client.models.Document.listDocumentByUserId({
          userId,
        });

      if (userErrors) {
        errors.push(...userErrors);
      } else if (userDocuments) {
        // Filter documents by status
        const filteredDocuments = userDocuments.filter(doc =>
          [
            'sent',
            'approved',
            'rejected',
            'signed',
            'shipped',
            'received',
          ].includes(doc.status!)
        );
        allDocuments.push(...filteredDocuments);
      }
    }

    const data = allDocuments;

    if (errors.length > 0) {
      console.error('Errors fetching Welon Trust documents:', errors);
      throw new Error('Failed to fetch documents for assigned users');
    }

    return data.map(doc => ({
      id: doc.id!,
      title: doc.title,
      type: doc.type!,
      status: doc.status!,
      dateCreated: doc.dateCreated,
      lastModified: doc.lastModified || undefined,
      version: doc.version,
      content: doc.content,
      userId: doc.userId,
      fileUrl: doc.fileUrl || undefined,
      trackingNumber: doc.trackingNumber || undefined,
      signatureType: doc.signatureType || undefined,
      executionDate: doc.executionDate || undefined,
      createdAt: doc.createdAt || undefined,
      updatedAt: doc.updatedAt || undefined,
      rejectionReason: doc.rejectionReason || undefined,
      templateId: doc.templateId || undefined,
      templateVersion: doc.templateVersion || undefined,
      documentState: doc.documentState || undefined,
    }));
  } catch (error) {
    console.error('Error fetching Welon Trust documents:', error);
    throw error;
  }
}

/**
 * Get documents for a specific user (for Welon Trust users viewing selected user's documents)
 */
export async function getDocumentsForSelectedUser(
  selectedUserId: string
): Promise<DocumentResponse[]> {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    const currentUser = await fetchUserByCognitoId(decodedToken.externalId);

    if (!currentUser) {
      return [];
    }

    // Get documents for the selected user using listDocumentByUserId
    const { data, errors } = await client.models.Document.listDocumentByUserId({
      userId: selectedUserId,
    });

    if (errors) {
      console.error('Errors fetching documents for selected user:', errors);
      throw new Error('Failed to fetch documents for selected user');
    }

    // Filter documents by status after fetching
    const filteredDocuments =
      data?.filter(doc =>
        [
          'sent',
          'approved',
          'rejected',
          'signed',
          'shipped',
          'received',
        ].includes(doc.status!)
      ) || [];

    return filteredDocuments.map(doc => ({
      id: doc.id!,
      title: doc.title,
      type: doc.type!,
      status: doc.status!,
      dateCreated: doc.dateCreated,
      lastModified: doc.lastModified || undefined,
      version: doc.version,
      content: doc.content,
      userId: doc.userId,
      fileUrl: doc.fileUrl || undefined,
      trackingNumber: doc.trackingNumber || undefined,
      signatureType: doc.signatureType || undefined,
      executionDate: doc.executionDate || undefined,
      createdAt: doc.createdAt || undefined,
      updatedAt: doc.updatedAt || undefined,
      rejectionReason: doc.rejectionReason || undefined,
      templateId: doc.templateId || undefined,
      templateVersion: doc.templateVersion || undefined,
      documentState: doc.documentState || undefined,
    }));
  } catch (error) {
    console.error('Error fetching documents for selected user:', error);
    Sentry.captureException(error);
    throw error;
  }
}

export const getCareDocumentsForSelectedUser = async (
  selectedUserId: string
) => {
  try {
    const { data, errors } = await client.models.CareDocumentNew.list({
      filter: {
        userId: { eq: selectedUserId },
      },
    });

    if (errors) {
      console.error(
        'Errors fetching care documents for selected user:',
        errors
      );
      throw new Error('Failed to fetch care documents for selected user');
    }

    if (data.length === 0) {
      return {};
    }

    const careDocument = data[0];

    const loadedData: SectionDataMap = {};

    careDocument.data?.forEach(section => {
      if (section && section.sectionType) {
        try {
          const sectionContent = JSON.parse(section.content as string);
          loadedData[section.sectionType as CareDocumentSectionType] =
            sectionContent;
        } catch (err) {
          console.error(`Error parsing ${section.sectionType} data:`, err);
        }
      }
    });

    return loadedData;
  } catch (error) {
    console.error('Error fetching care documents for selected user:', error);
    Sentry.captureException(error);
    throw error;
  }
};

/**
 * Get a single document by ID
 */
export async function getDocumentById(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const { data, errors } = await client.models.Document.get({
      id: documentId,
    });

    console.log('===> DOCUMENT REVIEW DATA', data);

    if (errors) {
      console.error('Errors fetching document:', errors);
      throw new Error('Failed to fetch document');
    }

    if (!data) {
      throw new Error('Document not found');
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      content: data.content,
      version: data.version,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
      rejectionReason: data.rejectionReason || undefined,
      templateId: data.templateId || undefined,
      documentState: data.documentState || undefined,
    };
  } catch (error) {
    console.error('Error fetching document by ID:', error);
    throw error;
  }
}

/**
 * Sign document electronically with signature capture
 */
// export async function signDocumentElectronically(
//   documentId: string,
//   signatureData: string // Base64 signature image
// ): Promise<DocumentResponse> {
//   try {
//     console.log('🔐 Starting electronic signature process:', { documentId });
//
//     const cognitoUser = await getCurrentUser();
//     console.log('👤 Current Cognito user:', {
//       userId: cognitoUser?.userId,
//       username: cognitoUser?.username,
//     });
//
//     if (!cognitoUser?.userId) {
//       throw new Error('User not authenticated');
//     }
//
//     // Get the internal user record using cognitoId
//     console.log('🔍 Fetching internal user record...');
//     const { data: userData, errors: userErrors } =
//       await client.models.User.list({
//         filter: { cognitoId: { eq: cognitoUser.userId } },
//       });
//
//     if (userErrors || !userData || userData.length === 0) {
//       console.error('❌ Internal user not found:', userErrors);
//       throw new Error('Internal user record not found');
//     }
//
//     const internalUser = userData[0];
//     console.log('👤 Internal user found:', {
//       id: internalUser.id,
//       cognitoId: internalUser.cognitoId,
//       email: internalUser.email,
//     });
//
//     // 1. Get document data first
//     console.log('📄 Fetching document from database...');
//     const { data: documentData, errors: docErrors } =
//       await client.models.Document.get({
//         id: documentId,
//       });
//
//     console.log('📄 Document fetch result:', {
//       found: !!documentData,
//       errors: docErrors,
//       documentUserId: documentData?.userId,
//       currentInternalUserId: internalUser.id,
//       currentCognitoId: cognitoUser.userId,
//     });
//
//     if (docErrors || !documentData) {
//       console.error('❌ Document not found:', docErrors);
//       throw new Error('Document not found');
//     }
//
//     // Verify ownership - check both direct ownership and assigned access
//     const hasDirectOwnership = documentData.userId === internalUser.id;
//     const hasAssignedAccess =
//       documentData.assignedWelonTrustId === cognitoUser.userId;
//
//     console.log('🔒 Verifying ownership:', {
//       documentUserId: documentData.userId,
//       currentInternalUserId: internalUser.id,
//       currentCognitoId: cognitoUser.userId,
//       assignedWelonTrustId: documentData.assignedWelonTrustId,
//       hasDirectOwnership,
//       hasAssignedAccess,
//       canAccess: hasDirectOwnership || hasAssignedAccess,
//     });
//
//     if (!hasDirectOwnership && !hasAssignedAccess) {
//       console.error('❌ Ownership verification failed');
//       console.error('🔍 Debug info:', {
//         documentId,
//         documentTitle: documentData.title,
//         documentCreated: documentData.dateCreated,
//         documentStatus: documentData.status,
//         documentType: documentData.type,
//       });
//
//       throw new Error('Unauthorized access to document');
//     }
//
//     // 2. Generate HTML for PDF using the same logic as regular downloads
//     console.log('📝 Generating HTML with signature...');
//     const htmlContent = buildSignedDocumentHTMLForAPI(
//       documentData,
//       signatureData
//     );
//
//     // 3. Generate PDF using client-side approach
//     console.log('🖨️ Starting PDF generation...');
//
//     // Check if we're in browser environment
//     if (typeof window === 'undefined') {
//       throw new Error(
//         'Document signing must be performed in browser environment'
//       );
//     }
//
//     const pdfBlob = await generatePDFFromHTML(htmlContent);
//
//     console.log('✅ PDF generated successfully, size:', pdfBlob.size, 'bytes');
//
//     // 4. Generate document hash for integrity
//     const arrayBuffer = await pdfBlob.arrayBuffer();
//     const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
//     const hashArray = Array.from(new Uint8Array(hashBuffer));
//     const documentHash = hashArray
//       .map(b => b.toString(16).padStart(2, '0'))
//       .join('');
//
//     // 5. Upload to S3 using client credentials
//     const fileName = `signed-${documentId}-${Date.now()}.pdf`;
//     const signedDocumentUrl = `documents/{identity}/signed/${fileName}`;
//
//     console.log('☁️ Uploading to S3:', {
//       path: signedDocumentUrl,
//       size: pdfBlob.size,
//       hash: documentHash,
//       userId: cognitoUser.userId,
//     });
//
//     await uploadData({
//       path: signedDocumentUrl,
//       data: pdfBlob,
//       options: {
//         contentType: 'application/pdf',
//         metadata: {
//           documentId: documentId,
//           signedAt: new Date().toISOString(),
//           userId: cognitoUser.userId,
//           hash: documentHash,
//         },
//       },
//     }).result;
//
//     console.log('✅ S3 upload successful');
//
//     // 3. Update document in database
//     const now = new Date().toISOString();
//
//     const { data, errors } = await client.models.Document.update({
//       id: documentId,
//       status: 'signed',
//       signatureType: 'electronic',
//       executionDate: now,
//       signedAt: now,
//       signedDocumentUrl,
//       documentHash,
//       lastModified: now,
//       updatedAt: now,
//     });
//
//     if (errors) {
//       console.error('Errors signing document:', errors);
//       throw new Error('Failed to sign document');
//     }
//
//     if (!data) {
//       throw new Error('Failed to sign document: No data returned');
//     }
//
//     // Log the signing
//     try {
//       await client.models.DocumentUpdateLog.create({
//         documentId: documentId,
//         userId: internalUser.id,
//         changeType: 'Updated',
//         changeDescription: `Document "${data.title}" signed with electronic signature`,
//         timestamp: now,
//       });
//     } catch (logError) {
//       console.error('Error logging document signing:', logError);
//     }
//
//     return {
//       id: data.id!,
//       title: data.title,
//       type: data.type!,
//       status: data.status!,
//       dateCreated: data.dateCreated,
//       lastModified: data.lastModified || undefined,
//       version: data.version,
//       content: data.content,
//       userId: data.userId,
//       fileUrl: data.fileUrl || undefined,
//       trackingNumber: data.trackingNumber || undefined,
//       signatureType: data.signatureType || undefined,
//       executionDate: data.executionDate || undefined,
//       createdAt: data.createdAt || undefined,
//       updatedAt: data.updatedAt || undefined,
//       signedDocumentUrl: data.signedDocumentUrl || undefined,
//       signedAt: data.signedAt || undefined,
//       documentHash: data.documentHash || undefined,
//     };
//   } catch (error) {
//     console.error('Error signing document electronically:', error);
//     throw error;
//   }
// }

/**
 * Mark a document as signed (for manual/notarized signatures)
 * This also generates a new PDF without DRAFT watermark
 */
export async function signDocument(
  documentId: string,
  signatureType: 'manual' | 'electronic' | 'notarized' = 'electronic'
): Promise<DocumentResponse> {
  try {
    console.log('🔐 Starting manual/notarized signature process:', {
      documentId,
      signatureType,
    });

    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    // Get internal user record
    const { data: userData, errors: userErrors } =
      await client.models.User.list({
        filter: { cognitoId: { eq: decodedToken.externalId } },
      });

    if (userErrors || !userData || userData.length === 0) {
      console.error('❌ Internal user not found:', userErrors);
      throw new Error('Internal user record not found');
    }

    const internalUser = userData[0];

    // Get document data
    const { data: documentData, errors: docErrors } =
      await client.models.Document.get({ id: documentId });

    if (docErrors || !documentData) {
      console.error('❌ Document not found:', docErrors);
      throw new Error('Document not found');
    }

    // Verify ownership
    const hasDirectOwnership = documentData.userId === internalUser.id;
    const hasAssignedAccess =
      documentData.assignedWelonTrustId === internalUser.id;

    if (!hasDirectOwnership && !hasAssignedAccess) {
      console.error('❌ Ownership verification failed');
      throw new Error('Unauthorized access to document');
    }

    // Generate HTML for PDF (without signature data for manual/notarized)
    console.log('📝 Generating HTML for signed document...');
    const documentForHtml = {
      id: documentData.id!,
      title: documentData.title,
      type: documentData.type!,
      status: documentData.status!,
      content: documentData.content,
      dateCreated: documentData.dateCreated,
      lastModified: documentData.lastModified || documentData.dateCreated,
      version: documentData.version!,
      userId: documentData.userId,
      fileUrl: documentData.fileUrl || undefined,
      trackingNumber: documentData.trackingNumber || undefined,
      signatureType: documentData.signatureType || undefined,
      executionDate: documentData.executionDate || undefined,
      templateVersion: documentData.templateVersion || undefined,
      documentState: documentData.documentState || undefined,
      signedDocumentUrl: documentData.signedDocumentUrl || undefined,
      signedAt: documentData.signedAt || undefined,
      documentHash: documentData.documentHash || undefined,
    };

    const fullHtml = buildFullHtml(documentForHtml);

    // Generate signed PDF using Lambda function (without signature data)
    console.log('🖨️ Starting PDF generation via Lambda...');
    const pdfResult = await client.mutations.generatePdfDoc({
      documentId: documentId,
      html: fullHtml,
      isSignedVersion: true, // This ensures no DRAFT watermark
      signatureType: signatureType, // Pass the signature type
      userId: decodedToken.externalId,
    });

    // Parse the result from Lambda function
    const lambdaResult =
      typeof pdfResult.data === 'string'
        ? JSON.parse(pdfResult.data)
        : pdfResult.data;

    if (!lambdaResult?.success) {
      throw new Error(lambdaResult?.error || 'Failed to generate signed PDF');
    }

    console.log('✅ PDF generated successfully via Lambda');

    // Update document in database
    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'signed',
      signatureType: signatureType,
      executionDate: now,
      lastModified: now,
      updatedAt: now,
      fileUrl: lambdaResult.fileUrl, // Update with new signed PDF URL
    });

    if (errors) {
      console.error('Errors signing document:', errors);
      throw new Error('Failed to sign document');
    }

    if (!data) {
      throw new Error('Failed to sign document: No data returned');
    }

    console.log('✅ Document signed successfully:', {
      documentId,
      signatureType,
    });

    // Send notification to member about document being signed
    try {
      const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
      if (memberUser) {
        const notificationMessage = `Your document "${data.title}" has been successfully signed and is ready for shipping.`;
        const emailSubject = 'Document Signed Successfully - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          decodedToken.externalId,
          memberUser.email,
          emailSubject,
          'system-document-signed'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating document signed notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error signing document:', error);
    throw error;
  }
}

// FUNCTION THAT SENDS DOCUMENT FOR ELECTRONIC REVIEW
export const sendDocumentToWelon = async (documentId: string) => {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    const now = new Date().toISOString();

    // Get document details first
    const { data: document, errors: fetchErrors } =
      await client.models.Document.get({
        id: documentId,
      });

    if (fetchErrors || !document) {
      console.error('Error fetching document:', fetchErrors);
      throw new Error('Failed to fetch document details');
    }

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'sent',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors sending document to Welon:', errors);
      throw new Error('Failed to send document to Welon');
    }

    // Create notification for member about document being sent for review
    try {
      const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
      if (memberUser) {
        const notificationMessage = `Your document "${document.title}" has been sent for review by Welon Trust.`;
        const emailSubject = 'Document Sent for Review - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          decodedToken.externalId,
          memberUser.email,
          emailSubject,
          'system-document-status'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating document sent notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    // Create notification for assigned WelonTrust user about new document for review
    try {
      if (document.assignedWelonTrustId) {
        const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
        if (memberUser) {
          const memberName = `${memberUser.firstName} ${memberUser.lastName}`;
          const memberEmail = memberUser.email;
          const notificationMessage = `${memberName} (${memberEmail}) has sent their document "${document.title}" for your review.`;

          await createNotificationWithEmail(
            notificationMessage,
            document.assignedWelonTrustId,
            undefined, // No email for WelonTrust notification
            undefined,
            'member-document-sent-for-review'
          );
        }
      }
    } catch (notificationError) {
      console.error(
        'Error creating WelonTrust document review notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return data;
  } catch (error) {
    console.error('Error sending document to Welon:', error);
    throw error;
  }
};

/**
 * Mark a document as shipped by mail (UPS)
 */
// export async function shipDocumentByMail(
//   documentId: string,
//   trackingNumber?: string
// ): Promise<DocumentResponse> {
//   try {
//     const session = await fetchAuthSession();
//     const token = session.tokens?.accessToken?.toString();
//
//     if (!token) {
//       throw new Error('No access token found');
//     }
//
//     const decodedToken = jwtDecode(token) as any;
//
//     const now = new Date().toISOString();
//
//     const updateData: any = {
//       id: documentId,
//       status: 'shipped',
//       lastModified: now,
//       updatedAt: now,
//     };
//
//     if (trackingNumber) {
//       updateData.trackingNumber = trackingNumber;
//     }
//
//     const { data, errors } = await client.models.Document.update(updateData);
//
//     if (errors) {
//       console.error('Errors shipping document:', errors);
//       throw new Error('Failed to ship document');
//     }
//
//     if (!data) {
//       throw new Error('Failed to ship document: No data returned');
//     }
//
//     // Send notification to member about document shipping
//     try {
//       const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
//       if (memberUser) {
//         const trackingInfo = trackingNumber
//           ? ` Tracking number: ${trackingNumber}`
//           : '';
//         const notificationMessage = `Your document "${data.title}" has been shipped successfully.${trackingInfo}`;
//         const emailSubject = 'Document Shipped - ChildFree Trust®';
//
//         await createNotificationWithEmail(
//           notificationMessage,
//           decodedToken.externalId,
//           memberUser.email,
//           emailSubject,
//           'system-document-shipped'
//         );
//       }
//     } catch (notificationError) {
//       console.error(
//         'Error creating document shipping notification:',
//         notificationError
//       );
//       // Continue even if notification fails
//     }
//
//     return {
//       id: data.id || '',
//       title: data.title || '',
//       type: (data.type as any) || 'Other',
//       status: (data.status as any) || 'draft',
//       dateCreated: data.dateCreated || now,
//       lastModified: data.lastModified || data.dateCreated || now,
//       version: data.version || '1.0',
//       content: data.content || '',
//       userId: data.userId || '',
//       fileUrl: data.fileUrl || undefined,
//       trackingNumber: data.trackingNumber || undefined,
//       signatureType: (data.signatureType as any) || undefined,
//       executionDate: data.executionDate || undefined,
//     };
//   } catch (error) {
//     console.error('Error shipping document:', error);
//     throw error;
//   }
// }

/**
 * Mark a document as received by Welon Trust
 */
export async function markDocumentAsReceived(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'received',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors marking document as received:', errors);
      throw new Error('Failed to mark document as received');
    }

    if (!data) {
      throw new Error('Failed to mark document as received: No data returned');
    }

    // Log the action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: 'Document marked as received by Welon Trust',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document received:', logError);
      // Continue even if logging fails
    }

    // Send notification to document owner about document being received
    try {
      const documentOwner = await fetchUserByCognitoId(data.userId);
      if (documentOwner) {
        const notificationMessage = `Your document "${data.title}" has been received by Welon Trust and is being processed.`;
        const emailSubject = 'Document Received - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          documentOwner.cognitoId!,
          documentOwner.email,
          emailSubject,
          'welon-trust-received'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating document received notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      rejectionReason: data.rejectionReason || undefined,
      templateId: data.templateId || undefined,
      templateVersion: data.templateVersion || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error marking document as received:', error);
    throw error;
  }
}

/**
 * Approve a document (Welon Trust action)
 */
export async function approveDocument(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'approved',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors approving document:', errors);
      throw new Error('Failed to approve document');
    }

    if (!data) {
      throw new Error('Failed to approve document: No data returned');
    }

    // Log the approval action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: 'Document approved',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document approval:', logError);
      // Continue even if logging fails
    }

    // Send notification to document owner
    try {
      // Get the user by database ID for notification
      const documentOwner = await fetchUserById(data.userId);
      if (documentOwner) {
        const notificationMessage = `Your document "${data.title}" has been approved by Welon Trust and is ready for signing!`;
        const emailSubject =
          'Document Approved - Ready for Signing - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          documentOwner.cognitoId!,
          documentOwner.email,
          emailSubject,
          'welon-trust-approval'
        );
      }
    } catch (notificationError) {
      console.error('Error creating approval notification:', notificationError);
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error approving document:', error);
    throw error;
  }
}

/**
 * Reject a document (Welon Trust action)
 */
export async function rejectDocument(
  documentId: string,
  rejectionReason: string
): Promise<DocumentResponse> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    if (!rejectionReason.trim()) {
      throw new Error('Rejection reason is required');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'rejected',
      rejectionReason: rejectionReason.trim(),
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors rejecting document:', errors);
      throw new Error('Failed to reject document');
    }

    if (!data) {
      throw new Error('Failed to reject document: No data returned');
    }

    // Log the rejection action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType: 'Reviewed',
        changeDescription: `Document rejected. Reason: ${rejectionReason}`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document rejection:', logError);
      // Continue even if logging fails
    }

    // Send notification to document owner
    try {
      // Get the user by database ID for notification
      const documentOwner = await fetchUserById(data.userId);
      if (documentOwner) {
        const notificationMessage = `Your document "${data.title}" has been rejected by Welon Trust. Reason: ${rejectionReason}`;
        const emailSubject =
          'Document Rejected - Action Required - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          documentOwner.cognitoId!,
          documentOwner.email,
          emailSubject,
          'welon-trust-rejection'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating rejection notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
    };
  } catch (error) {
    console.error('Error rejecting document:', error);
    throw error;
  }
}

/**
 * Recreate a rejected document with fresh interview data and templates
 * Archives the old document and creates a new one with the next version
 */
export async function recreateDocument(documentId: string): Promise<void> {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    // First, get the document to extract its templateId and type before archiving
    const { data: existingDocument, errors: fetchErrors } =
      await client.models.Document.get({
        id: documentId,
      });

    if (fetchErrors) {
      console.error('Errors fetching document for recreation:', fetchErrors);
      throw new Error('Failed to fetch document for recreation');
    }

    if (!existingDocument) {
      throw new Error('Document not found for recreation');
    }

    const templateId = existingDocument.templateId;
    const documentType = existingDocument.type;

    if (!templateId) {
      throw new Error(
        'Document does not have a template ID and cannot be recreated'
      );
    }

    if (!documentType) {
      throw new Error('Document does not have a type and cannot be recreated');
    }

    const now = new Date().toISOString();

    // Archive the old rejected document instead of deleting it
    const { errors: archiveErrors } = await client.models.Document.update({
      id: documentId,
      status: 'archived',
      lastModified: now,
      updatedAt: now,
    });

    if (archiveErrors) {
      console.error('Errors archiving old document:', archiveErrors);
      throw new Error('Failed to archive old document');
    }

    // Log the archiving
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: decodedToken.externalId,
        changeType: 'Archived',
        changeDescription: `Old rejected document archived for recreation from template ${templateId}`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document archiving:', logError);
      // Continue even if logging fails
    }

    // Create new document from the specific template (version will be auto-calculated)
    await createDocumentFromSpecificTemplate(
      templateId,
      decodedToken.externalId
    );

    // Send notification to member about document recreation
    try {
      const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
      if (memberUser) {
        const notificationMessage = `Your document "${existingDocument.title}" has been recreated with updated data and is ready for review.`;
        const emailSubject =
          'Document Recreated - Ready for Review - ChildFree Trust®';

        await createNotificationWithEmail(
          notificationMessage,
          decodedToken.externalId,
          memberUser.email,
          emailSubject,
          'system-document-recreate'
        );
      }
    } catch (notificationError) {
      console.error(
        'Error creating document recreation notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    console.log(
      `Successfully recreated document from template ${templateId} for user ${decodedToken.externalId}`
    );
  } catch (error) {
    console.error('Error recreating document:', error);
    throw error;
  }
}

/**
 * Resubmit a rejected document (change status from rejected to sent for review)
 * @deprecated Use recreateDocument instead for better data freshness
 */
// export async function resubmitDocument(
//   documentId: string
// ): Promise<DocumentResponse> {
//   try {
//     const user = await getCurrentUser();
//
//     if (!user?.userId) {
//       throw new Error('User not authenticated');
//     }
//
//     const now = new Date().toISOString();
//
//     const { data, errors } = await client.models.Document.update({
//       id: documentId,
//       status: 'sent',
//       lastModified: now,
//       updatedAt: now,
//     });
//
//     if (errors) {
//       console.error('Errors resubmitting document:', errors);
//       throw new Error('Failed to resubmit document');
//     }
//
//     if (!data) {
//       throw new Error('Failed to resubmit document: No data returned');
//     }
//
//     // Log the resubmission action
//     try {
//       await client.models.DocumentUpdateLog.create({
//         documentId,
//         userId: user.userId,
//         changeType: 'Updated',
//         changeDescription: 'Document resubmitted for review after rejection',
//         timestamp: now,
//       });
//     } catch (logError) {
//       console.error('Error creating resubmit log:', logError);
//       // Continue even if logging fails
//     }
//
//     return {
//       id: data.id!,
//       title: data.title,
//       type: data.type!,
//       status: data.status!,
//       dateCreated: data.dateCreated,
//       lastModified: data.lastModified || undefined,
//       version: data.version,
//       content: data.content,
//       userId: data.userId,
//       fileUrl: data.fileUrl || undefined,
//       trackingNumber: data.trackingNumber || undefined,
//       signatureType: data.signatureType || undefined,
//       executionDate: data.executionDate || undefined,
//       createdAt: data.createdAt || undefined,
//       updatedAt: data.updatedAt || undefined,
//       rejectionReason: data.rejectionReason || undefined,
//     };
//   } catch (error) {
//     console.error('Error resubmitting document:', error);
//     throw error;
//   }
// }

/**
 * Retract a sent document (change status from sent back to draft)
 * This allows members to cancel a document they've sent for review
 */
export async function retractDocument(
  documentId: string
): Promise<DocumentResponse> {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    const now = new Date().toISOString();

    // Get document details first to validate and get assigned WelonTrust info
    const { data: document, errors: fetchErrors } =
      await client.models.Document.get({
        id: documentId,
      });

    if (fetchErrors || !document) {
      console.error('Error fetching document:', fetchErrors);
      throw new Error('Failed to fetch document details');
    }

    // Validate that document is in 'sent' status
    if (document.status !== 'sent') {
      throw new Error('Document can only be retracted when in sent status');
    }

    // Validate that current user owns the document
    if (document.owner !== decodedToken.externalId) {
      throw new Error('You can only retract your own documents');
    }

    // Update document status back to draft
    const { data, errors } = await client.models.Document.update({
      id: documentId,
      status: 'draft',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors retracting document:', errors);
      throw new Error('Failed to retract document');
    }

    if (!data) {
      throw new Error('Failed to retract document: No data returned');
    }

    // Send notification to assigned WelonTrust user about document retraction
    try {
      if (document.assignedWelonTrustId) {
        const memberUser = await fetchUserByCognitoId(decodedToken.externalId);
        if (memberUser) {
          const memberName = `${memberUser.firstName} ${memberUser.lastName}`;
          const memberEmail = memberUser.email;
          const notificationMessage = `${memberName} (${memberEmail}) has retracted their document "${document.title}" from review.`;

          await createNotificationWithEmail(
            notificationMessage,
            document.assignedWelonTrustId,
            undefined, // No email for retraction
            undefined,
            'member-document-retracted'
          );
        }
      }
    } catch (notificationError) {
      console.error(
        'Error creating document retraction notification:',
        notificationError
      );
      // Continue even if notification fails
    }

    // Log the retraction action
    try {
      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: decodedToken.externalId,
        changeType: 'Updated',
        changeDescription: `Document "${document.title}" retracted from review`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error creating retraction log:', logError);
      // Continue even if logging fails
    }

    return {
      id: data.id!,
      title: data.title,
      type: data.type!,
      status: data.status!,
      dateCreated: data.dateCreated,
      lastModified: data.lastModified || undefined,
      version: data.version,
      content: data.content,
      userId: data.userId,
      fileUrl: data.fileUrl || undefined,
      trackingNumber: data.trackingNumber || undefined,
      signatureType: data.signatureType || undefined,
      executionDate: data.executionDate || undefined,
      createdAt: data.createdAt || undefined,
      updatedAt: data.updatedAt || undefined,
      rejectionReason: data.rejectionReason || undefined,
    };
  } catch (error) {
    console.error('Error retracting document:', error);
    throw error;
  }
}

/**
 * Delete (archive) a document
 */
export async function deleteDocument(documentId: string): Promise<void> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    const now = new Date().toISOString();

    const { errors } = await client.models.Document.update({
      id: documentId,
      status: 'archived',
      lastModified: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Errors archiving document:', errors);
      throw new Error('Failed to archive document');
    }

    // Log the archival
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: documentId,
        userId: user.userId,
        changeType: 'Archived',
        changeDescription: 'Document archived',
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document archival:', logError);
    }
  } catch (error) {
    console.error('Error archiving document:', error);
    throw error;
  }
}

/**
 * Permanently delete a document (only for archived documents)
 */
export async function permanentlyDeleteDocument(
  documentId: string
): Promise<void> {
  try {
    const session = await fetchAuthSession();
    const token = session.tokens?.accessToken?.toString();

    if (!token) {
      throw new Error('No access token found');
    }

    const decodedToken = jwtDecode(token) as any;

    // Get current user data to get the internal user ID
    const currentUser = await fetchUserByCognitoId(decodedToken.externalId);

    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // First, get the document to verify it's archived and belongs to the user
    const { data: document, errors: fetchErrors } =
      await client.models.Document.get({
        id: documentId,
      });

    if (fetchErrors) {
      console.error('Errors fetching document for deletion:', fetchErrors);
      throw new Error('Failed to fetch document for deletion');
    }

    if (!document) {
      throw new Error('Document not found');
    }

    if (document.userId !== currentUser.id) {
      throw new Error('Unauthorized: Document does not belong to current user');
    }

    if (document.status !== 'archived') {
      throw new Error('Only archived documents can be permanently deleted');
    }

    // Log the permanent deletion before deleting
    const now = new Date().toISOString();
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: documentId,
        userId: currentUser.id,
        changeType: 'Deleted',
        changeDescription: `Document "${document.title}" permanently deleted`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document permanent deletion:', logError);
    }

    // Permanently delete the document
    const { errors } = await client.models.Document.delete({
      id: documentId,
    });

    if (errors) {
      console.error('Errors permanently deleting document:', errors);
      throw new Error('Failed to permanently delete document');
    }

    console.log(`Document ${documentId} permanently deleted successfully`);
  } catch (error) {
    console.error('Error permanently deleting document:', error);
    throw error;
  }
}

/**
 * Generic function to update Welon Trust assignment for all user documents
 */
const updateUserDocumentsWelonTrust = async (
  userId: string,
  welonTrustId: string | null,
  operation: 'assign' | 'remove'
): Promise<{ success: boolean; updatedCount: number; errors: string[] }> => {
  try {
    console.log(`===> ${operation.toUpperCase()} WELON TO DOCUMENTS START`);

    const errors: string[] = [];
    let updatedCount = 0;

    // Fetch all documents for the specified user
    const { data: documents, errors: fetchErrors } = await client.models.Document.list({
      filter: {
        userId: { eq: userId },
      },
    });

    console.log(`===> ${operation.toUpperCase()} WELON TO DOCUMENTS USER DOCUMENTS LIST`, documents);

    if (fetchErrors) {
      console.error('Errors fetching user documents:', fetchErrors);
      return {
        success: false,
        updatedCount: 0,
        errors: ['Failed to fetch user documents'],
      };
    }

    if (!documents || documents.length === 0) {
      return {
        success: true,
        updatedCount: 0,
        errors: [],
      };
    }

    // Filter documents if removing (only process those with assigned Welon Trust)
    const documentsToUpdate = operation === 'remove' 
      ? documents.filter(doc => doc.assignedWelonTrustId)
      : documents;

    if (documentsToUpdate.length === 0) {
      return {
        success: true,
        updatedCount: 0,
        errors: [],
      };
    }

    // Update each document
    const updatePromises = documentsToUpdate.map(async (document) => {
      try {
        const updateData: any = {
          id: document.id,
          assignedWelonTrustId: welonTrustId,
        };

        // Only update timestamps when assigning
        if (operation === 'assign') {
          const now = new Date().toISOString();
          updateData.lastModified = now;
          updateData.updatedAt = now;
        }

        const { data, errors: updateErrors } = await client.models.Document.update(updateData);

        if (updateErrors) {
          const errorMessage = updateErrors.map(e => e.message).join(', ');
          console.error(`Error updating document ${document.id}:`, updateErrors);
          errors.push(`Failed to update document "${document.title}": ${errorMessage}`);
          return false;
        }

        if (data) {
          updatedCount++;
          return true;
        }
        return false;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Error updating document ${document.id}:`, error);
        errors.push(`Failed to update document "${document.title}": ${errorMessage}`);
        return false;
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    console.log(`===> ${operation.toUpperCase()} WELON TO DOCUMENTS COMPLETE`, {
      updatedCount,
      totalDocuments: documentsToUpdate.length,
      errors: errors.length
    });

    return {
      success: errors.length === 0,
      updatedCount,
      errors,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error(`Error in updateUserDocumentsWelonTrust (${operation}):`, error);
    Sentry.captureException(error);
    return {
      success: false,
      updatedCount: 0,
      errors: [errorMessage],
    };
  }
};

const updateUserCareDocumentsWelonTrust = async (
  userId: string,
  welonTrustId: string | null,
  operation: 'assign' | 'remove'
) => {
  try {
    console.log(`===> ${operation.toUpperCase()} WELON TO CARE DOCUMENTS START`);

    const errors: string[] = [];

    // Fetch all care documents for the specified user
    const { data: careDocuments, errors: fetchErrors } = await client.models.CareDocumentNew.list({
      filter: {
        userId: { eq: userId },
      },
    });

    console.log(`===> ${operation.toUpperCase()} WELON TO CARE DOCUMENTS USER DOCUMENTS LIST`, careDocuments);

    if (fetchErrors) {
      console.error('Errors fetching user care documents:', fetchErrors);
      return {
        success: false,
        errors: ['Failed to fetch user care documents'],
      };
    }

    if (!careDocuments || careDocuments.length === 0) {
      return {
        success: true,
        errors: [],
      };
    }

    // Filter documents if removing (only process those with assigned Welon Trust)
    const documentsToUpdate = operation === 'remove' 
      ? careDocuments.filter(doc => doc.assignedWelonTrustId)
      : careDocuments;

    if (documentsToUpdate.length === 0) {
      return {
        success: true,
        errors: [],
      };
    }

    // Update each document
    const updatePromises = documentsToUpdate.map(async (document) => {
      try {
        const updateData: any = {
          id: document.id,
          assignedWelonTrustId: welonTrustId,
        };

        // Only update timestamps when assigning
        if (operation === 'assign') {
          const now = new Date().toISOString();
          updateData.updatedAt = now;
        }

        const { data, errors: updateErrors } = await client.models.CareDocumentNew.update(updateData);

        if (updateErrors) {
          const errorMessage = updateErrors.map(e => e.message).join(', ');
          console.error(`Error updating care document ${document.id}:`, updateErrors);
          errors.push(`Failed to update care document "${document.id}": ${errorMessage}`);
          return false;
        }

        if (data) {
          return true;
        }
        return false;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Error updating care document ${document.id}:`, error);
        errors.push(`Failed to update care document "${document.id}": ${errorMessage}`);
        return false;
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    console.log(`===> ${operation.toUpperCase()} WELON TO CARE DOCUMENTS COMPLETE`, {
      totalDocuments: documentsToUpdate.length,
      errors: errors.length
    });

    return {
      success: errors.length === 0,
      errors,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error(`Error in updateUserCareDocumentsWelonTrust (${operation}):`, error);
    Sentry.captureException(error);
    return {
      success: false,
      errors: [errorMessage],
    };
  }
};

/**
 * Assign a Welon Trust user to all documents of a specific user
 */
export const assignWelonToUserDocuments = async (
  userId: string,
  welonTrustCognitoId: string
): Promise<{ success: boolean; updatedCount: number; errors: string[] }> => {
  return updateUserDocumentsWelonTrust(userId, welonTrustCognitoId, 'assign');
};

export const assignWelonToUserCareDocuments = async (
  userId: string,
  welonTrustCognitoId: string
) => {
  return updateUserCareDocumentsWelonTrust(userId, welonTrustCognitoId, 'assign');
};

/**
 * Remove Welon Trust assignment from all documents of a specific user
 */
export const removeWelonTrustFromUserDocuments = async (
  userId: string
): Promise<{ success: boolean; updatedCount: number; errors: string[] }> => {
  return updateUserDocumentsWelonTrust(userId, null, 'remove');
};

export const removeWelonTrustFromUserCareDocuments = async (userId: string) => {
  return updateUserCareDocumentsWelonTrust(userId, null, 'remove');
};


/**
 * Delete all documents for current user
 */
export async function deleteAllUserDocuments(): Promise<void> {
  try {
    const user = await getCurrentUser();

    if (!user?.userId) {
      throw new Error('User not authenticated');
    }

    // Get all documents for the current user
    const { data: documents, errors } = await client.models.Document.list({
      filter: { userId: { eq: user.userId } },
    });

    if (errors) {
      throw new Error(errors[0].message);
    }

    // Delete each document
    for (const document of documents) {
      await client.models.Document.delete({ id: document.id });
    }

    console.log(
      `Deleted ${documents.length} documents for user ${user.userId}`
    );
  } catch (error) {
    console.error('Error deleting all user documents:', error);
    throw error;
  }
}

// Helper function to generate HTML content for signed document (API version)
function buildSignedDocumentHTMLForAPI(
  documentData: any,
  signatureData: string
): string {
  // Use the same HTML structure as regular downloads, but add signature section
  const baseHtml = buildFullHtml(documentData);

  // Add signature section before closing body tag
  const signatureSection = `
    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 2px solid #e5e7eb;">
      <h2 style="color: #1f2937; margin-bottom: 1rem;">Electronic Signature</h2>
      <div style="background-color: #f9fafb; padding: 1.5rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
        <p><strong>Signed on:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Document ID:</strong> ${documentData.id}</p>
        <p><strong>Signature Type:</strong> Electronic</p>
        <p><strong>Status:</strong> Legally Binding</p>
        <div style="margin-top: 1rem; padding: 1rem; background-color: white; border: 1px solid #d1d5db; border-radius: 0.25rem;">
          <p style="margin: 0; font-style: italic; color: #6b7280;">
            This document has been electronically signed and is legally binding under applicable electronic signature laws.
          </p>
        </div>
      </div>
    </div>
  `;

  // Insert signature section before closing body tag
  return baseHtml.replace('</body>', `${signatureSection}</body>`);
}
