import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

export type UserShippingAddress = {
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  stateProvinceCode: string;
  postalCode: string;
  countryCode: string;
};

export const userShippingApi = {
  async getUserShippingAddress(
    userId: string
  ): Promise<UserShippingAddress | null> {
    try {
      const { data, errors } = await client.models.User.get(
        {
          id: userId,
        },
        {
          selectionSet: ['shippingAddress.*'],
        }
      );

      if (errors) {
        console.error('ERROR ON GET USER SHIPPING ADDRESS::', errors);
        throw new Error('Failed to fetch shipping address');
      }

      return data?.shippingAddress || null;
    } catch (error) {
      console.error('ERROR ON GET USER SHIPPING ADDRESS:', error);
      throw new Error('Failed to fetch shipping address');
    }
  },

  async saveUserShippingAddress(
    userId: string,
    address: Schema['Address']['type']
  ) {
    const { data, errors } = await client.models.User.update({
      id: userId,
      shippingAddress: address,
    });

    if (errors) {
      console.error('❌ User shipping address update errors:', errors);
      throw new Error();
    }

    return data?.shippingAddress ?? null;
  },
};
