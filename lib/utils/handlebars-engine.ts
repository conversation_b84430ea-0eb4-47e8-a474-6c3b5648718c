'use client';

// Dynamic import for Handlebars to avoid webpack issues
import Handlebars from 'handlebars';
import { normalizeStateName } from '@/app/utils/states';

/**
 * Interface for Handlebars template context
 */
export interface HandlebarsContext {
  user: {
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phone: string;
    address: string;
    state: string;
    dateOfBirth: string;
    gender: string;
  };
  document: {
    type: string;
    state: string;
    todayDate: string;
    currentDate: string;
  };
  answers: Record<
    string,
    {
      value: any;
      type: string;
      questionText?: string;
    }
  >;
  // Flat structure for easier access in templates
  [key: string]: any;
}

/**
 * Register Handlebars helpers - only register once
 */
let helpersRegistered = false;

const registerHelpers = () => {
  if (helpersRegistered) return;

  // Date formatting helper
  Handlebars.registerHelper('formatDate', function (date: string) {
    if (!date) return '';

    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return date;

    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  });

  // Conditional helpers
  Handlebars.registerHelper('eq', function (a: any, b: any) {
    return a === b;
  });

  Handlebars.registerHelper('ne', function (a: any, b: any) {
    return a !== b;
  });

  Handlebars.registerHelper('exists', function (value: any) {
    return value != null && value !== '';
  });

  // Have helper - checks if value exists and is not empty
  Handlebars.registerHelper('have', function (value: any) {
    return value != null && value !== '' && value !== undefined;
  });

  // Has helper - checks if value exists and is not empty
  Handlebars.registerHelper('has', function (value: any) {
    return value != null && value !== '' && value !== undefined;
  });

  // Lookup helper - access variables with spaces in names
  Handlebars.registerHelper('lookup', function (obj: any, key: string) {
    return obj && obj[key];
  });

  // Uppercase helper
  Handlebars.registerHelper('upper', function (str: string) {
    return str ? str.toUpperCase() : '';
  });

  // Lowercase helper
  Handlebars.registerHelper('lower', function (str: string) {
    return str ? str.toLowerCase() : '';
  });

  // Array length helper
  Handlebars.registerHelper('length', function (array: any[]) {
    return Array.isArray(array) ? array.length : 0;
  });

  // Join array helper
  Handlebars.registerHelper(
    'join',
    function (array: any[], separator: string = ', ') {
      return Array.isArray(array) ? array.join(separator) : '';
    }
  );

  // First item helper
  Handlebars.registerHelper('first', function (array: any[]) {
    return Array.isArray(array) && array.length > 0 ? array[0] : null;
  });

  // Last item helper
  Handlebars.registerHelper('last', function (array: any[]) {
    return Array.isArray(array) && array.length > 0
      ? array[array.length - 1]
      : null;
  });

  // Greater than helper
  Handlebars.registerHelper('gt', function (a: any, b: any) {
    return Number(a) > Number(b);
  });

  // Less than helper
  Handlebars.registerHelper('lt', function (a: any, b: any) {
    return Number(a) < Number(b);
  });

  // Greater than or equal helper
  Handlebars.registerHelper('gte', function (a: any, b: any) {
    return Number(a) >= Number(b);
  });

  // Less than or equal helper
  Handlebars.registerHelper('lte', function (a: any, b: any) {
    return Number(a) <= Number(b);
  });

  // Or helper
  Handlebars.registerHelper('or', function (...args: any[]) {
    // Remove the options object (last argument)
    const values = args.slice(0, -1);
    return values.some(val => !!val);
  });

  // And helper
  Handlebars.registerHelper('and', function (...args: any[]) {
    // Remove the options object (last argument)
    const values = args.slice(0, -1);
    return values.every(val => !!val);
  });

  // Not helper
  Handlebars.registerHelper('not', function (value: any) {
    return !value;
  });

  helpersRegistered = true;
};

/**
 * Validate Handlebars template syntax without rendering
 * Returns ok=false and error message if parsing fails
 */
export const validateHandlebarsTemplate = (
  templateContent: string
): { ok: boolean; error?: string } => {
  try {
    // Ensure helpers are registered (safe no-op if already registered)
    registerHelpers();
    // Parse will throw on syntax errors (e.g., unclosed blocks)
    Handlebars.parse(templateContent || '');
    return { ok: true };
  } catch (err: any) {
    return { ok: false, error: err?.message || String(err) };
  }
};

/**
 * Creates Handlebars context from user attributes
 */
export const createUserContext = (
  userAttributes: Record<string, any>
): HandlebarsContext['user'] => {
  const fullName =
    `${userAttributes.given_name || ''} ${
      userAttributes.family_name || ''
    }`.trim() || '';

  return {
    firstName: userAttributes.given_name || '',
    lastName: userAttributes.family_name || '',
    fullName,
    email: userAttributes.email || '',
    phone: userAttributes.phone_number || '',
    address: userAttributes.address || '',
    state: userAttributes.address || '', // address field contains state
    dateOfBirth: userAttributes.birthdate || '',
    gender: userAttributes.gender || '',
  };
};

/**
 * Creates answers context from user interview answers
 */
export const createAnswersContext = (
  userAnswers: any[],
  questions: any[] = []
): HandlebarsContext['answers'] => {
  const answers: HandlebarsContext['answers'] = {};

  if (!Array.isArray(userAnswers)) {
    return answers;
  }

  // Create a map of questionId to question for quick lookup
  const questionMap = new Map<string, any>();
  questions.forEach(question => {
    if (question?.questionId) {
      questionMap.set(question.questionId, question);
    }
  });

  userAnswers.forEach(answerData => {
    if (answerData?.questionMapping && answerData?.answer !== undefined) {
      // Clean the mapping name (remove brackets in proper order)
      let cleanMapping = answerData.questionMapping;

      // First, handle complex cases like [{{variable}}] -> variable
      cleanMapping = cleanMapping.replace(/^\[\{\{(.+)\}\}\]$/, '$1');

      // Then handle simple cases
      cleanMapping = cleanMapping.replace(/^\{\{(.+)\}\}$/, '$1'); // {{variable}} -> variable
      cleanMapping = cleanMapping.replace(/^\[(.+)\]$/, '$1'); // [variable] -> variable

      const question = questionMap.get(answerData.questionId);

      // Try to parse the answer if it's JSON (for arrays, objects)
      let parsedAnswer = answerData.answer;
      try {
        parsedAnswer = JSON.parse(answerData.answer);
      } catch {
        // If parsing fails, keep as string
      }

      // Convert boolean values to Yes/No for template compatibility
      let displayValue = parsedAnswer;
      if (typeof parsedAnswer === 'boolean') {
        displayValue = parsedAnswer ? 'Yes' : 'No';
      }

      answers[cleanMapping] = {
        value: displayValue,
        type: question?.type || 'text',
        questionText: question?.text,
      };
    }
  });

  return answers;
};

/**
 * Creates document context
 */
export const createDocumentContext = (
  documentType?: string,
  documentState?: string
): HandlebarsContext['document'] => {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return {
    type: documentType || '',
    state: documentState || '',
    todayDate: currentDate,
    currentDate,
  };
};

/**
 * Main function to render Handlebars templates
 * Works both synchronously and asynchronously
 */
export const renderTemplate = (
  templateContent: string,
  context: HandlebarsContext
): string => {
  try {
    // Register helpers
    registerHelpers();

    console.log('===> RENDER CONTENT', templateContent);
    console.log('===> RENDER CONTEXT', context);
    // Compile and render template
    const template = Handlebars.compile(templateContent);
    const result = template(context);

    return result;
  } catch (error) {
    console.error('Error rendering Handlebars template:', error);
    console.error(
      'Template content:',
      templateContent.substring(0, 200) + '...'
    );
    console.error('Context:', context);
    return templateContent; // Return original content if compilation fails
  }
};

/**
 * Synchronous version of renderHandlebarsTemplate for client-side use
 * @deprecated Use renderTemplate instead
 */
export const renderHandlebarsTemplateSync = (
  templateContent: string,
  context: HandlebarsContext
): string => {
  return renderTemplate(templateContent, context);
};

/**
 * Compiles and renders a Handlebars template
 * @deprecated Use renderTemplate instead
 */
export const renderHandlebarsTemplate = async (
  templateContent: string,
  context: HandlebarsContext
): Promise<string> => {
  return renderTemplate(templateContent, context);
};

/**
 * Creates complete Handlebars context from all data sources
 */
export const createHandlebarsContext = (
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): HandlebarsContext => {
  const userContext = createUserContext(userAttributes);
  const documentContext = createDocumentContext(documentType, documentState);
  const answersContext = createAnswersContext(userAnswers, questions);

  // Create flat structure for easier template access
  const flatContext: HandlebarsContext = {
    user: userContext,
    document: documentContext,
    answers: answersContext,
    // Add execution date variables
    execution_date_day: new Date().getDate().toString(),
    execution_date_month: new Date().toLocaleDateString('en-US', {
      month: 'long',
    }),
    execution_date_year: new Date().getFullYear().toString(),
  };

  // Add all answer values directly to the root context for easier access
  Object.keys(answersContext).forEach(key => {
    flatContext[key] = answersContext[key].value;
  });

  // Add user properties to root context
  Object.keys(userContext).forEach(key => {
    flatContext[`user.${key}`] = (userContext as any)[key];
  });

  // Add document properties to root context
  Object.keys(documentContext).forEach(key => {
    flatContext[`document.${key}`] = (documentContext as any)[key];
  });

  return flatContext;
};

/**
 * Synchronous fallback for client-side template processing
 * @deprecated Use processTemplate instead
 */
export const processTemplateSync = (
  templateContent: string,
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): string => {
  const context = createHandlebarsContext(
    userAttributes,
    userAnswers,
    questions,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};

/**
 * Main function to replace placeholders using Handlebars
 * This replaces the old regex-based replacePlaceholders function
 */
export const processTemplate = async (
  templateContent: string,
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): Promise<string> => {
  const context = createHandlebarsContext(
    userAttributes,
    userAnswers,
    questions,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};

/**
 * Creates Handlebars context from InterviewV2 StepsData for admin preview
 * This function is specifically for admin template preview and uses mock data
 */
export const createHandlebarsContextFromV2ForPreview = (
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): HandlebarsContext => {
  // This function is identical to createHandlebarsContextFromV2 but is explicitly
  // marked for admin preview use to avoid confusion with real user data processing
  return createHandlebarsContextFromV2(
    stepsData,
    userAttributes,
    documentType,
    documentState
  );
};

/**
 * Parse member state from JSON format and normalize it
 * @param memberState - The member state field (can be JSON string or plain string)
 * @returns Normalized state name
 */
const parseMemberState = (memberState: string | null | undefined): string => {
  if (!memberState) return '';

  try {
    // Try to parse as JSON first (new format)
    const parsedState = JSON.parse(memberState);
    const stateCode = parsedState.stateProvinceCode || '';
    return normalizeStateName(stateCode);
  } catch {
    // If parsing fails, assume it's already a state code/name (old format)
    return normalizeStateName(memberState);
  }
};

/**
 * Creates Handlebars context from InterviewV2 StepsData
 * This function processes real user data for document generation
 */
export const createHandlebarsContextFromV2 = (
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): HandlebarsContext => {
  // Create user context from profile data or fallback to userAttributes
  const profile = stepsData?.profile || {};

  // Parse the member state from userAttributes if available
  const memberState = userAttributes.address || userAttributes.state || '';
  const normalizedState = parseMemberState(memberState);

  const userContext = {
    firstName: profile.firstName || userAttributes.given_name || '',
    middleName: profile.middleName || userAttributes.middle_name || '',
    lastName: profile.lastName || userAttributes.family_name || '',
    fullName:
      `${profile.firstName || userAttributes.given_name || ''} ${profile.middleName || userAttributes.middle_name ? (profile.middleName || userAttributes.middle_name) + ' ' : ''}${profile.lastName || userAttributes.family_name || ''}`.trim(),
    email: profile.email || userAttributes.email || '',
    phone: profile.phone || userAttributes.phone_number || '',
    address: profile.address
      ? `${profile.address.street || ''}, ${profile.address.city || ''}, ${profile.address.county || ''}, ${profile.address.state || ''} ${profile.address.zip || ''}`.trim()
      : userAttributes.address || '',
    state: profile.address?.state || normalizedState || documentState || '',
    dateOfBirth: profile.dateOfBirth || userAttributes.birthdate || '',
    gender: userAttributes.gender || '',
  };

  const documentContext = createDocumentContext(documentType, documentState);

  // Create answers context from StepsData
  const answersContext: HandlebarsContext['answers'] = {};

  // Helper function to get person name from Person object
  const getPersonName = (person: any) => {
    if (!person) return '';
    if (person.type === 'individual') {
      const firstName = person.firstName || '';
      const middleName = person.middleName || '';
      const lastName = person.lastName || '';
      return `${firstName} ${middleName} ${lastName}`
        .replace(/\s+/g, ' ')
        .trim();
    }
    return person.entityName || '';
  };

  // Add profile data as answers

  answersContext['firstName'] = {
    value: profile.firstName || '',
    type: 'text',
  };
  answersContext['middleName'] = {
    value: profile.middleName || '',
    type: 'text',
  };
  answersContext['lastName'] = { value: profile.lastName || '', type: 'text' };
  answersContext['email'] = { value: profile.email || '', type: 'text' };
  answersContext['phone'] = { value: profile.phone || '', type: 'text' };
  answersContext['dateOfBirth'] = {
    value: profile.dateOfBirth || '',
    type: 'date',
  };
  answersContext['maritalStatus'] = {
    value: profile.maritalStatus || '',
    type: 'text',
  };

  // Add address fields
  answersContext['address_street'] = {
    value: profile.address?.street || '',
    type: 'text',
  };
  answersContext['address_city'] = {
    value: profile.address?.city || '',
    type: 'text',
  };
  answersContext['address_county'] = {
    value: profile.address?.county || '',
    type: 'text',
  };
  answersContext['address_state'] = {
    value: normalizeStateName(profile.address?.state || ''),
    type: 'text',
  };
  answersContext['address_zip'] = {
    value: profile.address?.zip || '',
    type: 'text',
  };
  answersContext['address_country'] = {
    value: profile.address?.country || '',
    type: 'text',
  };

  // Add spouse info
  answersContext['spouse_firstName'] = {
    value: profile.spouse?.firstName || '',
    type: 'text',
  };
  answersContext['spouse_middleName'] = {
    value: profile.spouse?.middleName || '',
    type: 'text',
  };
  answersContext['spouse_lastName'] = {
    value: profile.spouse?.lastName || '',
    type: 'text',
  };
  answersContext['spouse_dateOfBirth'] = {
    value: profile.spouse?.dateOfBirth || '',
    type: 'date',
  };

  // Add selected documents
  answersContext['will_selected'] = {
    value: profile.selectedDocuments?.will ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['trust_selected'] = {
    value: profile.selectedDocuments?.trust ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['financialPOA_selected'] = {
    value: profile.selectedDocuments?.financialPOA ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['medicalPOA_selected'] = {
    value: profile.selectedDocuments?.medicalPOA ? 'Yes' : 'No',
    type: 'boolean',
  };

  // Add sound mind
  answersContext['soundMind'] = {
    value: profile.soundMind ? 'Yes' : 'No',
    type: 'boolean',
  };

  // Add emergency data
  const emergency = stepsData.emergency || {};
  answersContext['emergency_dependents_count'] = {
    value: (emergency.dependents?.length || 0).toString(),
    type: 'number',
  };
  answersContext['emergency_pets_count'] = {
    value: (emergency.pets?.length || 0).toString(),
    type: 'number',
  };

  // Add no dependents/pets flags
  answersContext['emergency_noDependents'] = {
    value: emergency.noDependents ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['emergency_noPets'] = {
    value: emergency.noPets ? 'Yes' : 'No',
    type: 'boolean',
  };

  // Add dependents data
  if (emergency.dependents) {
    emergency.dependents.forEach((dependent: any, index: number) => {
      const i = index + 1;
      answersContext[`emergency_dependent_${i}_name`] = {
        value: getPersonName(dependent.person) || '',
        type: 'text',
      };
      answersContext[`emergency_dependent_${i}_relationship`] = {
        value: dependent.relationship || '',
        type: 'text',
      };
      answersContext[`emergency_dependent_${i}_careInstructions`] = {
        value: dependent.careInstructions || '',
        type: 'text',
      };
    });
  }

  // Add pets data
  if (emergency.pets) {
    emergency.pets.forEach((pet: any, index: number) => {
      const i = index + 1;
      answersContext[`emergency_pet_${i}_name`] = {
        value: pet.name || '',
        type: 'text',
      };
      answersContext[`emergency_pet_${i}_primaryGuardian`] = {
        value: getPersonName(pet.primaryGuardian) || '',
        type: 'text',
      };
      answersContext[`emergency_pet_${i}_secondaryGuardian`] = {
        value: getPersonName(pet.secondaryGuardian) || '',
        type: 'text',
      };
      answersContext[`emergency_pet_${i}_petTrustSupport`] = {
        value: pet.petTrustSupport ? `$${pet.petTrustSupport}` : '',
        type: 'text',
      };
      answersContext[`emergency_pet_${i}_takerOfLastResort`] = {
        value: getPersonName(pet.takerOfLastResort) || 'Our Company',
        type: 'text',
      };
      // Keep backward compatibility
      answersContext[`emergency_pet_${i}_guardian`] = {
        value: getPersonName(pet.primaryGuardian) || '',
        type: 'text',
      };
    });
  }

  // Add afterYouDie data
  const afterYouDie = stepsData.afterYouDie || {};
  answersContext['afterYouDie_executor_useCompany'] = {
    value: afterYouDie.executor?.useCompany ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['afterYouDie_executor_primary'] = {
    value: getPersonName(afterYouDie.executor?.primary) || '',
    type: 'text',
  };
  answersContext['afterYouDie_executor_successors'] = {
    value:
      afterYouDie.executor?.successors
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };

  answersContext['afterYouDie_successorTrustee_useCompany'] = {
    value: afterYouDie.successorTrustee?.useCompany ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['afterYouDie_successorTrustee_primary'] = {
    value: getPersonName(afterYouDie.successorTrustee?.primary) || '',
    type: 'text',
  };
  answersContext['afterYouDie_successorTrustee_successors'] = {
    value:
      afterYouDie.successorTrustee?.successors
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };

  answersContext['afterYouDie_lastResortBeneficiary'] = {
    value: getPersonName(afterYouDie.lastResortBeneficiary) || '',
    type: 'text',
  };

  // Add no owned properties flag
  answersContext['afterYouDie_noOwnedProperties'] = {
    value: afterYouDie.noOwnedProperties ? 'Yes' : 'No',
    type: 'boolean',
  };

  // Add properties data
  if (afterYouDie.properties) {
    afterYouDie.properties.forEach((property: any, index: number) => {
      const i = index + 1;
      answersContext[`afterYouDie_property_${i}_type`] = {
        value: property.type || '',
        type: 'text',
      };
      answersContext[`afterYouDie_property_${i}_address`] = {
        value: property.address || '',
        type: 'text',
      };
      answersContext[`afterYouDie_property_${i}_title`] = {
        value: property.title || '',
        type: 'text',
      };
      answersContext[`afterYouDie_property_${i}_ownership`] = {
        value: (property.ownership || 0).toString(),
        type: 'number',
      };
      answersContext[`afterYouDie_property_${i}_recipient`] = {
        value: getPersonName(property.recipient) || '',
        type: 'text',
      };
    });
  }

  // Add specific gifts data
  if (afterYouDie.specificGifts) {
    afterYouDie.specificGifts.forEach((gift: any, index: number) => {
      const i = index + 1;
      answersContext[`afterYouDie_gift_${i}_recipient`] = {
        value: getPersonName(gift.recipient) || '',
        type: 'text',
      };
      answersContext[`afterYouDie_gift_${i}_type`] = {
        value: gift.type || '',
        type: 'text',
      };
      answersContext[`afterYouDie_gift_${i}_description`] = {
        value: gift.description || '',
        type: 'text',
      };
      answersContext[`afterYouDie_gift_${i}_amount`] = {
        value: (gift.amount || 0).toString(),
        type: 'number',
      };
      answersContext[`afterYouDie_gift_${i}_bankName`] = {
        value: gift.bankName || '',
        type: 'text',
      };
      answersContext[`afterYouDie_gift_${i}_accountType`] = {
        value: gift.accountType || '',
        type: 'text',
      };
      answersContext[`afterYouDie_gift_${i}_accountNumber`] = {
        value: gift.accountNumber || '',
        type: 'text',
      };
    });
  }

  // Add main beneficiaries data
  if (afterYouDie.mainBeneficiaries) {
    afterYouDie.mainBeneficiaries.forEach((beneficiary: any, index: number) => {
      const i = index + 1;
      answersContext[`afterYouDie_beneficiary_${i}_primary`] = {
        value: getPersonName(beneficiary.primaryBeneficiary) || '',
        type: 'text',
      };
      answersContext[`afterYouDie_beneficiary_${i}_percentage`] = {
        value: (beneficiary.percentage || 0).toString(),
        type: 'number',
      };
      answersContext[`afterYouDie_beneficiary_${i}_successors`] = {
        value:
          beneficiary.successors
            ?.map((s: any) => getPersonName(s.person))
            .join(', ') || '',
        type: 'text',
      };
    });
  }

  // Add financial data
  const financial = stepsData.financial || {};
  answersContext['financial_agent_useCompany'] = {
    value: financial.agent?.useCompany ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['financial_agent_primary'] = {
    value: getPersonName(financial.agent?.primary) || '',
    type: 'text',
  };
  answersContext['financial_agent_successors'] = {
    value:
      financial.agent?.successors
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };

  // Add financial powers
  const financialPowers = financial.powers || {};
  Object.keys(financialPowers).forEach(power => {
    answersContext[`financial_power_${power}`] = {
      value: financialPowers[power] ? 'Yes' : 'No',
      type: 'boolean',
    };
  });

  // Add medical data
  const medical = stepsData.medical || {};
  answersContext['medical_proxy_useCompany'] = {
    value: medical.proxy?.useCompany ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['medical_proxy_primary'] = {
    value: getPersonName(medical.proxy?.primary) || '',
    type: 'text',
  };
  answersContext['medical_proxy_successors'] = {
    value:
      medical.proxy?.successors
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };
  answersContext['medical_proxyAuthority'] = {
    value: medical.proxyAuthority || '',
    type: 'text',
  };
  answersContext['medical_wantConsultation'] = {
    value: medical.wantConsultation ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['medical_consultationPeople'] = {
    value:
      medical.consultationPeople
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };
  answersContext['medical_medicalInfoAccessPeople'] = {
    value:
      medical.medicalInfoAccessPeople
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };

  // Add medical directives
  const medicalDirectives = medical.directives || {};
  Object.keys(medicalDirectives).forEach(directive => {
    answersContext[`medical_directive_${directive}`] = {
      value: medicalDirectives[directive] ? 'Yes' : 'No',
      type: 'boolean',
    };
  });

  // Add organ donation data
  const organDonation = medical.organDonation || {};
  answersContext['medical_organDonation_enabled'] = {
    value: organDonation.enabled ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['medical_organDonation_specificOrgans'] = {
    value: organDonation.specificOrgans?.join(', ') || '',
    type: 'text',
  };
  answersContext['medical_organDonation_limitations'] = {
    value: organDonation.limitations || '',
    type: 'text',
  };

  // Add additional data
  const additional = stepsData.additional || {};
  answersContext['additional_personalProperty'] = {
    value: additional.personalProperty || '',
    type: 'text',
  };
  answersContext['additional_burial'] = {
    value: additional.burial || '',
    type: 'text',
  };
  answersContext['additional_burialOther'] = {
    value: additional.burialOther || '',
    type: 'text',
  };

  // Add guardian data
  answersContext['additional_guardian_useCompany'] = {
    value: additional.guardian?.useCompany ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['additional_guardian_primary'] = {
    value: getPersonName(additional.guardian?.primary) || '',
    type: 'text',
  };
  answersContext['additional_guardian_successors'] = {
    value:
      additional.guardian?.successors
        ?.map((person: any) => getPersonName(person))
        .join(', ') || '',
    type: 'text',
  };

  // Add legal provisions
  const legalProvisions = additional.legalProvisions || {};
  answersContext['additional_legalProvision_disclaimerTrust'] = {
    value: legalProvisions.disclaimerTrust ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['additional_legalProvision_drugAbuse'] = {
    value: legalProvisions.drugAbuse ? 'Yes' : 'No',
    type: 'boolean',
  };
  answersContext['additional_legalProvision_mineralRights'] = {
    value: legalProvisions.mineralRights ? 'Yes' : 'No',
    type: 'boolean',
  };

  // Add people data
  if (stepsData.people && Array.isArray(stepsData.people)) {
    stepsData.people.forEach((person: any, index: number) => {
      const prefix = `person_${index + 1}`;
      answersContext[`${prefix}_type`] = { value: person.type, type: 'text' };
      answersContext[`${prefix}_firstName`] = {
        value: person.firstName || '',
        type: 'text',
      };
      answersContext[`${prefix}_middleName`] = {
        value: person.middleName || '',
        type: 'text',
      };
      answersContext[`${prefix}_lastName`] = {
        value: person.lastName || '',
        type: 'text',
      };
      answersContext[`${prefix}_entityName`] = {
        value: person.entityName || '',
        type: 'text',
      };
      answersContext[`${prefix}_dateOfBirth`] = {
        value: person.dateOfBirth || '',
        type: 'date',
      };
      answersContext[`${prefix}_phoneNumber`] = {
        value: person.phoneNumber || '',
        type: 'text',
      };
      answersContext[`${prefix}_address`] = {
        value: person.address || '',
        type: 'text',
      };
    });
  }

  // Create flat structure for easier template access
  const flatContext: HandlebarsContext = {
    user: userContext,
    document: documentContext,
    answers: answersContext,
    // Add execution date variables
    execution_date_day: new Date().getDate().toString(),
    execution_date_month: new Date().toLocaleDateString('en-US', {
      month: 'long',
    }),
    execution_date_year: new Date().getFullYear().toString(),
  };

  // Add all answer values directly to the root context for easier access
  Object.keys(answersContext).forEach(key => {
    flatContext[key] = answersContext[key].value;
  });

  // Add user properties to root context
  Object.keys(userContext).forEach(key => {
    flatContext[`user.${key}`] = (userContext as any)[key];
  });

  // Add document properties to root context
  Object.keys(documentContext).forEach(key => {
    flatContext[`document.${key}`] = (documentContext as any)[key];
  });

  // Add arrays for Handlebars loops using existing data
  const emergencyData = stepsData.emergency || {};
  const afterYouDieData = stepsData.afterYouDie || {};

  // Add dependents array for loops
  if (emergencyData.dependents && emergencyData.dependents.length > 0) {
    flatContext['emergency_dependents'] = emergencyData.dependents.map(
      (dependent: any) => ({
        person: dependent.person || {},
        careInstructions: dependent.careInstructions || '',
      })
    );
  }

  // Add pets array for loops
  if (emergencyData.pets && emergencyData.pets.length > 0) {
    flatContext['emergency_pets'] = emergencyData.pets.map((pet: any) => ({
      name: pet.name || '',
      primaryGuardian: getPersonName(pet.primaryGuardian) || '',
      secondaryGuardian: getPersonName(pet.secondaryGuardian) || '',
      petTrustSupport: pet.petTrustSupport || '',
      takerOfLastResort: getPersonName(pet.takerOfLastResort) || 'Our Company',
      // Keep backward compatibility
      guardian: getPersonName(pet.primaryGuardian) || '',
    }));
  }

  // Add properties array for loops
  if (afterYouDieData.properties && afterYouDieData.properties.length > 0) {
    flatContext['afterYouDie_properties'] = afterYouDieData.properties.map(
      (property: any) => ({
        type: property.type || '',
        address: property.address || '',
        title: property.title || '',
        ownership: property.ownership || 0,
        recipient: getPersonName(property.recipient) || '',
      })
    );
  }

  // Add specific gifts array for loops
  if (
    afterYouDieData.specificGifts &&
    afterYouDieData.specificGifts.length > 0
  ) {
    flatContext['afterYouDie_specificGifts'] =
      afterYouDieData.specificGifts.map((gift: any) => ({
        recipient: getPersonName(gift.recipient) || '',
        type: gift.type || '',
        description: gift.description || '',
        amount: gift.amount || 0,
        bankName: gift.bankName || '',
        accountType: gift.accountType || '',
        accountNumber: gift.accountNumber || '',
      }));
  }

  // Add main beneficiaries array for loops
  if (
    afterYouDieData.mainBeneficiaries &&
    afterYouDieData.mainBeneficiaries.length > 0
  ) {
    flatContext['afterYouDie_mainBeneficiaries'] =
      afterYouDieData.mainBeneficiaries.map((beneficiary: any) => ({
        primary: getPersonName(beneficiary.primaryBeneficiary) || '',
        percentage: beneficiary.percentage || 0,
        successors:
          beneficiary.successors
            ?.map((s: any) => getPersonName(s.person))
            .join(', ') || '',
      }));
  }

  console.log('===> FINAL FLAT CONTEXT KEYS:', Object.keys(flatContext));
  console.log('===> SAMPLE FLAT CONTEXT VALUES:', {
    firstName: flatContext.firstName,
    lastName: flatContext.lastName,
    spouse_firstName: flatContext.spouse_firstName,
    emergency_dependents_count: flatContext.emergency_dependents_count,
  });

  return flatContext;
};

/**
 * Process template using InterviewV2 data for real user documents
 * Use this function for actual document generation with real user data
 */
export const processTemplateWithV2 = async (
  templateContent: string,
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): Promise<string> => {
  const context = createHandlebarsContextFromV2(
    stepsData,
    userAttributes,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};

/**
 * Synchronous version of processTemplateWithV2 for real user documents
 * Use this function for actual document generation with real user data
 */
export const processTemplateWithV2Sync = (
  templateContent: string,
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): string => {
  const context = createHandlebarsContextFromV2(
    stepsData,
    userAttributes,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};

/**
 * Process template using InterviewV2 data for admin preview only
 * Use this function for admin template preview with mock data
 */
export const processTemplateWithV2ForPreview = async (
  templateContent: string,
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): Promise<string> => {
  const context = createHandlebarsContextFromV2ForPreview(
    stepsData,
    userAttributes,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};

/**
 * Synchronous version of processTemplateWithV2ForPreview for admin preview only
 * Use this function for admin template preview with mock data
 */
export const processTemplateWithV2ForPreviewSync = (
  templateContent: string,
  stepsData: any,
  userAttributes: Record<string, any> = {},
  documentType?: string,
  documentState?: string
): string => {
  const context = createHandlebarsContextFromV2ForPreview(
    stepsData,
    userAttributes,
    documentType,
    documentState
  );

  return renderTemplate(templateContent, context);
};
