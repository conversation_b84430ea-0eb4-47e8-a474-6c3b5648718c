import type { Schema } from '@/amplify/data/resource';
import { generateClient } from 'aws-amplify/data';

const client = generateClient<Schema>({
  authMode: 'iam',
});

/**
 * Utility functions for handling promo code information
 */
export interface PromoCodeInfo {
  activated: boolean;
  discountPercentage: number;
  activatedAt: string;
  promoCodeId: string;
}

/**
 * Parse promoCodeInfo from database (can be JSON string or object)
 */
export function parsePromoCodeInfo(
  promoCodeInfoRaw: any
): PromoCodeInfo | null {
  if (!promoCodeInfoRaw) {
    return null;
  }

  let promoCodeInfo = null;

  // Parse JSON string if needed
  if (typeof promoCodeInfoRaw === 'string' && promoCodeInfoRaw.trim()) {
    try {
      promoCodeInfo = JSON.parse(promoCodeInfoRaw);
    } catch (e) {
      console.error('Error parsing promoCodeInfo JSON:', e);
      return null;
    }
  } else if (typeof promoCodeInfoRaw === 'object' && promoCodeInfoRaw) {
    promoCodeInfo = promoCodeInfoRaw;
  }

  // Validate the structure
  if (
    promoCodeInfo &&
    typeof promoCodeInfo === 'object' &&
    typeof promoCodeInfo.activated === 'boolean' &&
    typeof promoCodeInfo.discountPercentage === 'number' &&
    typeof promoCodeInfo.activatedAt === 'string' &&
    typeof promoCodeInfo.promoCodeId === 'string'
  ) {
    return promoCodeInfo as PromoCodeInfo;
  }

  return null;
}

/**
 * Check if user has an active promo code
 */
export function hasActivePromoCode(promoCodeInfoRaw: any): boolean {
  const promoCodeInfo = parsePromoCodeInfo(promoCodeInfoRaw);
  return promoCodeInfo?.activated === true;
}

/**
 * Get discount percentage from promo code info
 */
export function getDiscountPercentage(promoCodeInfoRaw: any): number {
  const promoCodeInfo = parsePromoCodeInfo(promoCodeInfoRaw);
  return promoCodeInfo?.discountPercentage || 0;
}

export const verifyPromoCode = async (promoCode: string): Promise<boolean> => {
  const { data, errors } = await client.queries.verifyPromoCode({
    promoCode,
  });
  if (errors) {
    console.error('Error verifying promo code:', errors);
    throw new Error(JSON.stringify(errors));
  }
  return data ?? false;
};
