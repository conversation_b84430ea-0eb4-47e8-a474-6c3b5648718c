import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';
import * as Sentry from '@sentry/nextjs';

export type SecretName = 'GOOGLE_API_KEY';

export async function getSecret(secretName: SecretName): Promise<string> {
  try {
    // Configure Amplify for server-side usage
    Amplify.configure(outputs, { ssr: true });

    // Generate client with API key auth (public access)
    const client = generateClient<Schema>({
      authMode: 'apiKey',
    });

    console.log('Making secrets query...', secretName);

    const response = await client.queries.getSecrets({
      secretName,
    });

    console.log('Secrets query result:', response);

    if (response.errors && response.errors.length > 0) {
      throw new Error(response.errors[0].message);
    }

    // Parse JSON string response
    let data: { value?: string; error?: string };
    if (typeof response.data === 'string') {
      data = JSON.parse(response.data);
    } else {
      data = response.data as { value?: string; error?: string };
    }

    if (data?.error) {
      throw new Error(data.error);
    }

    if (!data?.value) {
      throw new Error('Secret value not found');
    }

    // Check if it's a placeholder value (local development)
    if (data.value === '<value will be resolved during runtime>') {
      console.log('🔄 Detected placeholder, using fallback from process.env');

      // Fallback to process.env for local development
      let fallbackValue: string | undefined;
      switch (secretName) {
        case 'GOOGLE_API_KEY':
          fallbackValue = process.env.NEXT_PUBLIC_GOOGLE_API_KEY;
          console.log('Google API Key:', fallbackValue);
          break;
      }

      if (!fallbackValue) {
        throw new Error(
          `Secret ${secretName} not found in environment variables (placeholder detected)`
        );
      }

      return fallbackValue;
    }

    return data.value;
  } catch (error) {
    Sentry.captureException(error);
    console.error(`Error getting secret ${secretName}:`, error);
    throw error;
  }
}
