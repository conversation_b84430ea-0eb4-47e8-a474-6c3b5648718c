'use client';

import { Variable } from '@/components/ui/tiptap-editor';

// Types from InterviewV2
export interface Person {
  id: string;
  type: 'individual' | 'charity' | 'business';
  firstName?: string;
  middleName?: string;
  lastName?: string;
  entityName?: string;
  dateOfBirth?: string;
  ein?: string;
  phoneNumber?: string;
  address?: string;
  relationshipType?: string; // e.g., 'Spouse', 'Friend', 'Family', 'Business Partner', etc.
}

export interface StepsData {
  people?: Person[];
  profile?: {
    firstName?: string;
    middleName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    dateOfBirth?: string;
    address?: {
      street?: string;
      street2?: string;
      county?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    maritalStatus?: 'married' | 'single' | 'widowed';
    spouse?: Person; // Reference to spouse in People Library
    soundMind?: boolean;
    selectedDocuments?: {
      will?: boolean;
      trust?: boolean;
      financialPOA?: boolean;
      medicalPOA?: boolean;
    };
  };
  emergency?: {
    dependents?: Array<{
      id: string;
      person: Person; // Reference to person in People Library (relationshipType is stored in person)
      careInstructions: string;
    }>;
    pets?: Array<{
      id: string;
      name: string;
      primaryGuardian?: Person; // Required field
      secondaryGuardian?: Person; // Optional backup
      petTrustSupport?: number; // Dollar amount for pet care
      takerOfLastResort?: Person; // Final guardian field
    }>;
    noDependents?: boolean;
    noPets?: boolean;
  };
  afterYouDie?: {
    executor?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    successorTrustee?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    properties?: Array<{
      id: string;
      type: string;
      address: string;
      title: string;
      ownership: number;
      coOwners?: Array<{ person: Person; percentage: number }>;
      recipient?: Person;
    }>;
    specificGifts?: Array<{
      id: string;
      recipient: Person;
      type: 'financial' | 'other';
      bankName?: string;
      accountNumber?: string;
      accountType?: 'IRA' | 'Brokerage' | 'Savings' | 'Checking' | 'Other';
      distributionMethod?: 'fixed' | 'percentage';
      amount?: number;
      description?: string;
    }>;
    mainBeneficiaries?: Array<{
      id: string;
      primaryBeneficiary: Person;
      percentage: number;
      successors: Array<{ person: Person; isDescendants?: boolean }>;
      distributionStages?: Array<{ age: number; percentage: number }>;
    }>;
    lastResortBeneficiary?: Person;
    noOwnedProperties?: boolean;
  };
  financial?: {
    agent?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    powers?: Record<string, boolean>;
  };
  medical?: {
    proxy?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    directives?: Record<string, boolean>;
    proxyAuthority?: string;
    wantConsultation?: boolean;
    consultationPeople?: Person[];
    organDonation?: {
      enabled?: boolean;
      specificOrgans?: string[];
      limitations?: string;
    };
    medicalInfoAccessPeople?: Person[];
  };
  additional?: {
    personalProperty?: 'spouse' | 'list';
    burial?: 'buried' | 'cremated' | 'other';
    burialOther?: string;
    guardian?: {
      useCompany?: boolean;
      primary?: Person;
      successors?: Person[];
    };
    legalProvisions?: {
      disclaimerTrust?: boolean;
      drugAbuse?: boolean;
      mineralRights?: boolean;
    };
  };
  review?: {
    completed?: boolean;
  };
}

/**
 * Creates ALL possible variables for TipTap editor from InterviewV2 structure
 * This creates variables for all possible fields, not just filled ones
 */
export const createVariablesFromInterviewV2 = (): Variable[] => {
  const variables: Variable[] = [];

  // Helper function to add variable
  const addVariable = (
    id: string,
    label: string,
    value: string,
    questionInfo?: {
      question: string;
      description: string;
      section: string;
      dataType: string;
      possibleValues?: string[];
      example?: string;
    }
  ) => {
    variables.push({ id, label, value, questionInfo });
  };

  // Profile variables - always add all possible profile variables
  addVariable('profile_firstName', 'First Name', `{{firstName}}`, {
    question: 'What is your first name?',
    description: 'Your legal first name as it appears on official documents',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'John',
  });
  addVariable('profile_middleName', 'Middle Name', `{{middleName}}`, {
    question: 'What is your middle name?',
    description: 'Your middle name or initial (optional)',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'Michael',
  });
  addVariable('profile_lastName', 'Last Name', `{{lastName}}`, {
    question: 'What is your last name?',
    description: 'Your legal last name as it appears on official documents',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'Smith',
  });
  addVariable('profile_fullName', 'Full Name', `{{firstName}} {{lastName}}`, {
    question: 'Full Name (computed)',
    description: 'Automatically combines first and last name',
    section: 'Profile Information',
    dataType: 'Text (computed)',
    example: 'John Smith',
  });
  addVariable('profile_email', 'Email', `{{email}}`, {
    question: 'What is your email address?',
    description: 'Your primary email address for communication',
    section: 'Profile Information',
    dataType: 'Email',
    example: '<EMAIL>',
  });
  addVariable('profile_phone', 'Phone', `{{phone}}`, {
    question: 'What is your phone number?',
    description: 'Your primary phone number',
    section: 'Profile Information',
    dataType: 'Phone Number',
    example: '+****************',
  });
  addVariable('profile_dateOfBirth', 'Date of Birth', `{{dateOfBirth}}`, {
    question: 'What is your date of birth?',
    description: 'Your birth date for age verification and legal purposes',
    section: 'Profile Information',
    dataType: 'Date',
    example: '1985-03-15',
  });
  addVariable('profile_maritalStatus', 'Marital Status', `{{maritalStatus}}`, {
    question: 'What is your marital status?',
    description: 'Your current marital status',
    section: 'Profile Information',
    dataType: 'Selection',
    possibleValues: ['married', 'single', 'widowed'],
    example: 'married',
  });

  // Address variables
  addVariable(
    'profile_address_street',
    'Street Address',
    `{{address_street}}`,
    {
      question: 'What is your street address?',
      description: 'Your street address including house number and street name',
      section: 'Profile Information',
      dataType: 'Text',
      example: '123 Main Street',
    }
  );
  addVariable('profile_address_city', 'City', `{{address_city}}`, {
    question: 'What city do you live in?',
    description: 'Your city of residence',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'Springfield',
  });
  addVariable('profile_address_county', 'County', `{{address_county}}`, {
    question: 'What county do you live in?',
    description: 'Your county of residence',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'Los Angeles County',
  });
  addVariable('profile_address_state', 'State', `{{address_state}}`, {
    question: 'What state do you live in?',
    description: 'Your state of residence',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'California',
  });
  addVariable('profile_address_zip', 'ZIP Code', `{{address_zip}}`, {
    question: 'What is your ZIP code?',
    description: 'Your postal ZIP code',
    section: 'Profile Information',
    dataType: 'Text',
    example: '90210',
  });
  addVariable('profile_address_country', 'Country', `{{address_country}}`, {
    question: 'What country do you live in?',
    description: 'Your country of residence',
    section: 'Profile Information',
    dataType: 'Text',
    example: 'United States',
  });
  addVariable(
    'profile_address_full',
    'Full Address',
    `{{address_street}}, {{address_city}}, {{address_county}}, {{address_state}} {{address_zip}}`,
    {
      question: 'Full Address (computed)',
      description: 'Automatically combines all address fields including county',
      section: 'Profile Information',
      dataType: 'Text (computed)',
      example:
        '123 Main Street, Springfield, Los Angeles County, California 90210',
    }
  );

  // Spouse variables
  addVariable(
    'profile_spouse_firstName',
    'Spouse First Name',
    `{{spouse_firstName}}`,
    {
      question: "What is your spouse's first name?",
      description: "Your spouse's legal first name",
      section: 'Profile Information',
      dataType: 'Text',
      example: 'Mary',
    }
  );
  addVariable(
    'profile_spouse_middleName',
    'Spouse Middle Name',
    `{{spouse_middleName}}`,
    {
      question: "What is your spouse's middle name?",
      description: "Your spouse's middle name or initial (optional)",
      section: 'Profile Information',
      dataType: 'Text',
      example: 'Elizabeth',
    }
  );
  addVariable(
    'profile_spouse_lastName',
    'Spouse Last Name',
    `{{spouse_lastName}}`,
    {
      question: "What is your spouse's last name?",
      description: "Your spouse's legal last name",
      section: 'Profile Information',
      dataType: 'Text',
      example: 'Smith',
    }
  );
  addVariable(
    'profile_spouse_fullName',
    'Spouse Full Name',
    `{{spouse_firstName}} {{spouse_middleName}} {{spouse_lastName}}`,
    {
      question: 'Spouse Full Name (computed)',
      description: 'Automatically combines spouse first, middle, and last name',
      section: 'Profile Information',
      dataType: 'Text (computed)',
      example: 'Mary Elizabeth Smith',
    }
  );
  addVariable(
    'profile_spouse_dateOfBirth',
    'Spouse Date of Birth',
    `{{spouse_dateOfBirth}}`,
    {
      question: "What is your spouse's date of birth?",
      description: "Your spouse's birth date",
      section: 'Profile Information',
      dataType: 'Date',
      example: '1987-08-22',
    }
  );

  // Document selection variables
  addVariable('profile_will_selected', 'Will Selected', `{{will_selected}}`, {
    question: 'Do you want to create a Will?',
    description: 'Whether the user selected to create a Will document',
    section: 'Profile Information',
    dataType: 'Boolean',
    possibleValues: ['Yes', 'No'],
    example: 'Yes',
  });
  addVariable(
    'profile_trust_selected',
    'Trust Selected',
    `{{trust_selected}}`,
    {
      question: 'Do you want to create a Trust?',
      description: 'Whether the user selected to create a Trust document',
      section: 'Profile Information',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );
  addVariable(
    'profile_financialPOA_selected',
    'Financial POA Selected',
    `{{financialPOA_selected}}`,
    {
      question: 'Do you want to create a Financial Power of Attorney?',
      description:
        'Whether the user selected to create a Financial POA document',
      section: 'Profile Information',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'Yes',
    }
  );
  addVariable(
    'profile_medicalPOA_selected',
    'Medical POA Selected',
    `{{medicalPOA_selected}}`,
    {
      question: 'Do you want to create a Medical Power of Attorney?',
      description: 'Whether the user selected to create a Medical POA document',
      section: 'Profile Information',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'Yes',
    }
  );

  // Sound mind
  addVariable('profile_soundMind', 'Sound Mind Declaration', `{{soundMind}}`, {
    question: 'Do you declare that you are of sound mind?',
    description: 'Legal declaration of mental competency',
    section: 'Profile Information',
    dataType: 'Boolean',
    possibleValues: ['Yes', 'No'],
    example: 'Yes',
  });

  // Emergency section variables
  addVariable(
    'emergency_dependents_count',
    'Number of Dependents',
    `{{emergency_dependents_count}}`,
    {
      question: 'How many dependents do you have?',
      description:
        'Count of people who depend on you for care (children, elderly parents, etc.)',
      section: 'Emergency Key Considerations',
      dataType: 'Number',
      example: '2',
    }
  );
  addVariable(
    'emergency_pets_count',
    'Number of Pets',
    `{{emergency_pets_count}}`,
    {
      question: 'How many pets do you have?',
      description: 'Count of pets that need care arrangements',
      section: 'Emergency Key Considerations',
      dataType: 'Number',
      example: '1',
    }
  );

  // No dependents/pets flags
  addVariable(
    'emergency_noDependents',
    'No Dependents Flag',
    `{{emergency_noDependents}}`,
    {
      question: 'Do you have any dependents?',
      description: 'Whether user explicitly indicated they have no dependents',
      section: 'Emergency Key Considerations',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );

  addVariable('emergency_noPets', 'No Pets Flag', `{{emergency_noPets}}`, {
    question: 'Do you have any pets?',
    description: 'Whether user explicitly indicated they have no pets',
    section: 'Emergency Key Considerations',
    dataType: 'Boolean',
    possibleValues: ['Yes', 'No'],
    example: 'No',
  });

  // Dependents array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'emergency_dependents_loop',
    'Dependents Loop (for templates)',
    '{{#each emergency_dependents}}\n' +
      '  Name: {{person.firstName}} {{person.lastName}}\n' +
      '  Relationship: {{person.relationshipType}}\n' +
      '  Care Instructions: {{careInstructions}}\n' +
      '{{/each}}',
    {
      question: 'List your dependents',
      description: 'Loop through all dependents with their details',
      section: 'Emergency Key Considerations',
      dataType: 'Array Loop',
      example:
        'Loops through each dependent showing name, relationship, and care instructions',
    }
  );

  // Conditional dependents display
  addVariable(
    'emergency_dependents_conditional',
    'Show dependents if any exist',
    '{{#if (or (gt (length emergency_dependents) 0) emergency_noDependents)}}\n' +
      '{{#if (gt (length emergency_dependents) 0)}}\n' +
      'I have {{length emergency_dependents}} dependent(s):\n' +
      '{{#each emergency_dependents}}\n' +
      '- {{person.firstName}} {{person.lastName}} ({{person.relationshipType}})\n' +
      '{{/each}}\n' +
      '{{else}}\n' +
      'I have no dependents.\n' +
      '{{/if}}\n' +
      '{{else}}\n' +
      'Dependents information not provided.\n' +
      '{{/if}}',
    {
      question: 'Conditional dependents display',
      description:
        'Shows dependents list if any exist, shows "no dependents" if explicitly stated, or shows "not provided" message',
      section: 'Emergency Key Considerations',
      dataType: 'Conditional Logic',
      example:
        'Shows different text based on whether dependents exist or user indicated no dependents',
    }
  );

  // Note: Individual dependent properties ({{person.firstName}}, {{person.relationshipType}}, {{careInstructions}})
  // are only available within the {{#each emergency_dependents}} loop context

  // Pets array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'emergency_pets_loop',
    'Pets Loop (for templates)',
    '{{#each emergency_pets}}\n' +
      '  Pet Name: {{name}}\n' +
      '  Primary Guardian: {{primaryGuardian}}\n' +
      '  {{#if secondaryGuardian}}Secondary Guardian: {{secondaryGuardian}}\n{{/if}}' +
      '  {{#if petTrustSupport}}Pet Trust Support: ${{petTrustSupport}}\n{{/if}}' +
      '  {{#if takerOfLastResort}}Taker of Last Resort: {{takerOfLastResort}}\n{{/if}}' +
      '{{/each}}'
  );

  // Conditional pets display
  addVariable(
    'emergency_pets_conditional',
    'Show pets if any exist',
    '{{#if (or (gt (length emergency_pets) 0) emergency_noPets)}}\n' +
      '{{#if (gt (length emergency_pets) 0)}}\n' +
      'I have {{length emergency_pets}} pet(s):\n' +
      '{{#each emergency_pets}}\n' +
      '- {{name}}\n' +
      '  Primary Guardian: {{primaryGuardian}}\n' +
      '  {{#if secondaryGuardian}}Secondary Guardian: {{secondaryGuardian}}\n{{/if}}' +
      '  {{#if petTrustSupport}}Pet Trust Support: ${{petTrustSupport}}\n{{/if}}' +
      '  {{#if takerOfLastResort}}Taker of Last Resort: {{takerOfLastResort}}\n{{/if}}' +
      '{{/each}}\n' +
      '{{else}}\n' +
      'I have no pets.\n' +
      '{{/if}}\n' +
      '{{else}}\n' +
      'Pet information not provided.\n' +
      '{{/if}}',
    {
      question: 'Conditional pets display',
      description:
        'Shows pets list if any exist, shows "no pets" if explicitly stated, or shows "not provided" message',
      section: 'Emergency Key Considerations',
      dataType: 'Conditional Logic',
      example:
        'Shows different text based on whether pets exist or user indicated no pets',
    }
  );

  // Note: Individual pet properties ({{name}}, {{primaryGuardian}}, {{secondaryGuardian}}, etc.)
  // are only available within the {{#each emergency_pets}} loop context

  // After You Die section variables
  addVariable(
    'afterYouDie_executor_useCompany',
    'Executor Use Company',
    `{{afterYouDie_executor_useCompany}}`,
    {
      question: 'Do you want to use our company as executor?',
      description:
        'Whether to use the company or a person as executor of your will',
      section: 'After You Die',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );
  addVariable(
    'afterYouDie_executor_primary',
    'Primary Executor',
    `{{afterYouDie_executor_primary}}`,
    {
      question: 'Who is your primary executor?',
      description: 'The person responsible for carrying out your will',
      section: 'After You Die',
      dataType: 'Person Object',
      example: 'Jane Smith',
    }
  );
  addVariable(
    'afterYouDie_executor_successors',
    'Executor Successors',
    `{{afterYouDie_executor_successors}}`,
    {
      question: 'Who are your successor executors?',
      description: 'Backup executors if the primary cannot serve',
      section: 'After You Die',
      dataType: 'Array of Person Objects',
      example: 'Bob Johnson, Mary Wilson',
    }
  );

  addVariable(
    'afterYouDie_successorTrustee_useCompany',
    'Successor Trustee Use Company',
    `{{afterYouDie_successorTrustee_useCompany}}`,
    {
      question: 'Do you want to use our company as your successor trustee?',
      description:
        'Whether to use the company or a person as successor trustee',
      section: 'After You Die',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );
  addVariable(
    'afterYouDie_successorTrustee_primary',
    'Primary Successor Trustee',
    `{{afterYouDie_successorTrustee_primary}}`,
    {
      question: 'Who is your primary successor trustee?',
      description: 'The person who will manage your trust after you die',
      section: 'After You Die',
      dataType: 'Person Object',
      example: 'Michael Johnson',
    }
  );
  addVariable(
    'afterYouDie_successorTrustee_successors',
    'Successor Trustee Successors',
    `{{afterYouDie_successorTrustee_successors}}`,
    {
      question: 'Who are your backup successor trustees?',
      description: 'Backup trustees if the primary cannot serve',
      section: 'After You Die',
      dataType: 'Array of Person Objects',
      example: 'Jennifer Smith, Robert Wilson',
    }
  );

  addVariable(
    'afterYouDie_lastResortBeneficiary',
    'Last Resort Beneficiary',
    `{{afterYouDie_lastResortBeneficiary}}`,
    {
      question: 'Who is your last resort beneficiary?',
      description:
        'The person who receives your estate if all other beneficiaries cannot',
      section: 'After You Die',
      dataType: 'Person Object',
      example: 'American Red Cross',
    }
  );

  // No owned properties flag
  addVariable(
    'afterYouDie_noOwnedProperties',
    'No Owned Properties Flag',
    `{{afterYouDie_noOwnedProperties}}`,
    {
      question: 'Do you own any properties?',
      description: 'Whether user explicitly indicated they own no properties',
      section: 'After You Die',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );

  // Properties array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'afterYouDie_properties_loop',
    'Properties Loop (for templates)',
    '{{#each afterYouDie_properties}}\n' +
      '  Property Type: {{type}}\n' +
      '  Address: {{address}}\n' +
      '  Title: {{title}}\n' +
      '  Ownership: {{ownership}}%\n' +
      '  Recipient: {{recipient}}\n' +
      '{{/each}}',
    {
      question: 'List your properties',
      description: 'Loop through all properties with their details',
      section: 'After You Die',
      dataType: 'Array Loop',
      example:
        'Loops through each property showing type, address, ownership, and recipient',
    }
  );

  // Conditional properties display
  addVariable(
    'afterYouDie_properties_conditional',
    'Show properties if any exist',
    '{{#if (or (gt (length afterYouDie_properties) 0) afterYouDie_noOwnedProperties)}}\n' +
      '{{#if (gt (length afterYouDie_properties) 0)}}\n' +
      'I own {{length afterYouDie_properties}} propert(y/ies):\n' +
      '{{#each afterYouDie_properties}}\n' +
      '- {{type}} at {{address}} ({{ownership}}% ownership) → {{recipient}}\n' +
      '{{/each}}\n' +
      '{{else}}\n' +
      'I do not own any properties.\n' +
      '{{/if}}\n' +
      '{{else}}\n' +
      'Property information not provided.\n' +
      '{{/if}}',
    {
      question: 'Conditional properties display',
      description:
        'Shows properties list if any exist, shows "no properties" if explicitly stated, or shows "not provided" message',
      section: 'After You Die',
      dataType: 'Conditional Logic',
      example:
        'Shows different text based on whether properties exist or user indicated no properties',
    }
  );

  // Note: Individual property properties ({{type}}, {{address}}, {{title}}, {{ownership}}, {{recipient}})
  // are only available within the {{#each afterYouDie_properties}} loop context

  // Specific gifts array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'afterYouDie_specificGifts_loop',
    'Specific Gifts Loop (for templates)',
    '{{#each afterYouDie_specificGifts}}\n' +
      '  Recipient: {{recipient}}\n' +
      '  Type: {{type}}\n' +
      '  {{#if description}}Description: {{description}}{{/if}}\n' +
      '  {{#if amount}}Amount: ${{amount}}{{/if}}\n' +
      '  {{#if bankName}}Bank: {{bankName}}{{/if}}\n' +
      '  {{#if accountType}}Account Type: {{accountType}}{{/if}}\n' +
      '  {{#if accountNumber}}Account: {{accountNumber}}{{/if}}\n' +
      '{{/each}}',
    {
      question: 'List your specific gifts',
      description: 'Loop through all specific gifts with their details',
      section: 'After You Die',
      dataType: 'Array Loop',
      example:
        'Loops through each gift showing recipient, type, amount, account type, and description',
    }
  );

  // Conditional specific gifts display
  addVariable(
    'afterYouDie_specificGifts_conditional',
    'Show specific gifts if any exist',
    '{{#if (gt (length afterYouDie_specificGifts) 0)}}\n' +
      'I am making {{length afterYouDie_specificGifts}} specific gift(s):\n' +
      '{{#each afterYouDie_specificGifts}}\n' +
      '- {{#if (eq type "financial")}}${{amount}} to {{recipient}}{{#if bankName}} from {{bankName}}{{/if}}{{#if accountType}} ({{accountType}}){{/if}}{{else}}{{description}} to {{recipient}}{{/if}}\n' +
      '{{/each}}\n' +
      '{{else}}\n' +
      'I am not making any specific gifts.\n' +
      '{{/if}}',
    {
      question: 'Conditional specific gifts display',
      description:
        'Shows gifts list if any exist, otherwise shows "no gifts" message',
      section: 'After You Die',
      dataType: 'Conditional Logic',
      example: 'Shows different text based on whether specific gifts exist',
    }
  );

  // Note: Individual gift properties ({{recipient}}, {{type}}, {{description}}, {{amount}}, {{bankName}}, {{accountType}}, {{accountNumber}})
  // are only available within the {{#each afterYouDie_specificGifts}} loop context

  // Main beneficiaries array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'afterYouDie_mainBeneficiaries_loop',
    'Main Beneficiaries Loop (for templates)',
    '{{#each afterYouDie_mainBeneficiaries}}\n' +
      '  Primary Beneficiary: {{primaryBeneficiary}}\n' +
      '  Percentage: {{percentage}}%\n' +
      '  {{#if successors}}Successors: {{#each successors}}{{name}}{{#unless @last}}, {{/unless}}{{/each}}{{/if}}\n' +
      '{{/each}}'
  );

  // Conditional main beneficiaries display
  addVariable(
    'afterYouDie_mainBeneficiaries_conditional',
    'Show main beneficiaries if any exist',
    '{{#if (gt (length afterYouDie_mainBeneficiaries) 0)}}\n' +
      'My main beneficiaries are:\n' +
      '{{#each afterYouDie_mainBeneficiaries}}\n' +
      '- {{primaryBeneficiary}} ({{percentage}}%){{#if successors}} with successors: {{join successors ", "}}{{/if}}\n' +
      '{{/each}}\n' +
      '{{else}}\n' +
      'I have not designated any main beneficiaries.\n' +
      '{{/if}}'
  );

  // Note: Individual beneficiary properties ({{primaryBeneficiary}}, {{percentage}}, {{successors}})
  // are only available within the {{#each afterYouDie_mainBeneficiaries}} loop context

  // Financial section variables
  addVariable(
    'financial_agent_useCompany',
    'Financial Agent Use Company',
    `{{financial_agent_useCompany}}`,
    {
      question: 'Do you want to use our company as your financial agent?',
      description:
        'Whether to use the company or a person as financial power of attorney',
      section: 'Financial Decisions',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );
  addVariable(
    'financial_agent_primary',
    'Primary Financial Agent',
    `{{financial_agent_primary}}`,
    {
      question: 'Who is your primary financial agent?',
      description:
        'The person who will handle your financial affairs if you become incapacitated',
      section: 'Financial Decisions',
      dataType: 'Person Object',
      example: 'John Doe',
    }
  );
  addVariable(
    'financial_agent_successors',
    'Financial Agent Successors',
    `{{financial_agent_successors}}`,
    {
      question: 'Who are your successor financial agents?',
      description: 'Backup financial agents if the primary cannot serve',
      section: 'Financial Decisions',
      dataType: 'Array of Person Objects',
      example: 'Jane Smith, Bob Wilson',
    }
  );

  // Financial powers (common powers)
  const financialPowers = [
    'banking',
    'investments',
    'taxes',
    'insurance',
    'realEstate',
    'business',
    'litigation',
    'personalProperty',
    'benefits',
    'retirement',
    'gifts',
  ];
  financialPowers.forEach(power => {
    addVariable(
      `financial_power_${power}`,
      `Financial Power: ${power}`,
      `{{financial_power_${power}}}`
    );
  });

  // Medical section variables
  addVariable(
    'medical_proxy_useCompany',
    'Medical Proxy Use Company',
    `{{medical_proxy_useCompany}}`,
    {
      question: 'Do you want to use our company as your healthcare proxy?',
      description:
        'Whether to use the company or a person as healthcare power of attorney',
      section: 'Medical Decisions',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'No',
    }
  );
  addVariable(
    'medical_proxy_primary',
    'Primary Medical Proxy',
    `{{medical_proxy_primary}}`,
    {
      question: 'Who is your primary healthcare proxy?',
      description:
        'The person who will make medical decisions for you if you cannot',
      section: 'Medical Decisions',
      dataType: 'Person Object',
      example: 'Mary Johnson',
    }
  );
  addVariable(
    'medical_proxy_successors',
    'Medical Proxy Successors',
    `{{medical_proxy_successors}}`,
    {
      question: 'Who are your successor healthcare proxies?',
      description: 'Backup healthcare proxies if the primary cannot serve',
      section: 'Medical Decisions',
      dataType: 'Array of Person Objects',
      example: 'Tom Brown, Lisa Davis',
    }
  );
  addVariable(
    'medical_proxyAuthority',
    'Medical Proxy Authority',
    `{{medical_proxyAuthority}}`,
    {
      question: 'What level of authority should your proxy have?',
      description:
        'The scope of decision-making authority for your healthcare proxy',
      section: 'Medical Decisions',
      dataType: 'Selection',
      possibleValues: [
        'Full authority',
        'Limited authority',
        'Specific conditions only',
      ],
      example: 'Full authority',
    }
  );
  addVariable(
    'medical_wantConsultation',
    'Want Medical Consultation',
    `{{medical_wantConsultation}}`,
    {
      question:
        'Do you want your proxy to consult with others before making decisions?',
      description:
        'Whether the healthcare proxy should consult with family/friends',
      section: 'Medical Decisions',
      dataType: 'Boolean',
      possibleValues: ['Yes', 'No'],
      example: 'Yes',
    }
  );
  addVariable(
    'medical_consultationPeople',
    'Medical Consultation People',
    `{{medical_consultationPeople}}`,
    {
      question: 'Who should your proxy consult with?',
      description:
        'People the healthcare proxy should consult before making decisions',
      section: 'Medical Decisions',
      dataType: 'Array of Person Objects',
      example: 'Dr. Smith, Sarah Wilson',
    }
  );
  addVariable(
    'medical_medicalInfoAccessPeople',
    'Medical Information Access People',
    `{{medical_medicalInfoAccessPeople}}`,
    {
      question: 'Who else should access your medical info (HIPAA release)?',
      description: 'People who have access to medical information',
      section: 'Medical Decisions',
      dataType: 'Array of Person Objects',
      example: 'Dr. Smith, Sarah Wilson',
    }
  );

  // Medical directives (common directives)
  const medicalDirectives = [
    'lifeSustaining',
    'artificialNutrition',
    'antibiotics',
    'dialysis',
    'cpr',
    'ventilator',
    'painMedication',
    'comfortCare',
  ];
  medicalDirectives.forEach(directive => {
    addVariable(
      `medical_directive_${directive}`,
      `Medical Directive: ${directive}`,
      `{{medical_directive_${directive}}}`
    );
  });

  // Organ donation variables
  addVariable(
    'medical_organDonation_enabled',
    'Organ Donation Enabled',
    `{{medical_organDonation_enabled}}`
  );
  addVariable(
    'medical_organDonation_specificOrgans',
    'Specific Organs for Donation',
    `{{medical_organDonation_specificOrgans}}`
  );
  addVariable(
    'medical_organDonation_limitations',
    'Organ Donation Limitations',
    `{{medical_organDonation_limitations}}`
  );

  // Additional section variables
  addVariable(
    'additional_personalProperty',
    'Personal Property Distribution',
    `{{additional_personalProperty}}`
  );
  addVariable(
    'additional_burial',
    'Burial Preference',
    `{{additional_burial}}`
  );
  addVariable(
    'additional_burialOther',
    'Other Burial Instructions',
    `{{additional_burialOther}}`
  );

  // Guardian variables
  addVariable(
    'additional_guardian_useCompany',
    'Guardian Use Company',
    `{{additional_guardian_useCompany}}`
  );
  addVariable(
    'additional_guardian_primary',
    'Primary Guardian',
    `{{additional_guardian_primary}}`
  );
  addVariable(
    'additional_guardian_successors',
    'Guardian Successors',
    `{{additional_guardian_successors}}`
  );

  // Legal provisions
  addVariable(
    'additional_legalProvision_disclaimerTrust',
    'Disclaimer Trust Provision',
    `{{additional_legalProvision_disclaimerTrust}}`
  );
  addVariable(
    'additional_legalProvision_drugAbuse',
    'Drug Abuse Provision',
    `{{additional_legalProvision_drugAbuse}}`
  );
  addVariable(
    'additional_legalProvision_mineralRights',
    'Mineral Rights Provision',
    `{{additional_legalProvision_mineralRights}}`
  );

  // People array - use Handlebars loops instead of hardcoded variables
  addVariable(
    'people_loop',
    'People Loop (for templates)',
    '{{#each people}}\n' +
      '  Type: {{type}}\n' +
      '  {{#if (eq type "individual")}}\n' +
      '    Name: {{firstName}} {{#if middleName}}{{middleName}} {{/if}}{{lastName}}\n' +
      '    {{#if dateOfBirth}}Date of Birth: {{dateOfBirth}}{{/if}}\n' +
      '  {{else}}\n' +
      '    Entity Name: {{entityName}}\n' +
      '    {{#if ein}}EIN: {{ein}}{{/if}}\n' +
      '  {{/if}}\n' +
      '  {{#if phoneNumber}}Phone: {{phoneNumber}}{{/if}}\n' +
      '  {{#if address}}Address: {{address}}{{/if}}\n' +
      '{{/each}}'
  );

  // Note: Individual person properties ({{type}}, {{firstName}}, {{lastName}}, {{entityName}}, etc.)
  // are only available within the {{#each people}} loop context

  // Standard document variables (only the essential ones)
  addVariable('document_todayDate', "Today's Date", '{{document.todayDate}}', {
    question: "Today's Date (computed)",
    description:
      'Automatically generated current date when the document is created',
    section: 'Document Information',
    dataType: 'Date (computed)',
    example: 'January 15, 2024',
  });
  // addVariable('document_type', 'Document Type', '{{document.type}}', {
  //   question: 'Document Type (computed)',
  //   description: 'The type of legal document being generated',
  //   section: 'Document Information',
  //   dataType: 'Text (computed)',
  //   possibleValues: ['Will', 'Trust', 'Healthcare POA', 'Financial POA', 'Advance Directive'],
  //   example: 'Will',
  // });
  addVariable('document_state', 'Document State', '{{document.state}}', {
    question: 'Document State (computed)',
    description:
      'The state jurisdiction for which this document is being generated',
    section: 'Document Information',
    dataType: 'Text (computed)',
    example: 'California',
  });
  // addVariable(
  //   'execution_date_day',
  //   'Execution Date Day',
  //   '{{execution_date_day}}',
  //   {
  //     question: 'Execution Date Day (computed)',
  //     description: 'The day of the month when the document is executed',
  //     section: 'Document Information',
  //     dataType: 'Number (computed)',
  //     example: '15',
  //   }
  // );
  // addVariable(
  //   'execution_date_month',
  //   'Execution Date Month',
  //   '{{execution_date_month}}',
  //   {
  //     question: 'Execution Date Month (computed)',
  //     description: 'The month when the document is executed',
  //     section: 'Document Information',
  //     dataType: 'Text (computed)',
  //     example: 'January',
  //   }
  // );
  // addVariable(
  //   'execution_date_year',
  //   'Execution Date Year',
  //   '{{execution_date_year}}',
  //   {
  //     question: 'Execution Date Year (computed)',
  //     description: 'The year when the document is executed',
  //     section: 'Document Information',
  //     dataType: 'Number (computed)',
  //     example: '2024',
  //   }
  // );

  return variables;
};

/**
 * Gets the structure of InterviewV2 questions (without real data)
 * This shows what questions are available in each section
 */
export const getInterviewV2Structure = (): {
  section: string;
  questions: { question: string; description: string }[];
}[] => {
  return [
    {
      section: 'Your Profile',
      questions: [
        { question: 'First Name', description: 'Your legal first name' },
        { question: 'Middle Name', description: 'Your middle name (optional)' },
        { question: 'Last Name', description: 'Your legal last name' },
        { question: 'Email', description: 'Your email address' },
        { question: 'Phone', description: 'Your phone number' },
        { question: 'Date of Birth', description: 'Your date of birth' },
        {
          question: 'Marital Status',
          description: 'Single, Married, or Widowed',
        },
        { question: 'Street Address', description: 'Your street address' },
        { question: 'City', description: 'Your city' },
        { question: 'County', description: 'Your county' },
        { question: 'State', description: 'Your state' },
        { question: 'ZIP Code', description: 'Your ZIP code' },
        { question: 'Country', description: 'Your country' },
        {
          question: 'Spouse First Name',
          description: "Your spouse's first name (if married)",
        },
        {
          question: 'Spouse Last Name',
          description: "Your spouse's last name (if married)",
        },
        {
          question: 'Spouse Date of Birth',
          description: "Your spouse's date of birth (if married)",
        },
        {
          question: 'Selected Documents',
          description: 'Which documents you want to create (Will, Trust, POAs)',
        },
        {
          question: 'Sound Mind Declaration',
          description: 'Declaration of sound mind and memory',
        },
      ],
    },
    {
      section: 'People Library',
      questions: [
        {
          question: 'Person Type',
          description: 'Individual, Charity, or Business',
        },
        {
          question: 'Person Name',
          description: 'Full name for individuals or entity name',
        },
        {
          question: 'Date of Birth',
          description: 'Date of birth for individuals',
        },
        { question: 'Phone Number', description: 'Contact phone number' },
        { question: 'Address', description: 'Full address' },
      ],
    },
    {
      section: 'Emergency Key Considerations',
      questions: [
        {
          question: 'Dependents',
          description: 'People who depend on you for care',
        },
        {
          question: 'Dependent Relationship',
          description: 'How they are related to you',
        },
        {
          question: 'Care Instructions',
          description: 'Special care instructions for dependents',
        },
        { question: 'Pets', description: 'Pets that need care' },
        {
          question: 'Pet Guardian',
          description: 'Who will care for your pets',
        },
      ],
    },
    {
      section: 'After You Die',
      questions: [
        { question: 'Executor', description: 'Who will execute your will' },
        {
          question: 'Successor Trustee',
          description: 'Who will manage your trust',
        },
        {
          question: 'Properties',
          description: 'Real estate and property ownership',
        },
        {
          question: 'Property Recipients',
          description: 'Who inherits specific properties',
        },
        {
          question: 'Specific Gifts',
          description: 'Specific monetary or item gifts',
        },
        {
          question: 'Gift Recipients',
          description: 'Who receives specific gifts',
        },
        {
          question: 'Main Beneficiaries',
          description: 'Primary beneficiaries of your estate',
        },
        {
          question: 'Beneficiary Percentages',
          description: 'What percentage each beneficiary receives',
        },
        {
          question: 'Successor Beneficiaries',
          description: 'Backup beneficiaries if primary cannot inherit',
        },
        {
          question: 'Last Resort Beneficiary',
          description: 'Final backup if all others cannot inherit',
        },
      ],
    },
    {
      section: 'Financial Decisions',
      questions: [
        {
          question: 'Financial Agent',
          description: 'Who makes financial decisions if you cannot',
        },
        {
          question: 'Agent Successors',
          description: 'Backup financial agents',
        },
        {
          question: 'Banking Powers',
          description: 'Authority over bank accounts',
        },
        {
          question: 'Investment Powers',
          description: 'Authority over investments',
        },
        { question: 'Tax Powers', description: 'Authority to handle taxes' },
        {
          question: 'Insurance Powers',
          description: 'Authority over insurance matters',
        },
        {
          question: 'Real Estate Powers',
          description: 'Authority over real estate transactions',
        },
        {
          question: 'Business Powers',
          description: 'Authority over business interests',
        },
        {
          question: 'Other Financial Powers',
          description: 'Additional financial authorities',
        },
      ],
    },
    {
      section: 'Medical Decisions',
      questions: [
        {
          question: 'Healthcare Proxy',
          description: 'Who makes medical decisions if you cannot',
        },
        {
          question: 'Proxy Successors',
          description: 'Backup healthcare proxies',
        },
        {
          question: 'Proxy Authority Level',
          description: 'How much authority your proxy has',
        },
        {
          question: 'Medical Consultation',
          description: 'Whether proxy should consult others',
        },
        {
          question: 'Consultation People',
          description: 'Who should be consulted on medical decisions',
        },
        {
          question: 'Life-Sustaining Treatment',
          description: 'Preferences for life support',
        },
        {
          question: 'Artificial Nutrition',
          description: 'Preferences for feeding tubes',
        },
        {
          question: 'Antibiotics',
          description: 'Preferences for antibiotic treatment',
        },
        {
          question: 'Dialysis',
          description: 'Preferences for dialysis treatment',
        },
        {
          question: 'CPR',
          description: 'Preferences for cardiopulmonary resuscitation',
        },
        {
          question: 'Ventilator',
          description: 'Preferences for mechanical ventilation',
        },
        {
          question: 'Pain Medication',
          description: 'Preferences for pain management',
        },
        {
          question: 'Comfort Care',
          description: 'Preferences for comfort-focused care',
        },
        {
          question: 'Organ Donation',
          description: 'Whether you want to donate organs',
        },
        {
          question: 'Specific Organs',
          description: 'Which organs you want to donate',
        },
        {
          question: 'Donation Limitations',
          description: 'Any limitations on organ donation',
        },
      ],
    },
    {
      section: 'Additional Considerations',
      questions: [
        {
          question: 'Personal Property Distribution',
          description: 'How personal items should be distributed',
        },
        {
          question: 'Burial Preferences',
          description: 'Burial, cremation, or other preferences',
        },
        {
          question: 'Burial Instructions',
          description: 'Specific burial or memorial instructions',
        },
        {
          question: 'Guardian for Minors',
          description: 'Who will care for minor children',
        },
        {
          question: 'Guardian Successors',
          description: 'Backup guardians for minor children',
        },
        {
          question: 'Disclaimer Trust',
          description: 'Advanced legal provision for tax planning',
        },
        {
          question: 'Drug Abuse Provision',
          description: 'Protections against substance abuse by beneficiaries',
        },
        {
          question: 'Mineral Rights',
          description: 'Special handling of mineral and subsurface rights',
        },
      ],
    },
  ];
};
