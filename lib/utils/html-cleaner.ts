/**
 * HTML cleaning utilities for template content
 * Removes unnecessary formatting and normalizes HTML structure
 */

/**
 * Clean HTML content from TipTap editor
 * Removes excessive whitespace, unnecessary attributes, and normalizes structure
 */
export function cleanTipTapHTML(html: string): string {
  if (!html) return '';

  const originalLength = html.length;
  let cleaned = html;

  // First, fix encoding issues
  cleaned = fixHTMLEncoding(cleaned);

  // Remove excessive non-breaking spaces
  cleaned = cleaned.replace(
    /(&nbsp;\s*){10,}/g,
    '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'
  ); // Replace 10+ nbsp with 8
  cleaned = cleaned.replace(/(&nbsp;\s*){5,9}/g, '&nbsp;&nbsp;&nbsp;&nbsp;'); // Replace 5-9 nbsp with 4
  cleaned = cleaned.replace(/(&nbsp;\s*){3,4}/g, '&nbsp;&nbsp;'); // Replace 3-4 nbsp with 2

  // Preserve empty paragraph spacing
  // Intentionally keep <p></p> and <p>&nbsp;</p> so user-inserted blank lines are not removed.
  // We previously removed these, which caused blank lines to disappear in generated documents.

  // Normalize text alignment (keep center and right, remove default left and justify)
  cleaned = cleaned.replace(/style="text-align:\s*justify"/g, ''); // Remove justify alignment (default)
  cleaned = cleaned.replace(/style="text-align:\s*left"/g, ''); // Remove left alignment (default)
  // Keep center and right alignments as they are important

  // Clean up excessive spacing in text-align center
  cleaned = cleaned.replace(
    /<p style="text-align:\s*center">\s*<strong>\s*(&nbsp;\s*){5,}/g,
    '<p style="text-align: center"><strong>'
  );

  // Normalize underlines
  cleaned = cleaned.replace(
    /<u>\s*_{10,}\s*<\/u>/g,
    '<u>____________________________</u>'
  );

  // Preserve line spacing between paragraphs; do not collapse consecutive paragraph tags

  // Remove empty style attributes
  cleaned = cleaned.replace(/\s+style=""\s*/g, ' ');
  cleaned = cleaned.replace(/\s+style=''\s*/g, ' ');

  // Normalize whitespace
  cleaned = cleaned.replace(/\s+/g, ' '); // Multiple spaces to single space
  cleaned = cleaned.replace(/>\s+</g, '><'); // Remove spaces between tags

  // Clean up specific patterns from your example
  cleaned = cleaned.replace(
    /(&nbsp;\s*){20,}/g,
    '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'
  ); // Long nbsp sequences

  // Fix UTF-8 encoding issues - more comprehensive approach
  cleaned = cleaned.replace(/Â/g, ''); // Remove stray Â characters
  cleaned = cleaned.replace(/â€œ/g, '"'); // Fix opening quotes
  cleaned = cleaned.replace(/â€/g, '"'); // Fix closing quotes
  cleaned = cleaned.replace(/â€™/g, "'"); // Fix apostrophes
  cleaned = cleaned.replace(/â€"/g, '—'); // Fix em dashes
  cleaned = cleaned.replace(/â€"/g, '–'); // Fix en dashes
  cleaned = cleaned.replace(/â€¦/g, '...'); // Fix ellipsis

  // Fix double-encoded characters
  cleaned = cleaned.replace(/Ã¡/g, 'á'); // Fix á
  cleaned = cleaned.replace(/Ã©/g, 'é'); // Fix é
  cleaned = cleaned.replace(/Ã­/g, 'í'); // Fix í
  cleaned = cleaned.replace(/Ã³/g, 'ó'); // Fix ó
  cleaned = cleaned.replace(/Ãº/g, 'ú'); // Fix ú
  cleaned = cleaned.replace(/Ã±/g, 'ñ'); // Fix ñ

  // Fix common problematic sequences
  cleaned = cleaned.replace(/Â /g, ' '); // Fix non-breaking space issues
  cleaned = cleaned.replace(/\u00A0/g, ' '); // Replace non-breaking spaces with regular spaces

  // Fix common TipTap formatting issues
  cleaned = cleaned.replace(
    /<p[^>]*>\s*<strong>\s*(&nbsp;\s*)+/g,
    '<p><strong>&nbsp;&nbsp;&nbsp;&nbsp;'
  ); // Clean strong with nbsp
  cleaned = cleaned.replace(
    /(&nbsp;\s*)+\s*<\/strong>\s*<\/p>/g,
    '</strong></p>'
  ); // Clean ending strong with nbsp

  // Normalize underline patterns
  cleaned = cleaned.replace(
    /<u>\s*_+\s*<\/u>/g,
    '<u>____________________________</u>'
  );

  // Clean up dotted lines (common in legal documents)
  cleaned = cleaned.replace(/\.{10,}/g, '............................'); // Normalize dot sequences

  const finalLength = cleaned.length;
  const reduction = (
    ((originalLength - finalLength) / originalLength) *
    100
  ).toFixed(1);

  if (originalLength !== finalLength) {
    console.log(
      `🧹 HTML cleaned: ${originalLength} → ${finalLength} chars (${reduction}% reduction)`
    );
  }

  return cleaned.trim();
}

/**
 * Convert HTML to clean plain text while preserving structure
 */
export function htmlToCleanText(html: string): string {
  if (!html) return '';

  // Create a temporary div to parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Function to recursively process nodes and preserve formatting
  function processNode(node: Node): string {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent || '';
    }

    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      let result = '';

      // Process child nodes
      for (const child of Array.from(node.childNodes)) {
        result += processNode(child);
      }

      // Add appropriate spacing/breaks based on element type
      switch (tagName) {
        case 'p':
          return result + '\n\n';
        case 'div':
          return result + '\n';
        case 'br':
          return '\n';
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          return result + '\n\n';
        case 'li':
          return '• ' + result + '\n';
        case 'strong':
        case 'b':
          return result; // Keep text but remove formatting
        case 'em':
        case 'i':
          return result; // Keep text but remove formatting
        case 'u':
          return result; // Keep text but remove formatting
        default:
          return result;
      }
    }

    return '';
  }

  let cleanText = processNode(tempDiv);

  // Clean up excessive line breaks and spaces
  cleanText = cleanText
    .replace(/\n{3,}/g, '\n\n') // Replace 3+ line breaks with 2
    .replace(/^\n+/, '') // Remove leading line breaks
    .replace(/\n+$/, '') // Remove trailing line breaks
    .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
    .trim();

  return cleanText;
}

/**
 * Normalize HTML structure for better display
 */
export function normalizeHTMLStructure(html: string): string {
  if (!html) return '';

  let normalized = html;

  // Ensure proper paragraph structure
  normalized = normalized.replace(/([^>])\n([^<])/g, '$1 $2'); // Join broken lines within content

  // Fix common formatting issues
  normalized = normalized.replace(
    /<p><strong>([^<]+)<\/strong><\/p>/g,
    '<p><strong>$1</strong></p>'
  );

  // Ensure consistent spacing around elements
  normalized = normalized.replace(/(<\/p>)(<p)/g, '$1\n$2');
  normalized = normalized.replace(/(<\/h[1-6]>)(<p)/g, '$1\n$2');

  return normalized.trim();
}

/**
 * Remove all HTML tags and return plain text
 */
export function stripHTML(html: string): string {
  if (!html) return '';

  // Create a temporary div to safely parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  return tempDiv.textContent || tempDiv.innerText || '';
}

/**
 * Validate HTML content length (excluding HTML tags)
 */
export function getTextContentLength(html: string): number {
  return stripHTML(html).length;
}

/**
 * Fix encoding issues in HTML content
 */
export function fixHTMLEncoding(html: string): string {
  if (!html) return '';

  let fixed = html;

  // Fix common UTF-8 encoding issues
  const encodingFixes: [RegExp, string][] = [
    [/Â/g, ''], // Remove stray Â characters
    [/â€œ/g, '"'], // Fix opening quotes
    [/â€/g, '"'], // Fix closing quotes
    [/â€™/g, "'"], // Fix apostrophes
    [/â€"/g, '—'], // Fix em dashes
    [/â€"/g, '–'], // Fix en dashes
    [/Ã¡/g, 'á'], // Fix á
    [/Ã©/g, 'é'], // Fix é
    [/Ã­/g, 'í'], // Fix í
    [/Ã³/g, 'ó'], // Fix ó
    [/Ãº/g, 'ú'], // Fix ú
    [/Ã±/g, 'ñ'], // Fix ñ
    [/Ã¼/g, 'ü'], // Fix ü
    [/â€¦/g, '...'], // Fix ellipsis
    [/Â /g, ' '], // Fix non-breaking space issues
  ];

  encodingFixes.forEach(([pattern, replacement]) => {
    fixed = fixed.replace(pattern, replacement);
  });

  return fixed;
}
