'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
const client = generateClient<Schema>({
  authMode: 'iam',
});

export const resetUserPassword = async (
  email: string,
  token: string,
  password: string
) => {
  try {
    const { data: data } = await client.mutations.resetUserPassword({
      email: email,
      token: token,
      newPassword: password,
    });

    const resultData: { success: boolean; error?: string } = JSON.parse(
      data as string
    );

    if (resultData?.success) {
      return;
    } else {
      throw new Error(resultData?.error || 'Failed to reset password');
    }
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
};
