'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import type { AdminRole } from '@/lib/utils/admin-utils';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';
import routes from '@/utils/routes';
import { Button } from '@workspace/ui/button';
import Link from 'next/link';

interface AdminGuardProps {
  children: React.ReactNode;
  requiredRole?: AdminRole;
  requiredPermissions?: string[];
  fallbackMessage?: string;
  redirectTo?: string;
}

export function AdminGuard({
  children,
  requiredRole = 'ADMINS',
  requiredPermissions = [],
  fallbackMessage = 'You need to be logged in and have admin privileges to access this page.',
  redirectTo = routes.login,
}: AdminGuardProps) {
  const { user, userRoles, loading } = useAuth();
  const router = useRouter();

  // Check if user has required admin role
  const hasRequiredRole = userRoles.includes(requiredRole);

  // useEffect(() => {
  //   if (!loading && !user) {
  //     router.push(redirectTo);
  //   } else if (!loading && user && !hasRequiredRole && !hasAnyAdminRole) {
  //     router.push('/dashboard');
  //   }
  // }, [user, loading, hasRequiredRole, hasAnyAdminRole, router, redirectTo]);

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  if (!user) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>{fallbackMessage}</p>
              <div className='mt-4 flex justify-end'>
                <Button asChild>
                  <Link href={redirectTo}>Go to Login</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!hasRequiredRole) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                You don't have the required privileges to access this page.
              </p>
              <div className='mt-4 flex justify-end'>
                <Button asChild>
                  <Link href={routes.home}>Go Home</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
