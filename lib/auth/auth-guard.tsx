'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';
import routes from '@/utils/routes';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';

interface AuthGuardProps {
  children: React.ReactNode;
  fallbackMessage?: string;
  redirectTo?: string;
}

export function AuthGuard({
  children,
  fallbackMessage = 'You need to be logged in to access this page.',
  redirectTo = routes.login,
}: AuthGuardProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Redirect unauthenticated users directly to login
  useEffect(() => {
    if (!loading && !user) {
      // Check if session expired to preserve the parameter
      const sessionExpired = searchParams.get('sessionExpired') === 'true';
      const loginUrl = sessionExpired
        ? `${redirectTo}?sessionExpired=true`
        : redirectTo;
      router.replace(loginUrl);
    }
  }, [user, loading, router, redirectTo, searchParams]);

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  // If user is not authenticated, show loading skeleton while redirecting
  if (!user) {
    return <DashboardContentLayoutSkeleton />;
  }

  return <>{children}</>;
}
