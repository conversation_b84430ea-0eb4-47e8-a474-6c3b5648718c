'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { OnboardingStep } from '@/app/utils/userOnboarding';
import routes from '@/utils/routes';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';
import { useAuth } from '@/context/AuthContext';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export function OnboardingGuard({ children }: OnboardingGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { onboardingStatus, userRoles, loading } = useAuth();

  const isOnboardingPage = pathname === routes.member.onboarding;
  const isPrivileged = isAdmin(userRoles) || isWelonTrust(userRoles);
  const needsOnboarding = onboardingStatus !== OnboardingStep.COMPLETED;

  useEffect(() => {
    if (loading) return;
    if (isPrivileged) return;

    if (!isOnboardingPage && needsOnboarding) {
      router.replace(routes.member.onboarding);
    }
  }, [loading, isPrivileged, isOnboardingPage, needsOnboarding, router]);

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  if (!isOnboardingPage && !isPrivileged && needsOnboarding) {
    // Prevent flashing of protected content while redirecting
    return null;
  }

  return <>{children}</>;
}
