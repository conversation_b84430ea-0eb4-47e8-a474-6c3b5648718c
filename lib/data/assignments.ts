import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { removeWelonTrustFromUserCareDocuments, removeWelonTrustFromUserDocuments } from '@/lib/api/documents';

const client = generateClient<Schema>();

// Create a Welon Trust assignment
export async function createWelonTrustAssignment(
  userId: string,
  welonTrustData: {
    welonTrustUserId: string;
    welonTrustName: string;
    welonTrustEmail: string;
    assignedBy: string;
    welonTrustCognitoId: string;
  }
): Promise<void> {
  try {
    const { errors } = await client.models.WelonTrustAssignment.create({
      userId,
      welonTrustUserId: welonTrustData.welonTrustUserId,
      welonTrustName: welonTrustData.welonTrustName,
      welonTrustEmail: welonTrustData.welonTrustEmail,
      welonTrustCognitoId: welonTrustData.welonTrustCognitoId,
      assignedAt: new Date().toISOString(),
      assignedBy: welonTrustData.assignedBy,
      status: 'pending',
    });

    if (errors) {
      console.error('Errors creating Welon Trust assignment:', errors);
      throw new Error('Failed to create Welon Trust assignment');
    }
  } catch (error) {
    console.error('Error creating Welon Trust assignment:', error);
    throw new Error('Failed to create Welon Trust assignment');
  }
}

// Update a Welon Trust assignment
export async function updateWelonTrustAssignment(
  assignmentId: string,
  updates: {
    welonTrustUserId?: string;
    welonTrustName?: string;
    welonTrustEmail?: string;
    welonTrustCognitoId?: string;
    status?: 'active' | 'pending' | 'revoked';
  }
): Promise<void> {
  try {
    const { errors } = await client.models.WelonTrustAssignment.update({
      id: assignmentId,
      ...updates,
    });

    if (errors) {
      console.error('Errors updating Welon Trust assignment:', errors);
      throw new Error('Failed to update Welon Trust assignment');
    }
  } catch (error) {
    console.error('Error updating Welon Trust assignment:', error);
    throw new Error('Failed to update Welon Trust assignment');
  }
}

// Delete a Welon Trust assignment
export async function deleteWelonTrustAssignment(
  userId: string,
  assignmentId: string
): Promise<void> {
  try {
    await Promise.all([
      client.models.WelonTrustAssignment.delete({
        id: assignmentId,
      }),

      client.models.User.update({
        id: userId,
        assignedWelonTrustId: null,
      }),

      removeWelonTrustFromUserDocuments(userId),
      removeWelonTrustFromUserCareDocuments(userId),
    ]);
  } catch (error) {
    console.error('Error deleting Welon Trust assignment:', error);
    throw new Error('Failed to delete Welon Trust assignment');
  }
}
